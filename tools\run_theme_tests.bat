@echo off
REM Baseline主题测试程序启动脚本
REM 作者: <PERSON> Assistant
REM 创建时间: 2024

echo =================================================
echo           LA Baseline主题测试程序
echo =================================================
echo.

echo 检查构建目录...
if not exist "%~dp0..\build\bin\baseline_theme_test.exe" (
    echo [错误] 测试程序不存在，请先编译项目
    echo.
    echo 编译命令:
    echo cd /d "%~dp0.."
    echo cmake -DBUILD_THEME_TESTS=ON -S . -B build
    echo cmake --build build --target baseline_theme_test -j 4
    echo.
    pause
    exit /b 1
)

echo 检查主题库...
if not exist "%~dp0..\build\bin\libLA_themes.dll" (
    echo [错误] 主题库不存在，请先编译主题模块
    echo.
    echo 编译命令:
    echo cd /d "%~dp0.."
    echo cmake --build build --target LA_themes_lib
    echo.
    pause
    exit /b 1
)

echo 启动测试程序...
cd /d "%~dp0..\build\bin"
start "" "baseline_theme_test.exe"

echo.
echo [信息] 测试程序已启动
echo.
echo 测试说明:
echo 1. 程序默认使用 Baseline 暗色主题
echo 2. 点击"切换主题"按钮可切换到工业主题对比
echo 3. 验证紫色按钮、暗色背景、现代化圆角等特性
echo 4. 测试各种组件的交互效果
echo.
echo 预期效果:
echo - 深色背景 (#1e1e1e, #2d2d30)
echo - 紫色主按钮 (#7c3aed)
echo - 高对比度白色文字 (#cccccc)
echo - 现代化圆角和间距
echo - 符合 Obsidian Baseline 风格
echo.
pause