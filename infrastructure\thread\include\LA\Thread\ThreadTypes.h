#ifndef LA_THREAD_TYPES_H
#define LA_THREAD_TYPES_H

#include <QThread>
#include <QMutex>
#include <QWaitCondition>
#include <QQueue>
#include <QTimer>
#include <QDebug>
#include <QDateTime>
#include <QString>
#include <functional>
#include <memory>

namespace LA {
namespace Thread {

// 线程状态枚举
enum class ThreadState {
    Idle,           // 空闲
    Running,        // 运行中
    Suspended,      // 暂停
    Stopping,       // 正在停止
    Stopped,        // 已停止
    Error           // 错误状态
};

// 线程优先级
enum class ThreadPriority {
    Low = QThread::LowPriority,
    Normal = QThread::NormalPriority,
    High = QThread::HighPriority,
    TimeCritical = QThread::TimeCriticalPriority
};

// 任务类型
enum class TaskType {
    Normal,         // 普通任务
    Critical,       // 关键任务
    Communication,  // 通信任务
    Background      // 后台任务
};

// 线程统计信息
struct ThreadStatistics {
    QString threadName;
    ThreadState state;
    qint64 runningTime;     // 运行时间(ms)
    qint64 taskCount;       // 处理任务数
    qint64 errorCount;      // 错误次数
    double cpuUsage;        // CPU使用率
    qint64 memoryUsage;     // 内存使用(bytes)
    
    ThreadStatistics() : state(ThreadState::Idle), runningTime(0), 
                        taskCount(0), errorCount(0), cpuUsage(0.0), memoryUsage(0) {}
};

// 任务函数类型定义
using TaskFunction = std::function<void()>;
using TaskCallback = std::function<void(bool success, const QString& error)>;

// 任务结构
struct Task {
    TaskFunction function;
    TaskCallback callback;
    TaskType type;
    int priority;
    QString name;
    qint64 timestamp;
    
    // 默认构造函数
    Task() : type(TaskType::Normal), priority(0), timestamp(0) {}
    
    Task(TaskFunction func, TaskType t = TaskType::Normal, int p = 0, const QString& n = "")
        : function(func), type(t), priority(p), name(n), timestamp(QDateTime::currentMSecsSinceEpoch()) {}
};

// 线程配置
struct ThreadConfig {
    QString name;
    ThreadPriority priority;
    int maxQueueSize;
    int timeoutMs;
    bool autoRestart;
    
    ThreadConfig(const QString& n = "Thread", 
                ThreadPriority p = ThreadPriority::Normal,
                int maxQueue = 1000,
                int timeout = 30000,
                bool restart = true)
        : name(n), priority(p), maxQueueSize(maxQueue), timeoutMs(timeout), autoRestart(restart) {}
};

} // namespace Thread
} // namespace LA

Q_DECLARE_METATYPE(LA::Thread::ThreadState)
Q_DECLARE_METATYPE(LA::Thread::ThreadStatistics)

#endif // LA_THREAD_TYPES_H