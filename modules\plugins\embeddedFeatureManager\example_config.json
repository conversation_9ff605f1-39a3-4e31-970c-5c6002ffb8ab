{"product_id": "Lidar-X100", "version": "1.0.0", "description": "激光雷达X100系列功能配置", "targets": [{"target_name": "LIDAR_X100_STD", "defines_file": "feature_defines_std.h", "macros_file": "feature_macros_std.json", "description": "标准版本配置"}, {"target_name": "LIDAR_X100_PRO", "defines_file": "feature_defines_pro.h", "macros_file": "feature_macros_pro.json", "description": "专业版本配置"}], "features": [{"id": "distance_measurement", "name": "Distance Measurement", "type": "group", "enabled": true, "description": "测距功能组", "children": [{"id": "max_range", "name": "Maximum Range (m)", "type": "number", "enabled": true, "description": "最大测距范围", "validation": {"min": 1, "max": 200, "step": 0.1}, "per_target": {"LIDAR_X100_STD": 40.0, "LIDAR_X100_PRO": 120.0}, "macro_mapping": {"define_name": "MAX_RANGE_METERS", "define_type": "float"}}, {"id": "measurement_accuracy", "name": "Measurement Accuracy (cm)", "type": "number", "enabled": true, "description": "测距精度", "validation": {"min": 0.1, "max": 5.0, "step": 0.1}, "per_target": {"LIDAR_X100_STD": 2.0, "LIDAR_X100_PRO": 1.0}, "macro_mapping": {"define_name": "MEASUREMENT_ACCURACY_CM", "define_type": "float"}}, {"id": "enable_filtering", "name": "Enable Signal Filtering", "type": "bool", "enabled": true, "description": "启用信号滤波", "per_target": {"LIDAR_X100_STD": true, "LIDAR_X100_PRO": true}, "macro_mapping": {"define_name": "ENABLE_SIGNAL_FILTERING", "define_type": "bool"}}]}, {"id": "communication", "name": "Communication Interface", "type": "group", "enabled": true, "description": "通信接口配置", "children": [{"id": "uart_enabled", "name": "UART Interface", "type": "bool", "enabled": true, "description": "UART通信接口", "per_target": {"LIDAR_X100_STD": true, "LIDAR_X100_PRO": true}, "macro_mapping": {"define_name": "UART_INTERFACE_ENABLED", "define_type": "bool"}}, {"id": "uart_baudrate", "name": "UART Baud Rate", "type": "enum", "enabled": true, "description": "UART波特率", "options": [9600, 19200, 38400, 57600, 115200, 230400], "per_target": {"LIDAR_X100_STD": 115200, "LIDAR_X100_PRO": 230400}, "macro_mapping": {"define_name": "UART_BAUDRATE", "define_type": "int"}, "dependencies": ["uart_enabled"]}, {"id": "can_enabled", "name": "CAN Interface", "type": "bool", "enabled": false, "description": "CAN总线接口", "per_target": {"LIDAR_X100_STD": false, "LIDAR_X100_PRO": true}, "macro_mapping": {"define_name": "CAN_INTERFACE_ENABLED", "define_type": "bool"}}, {"id": "can_id", "name": "CAN ID", "type": "number", "enabled": false, "description": "CAN节点ID", "validation": {"min": 1, "max": 2047}, "per_target": {"LIDAR_X100_STD": 0, "LIDAR_X100_PRO": 100}, "macro_mapping": {"define_name": "CAN_NODE_ID", "define_type": "int"}, "dependencies": ["can_enabled"]}]}, {"id": "power_management", "name": "Power Management", "type": "group", "enabled": true, "description": "电源管理功能", "children": [{"id": "low_power_mode", "name": "Low Power Mode", "type": "bool", "enabled": true, "description": "低功耗模式", "per_target": {"LIDAR_X100_STD": true, "LIDAR_X100_PRO": false}, "macro_mapping": {"define_name": "LOW_POWER_MODE_ENABLED", "define_type": "bool"}}, {"id": "sleep_timeout", "name": "Sleep Timeout (s)", "type": "number", "enabled": true, "description": "休眠超时时间", "validation": {"min": 10, "max": 3600}, "per_target": {"LIDAR_X100_STD": 300, "LIDAR_X100_PRO": 0}, "macro_mapping": {"define_name": "SLEEP_TIMEOUT_SECONDS", "define_type": "int"}, "dependencies": ["low_power_mode"]}]}, {"id": "debug_features", "name": "Debug Features", "type": "group", "enabled": false, "description": "调试功能配置", "children": [{"id": "debug_output", "name": "Debug Output", "type": "bool", "enabled": false, "description": "启用调试输出", "per_target": {"LIDAR_X100_STD": false, "LIDAR_X100_PRO": false}, "macro_mapping": {"define_name": "DEBUG_OUTPUT_ENABLED", "define_type": "bool"}}, {"id": "debug_level", "name": "Debug Level", "type": "enum", "enabled": false, "description": "调试级别", "options": ["ERROR", "WARNING", "INFO", "DEBUG", "VERBOSE"], "per_target": {"LIDAR_X100_STD": "ERROR", "LIDAR_X100_PRO": "INFO"}, "macro_mapping": {"define_name": "DEBUG_LEVEL", "define_type": "string"}, "dependencies": ["debug_output"]}]}], "output": {"header_file": "feature_defines.h", "json_file": "feature_config.json", "backup_enabled": true, "timestamp_comments": true, "header_guard_prefix": "LIDAR_X100_CONFIG"}, "permissions": {"current_user": "developer_001", "role": "DEVELOPER", "role_permissions": {"BUSINESS_USER": ["VIEW_FEATURES", "VIEW_CONFIG"], "PRODUCT_MANAGER": ["VIEW_FEATURES", "EDIT_FEATURES", "VIEW_CONFIG", "EDIT_CONFIG", "EXPORT_CONFIG", "CREATE_PROJECT", "APPROVE_CHANGES"], "PROJECT_MANAGER": ["VIEW_FEATURES", "EDIT_FEATURES", "DELETE_FEATURES", "VIEW_CONFIG", "EDIT_CONFIG", "VIEW_SENSITIVE_CONFIG", "EXPORT_CONFIG", "CREATE_PROJECT", "DELETE_PROJECT", "MANAGE_TARGETS", "APPROVE_CHANGES", "DEPLOY_CONFIG", "VIEW_AUDIT_LOG"], "DEVELOPER": ["VIEW_FEATURES", "EDIT_FEATURES", "VIEW_CONFIG", "EDIT_CONFIG", "VIEW_SENSITIVE_CONFIG", "SYNC_FILES", "IMPORT_CONFIG", "EXPORT_CONFIG", "MANAGE_TARGETS"], "TESTER": ["VIEW_FEATURES", "EDIT_FEATURES", "VIEW_CONFIG", "IMPORT_CONFIG", "EXPORT_CONFIG"]}}, "sync_options": {"auto_sync": false, "create_backup": true, "add_timestamp": true, "preserve_comments": false, "header_guard_prefix": "LIDAR_X100_CONFIG", "indent_size": 4, "use_spaces": true, "line_ending": "\n"}, "validation_rules": {"required_features": ["distance_measurement", "communication"], "mutual_exclusions": [["uart_enabled", "can_enabled"]], "dependencies": {"uart_baudrate": ["uart_enabled"], "can_id": ["can_enabled"], "sleep_timeout": ["low_power_mode"], "debug_level": ["debug_output"]}}, "metadata": {"created": "2025-08-12T10:00:00Z", "modified": "2025-08-12T15:30:00Z", "created_by": "product_manager_001", "modified_by": "developer_001", "version_history": [{"version": "1.0.0", "date": "2025-08-12T10:00:00Z", "author": "product_manager_001", "changes": "初始版本创建"}]}}