# ImageProcessing 模块

## 概述

ImageProcessing模块是对原有`algorithm/opencv/imageZoom/`模块的完全重构，遵循SOLID设计原则，提供现代化的图像处理功能。

## 主要特性

- ✅ **SOLID原则**: 单一职责、开闭原则、里氏替换、接口隔离、依赖倒置
- ✅ **模块化设计**: 插值和滤波算法分离
- ✅ **工厂模式**: 支持运行时算法选择
- ✅ **向后兼容**: 保持原有`my_interPolation`接口
- ✅ **异常安全**: 完善的错误处理机制
- ✅ **现代C++**: 使用C++17特性和智能指针
- ✅ **线程安全**: 部分算法支持多线程

## 架构设计

```
ImageProcessing/
├── interfaces/           # 抽象接口层
├── interpolation/        # 插值算法实现
├── filters/             # 滤波算法实现
├── factories/           # 工厂类
├── adapters/            # 适配器（向后兼容）
└── common/              # 通用组件
```

## 快速开始

### 1. 使用传统接口（向后兼容）

```cpp
#include "adapters/LegacyInterpolationAdapter.h"

// 创建适配器实例
my_interPolation interpolator;

// 准备数据
QVector<QVector<uint32_t>> srcData(4);
QVector<QVector<uint32_t>> dstData(6);
// ... 填充数据 ...

// 执行双线性插值
interpolator.bilinear_interpolation(srcData, dstData);

// 应用滤波
interpolator.kalman_filter();
interpolator.Convolution_filter();
```

### 2. 使用新接口（推荐）

```cpp
#include "factories/InterpolationFactory.h"
#include "factories/FilterFactory.h"

// 创建插值器
auto& factory = ImageProcessing::InterpolationFactory::getInstance();
auto interpolator = factory.createInterpolation(ImageProcessing::InterpolationType::Bilinear);

// 准备图像数据
ImageProcessing::ImageDataU32 srcImage(4, 4);
ImageProcessing::ImageDataU32 dstImage(6, 6);
// ... 填充数据 ...

// 执行插值
bool success = interpolator->interpolate(srcImage, dstImage);

// 创建滤波器
auto& filterFactory = ImageProcessing::FilterFactory::getInstance();
auto kalmanFilter = filterFactory.createFilter(ImageProcessing::FilterType::Kalman);

// 应用滤波
kalmanFilter->apply(dstImage);
```

## 支持的算法

### 插值算法

| 算法 | 类型 | 描述 | 状态 |
|------|------|------|------|
| 双线性插值 | `InterpolationType::Bilinear` | 平滑缩放，质量与性能平衡 | ✅ 已实现 |
| 双三次插值 | `InterpolationType::Bicubic` | 高质量缩放，平滑曲线 | 🚧 计划中 |
| 非线性插值 | `InterpolationType::Nonlinear` | 特殊用途算法 | 🚧 计划中 |
| 最近邻插值 | `InterpolationType::NearestNeighbor` | 快速但像素化缩放 | 🚧 计划中 |

### 滤波算法

| 算法 | 类型 | 描述 | 状态 |
|------|------|------|------|
| 卡尔曼滤波 | `FilterType::Kalman` | 时域噪声抑制和信号平滑 | ✅ 已实现 |
| 卷积滤波 | `FilterType::Convolution` | 通用滤波，支持自定义核 | ✅ 已实现 |
| 中值滤波 | `FilterType::Median` | 有效的边缘保持去噪 | 🚧 计划中 |
| 高斯滤波 | `FilterType::Gaussian` | 自然外观的平滑模糊 | 🚧 计划中 |
| 双边滤波 | `FilterType::Bilateral` | 边缘保持平滑 | 🚧 计划中 |

## 配置参数

### 插值参数

```cpp
ImageProcessing::InterpolationParams params;
params.offset = 0.5f;           // 插值偏移量
params.preserveEdges = true;    // 保持边缘
params.clampValues = true;      // 限制数值范围
params.minValue = 0;            // 最小值
params.maxValue = UINT32_MAX;   // 最大值

interpolator->setParameters(params);
```

### 滤波参数

```cpp
// 卡尔曼滤波参数
ImageProcessing::KalmanParams kalmanParams;
kalmanParams.strength = 1.0f;           // 滤波强度
kalmanParams.processNoise = 0.1f;       // 过程噪声
kalmanParams.measurementNoise = 0.1f;   // 测量噪声

kalmanFilter->setParameters(kalmanParams);

// 卷积滤波参数
ImageProcessing::ConvolutionParams convParams;
convParams.kernelSize = 3;
convParams.normalize = true;
// 设置自定义核或使用预定义核
auto convFilter = dynamic_cast<ImageProcessing::ConvolutionFilter*>(filter.get());
convFilter->setPredefinedKernel("sharpen");
```

## 性能特性

- **内存优化**: 使用智能指针和RAII管理内存
- **异常安全**: 完善的错误处理，不会导致内存泄漏
- **参数验证**: 输入参数自动验证，防止无效操作
- **性能监控**: 内置处理时间估算和监控

## 迁移指南

### 从旧接口迁移

1. **包含头文件**:
   ```cpp
   // 旧方式
   #include "algorithm/opencv/imageZoom/interpolation.h"
   
   // 新方式（兼容）
   #include "algorithm/imageProcessing/adapters/LegacyInterpolationAdapter.h"
   
   // 新方式（推荐）
   #include "algorithm/imageProcessing/factories/InterpolationFactory.h"
   ```

2. **创建对象**:
   ```cpp
   // 旧方式和新兼容方式相同
   my_interPolation interpolator;
   
   // 新推荐方式
   auto interpolator = ImageProcessing::InterpolationFactory::getInstance()
       .createInterpolation(ImageProcessing::InterpolationType::Bilinear);
   ```

3. **方法调用**: 保持完全兼容，无需修改现有代码

## 编译配置

### CMake集成

模块已集成到主项目的CMake构建系统中：

```cmake
# 在algorithm/CMakeLists.txt中已添加
add_subdirectory(imageProcessing)
target_link_libraries(your_target imageProcessing)
```

### 依赖要求

- Qt 5.14.2+
- C++17编译器
- CMake 3.5+

## 测试

运行测试程序验证功能：

```bash
# 编译测试程序
cd build
make imageProcessing

# 运行测试（如果有独立的测试可执行文件）
./test_imageprocessing
```

## 故障排除

### 常见问题

1. **编译错误**: 确保C++17支持和Qt版本正确
2. **链接错误**: 检查CMakeLists.txt中的库链接配置
3. **运行时错误**: 启用调试输出查看详细信息

### 调试支持

```cpp
// 启用调试输出
my_interPolation adapter;
adapter.setDebugEnabled(true);

// 查看处理时间
uint32_t processingTime = adapter.getLastProcessingTime();
qDebug() << "Processing time:" << processingTime << "ms";
```

## 版本信息

- **当前版本**: 2.0.0
- **兼容版本**: 1.x (通过适配器)
- **最后更新**: 2025-01-10

## 贡献指南

1. 遵循现有的代码风格和命名约定
2. 添加新算法时实现对应的接口
3. 提供充分的单元测试
4. 更新文档和示例

## 许可证

本模块遵循项目的整体许可证协议。
