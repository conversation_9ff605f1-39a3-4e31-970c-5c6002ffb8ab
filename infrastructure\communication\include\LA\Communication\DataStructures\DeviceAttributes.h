#pragma once

/**
 * @file DeviceAttributes.h
 * @brief 设备属性结构体定义
 *
 * 基于结构体映射原则，定义设备的完整属性结构，
 * 建立设备各种信息的关联关系
 */

// === Linus原则：引用Foundation层统一定义，避免重复 ===
#include "support/foundation/core/CommonTypes.h"

#include <QDateTime>
#include <QString>
#include <QStringList>
#include <QVariantMap>

// 前向声明
#include "CommunicationAttributes.h"
#include "PortAttributes.h"

namespace LA {
namespace Communication {
namespace DataStructures {

// 使用Foundation层的统一类型定义
using DeviceType = ::LA::Foundation::Core::DeviceType;

/**
 * @brief 设备状态枚举
 */
enum class DeviceState {
    Disconnected,  // 未连接
    Connecting,    // 连接中
    Connected,     // 已连接
    Working,       // 工作中
    Error,         // 错误状态
    Maintenance    // 维护状态
};

}  // namespace DataStructures
}  // namespace Communication
}  // namespace LA

// Qt元类型声明 - 必须在全局命名空间中
Q_DECLARE_METATYPE(LA::Communication::DataStructures::DeviceType)
Q_DECLARE_METATYPE(LA::Communication::DataStructures::DeviceState)

// Qt哈希函数声明
inline uint qHash(LA::Communication::DataStructures::DeviceType type, uint seed = 0) {
    return qHash(static_cast<int>(type), seed);
}

inline uint qHash(LA::Communication::DataStructures::DeviceState state, uint seed = 0) {
    return qHash(static_cast<int>(state), seed);
}

namespace LA {
namespace Communication {
namespace DataStructures {

/**
 * @brief 设备能力描述
 */
struct DeviceCapabilities {
    QStringList supportedCommands;   // 支持的指令列表
    QStringList supportedProtocols;  // 支持的协议列表
    QStringList supportedPorts;      // 支持的端口类型

    bool hasStatusReporting;   // 是否支持状态上报
    bool hasParameterSetting;  // 是否支持参数设置
    bool hasDataStreaming;     // 是否支持数据流
    bool hasRemoteControl;     // 是否支持远程控制
    bool hasCalibration;       // 是否支持校准

    int maxConcurrentCommands;  // 最大并发指令数
    int commandTimeout;         // 指令超时时间(ms)
    int heartbeatInterval;      // 心跳间隔(ms)
};

/**
 * @brief 设备版本信息
 */
struct DeviceVersion {
    QString   hardwareVersion;    // 硬件版本
    QString   firmwareVersion;    // 固件版本
    QString   softwareVersion;    // 软件版本
    QString   protocolVersion;    // 协议版本
    QDateTime manufacturingDate;  // 生产日期
    QDateTime lastUpdateDate;     // 最后更新日期
};

/**
 * @brief 设备基础属性
 *
 * 设备的核心识别和描述信息，作为设备的基础数据结构
 */
struct DeviceAttributes {
    // === 基础识别信息 ===
    QString    deviceId;     // 设备唯一标识
    QString    deviceName;   // 设备名称
    QString    displayName;  // 显示名称
    DeviceType deviceType;   // 设备类型
    QString    typeString;   // 类型字符串

    // === 制造商信息 ===
    QString       manufacturer;  // 制造商
    QString       model;         // 型号
    QString       serialNumber;  // 序列号
    DeviceVersion version;       // 版本信息

    // === 通信属性 ===
    CommunicationAttributes communication;  // 通信配置

    // === 设备能力 ===
    DeviceCapabilities capabilities;  // 设备能力描述

    // === 运行状态 ===
    DeviceState currentState;  // 当前状态
    QDateTime   lastSeen;      // 最后通信时间
    QString     lastError;     // 最后错误信息

    // === 配置参数 ===
    QVariantMap parameters;  // 设备参数
    QVariantMap metadata;    // 元数据

    // === 构造函数 ===
    DeviceAttributes() : deviceType(DeviceType::Unknown), currentState(DeviceState::Disconnected), lastSeen(QDateTime::currentDateTime()) {
    }

    /**
     * @brief 检查设备是否支持指定指令
     */
    bool supportsCommand(const QString &commandId) const {
        return capabilities.supportedCommands.contains(commandId);
    }

    /**
     * @brief 检查设备是否支持指定协议
     */
    bool supportsProtocol(const QString &protocolName) const {
        return capabilities.supportedProtocols.contains(protocolName);
    }

    /**
     * @brief 获取设备类型字符串
     */
    static QString deviceTypeToString(DeviceType type);

    /**
     * @brief 字符串转设备类型
     */
    static DeviceType stringToDeviceType(const QString &typeStr);

    /**
     * @brief 获取设备状态字符串
     */
    static QString deviceStateToString(DeviceState state);
};

/**
 * @brief 设备组/分类信息
 *
 * 用于设备的分组管理和批量操作
 */
struct DeviceGroup {
    QString     groupId;        // 分组ID
    QString     groupName;      // 分组名称
    QString     description;    // 描述
    QStringList deviceIds;      // 包含的设备ID列表
    QVariantMap groupSettings;  // 分组设置

    /**
     * @brief 检查是否包含指定设备
     */
    bool containsDevice(const QString &deviceId) const {
        return deviceIds.contains(deviceId);
    }
};

/**
 * @brief 设备关系映射
 *
 * 描述设备间的依赖和关联关系
 */
struct DeviceRelationship {
    QString     primaryDeviceId;     // 主设备ID
    QStringList dependentDeviceIds;  // 依赖设备ID列表
    QStringList relatedDeviceIds;    // 相关设备ID列表
    QString     relationshipType;    // 关系类型

    /**
     * @brief 检查设备关系
     */
    bool hasRelationWith(const QString &deviceId) const {
        return dependentDeviceIds.contains(deviceId) || relatedDeviceIds.contains(deviceId);
    }
};

/**
 * @brief 设备工具类
 */
class DeviceAttributesUtils {
  public:
    /**
     * @brief 验证设备属性完整性
     */
    static bool validateDeviceAttributes(const DeviceAttributes &device);

    /**
     * @brief 比较两个设备属性
     */
    static bool compareDeviceAttributes(const DeviceAttributes &device1, const DeviceAttributes &device2);

    /**
     * @brief 从配置数据创建设备属性
     */
    static DeviceAttributes fromVariantMap(const QVariantMap &data);

    /**
     * @brief 转换设备属性为配置数据
     */
    static QVariantMap toVariantMap(const DeviceAttributes &device);
};

/**
 * @brief 通讯组件依赖类型
 */
enum class CommunicationDependencyType {
    None = 0,  // 无通讯依赖
    Direct,    // 直接通讯（有自己的通讯接口）
    Indirect,  // 间接通讯（通过其他设备）
    Hybrid     // 混合模式（部分功能直接，部分间接）
};

/**
 * @brief 设备通讯依赖配置
 *
 * 支持依赖注入的通讯组件配置，根据设备类型动态注入所需组件
 */
struct DeviceCommunicationDependency {
    // === 依赖注入类型 ===
    CommunicationDependencyType dependencyType;  // 依赖类型

    // === 组件需求标识 ===
    bool needsConnection;     // 是否需要连接组件
    bool needsProtocol;       // 是否需要协议组件
    bool needsCommandSystem;  // 是否需要指令系统

    // === 服务标识符（用于依赖注入） ===
    QString connectionServiceId;  // 连接服务ID
    QString protocolServiceId;    // 协议服务ID
    QString commandServiceId;     // 指令服务ID

    // === 间接通讯配置（当dependencyType为Indirect时） ===
    QString parentDeviceId;     // 父设备ID（间接通讯时）
    QString proxyConnectionId;  // 代理连接ID

    // === 注入参数 ===
    QVariantMap injectionParameters;   // 组件注入时的参数
    QStringList requiredCapabilities;  // 必需的能力列表

    // === 运行时组件引用（弱引用，避免循环依赖） ===
    mutable QWeakPointer<QObject> connectionInstance;
    mutable QWeakPointer<QObject> protocolInstance;
    mutable QWeakPointer<QObject> commandInstance;

    DeviceCommunicationDependency()
        : dependencyType(CommunicationDependencyType::None), needsConnection(false), needsProtocol(false), needsCommandSystem(false) {
    }

    /**
     * @brief 检查是否为独立设备（无通讯依赖）
     */
    bool isStandalone() const {
        return dependencyType == CommunicationDependencyType::None;
    }

    /**
     * @brief 检查是否需要父设备
     */
    bool requiresParent() const {
        return dependencyType == CommunicationDependencyType::Indirect;
    }

    /**
     * @brief 检查是否有直接通讯能力
     */
    bool hasDirectCommunication() const {
        return dependencyType == CommunicationDependencyType::Direct || dependencyType == CommunicationDependencyType::Hybrid;
    }

    /**
     * @brief 获取所需的服务列表
     */
    QStringList getRequiredServices() const {
        QStringList services;
        if (needsConnection && !connectionServiceId.isEmpty()) {
            services << connectionServiceId;
        }
        if (needsProtocol && !protocolServiceId.isEmpty()) {
            services << protocolServiceId;
        }
        if (needsCommandSystem && !commandServiceId.isEmpty()) {
            services << commandServiceId;
        }
        return services;
    }
};

}  // namespace DataStructures
}  // namespace Communication
}  // namespace LA