#ifndef IMAGEPROCESSING_NOINTERPOLATION_H
#define IMAGEPROCESSING_NOINTERPOLATION_H

#include "../interfaces/IInterpolation.h"

namespace ImageProcessing {

/**
 * @brief 无插值算法实现
 *
 * 这个类实现了"不插值"的策略，直接返回原始数据而不进行任何处理。
 * 当配置为不使用插值时使用此算法。
 */
class NoInterpolation : public IInterpolation {
  public:
    NoInterpolation();
    virtual ~NoInterpolation() = default;

    // IInterpolation接口实现
    bool                interpolate(const ImageDataU32 &src, ImageDataU32 &dst) override;
    void                setParameters(const InterpolationParams &params) override;
    InterpolationParams getParameters() const override;
    QString             getAlgorithmName() const override;
    QString             getVersion() const override;
    QString             getDescription() const override;
    bool                isSupported(uint32_t srcWidth, uint32_t srcHeight, uint32_t dstWidth, uint32_t dstHeight) const override;
    uint32_t            estimateProcessingTime(uint32_t srcWidth, uint32_t srcHeight, uint32_t dstWidth, uint32_t dstHeight) const override;
    void                reset() override;

  private:
    void                logDebug(const QString &message) const;
    InterpolationParams m_params;  // 存储插值参数
};

}  // namespace ImageProcessing

#endif  // IMAGEPROCESSING_NOINTERPOLATION_H
