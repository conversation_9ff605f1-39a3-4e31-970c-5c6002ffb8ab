#ifndef IMAGEPROCESSING_BILATERALFILTER_H
#define IMAGEPROCESSING_BILATERALFILTER_H

#include "../interfaces/IImageFilter.h"
#include "../common/ValidationUtils.h"
#include <QVector>
#include <QDebug>
#include <QtMath>

namespace ImageProcessing {

/**
 * @brief 双边滤波器实现
 * 
 * 实现双边滤波算法，在平滑图像的同时保持边缘
 * 同时考虑空间距离和颜色相似性
 */
class BilateralFilter : public IImageFilter {
public:
    /**
     * @brief 构造函数
     */
    BilateralFilter();

    /**
     * @brief 析构函数
     */
    ~BilateralFilter() override = default;

    // IImageFilter接口实现
    bool apply(ImageDataU32& data) override;
    bool apply(const ImageDataU32& src, ImageDataU32& dst) override;
    void setParameters(const FilterParams& params) override;
    std::unique_ptr<FilterParams> getParameters() const override;
    QString getAlgorithmName() const override;
    QString getDescription() const override;
    bool isSupported(uint32_t width, uint32_t height) const override;
    uint32_t estimateProcessingTime(uint32_t width, uint32_t height) const override;
    void reset() override;
    QString getVersion() const override;
    bool isThreadSafe() const override;
    bool supportsInPlace() const override;

    /**
     * @brief 设置预定义的滤波模式
     * @param preset 预设模式
     */
    void setPreset(const QString& preset);

    /**
     * @brief 获取支持的预设模式列表
     * @return 预设模式列表
     */
    static QStringList getSupportedPresets();

private:
    BilateralParams params_;    ///< 滤波参数

    /**
     * @brief 应用双边滤波到单个像素
     * @param src 源图像
     * @param x 像素X坐标
     * @param y 像素Y坐标
     * @return 双边滤波结果
     */
    float applyBilateralAtPixel(const ImageDataU32& src, uint32_t x, uint32_t y) const;

    /**
     * @brief 获取安全的像素值（处理边界）
     * @param src 源图像
     * @param x X坐标
     * @param y Y坐标
     * @return 像素值
     */
    uint32_t getSafePixelValue(const ImageDataU32& src, int x, int y) const;

    /**
     * @brief 计算空间权重
     * @param dx X方向距离
     * @param dy Y方向距离
     * @param sigmaSpace 空间标准差
     * @return 空间权重
     */
    float calculateSpatialWeight(int dx, int dy, float sigmaSpace) const;

    /**
     * @brief 计算颜色权重
     * @param colorDiff 颜色差异
     * @param sigmaColor 颜色标准差
     * @return 颜色权重
     */
    float calculateColorWeight(float colorDiff, float sigmaColor) const;

    /**
     * @brief 验证双边滤波参数
     * @param params 参数对象
     * @throws InvalidParameterException 如果参数无效
     */
    void validateBilateralParams(const BilateralParams& params) const;

    /**
     * @brief 记录调试信息
     * @param message 调试信息
     */
    void logDebug(const QString& message) const;
};

} // namespace ImageProcessing

#endif // IMAGEPROCESSING_BILATERALFILTER_H
