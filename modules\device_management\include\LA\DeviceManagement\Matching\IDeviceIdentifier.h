#pragma once

#include <QByteArray>
#include <QString>
#include <QStringList>
#include <QVariantMap>
#include "../Core/SimpleTypes.h"  // 使用简化类型

namespace LA {
namespace DeviceManagement {
namespace Matching {

// 使用简化类型 - 遵循单一来源原则
using LA::DeviceManagement::Core::DeviceInfo;

/**
 * @brief 识别规则
 */
struct IdentificationRule {
    QString deviceType;          // 目标设备类型
    QByteArray pattern;          // 匹配模式
    QString patternType;         // 模式类型: "regex", "exact", "contains"
    int priority;                // 优先级
    QVariantMap extractRules;    // 数据提取规则
};

/**
 * @brief 设备识别器接口 - 纯粹的设备识别
 * 
 * Linus: "只做一件事：解析响应数据并识别设备类型"
 * ✅ 负责: 响应解析、设备类型识别、设备信息提取
 * ❌ 不涉及: 指令发送、端口操作、匹配算法
 */
class IDeviceIdentifier {
public:
    virtual ~IDeviceIdentifier() = default;
    
    /**
     * @brief 识别设备类型
     * @param response 设备响应数据
     * @return 设备类型，如果无法识别返回空字符串
     */
    virtual QString identifyDeviceType(const QByteArray& response) = 0;
    
    /**
     * @brief 提取设备信息
     * @param response 设备响应数据
     * @return 设备信息，如果无法解析返回无效的DeviceInfo
     */
    virtual DeviceInfo extractDeviceInfo(const QByteArray& response) = 0;
    
    /**
     * @brief 获取设备能力
     * @param deviceType 设备类型
     * @return 设备能力列表
     */
    virtual QStringList getDeviceCapabilities(const QString& deviceType) = 0;
    
    /**
     * @brief 验证设备响应是否有效
     * @param response 响应数据
     * @return 是否有效
     */
    virtual bool isValidDeviceResponse(const QByteArray& response) = 0;
    
    /**
     * @brief 添加识别规则
     * @param rule 识别规则
     */
    virtual void addIdentificationRule(const IdentificationRule& rule) = 0;
    
    /**
     * @brief 获取支持的设备类型
     * @return 设备类型列表
     */
    virtual QStringList getSupportedDeviceTypes() = 0;
};

} // namespace Matching
} // namespace DeviceManagement
} // namespace LA