#pragma once

#include "IPortScanner.h"
#include "IDeviceIdentifier.h"

namespace LA {
namespace DeviceManagement {
namespace Matching {

/**
 * @brief 匹配对
 */
struct MatchPair {
    PortInfo port;
    DeviceInfo device;
    float confidence;      // 匹配置信度 0.0-1.0
    QString matchReason;   // 匹配原因
    
    bool isValid() const { return port.isValid() && device.isValid() && confidence > 0.0f; }
};

/**
 * @brief 匹配策略
 */
enum class MatchingStrategy {
    BestMatch,      // 最佳匹配
    FirstAvailable, // 首个可用
    LoadBalance,    // 负载均衡
    UserDefined     // 用户自定义
};

/**
 * @brief 匹配配置
 */
struct MatchingConfig {
    MatchingStrategy strategy = MatchingStrategy::BestMatch;
    float minimumConfidence = 0.5f;    // 最小置信度阈值
    bool allowPartialMatch = true;     // 允许部分匹配
    QVariantMap customParams;          // 自定义参数
};

/**
 * @brief 匹配结果
 */
struct MatchingResult {
    QList<MatchPair> successMatches;      // 成功匹配的对
    QList<PortInfo> unmatchedPorts;       // 未匹配的端口
    QList<DeviceInfo> unmatchedDevices;   // 未匹配的设备
    QString status;                       // 匹配状态
    QString errorMessage;                 // 错误信息
    int totalProcessed;                   // 处理总数
    int successCount;                     // 成功数量
    
    bool isSuccessful() const { return !successMatches.isEmpty() && errorMessage.isEmpty(); }
};

/**
 * @brief 匹配算法接口 - 纯粹的匹配逻辑
 * 
 * Linus: "只做一件事：计算端口与设备的最优匹配"
 * ✅ 负责: 匹配算法、置信度计算、匹配评分
 * ❌ 不涉及: 端口发现、设备探测、实例注册
 */
class IMatchingAlgorithm {
public:
    virtual ~IMatchingAlgorithm() = default;
    
    /**
     * @brief 计算单个设备与端口的匹配度
     * @param port 端口信息
     * @param device 设备信息
     * @return 匹配置信度 0.0-1.0
     */
    virtual float calculateMatchConfidence(const PortInfo& port, const DeviceInfo& device) = 0;
    
    /**
     * @brief 为单个设备找到最佳匹配端口
     * @param device 设备信息
     * @param availablePorts 可用端口列表
     * @param config 匹配配置
     * @return 最佳匹配对，如果没有找到返回无效的MatchPair
     */
    virtual MatchPair findBestPortForDevice(
        const DeviceInfo& device, 
        const QList<PortInfo>& availablePorts,
        const MatchingConfig& config = MatchingConfig()) = 0;
    
    /**
     * @brief 批量匹配设备和端口
     * @param devices 设备列表
     * @param ports 端口列表
     * @param config 匹配配置
     * @return 匹配结果
     */
    virtual MatchingResult matchDevicesToPorts(
        const QList<DeviceInfo>& devices,
        const QList<PortInfo>& ports,
        const MatchingConfig& config = MatchingConfig()) = 0;
    
    /**
     * @brief 验证匹配是否有效
     * @param port 端口信息
     * @param device 设备信息
     * @return 是否有效
     */
    virtual bool validateMatch(const PortInfo& port, const DeviceInfo& device) = 0;
    
    /**
     * @brief 设置匹配策略
     * @param strategy 匹配策略
     */
    virtual void setMatchingStrategy(MatchingStrategy strategy) = 0;
    
    /**
     * @brief 获取当前匹配策略
     * @return 匹配策略
     */
    virtual MatchingStrategy getMatchingStrategy() const = 0;
};

} // namespace Matching
} // namespace DeviceManagement
} // namespace LA