/**
 * @file MatchingSystemExample.cpp
 * @brief 展示如何使用新的设备端口匹配系统
 * 
 * 这个示例演示了基于单一职责原则的设备端口匹配架构的使用方法
 */

#include "LA/DeviceManagement/Matching/MatchingCoordinator.h"
#include "LA/DeviceManagement/Matching/SystemPortScanner.h"
// 注意：以下是示例接口，实际实现需要根据具体设备类型创建
// #include "LA/DeviceManagement/Matching/SprmDeviceProber.h"
// #include "LA/DeviceManagement/Matching/UniversalDeviceIdentifier.h"
// #include "LA/DeviceManagement/Matching/BestMatchAlgorithm.h"

#include <QCoreApplication>
#include <QDebug>
#include <memory>

using namespace LA::DeviceManagement::Matching;

/**
 * @brief 临时的设备探测器实现（示例用）
 */
class ExampleDeviceProber : public IDeviceProber {
public:
    ProbeResult probeDevice(const QString& portName, const DeviceProbeConfig& config) override {
        ProbeResult result;
        result.portName = portName;
        result.isSuccessful = true;
        result.response = "SPRM_V1.0_ID:12345"; // 模拟SPRM设备响应
        result.responseTimeMs = 100;
        return result;
    }
    
    bool sendProbeCommand(const QString& portName, const QByteArray& command, int timeoutMs) override {
        Q_UNUSED(portName);
        Q_UNUSED(command);
        Q_UNUSED(timeoutMs);
        return true;
    }
    
    QByteArray receiveProbeResponse(const QString& portName, int timeoutMs) override {
        Q_UNUSED(portName);
        Q_UNUSED(timeoutMs);
        return "SPRM_V1.0_ID:12345";
    }
    
    void cancelProbe(const QString& portName) override {
        Q_UNUSED(portName);
    }
};

/**
 * @brief 临时的设备识别器实现（示例用）
 */
class ExampleDeviceIdentifier : public IDeviceIdentifier {
public:
    QString identifyDeviceType(const QByteArray& response) override {
        QString responseStr = QString::fromUtf8(response);
        if (responseStr.contains("SPRM")) {
            return "SPRM";
        }
        return QString();
    }
    
    DeviceInfo extractDeviceInfo(const QByteArray& response) override {
        DeviceInfo info;
        QString responseStr = QString::fromUtf8(response);
        
        if (responseStr.contains("SPRM")) {
            info.deviceType = "SPRM";
            info.deviceModel = "SPRM_V1.0";
            
            // 提取设备ID
            QRegExp idRegex("ID:(\\d+)");
            if (idRegex.indexIn(responseStr) != -1) {
                info.deviceId = idRegex.cap(1);
            }
            
            info.capabilities << "Distance_Measurement" << "High_Precision";
            info.properties["firmware"] = "1.0";
            info.rawResponse = response;
        }
        
        return info;
    }
    
    QStringList getDeviceCapabilities(const QString& deviceType) override {
        if (deviceType == "SPRM") {
            return {"Distance_Measurement", "High_Precision", "Serial_Communication"};
        }
        return QStringList();
    }
    
    bool isValidDeviceResponse(const QByteArray& response) override {
        return !response.isEmpty() && response.size() > 5;
    }
    
    void addIdentificationRule(const IdentificationRule& rule) override {
        Q_UNUSED(rule);
    }
    
    QStringList getSupportedDeviceTypes() override {
        return {"SPRM", "Motor", "Sensor"};
    }
};

/**
 * @brief 临时的匹配算法实现（示例用）
 */
class ExampleMatchingAlgorithm : public IMatchingAlgorithm {
private:
    MatchingStrategy m_strategy = MatchingStrategy::BestMatch;
    
public:
    float calculateMatchConfidence(const PortInfo& port, const DeviceInfo& device) override {
        float confidence = 0.0f;
        
        // 串口设备通常匹配度更高
        if (port.portType == "Serial" && device.capabilities.contains("Serial_Communication")) {
            confidence += 0.8f;
        }
        
        // SPRM设备与串口的匹配
        if (device.deviceType == "SPRM" && port.portType == "Serial") {
            confidence += 0.2f;
        }
        
        return qMin(1.0f, confidence);
    }
    
    MatchPair findBestPortForDevice(const DeviceInfo& device, const QList<PortInfo>& availablePorts, const MatchingConfig& config) override {
        MatchPair bestMatch;
        float bestConfidence = 0.0f;
        
        for (const auto& port : availablePorts) {
            float confidence = calculateMatchConfidence(port, device);
            if (confidence > bestConfidence && confidence >= config.minimumConfidence) {
                bestMatch.port = port;
                bestMatch.device = device;
                bestMatch.confidence = confidence;
                bestMatch.matchReason = QString("Best match with confidence %1").arg(confidence);
                bestConfidence = confidence;
            }
        }
        
        return bestMatch;
    }
    
    MatchingResult matchDevicesToPorts(const QList<DeviceInfo>& devices, const QList<PortInfo>& ports, const MatchingConfig& config) override {
        MatchingResult result;
        QList<PortInfo> availablePorts = ports;
        
        for (const auto& device : devices) {
            auto match = findBestPortForDevice(device, availablePorts, config);
            if (match.isValid()) {
                result.successMatches.append(match);
                // 移除已匹配的端口
                availablePorts.removeAll(match.port);
            } else {
                result.unmatchedDevices.append(device);
            }
        }
        
        result.unmatchedPorts = availablePorts;
        result.totalProcessed = devices.size();
        result.successCount = result.successMatches.size();
        result.status = "Completed";
        
        return result;
    }
    
    bool validateMatch(const PortInfo& port, const DeviceInfo& device) override {
        return calculateMatchConfidence(port, device) > 0.5f;
    }
    
    void setMatchingStrategy(MatchingStrategy strategy) override {
        m_strategy = strategy;
    }
    
    MatchingStrategy getMatchingStrategy() const override {
        return m_strategy;
    }
};

/**
 * @brief 主函数 - 演示完整的匹配流程
 */
int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    qDebug() << "=== 设备端口自动匹配系统示例 ===";
    qDebug() << "基于单一职责原则和最小模块设计";
    qDebug();
    
    // 1. 创建匹配协调器
    auto coordinator = std::make_unique<MatchingCoordinator>();
    
    // 2. 注入各个最小模块（依赖注入）
    qDebug() << "注入依赖模块...";
    coordinator->setPortScanner(std::make_unique<SystemPortScanner>());
    coordinator->setDeviceProber(std::make_unique<ExampleDeviceProber>());
    coordinator->setDeviceIdentifier(std::make_unique<ExampleDeviceIdentifier>());
    coordinator->setMatchingAlgorithm(std::make_unique<ExampleMatchingAlgorithm>());
    
    // 3. 检查系统就绪状态
    if (!coordinator->isReady()) {
        qCritical() << "系统未就绪:" << coordinator->getLastError();
        return -1;
    }
    
    qDebug() << "系统就绪，开始匹配...";
    qDebug();
    
    // 4. 配置匹配参数
    DeviceProbeConfig probeConfig;
    probeConfig.deviceTypes << "SPRM" << "Motor" << "Sensor";
    probeConfig.timeoutMs = 2000;
    probeConfig.retryCount = 2;
    
    MatchingConfig matchingConfig;
    matchingConfig.strategy = MatchingStrategy::BestMatch;
    matchingConfig.minimumConfidence = 0.6f;
    matchingConfig.allowPartialMatch = true;
    
    // 5. 执行完整的自动匹配
    auto result = coordinator->performAutoMatching(probeConfig, matchingConfig);
    
    // 6. 输出结果
    qDebug() << "=== 匹配结果 ===";
    qDebug() << "状态:" << result.status;
    qDebug() << "总处理数:" << result.totalProcessed;
    qDebug() << "成功匹配:" << result.successCount;
    qDebug();
    
    if (!result.successMatches.isEmpty()) {
        qDebug() << "成功匹配的设备-端口对:";
        for (const auto& match : result.successMatches) {
            qDebug() << QString("  %1 (%2) <-> %3 (%4) [置信度: %5]")
                        .arg(match.device.deviceType)
                        .arg(match.device.deviceId)
                        .arg(match.port.portName)
                        .arg(match.port.portType)
                        .arg(match.confidence);
        }
    }
    
    if (!result.unmatchedPorts.isEmpty()) {
        qDebug() << "未匹配的端口:";
        for (const auto& port : result.unmatchedPorts) {
            qDebug() << QString("  %1 (%2)").arg(port.portName).arg(port.portType);
        }
    }
    
    if (!result.unmatchedDevices.isEmpty()) {
        qDebug() << "未匹配的设备:";
        for (const auto& device : result.unmatchedDevices) {
            qDebug() << QString("  %1 (%2)").arg(device.deviceType).arg(device.deviceId);
        }
    }
    
    qDebug();
    qDebug() << "=== 架构优势展示 ===";
    qDebug() << "✅ 单一职责：每个模块只做一件事";
    qDebug() << "✅ 最小模块：可独立测试和复用";
    qDebug() << "✅ 依赖注入：松耦合，易于扩展";
    qDebug() << "✅ 组合设计：通过组合实现复杂功能";
    qDebug() << "✅ 清晰接口：接口简洁，职责明确";
    
    return 0;
}