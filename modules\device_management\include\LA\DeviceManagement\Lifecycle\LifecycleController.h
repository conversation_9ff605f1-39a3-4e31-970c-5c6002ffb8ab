#pragma once

#include <QObject>
#include <QStateMachine>
#include <QState>
#include <QTimer>
#include <QMap>
#include <QStringList>
#include "../Discovery/DeviceDiscoveryService.h"
#include "LA/Communication/PortManagement/PortDiscoveryService.h"
#include "../Matching/DevicePortMatcher.h"

namespace LA {
namespace DeviceManagement {
namespace Lifecycle {

/**
 * @brief 设备生命周期状态
 */
enum class DeviceLifecycleState {
    Uninitialized,
    Initializing,
    Ready,
    Connected,
    Active,
    Error,
    Disconnecting,
    Destroyed
};

/**
 * @brief 端口生命周期状态
 */
enum class PortLifecycleState {
    Unknown,
    Opening,
    Open,
    Active,
    Closing,
    Closed,
    Error
};

/**
 * @brief 生命周期事件
 */
enum class LifecycleEvent {
    Initialize,
    Connect,
    Activate,
    Deactivate,
    Disconnect,
    Error,
    Destroy
};

/**
 * @brief 生命周期状态信息
 */
struct LifecycleStateInfo {
    QString entityId;
    QString entityType;  // "device" or "port"
    QVariant currentState;
    QDateTime stateChangeTime;
    QString stateDescription;
    QVariantMap stateData;
};

/**
 * @brief 生命周期控制器 - 协调而非绑定
 * 
 * Linus: "协调端口和设备的生命周期，而不是绑定它们"
 * ✅ 负责: 状态协调、生命周期同步、失败回滚
 * ❌ 不涉及: 具体的设备操作、端口操作
 * 
 * 设计原则: "协调而非绑定"
 * - 端口和设备有独立的生命周期
 * - 通过协调器进行状态同步
 * - 失败时自动回滚
 */
class LifecycleController : public QObject {
    Q_OBJECT

public:
    explicit LifecycleController(QObject *parent = nullptr);
    virtual ~LifecycleController() = default;

    // ====== 协调操作 - 核心功能 ======
    bool openDeviceConnection(const QString& deviceId);
    bool closeDeviceConnection(const QString& deviceId);
    bool activateDeviceConnection(const QString& deviceId);
    bool deactivateDeviceConnection(const QString& deviceId);
    
    // ====== 状态查询 ======
    DeviceLifecycleState getDeviceState(const QString& deviceId) const;
    PortLifecycleState getPortState(const QString& portName) const;
    QStringList getActiveDevices() const;
    QStringList getActivePorts() const;
    
    // ====== 状态同步 ======
    void synchronizeStates(const QString& deviceId, const QString& portName);
    bool areStatesConsistent(const QString& deviceId, const QString& portName) const;
    
    // ====== 生命周期管理 ======
    void registerDeviceLifecycle(const QString& deviceId);
    void registerPortLifecycle(const QString& portName);
    void unregisterDeviceLifecycle(const QString& deviceId);
    void unregisterPortLifecycle(const QString& portName);

signals:
    void deviceStateChanged(const QString& deviceId, DeviceLifecycleState state);
    void portStateChanged(const QString& portName, PortLifecycleState state);
    void lifecycleError(const QString& entityId, const QString& error);
    void statesSynchronized(const QString& deviceId, const QString& portName);

private slots:
    void onDeviceStateTransition();
    void onPortStateTransition();
    void onSynchronizationTimeout();

private:
    // ====== 协调逻辑 ======
    bool openPort(const QString& portName);
    bool closePort(const QString& portName);
    bool openDevice(const QString& deviceId);
    bool closeDevice(const QString& deviceId);
    
    // ====== 状态管理 ======
    void updateDeviceState(const QString& deviceId, DeviceLifecycleState state);
    void updatePortState(const QString& portName, PortLifecycleState state);
    
    // ====== 失败回滚 ======
    void rollbackDeviceOperation(const QString& deviceId);
    void rollbackPortOperation(const QString& portName);
    
    // ====== 内部状态 ======
    QMap<QString, DeviceLifecycleState> m_deviceStates;
    QMap<QString, PortLifecycleState> m_portStates;
    QMap<QString, QString> m_devicePortMapping;  // deviceId -> portName
    QMap<QString, QString> m_portDeviceMapping;  // portName -> deviceId
    QTimer* m_synchronizationTimer;
};

} // namespace Lifecycle
} // namespace DeviceManagement
} // namespace LA
