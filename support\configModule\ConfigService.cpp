#include "ConfigService.h"
#include <QApplication>
#include <QDebug>
#include <QMutexLocker>

namespace Config {

ConfigService &ConfigService::getInstance() {
    static ConfigService instance;
    return instance;
}

ConfigService::ConfigService(QObject *parent) : QObject(parent), m_initialized(false) {
    initialize();
}

ConfigService::~ConfigService() {
    QMutexLocker locker(&m_mutex);
    m_providers.clear();
}

void ConfigService::initialize() {
    if (m_initialized) {
        return;
    }

    qDebug() << "[ConfigService] Initializing configuration service...";

    // 设置初始化标志
    m_initialized = true;

    Q_EMIT serviceStatusChanged("Initialized");
    qDebug() << "[ConfigService] Configuration service initialized successfully";
}

bool ConfigService::registerProvider(std::unique_ptr<IConfigProvider> provider) {
    if (!provider) {
        qWarning() << "[ConfigService] Cannot register null provider";
        return false;
    }

    QString moduleName = provider->getModuleName();
    if (moduleName.isEmpty()) {
        qWarning() << "[ConfigService] Cannot register provider with empty module name";
        return false;
    }

    QMutexLocker locker(&m_mutex);

    if (m_providers.find(moduleName) != m_providers.end()) {
        qWarning() << "[ConfigService] Module already registered:" << moduleName;
        return false;
    }

    // 连接信号
    connectProviderSignals(provider.get());

    // 注册提供者
    m_providers[moduleName] = std::move(provider);

    qDebug() << "[ConfigService] Registered module:" << moduleName;
    Q_EMIT moduleRegistered(moduleName);

    return true;
}

bool ConfigService::unregisterProvider(const QString &moduleName) {
    QMutexLocker locker(&m_mutex);

    auto it = m_providers.find(moduleName);
    if (it == m_providers.end()) {
        qWarning() << "[ConfigService] Module not found for unregistration:" << moduleName;
        return false;
    }

    // 断开信号
    disconnectProviderSignals(it->second.get());

    // 移除提供者
    m_providers.erase(it);

    qDebug() << "[ConfigService] Unregistered module:" << moduleName;
    Q_EMIT moduleUnregistered(moduleName);

    return true;
}

IConfigProvider *ConfigService::getProvider(const QString &moduleName) {
    QMutexLocker locker(&m_mutex);

    auto it = m_providers.find(moduleName);
    if (it != m_providers.end()) {
        return it->second.get();
    }

    return nullptr;
}

QStringList ConfigService::getRegisteredModules() const {
    QMutexLocker locker(&m_mutex);
    QStringList  modules;
    for (const auto &pair : m_providers) {
        modules.append(pair.first);
    }
    return modules;
}

bool ConfigService::isModuleRegistered(const QString &moduleName) const {
    QMutexLocker locker(&m_mutex);
    return m_providers.find(moduleName) != m_providers.end();
}

QVariant ConfigService::getParameter(const QString &moduleName, const QString &key, const QVariant &defaultValue) {
    IConfigProvider *provider = getProvider(moduleName);
    if (!provider) {
        qWarning() << "[ConfigService] Module not found:" << moduleName;
        return defaultValue;
    }

    return provider->getParameter(key, defaultValue);
}

bool ConfigService::setParameter(const QString &moduleName, const QString &key, const QVariant &value) {
    IConfigProvider *provider = getProvider(moduleName);
    if (!provider) {
        qWarning() << "[ConfigService] Module not found:" << moduleName;
        return false;
    }

    return provider->setParameter(key, value);
}

QVariantMap ConfigService::getModuleParameters(const QString &moduleName) {
    IConfigProvider *provider = getProvider(moduleName);
    if (!provider) {
        qWarning() << "[ConfigService] Module not found:" << moduleName;
        return QVariantMap();
    }

    return provider->getAllParameters();
}

ConfigResult ConfigService::loadAllConfigs() {
    QMutexLocker locker(&m_mutex);

    ConfigResult result(true);
    int          successCount = 0;
    int          totalCount   = m_providers.size();

    for (auto it = m_providers.begin(); it != m_providers.end(); ++it) {
        ConfigResult moduleResult = it->second->loadConfig();
        if (moduleResult.success) {
            successCount++;
        } else {
            qWarning() << "[ConfigService] Failed to load config for module:" << it->first << "-" << moduleResult.message;
            result.success = false;
            result.message += QString("Module %1: %2; ").arg(it->first, moduleResult.message);
        }
    }

    if (result.success) {
        result.message = QString("Successfully loaded %1/%2 module configurations").arg(successCount).arg(totalCount);
        qDebug() << "[ConfigService]" << result.message;
    } else {
        result.message = QString("Loaded %1/%2 module configurations with errors: %3").arg(successCount).arg(totalCount).arg(result.message);
        qWarning() << "[ConfigService]" << result.message;
    }

    return result;
}

ConfigResult ConfigService::saveAllConfigs() {
    QMutexLocker locker(&m_mutex);

    ConfigResult result(true);
    int          successCount = 0;
    int          totalCount   = m_providers.size();

    for (auto it = m_providers.begin(); it != m_providers.end(); ++it) {
        ConfigResult moduleResult = it->second->saveConfig();
        if (moduleResult.success) {
            successCount++;
        } else {
            qWarning() << "[ConfigService] Failed to save config for module:" << it->first << "-" << moduleResult.message;
            result.success = false;
            result.message += QString("Module %1: %2; ").arg(it->first, moduleResult.message);
        }
    }

    if (result.success) {
        result.message = QString("Successfully saved %1/%2 module configurations").arg(successCount).arg(totalCount);
        qDebug() << "[ConfigService]" << result.message;
    } else {
        result.message = QString("Saved %1/%2 module configurations with errors: %3").arg(successCount).arg(totalCount).arg(result.message);
        qWarning() << "[ConfigService]" << result.message;
    }

    return result;
}

ConfigResult ConfigService::validateAllConfigs() {
    QMutexLocker locker(&m_mutex);

    ConfigResult result(true);
    int          validCount = 0;
    int          totalCount = m_providers.size();

    for (auto it = m_providers.begin(); it != m_providers.end(); ++it) {
        ConfigResult moduleResult = it->second->validateConfig();
        if (moduleResult.success) {
            validCount++;
        } else {
            qWarning() << "[ConfigService] Invalid config for module:" << it->first << "-" << moduleResult.message;
            result.success = false;
            result.message += QString("Module %1: %2; ").arg(it->first, moduleResult.message);
        }
    }

    if (result.success) {
        result.message = QString("All %1 module configurations are valid").arg(totalCount);
        qDebug() << "[ConfigService]" << result.message;
    } else {
        result.message = QString("%1/%2 module configurations are valid. Errors: %3").arg(validCount).arg(totalCount).arg(result.message);
        qWarning() << "[ConfigService]" << result.message;
    }

    return result;
}

ConfigResult ConfigService::reloadModuleConfig(const QString &moduleName) {
    IConfigProvider *provider = getProvider(moduleName);
    if (!provider) {
        return ConfigResult(false, ErrorType::ModuleNotFound, QString("Module not found: %1").arg(moduleName));
    }

    return provider->loadConfig();
}

ConfigResult ConfigService::resetModuleToDefault(const QString &moduleName) {
    IConfigProvider *provider = getProvider(moduleName);
    if (!provider) {
        return ConfigResult(false, ErrorType::ModuleNotFound, QString("Module not found: %1").arg(moduleName));
    }

    return provider->resetToDefault();
}

QVariantMap ConfigService::getServiceStatus() const {
    QMutexLocker locker(&m_mutex);

    QVariantMap status;
    status["initialized"]  = m_initialized;
    status["module_count"] = static_cast<int>(m_providers.size());

    // 构建模块名称列表
    QStringList moduleNames;
    for (const auto &pair : m_providers) {
        moduleNames << pair.first;
    }
    status["registered_modules"] = moduleNames;

    return status;
}

void ConfigService::connectProviderSignals(IConfigProvider *provider) {
    if (!provider)
        return;

    connect(provider, &IConfigProvider::configChanged, this, &ConfigService::onProviderConfigChanged);
    connect(provider, &IConfigProvider::configLoaded, this, [this](const QString &moduleName, bool success) {
        qDebug() << "[ConfigService] Module" << moduleName << "config loaded:" << (success ? "success" : "failed");
    });
    connect(provider, &IConfigProvider::configSaved, this, [this](const QString &moduleName, bool success) {
        qDebug() << "[ConfigService] Module" << moduleName << "config saved:" << (success ? "success" : "failed");
    });
}

void ConfigService::disconnectProviderSignals(IConfigProvider *provider) {
    if (!provider)
        return;

    disconnect(provider, nullptr, this, nullptr);
}

void ConfigService::onProviderConfigChanged(const QString &moduleName, const QString &key, const QVariant &oldValue, const QVariant &newValue) {
    qDebug() << "[ConfigService] Config changed - Module:" << moduleName << "Key:" << key << "Old:" << oldValue << "New:" << newValue;
    Q_EMIT configChanged(moduleName, key, oldValue, newValue);
}

IConfigData *ConfigService::getConfig(const QString &typeName) {
    return m_dynamicManager.getConfig(typeName);
}

}  // namespace Config
