@echo off
REM 通用设备指令测试执行脚本
REM 遵循test_guideline.md标准，支持多设备类型配置化测试

setlocal EnableDelayedExpansion

REM 获取设备类型参数 (默认为sprm)
set DEVICE_TYPE=%1
if "%DEVICE_TYPE%"=="" set DEVICE_TYPE=sprm

REM 检查设备配置文件是否存在
set CONFIG_FILE=%~dp0..\tests\config\devices\%DEVICE_TYPE%_test_config.json
if not exist "%CONFIG_FILE%" (
    echo [ERROR] Device configuration not found: %CONFIG_FILE%
    echo [INFO] Available device configurations:
    dir /b "%~dp0..\tests\config\devices\*_test_config.json" 2>nul
    exit /b 1
)

echo ===============================================
echo    Generic Device Command Testing Suite
echo    Device Type: %DEVICE_TYPE%
echo ===============================================

REM 设置环境变量
set TEST_OUTPUT_DIR=%~dp0..\artifacts\device_command_tests\%DEVICE_TYPE%\%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set BUILD_DIR=%~dp0..\build

REM 创建输出目录
mkdir "%TEST_OUTPUT_DIR%" 2>nul

echo [INFO] Starting %DEVICE_TYPE% device command tests...
echo [INFO] Config file: %CONFIG_FILE%
echo [INFO] Output directory: %TEST_OUTPUT_DIR%

REM 解析设备配置
for /f "tokens=2 delims=:" %%i in ('findstr "test_target" "%CONFIG_FILE%"') do (
    set TEST_TARGET=%%i
    set TEST_TARGET=!TEST_TARGET: "=!
    set TEST_TARGET=!TEST_TARGET:"=!
    set TEST_TARGET=!TEST_TARGET:,=!
)

for /f "tokens=2 delims=:" %%i in ('findstr "test_executable" "%CONFIG_FILE%"') do (
    set TEST_EXECUTABLE=%%i
    set TEST_EXECUTABLE=!TEST_EXECUTABLE: "=!
    set TEST_EXECUTABLE=!TEST_EXECUTABLE:"=!
    set TEST_EXECUTABLE=!TEST_EXECUTABLE:,=!
)

echo [CONFIG] Test target: !TEST_TARGET!
echo [CONFIG] Test executable: !TEST_EXECUTABLE!

REM 构建测试
echo [BUILD] Building %DEVICE_TYPE% device command tests...
cd /d "%BUILD_DIR%"
cmake --build . --target !TEST_TARGET! --config Release
if !ERRORLEVEL! neq 0 (
    echo [ERROR] Build failed for target: !TEST_TARGET!
    exit /b 1
)

REM 执行测试
echo [TEST] Executing %DEVICE_TYPE% device command tests...
.\bin\!TEST_EXECUTABLE! -o "%TEST_OUTPUT_DIR%\%DEVICE_TYPE%_test_results.xml,junitxml" -v2

set TEST_RESULT=!ERRORLEVEL!

REM 生成测试报告
call :GenerateTestReport "%DEVICE_TYPE%" "%TEST_OUTPUT_DIR%" "!TEST_RESULT!"

if !TEST_RESULT! equ 0 (
    echo [SUCCESS] All %DEVICE_TYPE% device command tests passed!
) else (
    echo [FAILURE] Some %DEVICE_TYPE% device command tests failed!
)

echo ===============================================
echo Test artifacts saved to: %TEST_OUTPUT_DIR%
echo ===============================================

exit /b !TEST_RESULT!

:GenerateTestReport
set "device=%~1"
set "output_dir=%~2"
set "result=%~3"

echo [REPORT] Generating test report for %device%...
echo {> "%output_dir%\test_report.json"
echo   "run_id": "%RANDOM%-%date:~0,4%%date:~5,2%%date:~8,2%",>> "%output_dir%\test_report.json"
echo   "git_ref": "current",>> "%output_dir%\test_report.json"
echo   "agent": "device-command-tester",>> "%output_dir%\test_report.json"
echo   "agent_version": "v1.0.0",>> "%output_dir%\test_report.json"
echo   "device_type": "%device%",>> "%output_dir%\test_report.json"
echo   "start_time": "%date%T%time%",>> "%output_dir%\test_report.json"
echo   "test_type": "device_command",>> "%output_dir%\test_report.json"
echo   "status": "%result%",>> "%output_dir%\test_report.json"
echo   "artifacts": {>> "%output_dir%\test_report.json"
echo     "junit_xml": "%device%_test_results.xml",>> "%output_dir%\test_report.json"
echo     "config": "config/devices/%device%_test_config.json",>> "%output_dir%\test_report.json"
echo     "log": "%device%_test.log">> "%output_dir%\test_report.json"
echo   }>> "%output_dir%\test_report.json"
echo }>> "%output_dir%\test_report.json"
goto :eof