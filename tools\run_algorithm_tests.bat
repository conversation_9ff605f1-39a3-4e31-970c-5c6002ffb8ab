@echo off
echo =====================================
echo 静态映射关系验证测试
echo =====================================

set BUILD_DIR=%~dp0..\build
set TEST_DIR=%BUILD_DIR%\infrastructure\communication\tests

if not exist "%BUILD_DIR%" (
    echo 构建目录不存在，需要先构建项目
    echo 构建目录: %BUILD_DIR%
    goto end
)

echo.
echo 正在运行映射关系验证测试...
echo.

cd /d "%TEST_DIR%"

if exist "communication_mapping_test.exe" (
    echo 执行映射验证测试...
    communication_mapping_test.exe
    if %errorlevel% equ 0 (
        echo.
        echo ✓ 映射验证测试通过
    ) else (
        echo.
        echo ✗ 映射验证测试失败 (错误代码: %errorlevel%)
    )
) else (
    echo 测试可执行文件不存在: communication_mapping_test.exe
    echo 请先构建测试项目
)

echo.
echo =====================================
echo 测试完成
echo =====================================

:end
pause