@echo off
echo ================================================
echo LA Theme System Refactoring Test
echo ================================================
echo.

echo [1/5] 检查源文件更新...
if not exist "%~dp0..\core\sidebar\src\ActivityBar.cpp" (
    echo 错误: ActivityBar.cpp 文件不存在
    pause
    exit /b 1
)

echo ActivityBar.cpp - 已更新 ✓

echo.
echo [2/5] 检查主题系统文件...
if not exist "%~dp0..\ui\themes\src\ThemeManager.cpp" (
    echo 错误: ThemeManager.cpp 文件不存在  
    pause
    exit /b 1
)

echo ThemeManager.cpp - 存在 ✓

echo.
echo [3/5] 检查硬编码样式移除...
findstr /C:"rgb(" "%~dp0..\core\sidebar\src\ActivityBar.cpp" >nul
if %errorlevel% == 0 (
    echo 警告: 发现残留的硬编码rgb()值
    findstr /C:"rgb(" "%~dp0..\core\sidebar\src\ActivityBar.cpp"
) else (
    echo 硬编码rgb()值已移除 ✓
)

findstr /C:"rgba(" "%~dp0..\core\sidebar\src\ActivityBar.cpp" >nul
if %errorlevel% == 0 (
    echo 警告: 发现残留的硬编码rgba()值
    findstr /C:"rgba(" "%~dp0..\core\sidebar\src\ActivityBar.cpp"
) else (
    echo 硬编码rgba()值已移除 ✓
)

findstr /C:"#[0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f]" "%~dp0..\core\sidebar\src\ActivityBar.cpp" >nul
if %errorlevel% == 0 (
    echo 警告: 发现残留的硬编码十六进制颜色值
) else (
    echo 硬编码十六进制颜色值已移除 ✓
)

echo.
echo [4/5] 检查主题系统集成...
findstr /C:"LA::Themes::ThemeManager" "%~dp0..\core\sidebar\src\ActivityBar.cpp" >nul
if %errorlevel% == 0 (
    echo 主题管理器已集成 ✓
) else (
    echo 错误: 未找到主题管理器集成
    pause
    exit /b 1
)

findstr /C:"getSemanticColor" "%~dp0..\core\sidebar\src\ActivityBar.cpp" >nul
if %errorlevel% == 0 (
    echo 语义化颜色系统已使用 ✓
) else (
    echo 错误: 未使用语义化颜色系统
    pause
    exit /b 1
)

findstr /C:"onThemeChanged" "%~dp0..\core\sidebar\src\ActivityBar.cpp" >nul
if %errorlevel% == 0 (
    echo 主题变化监听已实现 ✓
) else (
    echo 错误: 未实现主题变化监听
    pause
    exit /b 1
)

echo.
echo [5/5] 重构验证总结...
echo ================================================
echo 重构状态: 成功 ✓
echo ================================================
echo.
echo 重构完成项目:
echo   - 移除硬编码RGB/RGBA颜色值
echo   - 移除硬编码十六进制颜色值  
echo   - 集成主题管理器
echo   - 使用语义化颜色系统
echo   - 实现主题变化监听
echo   - 应用主题到UI组件
echo.
echo 下一步建议:
echo   1. 运行完整编译测试
echo   2. 测试主题切换功能
echo   3. 验证在不同主题下的视觉效果
echo   4. 继续迁移其他模块
echo.
echo 按任意键继续...
pause >nul