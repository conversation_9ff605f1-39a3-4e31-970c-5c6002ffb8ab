#include "ILogOutput.h"
#include "../formatter/ILogFormatter.h"
#include "../logger/ILogger.h"
#include <QDir>
#include <QFileInfo>
#include <QDateTime>
#include <QMutexLocker>
#include <QTcpSocket>
#include <QUdpSocket>
#include <QHostAddress>
#include <iostream>
#include <cstdio>
#include <algorithm>

namespace LA {
namespace Support {
namespace Logging {

// 实用函数实现
QString logLevelToString(LogLevel level) {
    switch (level) {
        case LogLevel::TRACE: return "TRACE";
        case LogLevel::DEBUG: return "DEBUG";
        case LogLevel::INFO: return "INFO";
        case LogLevel::WARNING: return "WARNING";
        case LogLevel::ERROR: return "ERROR";
        case LogLevel::FATAL: return "FATAL";
        default: return "UNKNOWN";
    }
}

QString logTypeToString(LogType type) {
    switch (type) {
        case LogType::GENERAL: return "GENERAL";
        case LogType::SYSTEM: return "SYSTEM";
        case LogType::SECURITY: return "SECURITY";
        case LogType::PERFORMANCE: return "PERFORMANCE";
        case LogType::NETWORK: return "NETWORK";
        case LogType::DATABASE: return "DATABASE";
        case LogType::USER_ACTION: return "USER";
        case LogType::API: return "API";
        default: return "UNKNOWN";
    }
}

// 简单的默认格式化器实现  
class DefaultSimpleFormatter : public ILogFormatter {
public:
    QString format(const LogEntry& entry) override {
        return QString("[%1] [%2] [%3] %4")
            .arg(entry.context.timestamp.toString("yyyy-MM-dd hh:mm:ss.zzz"))
            .arg(logLevelToString(entry.level))
            .arg(logTypeToString(entry.type))
            .arg(entry.message);
    }
    
    void setPattern(const QString& pattern) override { Q_UNUSED(pattern) }
    QString getPattern() const override { return "[%{time}] [%{level}] [%{type}] %{message}"; }
    void setTimeFormat(const QString& format) override { Q_UNUSED(format) }
    QString getTimeFormat() const override { return "yyyy-MM-dd hh:mm:ss.zzz"; }
    void setColorEnabled(bool enabled) override { Q_UNUSED(enabled) }
    bool isColorEnabled() const override { return false; }
};

//=====================================================================
// ConsoleHandler Implementation
//=====================================================================

ConsoleHandler::ConsoleHandler(std::shared_ptr<ILogFormatter> formatter)
    : m_formatter(formatter)
    , m_stream(stdout) {
    if (!m_formatter) {
        m_formatter = std::make_shared<DefaultSimpleFormatter>();
    }
}

ConsoleHandler::~ConsoleHandler() {
    close();
}

void ConsoleHandler::handle(const LogEntry& entry) {
    QMutexLocker locker(&m_mutex);
    
    if (!m_formatter || !m_stream) {
        return;
    }
    
    QString formattedMessage = m_formatter->format(entry);
    
    // 根据日志级别选择输出流
    FILE* outputStream = m_stream;
    if (entry.level >= LogLevel::WARNING) {
        outputStream = stderr;
    }
    
    fprintf(outputStream, "%s\n", formattedMessage.toUtf8().constData());
    fflush(outputStream);
}

void ConsoleHandler::flush() {
    QMutexLocker locker(&m_mutex);
    
    if (m_stream) {
        fflush(m_stream);
        fflush(stderr);
    }
}

void ConsoleHandler::close() {
    QMutexLocker locker(&m_mutex);
    // 控制台流不需要关闭
}

void ConsoleHandler::setFormatter(std::shared_ptr<ILogFormatter> formatter) {
    QMutexLocker locker(&m_mutex);
    m_formatter = formatter;
}

std::shared_ptr<ILogFormatter> ConsoleHandler::getFormatter() const {
    QMutexLocker locker(&m_mutex);
    return m_formatter;
}

void ConsoleHandler::setOutputStream(FILE* stream) {
    QMutexLocker locker(&m_mutex);
    m_stream = stream;
}

//=====================================================================
// FileHandler Implementation
//=====================================================================

FileHandler::FileHandler(const QString& filename, std::shared_ptr<ILogFormatter> formatter)
    : m_filename(filename)
    , m_formatter(formatter)
    , m_stream(&m_file)
    , m_appendMode(true) {
    
    if (!m_formatter) {
        m_formatter = std::make_shared<DefaultSimpleFormatter>();
    }
    
    connect(&m_flushTimer, &QTimer::timeout, this, &FileHandler::flush);
    setAutoFlushInterval(1000);  // 默认1秒自动刷新
    
    openFile();
}

FileHandler::~FileHandler() {
    close();
}

void FileHandler::handle(const LogEntry& entry) {
    QMutexLocker locker(&m_mutex);
    
    if (!m_formatter || !m_file.isOpen()) {
        return;
    }
    
    QString formattedMessage = m_formatter->format(entry);
    m_stream << formattedMessage << Qt::endl;
}

void FileHandler::flush() {
    QMutexLocker locker(&m_mutex);
    
    if (m_file.isOpen()) {
        m_stream.flush();
        m_file.flush();
    }
}

void FileHandler::close() {
    QMutexLocker locker(&m_mutex);
    
    m_flushTimer.stop();
    closeFile();
}

void FileHandler::setFormatter(std::shared_ptr<ILogFormatter> formatter) {
    QMutexLocker locker(&m_mutex);
    m_formatter = formatter;
}

std::shared_ptr<ILogFormatter> FileHandler::getFormatter() const {
    QMutexLocker locker(&m_mutex);
    return m_formatter;
}

void FileHandler::setFilename(const QString& filename) {
    QMutexLocker locker(&m_mutex);
    
    if (filename != m_filename) {
        closeFile();
        m_filename = filename;
        openFile();
    }
}

QString FileHandler::getFilename() const {
    QMutexLocker locker(&m_mutex);
    return m_filename;
}

void FileHandler::setAppendMode(bool append) {
    QMutexLocker locker(&m_mutex);
    
    if (append != m_appendMode) {
        closeFile();
        m_appendMode = append;
        openFile();
    }
}

bool FileHandler::isAppendMode() const {
    QMutexLocker locker(&m_mutex);
    return m_appendMode;
}

void FileHandler::setAutoFlushInterval(int interval) {
    QMutexLocker locker(&m_mutex);
    
    if (interval > 0) {
        m_flushTimer.start(interval);
    } else {
        m_flushTimer.stop();
    }
}

int FileHandler::getAutoFlushInterval() const {
    QMutexLocker locker(&m_mutex);
    return m_flushTimer.interval();
}

void FileHandler::openFile() {
    // 确保目录存在
    QFileInfo fileInfo(m_filename);
    QDir dir = fileInfo.dir();
    if (!dir.exists()) {
        dir.mkpath(".");
    }
    
    m_file.setFileName(m_filename);
    
    QIODevice::OpenMode mode = QIODevice::WriteOnly | QIODevice::Text;
    if (m_appendMode) {
        mode |= QIODevice::Append;
    } else {
        mode |= QIODevice::Truncate;
    }
    
    if (m_file.open(mode)) {
        m_stream.setDevice(&m_file);
        m_stream.setCodec("UTF-8");
    }
}

void FileHandler::closeFile() {
    if (m_file.isOpen()) {
        m_stream.flush();
        m_file.close();
        m_stream.setDevice(nullptr);
    }
}

//=====================================================================
// RotatingFileHandler Implementation
//=====================================================================

RotatingFileHandler::RotatingFileHandler(const QString& baseFilename, 
                                       qint64 maxFileSize, 
                                       int maxBackupCount,
                                       std::shared_ptr<ILogFormatter> formatter)
    : m_baseFilename(baseFilename)
    , m_currentFilename(baseFilename)
    , m_maxFileSize(maxFileSize)
    , m_maxBackupCount(maxBackupCount)
    , m_formatter(formatter)
    , m_stream(&m_file) {
    
    if (!m_formatter) {
        m_formatter = std::make_shared<DefaultSimpleFormatter>();
    }
    
    // 确保目录存在
    QFileInfo fileInfo(m_baseFilename);
    QDir dir = fileInfo.dir();
    if (!dir.exists()) {
        dir.mkpath(".");
    }
    
    m_file.setFileName(m_currentFilename);
    if (m_file.open(QIODevice::WriteOnly | QIODevice::Append | QIODevice::Text)) {
        m_stream.setDevice(&m_file);
        m_stream.setCodec("UTF-8");
    }
}

RotatingFileHandler::~RotatingFileHandler() {
    close();
}

void RotatingFileHandler::handle(const LogEntry& entry) {
    QMutexLocker locker(&m_mutex);
    
    if (!m_formatter || !m_file.isOpen()) {
        return;
    }
    
    checkRotation();
    
    QString formattedMessage = m_formatter->format(entry);
    m_stream << formattedMessage << Qt::endl;
}

void RotatingFileHandler::flush() {
    QMutexLocker locker(&m_mutex);
    
    if (m_file.isOpen()) {
        m_stream.flush();
        m_file.flush();
    }
}

void RotatingFileHandler::close() {
    QMutexLocker locker(&m_mutex);
    
    if (m_file.isOpen()) {
        m_stream.flush();
        m_file.close();
        m_stream.setDevice(nullptr);
    }
}

void RotatingFileHandler::setFormatter(std::shared_ptr<ILogFormatter> formatter) {
    QMutexLocker locker(&m_mutex);
    m_formatter = formatter;
}

std::shared_ptr<ILogFormatter> RotatingFileHandler::getFormatter() const {
    QMutexLocker locker(&m_mutex);
    return m_formatter;
}

void RotatingFileHandler::setMaxFileSize(qint64 size) {
    QMutexLocker locker(&m_mutex);
    m_maxFileSize = size;
}

qint64 RotatingFileHandler::getMaxFileSize() const {
    QMutexLocker locker(&m_mutex);
    return m_maxFileSize;
}

void RotatingFileHandler::setMaxBackupCount(int count) {
    QMutexLocker locker(&m_mutex);
    m_maxBackupCount = count;
}

int RotatingFileHandler::getMaxBackupCount() const {
    QMutexLocker locker(&m_mutex);
    return m_maxBackupCount;
}

void RotatingFileHandler::rotate() {
    QMutexLocker locker(&m_mutex);
    performRotation();
}

void RotatingFileHandler::checkRotation() {
    if (m_file.isOpen() && m_file.size() >= m_maxFileSize) {
        performRotation();
    }
}

void RotatingFileHandler::performRotation() {
    if (m_file.isOpen()) {
        m_stream.flush();
        m_file.close();
        m_stream.setDevice(nullptr);
    }
    
    // 移动现有的备份文件
    for (int i = m_maxBackupCount - 1; i > 0; --i) {
        QString oldBackup = getBackupFilename(i);
        QString newBackup = getBackupFilename(i + 1);
        
        if (QFile::exists(oldBackup)) {
            if (QFile::exists(newBackup)) {
                QFile::remove(newBackup);
            }
            QFile::rename(oldBackup, newBackup);
        }
    }
    
    // 移动当前文件到第一个备份
    if (QFile::exists(m_currentFilename)) {
        QString firstBackup = getBackupFilename(1);
        if (QFile::exists(firstBackup)) {
            QFile::remove(firstBackup);
        }
        QFile::rename(m_currentFilename, firstBackup);
    }
    
    // 重新打开文件
    m_file.setFileName(m_currentFilename);
    if (m_file.open(QIODevice::WriteOnly | QIODevice::Truncate | QIODevice::Text)) {
        m_stream.setDevice(&m_file);
        m_stream.setCodec("UTF-8");
    }
}

QString RotatingFileHandler::getBackupFilename(int index) const {
    return QString("%1.%2").arg(m_baseFilename).arg(index);
}

//=====================================================================
// TimedRotatingFileHandler Implementation
//=====================================================================

TimedRotatingFileHandler::TimedRotatingFileHandler(const QString& baseFilename,
                                                 RotationInterval interval,
                                                 int maxBackupCount,
                                                 std::shared_ptr<ILogFormatter> formatter)
    : m_baseFilename(baseFilename)
    , m_interval(interval)
    , m_maxBackupCount(maxBackupCount)
    , m_formatter(formatter)
    , m_stream(&m_file) {
    
    if (!m_formatter) {
        m_formatter = std::make_shared<DefaultSimpleFormatter>();
    }
    
    m_nextRotationTime = getNextRotationTime();
    
    // 确保目录存在
    QFileInfo fileInfo(m_baseFilename);
    QDir dir = fileInfo.dir();
    if (!dir.exists()) {
        dir.mkpath(".");
    }
    
    QString currentFilename = getCurrentFilename();
    m_file.setFileName(currentFilename);
    if (m_file.open(QIODevice::WriteOnly | QIODevice::Append | QIODevice::Text)) {
        m_stream.setDevice(&m_file);
        m_stream.setCodec("UTF-8");
    }
}

TimedRotatingFileHandler::~TimedRotatingFileHandler() {
    close();
}

void TimedRotatingFileHandler::handle(const LogEntry& entry) {
    QMutexLocker locker(&m_mutex);
    
    if (!m_formatter || !m_file.isOpen()) {
        return;
    }
    
    checkRotation();
    
    QString formattedMessage = m_formatter->format(entry);
    m_stream << formattedMessage << Qt::endl;
}

void TimedRotatingFileHandler::flush() {
    QMutexLocker locker(&m_mutex);
    
    if (m_file.isOpen()) {
        m_stream.flush();
        m_file.flush();
    }
}

void TimedRotatingFileHandler::close() {
    QMutexLocker locker(&m_mutex);
    
    if (m_file.isOpen()) {
        m_stream.flush();
        m_file.close();
        m_stream.setDevice(nullptr);
    }
}

void TimedRotatingFileHandler::setFormatter(std::shared_ptr<ILogFormatter> formatter) {
    QMutexLocker locker(&m_mutex);
    m_formatter = formatter;
}

std::shared_ptr<ILogFormatter> TimedRotatingFileHandler::getFormatter() const {
    QMutexLocker locker(&m_mutex);
    return m_formatter;
}

void TimedRotatingFileHandler::setRotationInterval(RotationInterval interval) {
    QMutexLocker locker(&m_mutex);
    m_interval = interval;
    m_nextRotationTime = getNextRotationTime();
}

TimedRotatingFileHandler::RotationInterval TimedRotatingFileHandler::getRotationInterval() const {
    QMutexLocker locker(&m_mutex);
    return m_interval;
}

void TimedRotatingFileHandler::setMaxBackupCount(int count) {
    QMutexLocker locker(&m_mutex);
    m_maxBackupCount = count;
}

int TimedRotatingFileHandler::getMaxBackupCount() const {
    QMutexLocker locker(&m_mutex);
    return m_maxBackupCount;
}

void TimedRotatingFileHandler::checkRotation() {
    QDateTime now = QDateTime::currentDateTime();
    if (now >= m_nextRotationTime) {
        performRotation();
    }
}

void TimedRotatingFileHandler::performRotation() {
    if (m_file.isOpen()) {
        m_stream.flush();
        m_file.close();
        m_stream.setDevice(nullptr);
    }
    
    // 更新下次轮转时间
    m_nextRotationTime = getNextRotationTime();
    
    // 打开新的日志文件
    QString newFilename = getCurrentFilename();
    m_file.setFileName(newFilename);
    if (m_file.open(QIODevice::WriteOnly | QIODevice::Append | QIODevice::Text)) {
        m_stream.setDevice(&m_file);
        m_stream.setCodec("UTF-8");
    }
    
    // TODO: 清理旧的备份文件（保留m_maxBackupCount个）
}

QString TimedRotatingFileHandler::getCurrentFilename() const {
    QString suffix = getTimeSuffix();
    QFileInfo info(m_baseFilename);
    QString baseName = info.completeBaseName();
    QString extension = info.suffix();
    
    if (extension.isEmpty()) {
        return QString("%1_%2").arg(baseName, suffix);
    } else {
        return QString("%1_%2.%3").arg(baseName, suffix, extension);
    }
}

QString TimedRotatingFileHandler::getTimeSuffix() const {
    QDateTime now = QDateTime::currentDateTime();
    
    switch (m_interval) {
        case RotationInterval::HOURLY:
            return now.toString("yyyy-MM-dd_hh");
        case RotationInterval::DAILY:
            return now.toString("yyyy-MM-dd");
        case RotationInterval::WEEKLY:
            return now.toString("yyyy-'W'ww");
        case RotationInterval::MONTHLY:
            return now.toString("yyyy-MM");
        default:
            return now.toString("yyyy-MM-dd");
    }
}

QDateTime TimedRotatingFileHandler::getNextRotationTime() const {
    QDateTime now = QDateTime::currentDateTime();
    QDateTime next = now;
    
    switch (m_interval) {
        case RotationInterval::HOURLY:
            next = next.addSecs(3600 - (now.time().minute() * 60 + now.time().second()));
            break;
        case RotationInterval::DAILY:
            next = QDateTime(now.date().addDays(1), QTime(0, 0));
            break;
        case RotationInterval::WEEKLY:
            next = QDateTime(now.date().addDays(7 - now.date().dayOfWeek()), QTime(0, 0));
            break;
        case RotationInterval::MONTHLY:
            next = QDateTime(QDate(now.date().year(), now.date().month(), 1).addMonths(1), QTime(0, 0));
            break;
    }
    
    return next;
}

//=====================================================================
// NetworkHandler Implementation
//=====================================================================

NetworkHandler::NetworkHandler(const QString& host, quint16 port, 
                             Protocol protocol, 
                             std::shared_ptr<ILogFormatter> formatter)
    : m_host(host)
    , m_port(port)
    , m_protocol(protocol)
    , m_formatter(formatter)
    , m_connectionTimeout(5000)
    , m_socket(nullptr) {
    
    if (!m_formatter) {
        m_formatter = std::make_shared<DefaultSimpleFormatter>();
    }
    
    connectToHost();
}

NetworkHandler::~NetworkHandler() {
    close();
}

void NetworkHandler::handle(const LogEntry& entry) {
    QMutexLocker locker(&m_mutex);
    
    if (!m_formatter || !m_socket) {
        return;
    }
    
    QString formattedMessage = m_formatter->format(entry);
    QByteArray data = formattedMessage.toUtf8() + "\n";
    
    if (m_protocol == Protocol::UDP) {
        QUdpSocket* udpSocket = static_cast<QUdpSocket*>(m_socket);
        udpSocket->writeDatagram(data, QHostAddress(m_host), m_port);
    } else {
        QTcpSocket* tcpSocket = static_cast<QTcpSocket*>(m_socket);
        if (tcpSocket->state() == QAbstractSocket::ConnectedState) {
            tcpSocket->write(data);
        }
    }
}

void NetworkHandler::flush() {
    QMutexLocker locker(&m_mutex);
    
    if (m_socket && m_protocol == Protocol::TCP) {
        QTcpSocket* tcpSocket = static_cast<QTcpSocket*>(m_socket);
        if (tcpSocket->state() == QAbstractSocket::ConnectedState) {
            tcpSocket->flush();
        }
    }
}

void NetworkHandler::close() {
    QMutexLocker locker(&m_mutex);
    disconnectFromHost();
}

void NetworkHandler::setFormatter(std::shared_ptr<ILogFormatter> formatter) {
    QMutexLocker locker(&m_mutex);
    m_formatter = formatter;
}

std::shared_ptr<ILogFormatter> NetworkHandler::getFormatter() const {
    QMutexLocker locker(&m_mutex);
    return m_formatter;
}

void NetworkHandler::setConnectionTimeout(int timeout) {
    QMutexLocker locker(&m_mutex);
    m_connectionTimeout = timeout;
}

int NetworkHandler::getConnectionTimeout() const {
    QMutexLocker locker(&m_mutex);
    return m_connectionTimeout;
}

void NetworkHandler::connectToHost() {
    if (m_protocol == Protocol::UDP) {
        m_socket = new QUdpSocket();
    } else {
        QTcpSocket* tcpSocket = new QTcpSocket();
        tcpSocket->connectToHost(m_host, m_port);
        tcpSocket->waitForConnected(m_connectionTimeout);
        m_socket = tcpSocket;
    }
}

void NetworkHandler::disconnectFromHost() {
    if (m_socket) {
        if (m_protocol == Protocol::UDP) {
            QUdpSocket* udpSocket = static_cast<QUdpSocket*>(m_socket);
            udpSocket->close();
            delete udpSocket;
        } else {
            QTcpSocket* tcpSocket = static_cast<QTcpSocket*>(m_socket);
            tcpSocket->disconnectFromHost();
            tcpSocket->waitForDisconnected(1000);
            delete tcpSocket;
        }
        m_socket = nullptr;
    }
}

}  // namespace Logging
}  // namespace Support
}  // namespace LA