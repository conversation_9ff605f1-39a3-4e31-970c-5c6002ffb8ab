#ifndef LA_THREAD_H
#define LA_THREAD_H

/**
 * @brief LA线程管理系统统一包含头文件
 * 
 * 该文件提供LA线程管理系统的统一访问接口，包含所有必要的头文件。
 * 使用方只需包含此文件即可访问完整的线程管理功能。
 * 
 * 使用示例：
 * @code
 * #include <LA/Thread/Thread.h>
 * 
 * using namespace LA::Thread;
 * 
 * // 获取线程管理器
 * auto threadManager = IThreadManager::instance();
 * 
 * // 创建通信线程
 * auto commThread = threadManager->createCommunicationThread("device001");
 * 
 * // 使用线程池
 * auto threadPool = threadManager->getThreadPool();
 * threadPool->submitTask([](){
 *     // 执行任务
 * });
 * @endcode
 */

// 核心类型定义
#include "ThreadTypes.h"

// 主要接口
#include "IThreadManager.h"
#include "IThreadPool.h"
#include "ICommunicationThread.h"

// 版本信息
#define LA_THREAD_VERSION_MAJOR 1
#define LA_THREAD_VERSION_MINOR 0
#define LA_THREAD_VERSION_PATCH 0
#define LA_THREAD_VERSION "1.0.0"

// 命名空间别名（可选）
namespace LAThread = LA::Thread;

#endif // LA_THREAD_H