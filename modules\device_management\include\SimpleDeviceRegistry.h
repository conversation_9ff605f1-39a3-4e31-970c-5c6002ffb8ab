#pragma once

/**
 * @file SimpleDeviceRegistry.h
 * @brief 简化的设备注册表实现声明
 * 
 * 遵循Linus开发哲学的简单实现：
 * - 最小可行产品：先让基本功能工作
 * - 单一职责：专注于设备信息管理
 * - 可组合性：为上层提供稳定接口
 */

#include "IDeviceRegistry.h"
#include <memory>

namespace LA {
namespace DeviceManagement {

/**
 * @brief 创建设备注册表实例
 * @return 设备注册表智能指针
 */
std::shared_ptr<IDeviceRegistry> createDeviceRegistry();

/**
 * @brief 创建设备注册表工厂实例
 * @return 工厂智能指针
 */
std::shared_ptr<IDeviceRegistryFactory> createDeviceRegistryFactory();

} // namespace DeviceManagement
} // namespace LA