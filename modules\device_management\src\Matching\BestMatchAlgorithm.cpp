#include "LA/DeviceManagement/Matching/BestMatchAlgorithm.h"
#include <QDebug>
#include <algorithm>

namespace LA {
namespace DeviceManagement {
namespace Matching {

// 使用简化类型定义和转换函数
using LA::DeviceManagement::Core::DeviceInfo;
using LA::DeviceManagement::Core::DeviceType;
using LA::DeviceManagement::Core::deviceTypeToString;
using LA::DeviceManagement::Core::PortInfo;
using LA::DeviceManagement::Core::PortStatus;
using LA::DeviceManagement::Core::portStatusToString;
using LA::DeviceManagement::Core::PortType;
using LA::DeviceManagement::Core::portTypeToString;

BestMatchAlgorithm::BestMatchAlgorithm() : m_strategy(MatchingStrategy::BestMatch) {
    initializeWeights();
    initializeCompatibilityMatrix();
    initializePreferenceRules();

    qDebug() << "BestMatchAlgorithm initialized with strategy:" << (int)m_strategy;
}

float BestMatchAlgorithm::calculateMatchConfidence(const PortInfo &port, const DeviceInfo &device) {
    if (!port.isValid() || !device.isValid()) {
        return 0.0f;
    }

    // 计算各个匹配因子
    float portTypeScore      = calculatePortTypeScore(port, device);
    float capabilityScore    = calculateCapabilityScore(port, device);
    float compatibilityScore = calculateCompatibilityScore(port, device);
    float preferenceScore    = calculatePreferenceScore(port, device);

    // 加权计算总置信度
    float confidence = portTypeScore * m_portTypeWeight + capabilityScore * m_capabilityWeight + compatibilityScore * m_compatibilityWeight +
                       preferenceScore * m_preferenceWeight;

    // 确保置信度在0-1范围内
    confidence = qBound(0.0f, confidence, 1.0f);

    qDebug() << QString("Match confidence for %1 <-> %2: %3 (PT:%4 C:%5 CM:%6 P:%7)")
                    .arg(deviceTypeToString(device.deviceType))
                    .arg(port.portName)
                    .arg(confidence)
                    .arg(portTypeScore)
                    .arg(capabilityScore)
                    .arg(compatibilityScore)
                    .arg(preferenceScore);

    return confidence;
}

MatchPair BestMatchAlgorithm::findBestPortForDevice(const DeviceInfo &device, const QList<PortInfo> &availablePorts, const MatchingConfig &config) {
    MatchPair bestMatch;
    float     bestConfidence = 0.0f;

    for (const auto &port : availablePorts) {
        // 跳过不可用的端口
        if (port.status != PortStatus::Available) {
            continue;
        }

        float confidence = calculateMatchConfidence(port, device);

        if (confidence >= config.minimumConfidence && confidence > bestConfidence) {
            bestMatch.port        = port;
            bestMatch.device      = device;
            bestMatch.confidence  = confidence;
            bestMatch.matchReason = generateMatchReason(port, device, confidence);
            bestConfidence        = confidence;
        }
    }

    if (bestMatch.isValid()) {
        qDebug() << QString("Best port for %1 is %2 with confidence %3")
                        .arg(deviceTypeToString(device.deviceType))
                        .arg(bestMatch.port.portName)
                        .arg(bestMatch.confidence);
    } else {
        qDebug() << "No suitable port found for device:" << deviceTypeToString(device.deviceType);
    }

    return bestMatch;
}

MatchingResult BestMatchAlgorithm::matchDevicesToPorts(const QList<DeviceInfo> &devices, const QList<PortInfo> &ports, const MatchingConfig &config) {
    qDebug() << "Starting device-port matching with strategy:" << (int)m_strategy;
    qDebug() << "Devices:" << devices.size() << "Ports:" << ports.size();

    MatchingResult result;

    switch (m_strategy) {
    case MatchingStrategy::BestMatch:
        result = performBestMatching(devices, ports, config);
        break;
    case MatchingStrategy::FirstAvailable:
        result = performFirstAvailableMatching(devices, ports, config);
        break;
    case MatchingStrategy::LoadBalance:
        result = performLoadBalanceMatching(devices, ports, config);
        break;
    case MatchingStrategy::UserDefined:
        // 用户自定义策略暂时使用最佳匹配
        result = performBestMatching(devices, ports, config);
        break;
    }

    result.totalProcessed = devices.size();
    result.successCount   = result.successMatches.size();
    result.status         = result.errorMessage.isEmpty() ? "Success" : "Error";

    qDebug() << QString("Matching completed: %1/%2 successful matches").arg(result.successCount).arg(result.totalProcessed);

    return result;
}

bool BestMatchAlgorithm::validateMatch(const PortInfo &port, const DeviceInfo &device) {
    // 基本有效性检查
    if (!port.isValid() || !device.isValid()) {
        return false;
    }

    // 端口必须可用
    if (port.status != PortStatus::Available) {
        return false;
    }

    // 检查端口类型兼容性
    if (!isPortTypeCompatible(portTypeToString(port.portType), device)) {
        return false;
    }

    // 检查必需的能力
    if (!hasRequiredCapabilities(port, device)) {
        return false;
    }

    return true;
}

void BestMatchAlgorithm::setMatchingStrategy(MatchingStrategy strategy) {
    m_strategy = strategy;
    qDebug() << "Matching strategy changed to:" << (int)strategy;
}

MatchingStrategy BestMatchAlgorithm::getMatchingStrategy() const {
    return m_strategy;
}

float BestMatchAlgorithm::calculatePortTypeScore(const PortInfo &port, const DeviceInfo &device) {
    // 检查端口类型与设备的兼容性
    if (!isPortTypeCompatible(portTypeToString(port.portType), device)) {
        return 0.0f;
    }

    QString deviceType = deviceTypeToString(device.deviceType);
    QString portType   = portTypeToString(port.portType);

    // 完美匹配情况
    if ((deviceType == "GENERIC" && portType == "SERIAL") || (deviceType == "MOTOR" && portType == "SERIAL") ||
        (deviceType.contains("SENSOR") && portType == "SERIAL")) {
        return 1.0f;
    }

    // 良好匹配
    if (portType == "SERIAL" && device.capabilities.contains("Serial_Communication")) {
        return 0.8f;
    }

    if (portType == "TCP" && device.capabilities.contains("Network_Communication")) {
        return 0.8f;
    }

    // 基本兼容
    return 0.5f;
}

float BestMatchAlgorithm::calculateCapabilityScore(const PortInfo &port, const DeviceInfo &device) {
    float score               = 0.0f;
    int   matchedCapabilities = 0;
    int   totalCapabilities   = device.capabilities.size();

    if (totalCapabilities == 0) {
        return 0.5f;  // 默认分数
    }

    // 检查设备能力与端口特性的匹配
    for (const QString &capability : device.capabilities) {
        if (capability.contains("Serial") && port.portType == PortType::Serial) {
            matchedCapabilities++;
        } else if (capability.contains("Network") && port.portType == PortType::TCP) {
            matchedCapabilities++;
        } else if (capability.contains("Communication")) {
            matchedCapabilities++;  // 通用通讯能力
        }
    }

    score = static_cast<float>(matchedCapabilities) / totalCapabilities;
    return qBound(0.0f, score, 1.0f);
}

float BestMatchAlgorithm::calculateCompatibilityScore(const PortInfo &port, const DeviceInfo &device) {
    QString deviceType = deviceTypeToString(device.deviceType);
    QString portType   = portTypeToString(port.portType);

    // 查找兼容性矩阵
    auto deviceMatrix = m_compatibilityMatrix.find(deviceType);
    if (deviceMatrix != m_compatibilityMatrix.end()) {
        auto portScore = deviceMatrix.value().find(portType);
        if (portScore != deviceMatrix.value().end()) {
            return portScore.value();
        }
    }

    // 默认兼容性分数
    return 0.5f;
}

float BestMatchAlgorithm::calculatePreferenceScore(const PortInfo &port, const DeviceInfo &device) {
    QString deviceType = deviceTypeToString(device.deviceType);
    QString portType   = portTypeToString(port.portType);

    // 检查偏好规则
    auto preferences = m_preferenceRules.find(deviceType);
    if (preferences != m_preferenceRules.end()) {
        const QStringList &preferredPorts = preferences.value();

        int index = preferredPorts.indexOf(portType);
        if (index != -1) {
            // 越靠前的偏好分数越高
            return 1.0f - (static_cast<float>(index) / preferredPorts.size());
        }
    }

    return 0.3f;  // 默认偏好分数
}

MatchingResult BestMatchAlgorithm::performBestMatching(const QList<DeviceInfo> &devices, const QList<PortInfo> &ports, const MatchingConfig &config) {
    MatchingResult  result;
    QList<PortInfo> availablePorts = ports;

    // 创建所有可能的匹配对并计算置信度
    QList<MatchPair> allPairs;

    for (const auto &device : devices) {
        for (const auto &port : availablePorts) {
            if (validateMatch(port, device)) {
                MatchPair pair;
                pair.device      = device;
                pair.port        = port;
                pair.confidence  = calculateMatchConfidence(port, device);
                pair.matchReason = generateMatchReason(port, device, pair.confidence);

                if (pair.confidence >= config.minimumConfidence) {
                    allPairs.append(pair);
                }
            }
        }
    }

    // 按置信度排序
    sortMatchesByConfidence(allPairs);

    // 贪心选择最佳匹配
    QList<DeviceInfo> remainingDevices = devices;

    for (const auto &pair : allPairs) {
        // 检查设备和端口是否还可用
        if (remainingDevices.contains(pair.device) && availablePorts.contains(pair.port)) {
            result.successMatches.append(pair);
            remainingDevices.removeAll(pair.device);
            availablePorts.removeAll(pair.port);
        }
    }

    result.unmatchedDevices = remainingDevices;
    result.unmatchedPorts   = availablePorts;

    return result;
}

MatchingResult BestMatchAlgorithm::performFirstAvailableMatching(const QList<DeviceInfo> &devices, const QList<PortInfo> &ports, const MatchingConfig &config) {
    MatchingResult  result;
    QList<PortInfo> availablePorts = ports;

    for (const auto &device : devices) {
        bool matched = false;

        for (const auto &port : availablePorts) {
            if (validateMatch(port, device)) {
                float confidence = calculateMatchConfidence(port, device);

                if (confidence >= config.minimumConfidence) {
                    MatchPair pair;
                    pair.device      = device;
                    pair.port        = port;
                    pair.confidence  = confidence;
                    pair.matchReason = generateMatchReason(port, device, confidence);

                    result.successMatches.append(pair);
                    availablePorts.removeAll(port);
                    matched = true;
                    break;
                }
            }
        }

        if (!matched) {
            result.unmatchedDevices.append(device);
        }
    }

    result.unmatchedPorts = availablePorts;
    return result;
}

MatchingResult BestMatchAlgorithm::performLoadBalanceMatching(const QList<DeviceInfo> &devices, const QList<PortInfo> &ports, const MatchingConfig &config) {
    // 负载均衡匹配：尽量平均分配端口使用
    // 这里简化实现为轮询分配

    MatchingResult  result;
    QList<PortInfo> availablePorts = ports;
    int             portIndex      = 0;

    for (const auto &device : devices) {
        bool matched  = false;
        int  attempts = 0;

        // 从当前索引开始尝试匹配
        while (attempts < availablePorts.size() && !matched) {
            if (portIndex >= availablePorts.size()) {
                portIndex = 0;
            }

            const auto &port = availablePorts[portIndex];

            if (validateMatch(port, device)) {
                float confidence = calculateMatchConfidence(port, device);

                if (confidence >= config.minimumConfidence) {
                    MatchPair pair;
                    pair.device      = device;
                    pair.port        = port;
                    pair.confidence  = confidence;
                    pair.matchReason = generateMatchReason(port, device, confidence);

                    result.successMatches.append(pair);
                    availablePorts.removeAt(portIndex);
                    matched = true;
                } else {
                    portIndex++;
                }
            } else {
                portIndex++;
            }

            attempts++;
        }

        if (!matched) {
            result.unmatchedDevices.append(device);
        }
    }

    result.unmatchedPorts = availablePorts;
    return result;
}

bool BestMatchAlgorithm::isPortTypeCompatible(const QString &portType, const DeviceInfo &device) {
    QString deviceType = deviceTypeToString(device.deviceType);
    QString type       = portType.toUpper();

    // 通用设备兼容性
    if (deviceType == "GENERIC") {
        return type == "SERIAL" || type == "USB";
    }

    // 电机设备兼容性
    if (deviceType == "MOTOR") {
        return type == "SERIAL" || type == "TCP" || type == "USB";
    }

    // 传感器设备兼容性
    if (deviceType.contains("SENSOR")) {
        return type == "SERIAL" || type == "TCP" || type == "USB";
    }

    // 默认：串口通常兼容大部分设备
    return type == "SERIAL";
}

bool BestMatchAlgorithm::hasRequiredCapabilities(const PortInfo &port, const DeviceInfo &device) {
    // 检查端口是否支持设备所需的能力
    // 这里简化实现，主要检查通信能力

    QString portType = portTypeToString(port.portType);

    for (const QString &capability : device.capabilities) {
        if (capability.contains("Serial") && portType != "SERIAL") {
            return false;
        }
        if (capability.contains("Network") && portType != "TCP" && portType != "UDP") {
            return false;
        }
    }

    return true;
}

QString BestMatchAlgorithm::generateMatchReason(const PortInfo &port, const DeviceInfo &device, float confidence) {
    QStringList reasons;

    if (confidence >= 0.9f) {
        reasons << "Perfect match";
    } else if (confidence >= 0.7f) {
        reasons << "Good match";
    } else if (confidence >= 0.5f) {
        reasons << "Acceptable match";
    } else {
        reasons << "Low confidence match";
    }

    // 添加具体匹配因素
    if (isPortTypeCompatible(portTypeToString(port.portType), device)) {
        reasons << QString("Port type %1 compatible with %2").arg(portTypeToString(port.portType)).arg(deviceTypeToString(device.deviceType));
    }

    return reasons.join("; ");
}

void BestMatchAlgorithm::sortMatchesByConfidence(QList<MatchPair> &matches) {
    std::sort(matches.begin(), matches.end(), [](const MatchPair &a, const MatchPair &b) { return a.confidence > b.confidence; });
}

void BestMatchAlgorithm::initializeWeights() {
    // 可以通过配置文件或参数调整这些权重
    m_portTypeWeight      = 0.4f;
    m_capabilityWeight    = 0.3f;
    m_compatibilityWeight = 0.2f;
    m_preferenceWeight    = 0.1f;

    qDebug() << "Match weights initialized: PT:" << m_portTypeWeight << "C:" << m_capabilityWeight << "CM:" << m_compatibilityWeight
             << "P:" << m_preferenceWeight;
}

void BestMatchAlgorithm::initializeCompatibilityMatrix() {
    // SPRM设备兼容性
    m_compatibilityMatrix["SPRM"]["SERIAL"] = 1.0f;
    m_compatibilityMatrix["SPRM"]["USB"]    = 0.8f;
    m_compatibilityMatrix["SPRM"]["TCP"]    = 0.3f;

    // 电机设备兼容性
    m_compatibilityMatrix["MOTOR"]["SERIAL"] = 0.9f;
    m_compatibilityMatrix["MOTOR"]["TCP"]    = 0.8f;
    m_compatibilityMatrix["MOTOR"]["USB"]    = 0.7f;

    // 传感器设备兼容性
    m_compatibilityMatrix["SENSOR"]["SERIAL"] = 0.8f;
    m_compatibilityMatrix["SENSOR"]["TCP"]    = 0.9f;
    m_compatibilityMatrix["SENSOR"]["USB"]    = 0.7f;

    qDebug() << "Compatibility matrix initialized";
}

void BestMatchAlgorithm::initializePreferenceRules() {
    // SPRM设备偏好：优先串口，其次USB
    m_preferenceRules["SPRM"] = QStringList({"SERIAL", "USB", "TCP"});

    // 电机设备偏好：优先串口和网络
    m_preferenceRules["MOTOR"] = QStringList({"SERIAL", "TCP", "USB"});

    // 传感器设备偏好：优先网络，其次串口
    m_preferenceRules["SENSOR"] = QStringList({"TCP", "SERIAL", "USB"});

    qDebug() << "Preference rules initialized";
}

}  // namespace Matching
}  // namespace DeviceManagement
}  // namespace LA