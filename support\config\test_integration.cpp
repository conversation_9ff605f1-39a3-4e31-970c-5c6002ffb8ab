#include <QCoreApplication>
#include <QDebug>
#include <QVariantMap>
#include <QJsonDocument>
#include <QJsonObject>

// 包含重构后的配置模块头文件
#include "parameter/ParameterManager.h"
#include "file/JsonConfigFileHandler.h"
#include "validation/ConfigValidator.h"

using namespace LA::Support::Config;

/**
 * @brief 配置模块集成测试
 * 
 * 测试三个重构后的配置模块的基本功能和相互集成：
 * 1. 参数管理模块 - 定义和管理配置参数
 * 2. 配置文件处理模块 - 读写JSON/INI配置文件
 * 3. 配置验证模块 - 验证配置数据的有效性
 */
class ConfigModuleIntegrationTest {
public:
    static bool runAllTests() {
        qDebug() << "=== 配置模块集成测试开始 ===";
        
        bool allPassed = true;
        
        // 测试参数管理模块
        if (!testParameterManager()) {
            qDebug() << "❌ 参数管理模块测试失败";
            allPassed = false;
        } else {
            qDebug() << "✅ 参数管理模块测试通过";
        }
        
        // 测试配置文件处理模块
        if (!testConfigFileHandler()) {
            qDebug() << "❌ 配置文件处理模块测试失败";
            allPassed = false;
        } else {
            qDebug() << "✅ 配置文件处理模块测试通过";
        }
        
        // 测试配置验证模块
        if (!testConfigValidator()) {
            qDebug() << "❌ 配置验证模块测试失败";
            allPassed = false;
        } else {
            qDebug() << "✅ 配置验证模块测试通过";
        }
        
        // 测试模块集成
        if (!testModuleIntegration()) {
            qDebug() << "❌ 模块集成测试失败";
            allPassed = false;
        } else {
            qDebug() << "✅ 模块集成测试通过";
        }
        
        qDebug() << "=== 配置模块集成测试" << (allPassed ? "完全通过" : "部分失败") << " ===";
        return allPassed;
    }

private:
    static bool testParameterManager() {
        qDebug() << "\n--- 测试参数管理模块 ---";
        
        try {
            // 创建参数管理器
            auto factory = std::make_unique<ParameterManagerFactory>();
            auto manager = factory->createParameterManager();
            
            if (!manager) {
                qDebug() << "无法创建参数管理器";
                return false;
            }
            
            // 测试参数定义
            ParameterDefinition definition;
            definition.key = "app_timeout";
            definition.name = "Application Timeout";
            definition.description = "Timeout value in seconds";
            definition.type = ParameterType::INT;
            definition.defaultValue = 30;
            definition.validation.minValue = 1;
            definition.validation.maxValue = 300;
            
            auto defineResult = manager->defineParameter(definition);
            if (!defineResult.success) {
                qDebug() << "参数定义失败:" << defineResult.message;
                return false;
            }
            
            // 测试参数设置
            auto setResult = manager->setParameter("app_timeout", 60, "test", "测试设置");
            if (!setResult.success) {
                qDebug() << "参数设置失败:" << setResult.message;
                return false;
            }
            
            // 测试参数获取
            QVariant value = manager->getParameter("app_timeout", 30);
            if (value.toInt() != 60) {
                qDebug() << "参数获取失败，期望60，实际" << value.toInt();
                return false;
            }
            
            qDebug() << "参数管理模块基本功能正常";
            return true;
            
        } catch (const std::exception& e) {
            qDebug() << "参数管理模块测试异常:" << e.what();
            return false;
        }
    }
    
    static bool testConfigFileHandler() {
        qDebug() << "\n--- 测试配置文件处理模块 ---";
        
        try {
            // 创建测试数据
            QVariantMap testData;
            testData["app_name"] = "ConfigTest";
            testData["version"] = "1.0.0";
            testData["server"] = QVariantMap{
                {"host", "localhost"},
                {"port", 8080},
                {"ssl", true}
            };
            testData["database"] = QVariantMap{
                {"type", "mysql"},
                {"host", "db.example.com"},
                {"port", 3306},
                {"name", "testdb"}
            };
            
            // 测试JSON文件保存
            QString testFile = "test_config.json";
            ConfigFileOptions options;
            options.prettyPrint = true;
            options.createIfNotExists = true;
            
            auto saveResult = JsonConfigFileHandler::saveJsonFile(testFile, testData, options);
            if (!saveResult.success) {
                qDebug() << "JSON文件保存失败:" << saveResult.message;
                return false;
            }
            
            // 测试JSON文件加载
            auto loadResult = JsonConfigFileHandler::loadJsonFile(testFile, options);
            if (!loadResult.success) {
                qDebug() << "JSON文件加载失败:" << loadResult.message;
                return false;
            }
            
            // 验证数据一致性
            QVariantMap loadedData = loadResult.data;
            if (loadedData["app_name"].toString() != "ConfigTest") {
                qDebug() << "数据验证失败: app_name不匹配";
                return false;
            }
            
            if (loadedData["server"].toMap()["port"].toInt() != 8080) {
                qDebug() << "数据验证失败: server.port不匹配";
                return false;
            }
            
            // 测试JSON路径操作
            QVariant serverHost = JsonConfigFileHandler::getJsonPathValue(loadedData, "server.host");
            if (serverHost.toString() != "localhost") {
                qDebug() << "JSON路径获取失败";
                return false;
            }
            
            qDebug() << "配置文件处理模块基本功能正常";
            return true;
            
        } catch (const std::exception& e) {
            qDebug() << "配置文件处理模块测试异常:" << e.what();
            return false;
        }
    }
    
    static bool testConfigValidator() {
        qDebug() << "\n--- 测试配置验证模块 ---";
        
        try {
            // 创建配置验证器
            auto factory = std::make_unique<ConfigValidatorFactory>();
            auto validator = factory->createConfigValidator();
            
            if (!validator) {
                qDebug() << "无法创建配置验证器";
                return false;
            }
            
            // 创建验证规则
            ValidationRule portRule;
            portRule.id = "port_range_check";
            portRule.name = "Port Range Validation";
            portRule.type = ValidationRuleType::RANGE_CHECK;
            portRule.fieldPath = "server.port";
            portRule.minValue = 1;
            portRule.maxValue = 65535;
            portRule.severity = ValidationSeverity::ERROR;
            
            auto addResult = validator->addRule(portRule);
            if (!addResult.success) {
                qDebug() << "验证规则添加失败:" << addResult.message;
                return false;
            }
            
            // 创建应用类型验证规则
            ValidationRule appNameRule;
            appNameRule.id = "app_name_required";
            appNameRule.name = "App Name Required";
            appNameRule.type = ValidationRuleType::REQUIRED_CHECK;
            appNameRule.fieldPath = "app_name";
            appNameRule.severity = ValidationSeverity::ERROR;
            
            validator->addRule(appNameRule);
            
            // 测试有效配置
            QVariantMap validConfig;
            validConfig["app_name"] = "TestApp";
            validConfig["server"] = QVariantMap{{"port", 8080}};
            
            auto validResult = validator->validate(validConfig);
            if (!validResult.success) {
                qDebug() << "有效配置验证失败:" << validResult.message;
                return false;
            }
            
            ValidationReport validReport = validResult.data;
            if (!validReport.isValid) {
                qDebug() << "有效配置应该通过验证，但失败了";
                qDebug() << "错误数量:" << validReport.errorCount;
                return false;
            }
            
            // 测试无效配置
            QVariantMap invalidConfig;
            invalidConfig["server"] = QVariantMap{{"port", 70000}}; // 端口超出范围
            // 缺少app_name字段
            
            auto invalidResult = validator->validate(invalidConfig);
            if (!invalidResult.success) {
                qDebug() << "无效配置验证出错:" << invalidResult.message;
                return false;
            }
            
            ValidationReport invalidReport = invalidResult.data;
            if (invalidReport.isValid) {
                qDebug() << "无效配置应该验证失败，但通过了";
                return false;
            }
            
            if (invalidReport.errorCount == 0) {
                qDebug() << "无效配置应该有错误，但错误数量为0";
                return false;
            }
            
            qDebug() << "配置验证模块基本功能正常";
            return true;
            
        } catch (const std::exception& e) {
            qDebug() << "配置验证模块测试异常:" << e.what();
            return false;
        }
    }
    
    static bool testModuleIntegration() {
        qDebug() << "\n--- 测试模块集成 ---";
        
        try {
            // 创建所有三个模块
            auto parameterFactory = std::make_unique<ParameterManagerFactory>();
            auto parameterManager = parameterFactory->createParameterManager();
            
            auto validatorFactory = std::make_unique<ConfigValidatorFactory>();
            auto validator = validatorFactory->createConfigValidator();
            
            if (!parameterManager || !validator) {
                qDebug() << "无法创建必要的管理器";
                return false;
            }
            
            // 1. 使用参数管理器定义应用配置结构
            ParameterDefinition serverPortDef;
            serverPortDef.key = "server.port";
            serverPortDef.name = "Server Port";
            serverPortDef.type = ParameterType::INT;
            serverPortDef.defaultValue = 8080;
            serverPortDef.validation.minValue = 1;
            serverPortDef.validation.maxValue = 65535;
            
            parameterManager->defineParameter(serverPortDef);
            
            ParameterDefinition appNameDef;
            appNameDef.key = "app.name";
            appNameDef.name = "Application Name";
            appNameDef.type = ParameterType::STRING;
            appNameDef.defaultValue = "DefaultApp";
            appNameDef.validation.required = true;
            
            parameterManager->defineParameter(appNameDef);
            
            // 2. 创建对应的验证规则
            ValidationRule portValidationRule;
            portValidationRule.id = "server_port_validation";
            portValidationRule.name = "Server Port Validation";
            portValidationRule.type = ValidationRuleType::RANGE_CHECK;
            portValidationRule.fieldPath = "server.port";
            portValidationRule.minValue = 1;
            portValidationRule.maxValue = 65535;
            portValidationRule.severity = ValidationSeverity::ERROR;
            
            validator->addRule(portValidationRule);
            
            ValidationRule nameValidationRule;
            nameValidationRule.id = "app_name_validation";
            nameValidationRule.name = "App Name Validation";
            nameValidationRule.type = ValidationRuleType::REQUIRED_CHECK;
            nameValidationRule.fieldPath = "app.name";
            nameValidationRule.severity = ValidationSeverity::ERROR;
            
            validator->addRule(nameValidationRule);
            
            // 3. 创建配置数据
            QVariantMap appConfig;
            appConfig["app"] = QVariantMap{{"name", "IntegratedTestApp"}};
            appConfig["server"] = QVariantMap{{"port", 9090}};
            appConfig["database"] = QVariantMap{
                {"host", "localhost"},
                {"port", 3306},
                {"name", "testdb"}
            };
            
            // 4. 使用文件处理模块保存配置
            QString configFile = "integrated_test_config.json";
            ConfigFileOptions fileOptions;
            fileOptions.prettyPrint = true;
            fileOptions.createIfNotExists = true;
            
            auto saveResult = JsonConfigFileHandler::saveJsonFile(configFile, appConfig, fileOptions);
            if (!saveResult.success) {
                qDebug() << "集成测试：配置文件保存失败:" << saveResult.message;
                return false;
            }
            
            // 5. 重新加载配置文件
            auto loadResult = JsonConfigFileHandler::loadJsonFile(configFile, fileOptions);
            if (!loadResult.success) {
                qDebug() << "集成测试：配置文件加载失败:" << loadResult.message;
                return false;
            }
            
            QVariantMap loadedConfig = loadResult.data;
            
            // 6. 使用验证器验证加载的配置
            auto validationResult = validator->validate(loadedConfig);
            if (!validationResult.success) {
                qDebug() << "集成测试：配置验证失败:" << validationResult.message;
                return false;
            }
            
            ValidationReport report = validationResult.data;
            if (!report.isValid) {
                qDebug() << "集成测试：配置应该有效但验证失败";
                qDebug() << "错误数量:" << report.errorCount;
                
                // 输出详细错误信息
                for (const auto& result : report.results) {
                    if (result.status == ValidationStatus::FAILED) {
                        qDebug() << "验证失败:" << result.ruleName << "-" << result.message;
                    }
                }
                return false;
            }
            
            // 7. 使用参数管理器更新参数值
            parameterManager->setParameter("server.port", 8888, "integration_test", "集成测试更新");
            
            // 8. 获取更新后的参数值
            QVariant updatedPort = parameterManager->getParameter("server.port", 8080);
            if (updatedPort.toInt() != 8888) {
                qDebug() << "集成测试：参数更新失败，期望8888，实际" << updatedPort.toInt();
                return false;
            }
            
            qDebug() << "模块集成测试完成，所有功能正常协作";
            return true;
            
        } catch (const std::exception& e) {
            qDebug() << "模块集成测试异常:" << e.what();
            return false;
        }
    }
};

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    qDebug() << "配置模块重构测试程序";
    qDebug() << "测试新架构下的三个配置管理模块：";
    qDebug() << "1. 参数管理模块 (Parameter Management)";
    qDebug() << "2. 配置文件处理模块 (File Handling)";
    qDebug() << "3. 配置验证模块 (Validation)";
    
    bool testResult = ConfigModuleIntegrationTest::runAllTests();
    
    if (testResult) {
        qDebug() << "\n🎉 所有测试通过！配置模块重构成功！";
        qDebug() << "\n新架构优势：";
        qDebug() << "✅ 模块化设计，职责分离清晰";
        qDebug() << "✅ 支持多种配置文件格式";
        qDebug() << "✅ 强大的验证功能";
        qDebug() << "✅ 良好的扩展性和可维护性";
        qDebug() << "✅ 符合SOLID设计原则";
        return 0;
    } else {
        qDebug() << "\n❌ 部分测试失败，需要进一步修复";
        return 1;
    }
}

// #include "test_integration.moc"  // 注释掉，因为该文件没有Q_OBJECT宏