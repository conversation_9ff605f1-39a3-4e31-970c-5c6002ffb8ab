#ifndef IMAGEPROCESSING_LEGACYINTERPOLATIONADAPTER_H
#define IMAGEPROCESSING_LEGACYINTERPOLATIONADAPTER_H

#include "../factories/FilterFactory.h"
#include "../factories/InterpolationFactory.h"
#include "../interfaces/IImageFilter.h"
#include "../interfaces/IInterpolation.h"
#include <QDebug>
#include <QVector>
#include <memory>
#include <vector>

namespace ImageProcessing {

/**
 * @brief 传统插值适配器类
 *
 * 保持与原有my_interPolation类的接口兼容性
 * 内部使用新的模块化实现，遵循适配器模式
 */
class LegacyInterpolationAdapter {
  public:
    /**
     * @brief 构造函数
     */
    explicit LegacyInterpolationAdapter();

    /**
     * @brief 析构函数
     */
    ~LegacyInterpolationAdapter();

    // 原有接口兼容方法

    /**
     * @brief 双线性插值（兼容原接口）
     * @param src_array 源图像数据
     * @param dst_array 目标图像数据
     */
    void bilinear_interpolation(const QVector<QVector<uint32_t>> &src_array, QVector<QVector<uint32_t>> &dst_array);

    /**
     * @brief 双三次插值（兼容原接口）
     * @param array 图像数据指针
     * @param dst_row_num 目标行数
     * @param dst_column_num 目标列数
     */
    void biCubic_interpolation(QVector<QVector<uint32_t>> *array, uint8_t dst_row_num, uint8_t dst_column_num);

    /**
     * @brief 非线性插值（兼容原接口）
     * @param array 图像数据指针
     * @param dst_row_num 目标行数
     * @param dst_column_num 目标列数
     */
    void nonlinear_interpolation(QVector<QVector<uint32_t>> *array, uint8_t dst_row_num, uint8_t dst_column_num);

    /**
     * @brief 中值滤波（兼容原接口）
     */
    void median_filter();

    /**
     * @brief 高斯滤波（兼容原接口）
     */
    void gaussian_filter();

    /**
     * @brief 卡尔曼滤波（兼容原接口）
     */
    void kalman_filter();

    /**
     * @brief 双边滤波（兼容原接口）
     */
    void bilateral_filter();

    /**
     * @brief 引导滤波（兼容原接口）
     */
    void guide_filter();

    /**
     * @brief 卷积滤波（兼容原接口）
     */
    void Convolution_filter();

    // 新增的配置方法

    /**
     * @brief 设置插值参数
     * @param params 插值参数
     */
    void setInterpolationParameters(const InterpolationParams &params);

    /**
     * @brief 设置滤波参数
     * @param type 滤波器类型
     * @param params 滤波参数
     */
    void setFilterParameters(FilterType type, const FilterParams &params);

    /**
     * @brief 启用/禁用调试输出
     * @param enabled 是否启用
     */
    void setDebugEnabled(bool enabled);

    /**
     * @brief 获取最后一次操作的处理时间
     * @return 处理时间（毫秒）
     */
    uint32_t getLastProcessingTime() const;

    /**
     * @brief 获取适配器版本
     * @return 版本字符串
     */
    QString getVersion() const;

  private:
    std::unique_ptr<IInterpolation>            interpolator_;  ///< 插值算法实例
    std::vector<std::unique_ptr<IImageFilter>> filters_;       ///< 滤波器实例列表

    ImageDataU32     currentImage_;        ///< 当前处理的图像数据
    bool             debugEnabled_;        ///< 是否启用调试输出
    mutable uint32_t lastProcessingTime_;  ///< 最后一次处理时间

    /**
     * @brief 转换QVector到ImageData
     * @param qvector Qt向量数据
     * @return ImageData对象
     */
    ImageDataU32 convertFromQVector(const QVector<QVector<uint32_t>> &qvector);

    /**
     * @brief 转换ImageData到QVector
     * @param imageData 图像数据
     * @return Qt向量数据
     */
    QVector<QVector<uint32_t>> convertToQVector(const ImageDataU32 &imageData);

    /**
     * @brief 确保滤波器已创建
     * @param type 滤波器类型
     */
    void ensureFilterCreated(FilterType type);

    /**
     * @brief 应用滤波器到当前图像
     * @param type 滤波器类型
     * @return true if successful, false otherwise
     */
    bool applyFilterToCurrentImage(FilterType type);

    /**
     * @brief 记录调试信息
     * @param message 调试信息
     */
    void logDebug(const QString &message) const;

    /**
     * @brief 记录性能信息
     * @param operation 操作名称
     * @param startTime 开始时间
     */
    void logPerformance(const QString &operation, qint64 startTime) const;

    /**
     * @brief 添加滤波器
     * @param type 滤波器类型
     */
    void addFilter(FilterType type);

    /**
     * @brief 应用所有滤波器到当前图像
     * @return true if successful, false otherwise
     */
    bool applyAllFilters();
};

}  // namespace ImageProcessing

// 为了保持完全的向后兼容，提供全局类型别名
using my_interPolation = ImageProcessing::LegacyInterpolationAdapter;

#endif  // IMAGEPROCESSING_LEGACYINTERPOLATIONADAPTER_H
