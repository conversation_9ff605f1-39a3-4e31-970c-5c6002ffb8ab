#pragma once

/**
 * @file IDeviceRegistry.h
 * @brief 纯粹的设备注册表接口定义 - Linus式最小接口设计
 * 
 * 遵循Linus Torvalds的设计哲学：
 * - "Good programmers worry about data structures" - 基于CommonTypes.h的数据结构
 * - 最小接口原则 - 只负责设备定义管理，不涉及通信、协议处理
 * - 单一职责 - 纯粹的设备信息注册表（单一来源）
 * - Layer 3接口 - 依赖Layer 1-2的稳定基础，为Layer 4提供设备抽象
 */

#include <QObject>
#include <QString>
#include <QStringList>
#include "support/foundation/core/CommonTypes.h"

namespace LA {
namespace DeviceManagement {

// 使用Foundation层的标准类型 (暂时注释掉，避免DeviceInfo名称冲突)
// using namespace LA::Foundation::Core;

/**
 * @brief 纯粹的设备注册表接口
 * 
 * Linus式设计原则:
 * - 最小接口: 只提供必需的设备信息管理功能
 * - 单一来源: 设备定义的唯一权威来源
 * - 无业务逻辑: 不涉及通信、协议、连接管理
 * - 可测试性: 每个方法都可以独立测试
 * - 可组合性: 为上层设备管理提供基础数据
 */
class IDeviceRegistry : public QObject {
    Q_OBJECT

public:
    explicit IDeviceRegistry(QObject *parent = nullptr) : QObject(parent) {}
    virtual ~IDeviceRegistry() = default;

    // === 设备注册管理 ===
    
    /**
     * @brief 注册设备
     * @param device 设备信息
     * @return 操作结果
     */
    virtual LA::Foundation::Core::SimpleResult registerDevice(const LA::Foundation::Core::DeviceInfo& device) = 0;
    
    /**
     * @brief 注销设备
     * @param deviceId 设备ID
     * @return 操作结果
     */
    virtual LA::Foundation::Core::SimpleResult unregisterDevice(const QString& deviceId) = 0;
    
    /**
     * @brief 检查设备是否已注册
     * @param deviceId 设备ID
     * @return 是否已注册
     */
    virtual bool isDeviceRegistered(const QString& deviceId) const = 0;

    // === 设备信息查询 ===
    
    /**
     * @brief 获取设备信息
     * @param deviceId 设备ID
     * @return 设备详细信息
     */
    virtual LA::Foundation::Core::DeviceInfo getDeviceInfo(const QString& deviceId) const = 0;
    
    /**
     * @brief 获取所有已注册设备
     * @return 设备信息列表
     */
    virtual LA::Foundation::Core::DeviceInfoList getAllDevices() const = 0;
    
    /**
     * @brief 获取指定类型的设备
     * @param type 设备类型
     * @return 该类型的设备列表
     */
    virtual LA::Foundation::Core::DeviceInfoList getDevicesByType(LA::Foundation::Core::DeviceType type) const = 0;
    
    /**
     * @brief 获取所有设备ID
     * @return 设备ID列表
     */
    virtual QStringList getAllDeviceIds() const = 0;

    // === 设备命令管理 ===
    
    /**
     * @brief 获取设备支持的命令列表
     * @param deviceId 设备ID
     * @return 命令ID列表
     */
    virtual QStringList getSupportedCommands(const QString& deviceId) const = 0;
    
    /**
     * @brief 检查设备是否支持指定命令
     * @param deviceId 设备ID
     * @param commandId 命令ID
     * @return 是否支持
     */
    virtual bool supportsCommand(const QString& deviceId, const QString& commandId) const = 0;
    
    /**
     * @brief 获取命令定义
     * @param deviceId 设备ID
     * @param commandId 命令ID
     * @return 命令配置参数
     */
    virtual ConfigParameters getCommandDefinition(const QString& deviceId, const QString& commandId) const = 0;

    // === 设备配置管理 ===
    
    /**
     * @brief 更新设备配置
     * @param deviceId 设备ID
     * @param parameters 配置参数
     * @return 操作结果
     */
    virtual LA::Foundation::Core::SimpleResult updateDeviceConfig(const QString& deviceId, const LA::Foundation::Core::ConfigParameters& parameters) = 0;
    
    /**
     * @brief 获取设备配置
     * @param deviceId 设备ID
     * @return 配置参数
     */
    virtual LA::Foundation::Core::ConfigParameters getDeviceConfig(const QString& deviceId) const = 0;

    // === 状态查询 ===
    
    /**
     * @brief 获取注册表统计信息
     * @return 统计信息
     */
    virtual LA::Foundation::Core::DeviceStatistics getStatistics() const = 0;
    
    /**
     * @brief 获取设备数量
     * @return 设备总数
     */
    virtual int getDeviceCount() const = 0;
    
    /**
     * @brief 获取最后错误信息
     * @return 错误描述
     */
    virtual QString errorString() const = 0;
    
    /**
     * @brief 清空所有设备注册
     * @return 操作结果
     */
    virtual LA::Foundation::Core::SimpleResult clear() = 0;

signals:
    /**
     * @brief 设备注册信号
     * @param device 新注册的设备信息
     */
    void deviceRegistered(const LA::Foundation::Core::DeviceInfo& device);
    
    /**
     * @brief 设备注销信号
     * @param deviceId 注销的设备ID
     */
    void deviceUnregistered(const QString& deviceId);
    
    /**
     * @brief 设备配置更新信号
     * @param deviceId 设备ID
     * @param parameters 新配置
     */
    void deviceConfigUpdated(const QString& deviceId, const ConfigParameters& parameters);
    
    /**
     * @brief 注册表错误信号
     * @param error 错误描述
     */
    void errorOccurred(const QString& error);
};

/**
 * @brief 设备注册表工厂接口
 * 
 * 用于创建不同类型的设备注册表
 */
class IDeviceRegistryFactory {
public:
    virtual ~IDeviceRegistryFactory() = default;
    
    /**
     * @brief 创建设备注册表实例
     * @return 设备注册表实例的智能指针
     */
    virtual std::shared_ptr<IDeviceRegistry> createDeviceRegistry() = 0;
    
    /**
     * @brief 检查是否支持设备注册表
     * @return 是否支持
     */
    virtual bool isSupported() const = 0;
};

} // namespace DeviceManagement
} // namespace LA