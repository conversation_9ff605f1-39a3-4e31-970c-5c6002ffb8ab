#include "ComCheckPluginSimple.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QGroupBox>
#include <QDateTime>
#include <QMessageBox>
#include <QApplication>
#include <QDebug>

namespace LA {
namespace Plugins {
namespace ComCheck {

ComCheckPluginSimple::ComCheckPluginSimple(QObject* parent)
    : BaseFunctionPlugin(parent)
    , m_serialPort(new QSerialPort(this))
    , m_isConnected(false)
{
    connect(m_serialPort, &QSerialPort::readyRead, this, &ComCheckPluginSimple::onReceiveData);
    connect(m_serialPort, QOverload<QSerialPort::SerialPortError>::of(&QSerialPort::error),
            [this](QSerialPort::SerialPortError error) {
                if (error != QSerialPort::NoError) {
                    appendLog(QString("Serial port error: %1").arg(m_serialPort->errorString()), "[ERROR]");
                }
            });
}

bool ComCheckPluginSimple::initialize() {
    qDebug() << "ComCheck Plugin (Simple Demo) initializing...";
    setPluginInfo("com.la.plugins.comcheck.demo", "ComCheck (演示版)", "1.0.0-demo", 
                  "通信检查插件 - Linus式重构演示", "LA Development Team");
    setCategory("Communication");
    setWindowSizes(QSize(400, 300), QSize(600, 500));
    setSupportsFloating(true);
    setSupportedThemes({"Default", "Dark"});
    return true;
}

void ComCheckPluginSimple::shutdown() {
    if (m_serialPort && m_serialPort->isOpen()) {
        m_serialPort->close();
    }
    qDebug() << "ComCheck Plugin (Simple Demo) shutdown";
}

QWidget* ComCheckPluginSimple::doCreateWidget(QWidget* parent) {
    m_mainWidget = new QWidget(parent);
    setupUI();
    updateDeviceList();
    updatePortList();
    return m_mainWidget;
}

QWidget* ComCheckPluginSimple::createSidebarPanel(QWidget* parent) {
    // 创建侧边栏快捷面板
    QWidget* panel = new QWidget(parent);
    QVBoxLayout* layout = new QVBoxLayout(panel);
    
    QLabel* title = new QLabel("通信检查 (演示)", panel);
    title->setStyleSheet("font-weight: bold; font-size: 14px;");
    
    QPushButton* openButton = new QPushButton("打开通信检查", panel);
    connect(openButton, &QPushButton::clicked, [this]() {
        if (m_mainWidget) {
            m_mainWidget->show();
            m_mainWidget->raise();
        }
    });
    
    layout->addWidget(title);
    layout->addWidget(openButton);
    layout->addStretch();
    
    return panel;
}

void ComCheckPluginSimple::setupUI() {
    if (!m_mainWidget) return;
    
    QVBoxLayout* mainLayout = new QVBoxLayout(m_mainWidget);
    
    // 标题
    QLabel* titleLabel = new QLabel("通信检查工具 (Linus式重构演示)", m_mainWidget);
    titleLabel->setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px; color: #2c5aa0;");
    titleLabel->setAlignment(Qt::AlignCenter);
    mainLayout->addWidget(titleLabel);
    
    // 架构信息按钮
    m_architectureInfoButton = new QPushButton("🏗️ 查看重构架构", m_mainWidget);
    m_architectureInfoButton->setStyleSheet("QPushButton { background-color: #e8f4fd; border: 1px solid #2c5aa0; padding: 5px; }");
    connect(m_architectureInfoButton, &QPushButton::clicked, this, &ComCheckPluginSimple::showArchitectureInfo);
    mainLayout->addWidget(m_architectureInfoButton);
    
    // 连接设置组
    QGroupBox* connectionGroup = new QGroupBox("连接设置", m_mainWidget);
    QGridLayout* connectionLayout = new QGridLayout(connectionGroup);
    
    // 设备选择 (演示统一设备注册表)
    connectionLayout->addWidget(new QLabel("设备类型:"), 0, 0);
    m_deviceComboBox = new QComboBox();
    m_deviceComboBox->setMinimumWidth(120);
    m_deviceComboBox->setToolTip("演示：统一设备注册表 (UnifiedDeviceRegistry)");
    connect(m_deviceComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &ComCheckPluginSimple::onDeviceChanged);
    connectionLayout->addWidget(m_deviceComboBox, 0, 1);
    
    // 端口选择
    connectionLayout->addWidget(new QLabel("端口:"), 1, 0);
    m_portComboBox = new QComboBox();
    m_portComboBox->setMinimumWidth(100);
    connect(m_portComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &ComCheckPluginSimple::onPortChanged);
    connectionLayout->addWidget(m_portComboBox, 1, 1);
    
    // 刷新按钮
    m_refreshButton = new QPushButton("刷新");
    connect(m_refreshButton, &QPushButton::clicked, this, &ComCheckPluginSimple::onRefreshPorts);
    connectionLayout->addWidget(m_refreshButton, 1, 2);
    
    // 波特率
    connectionLayout->addWidget(new QLabel("波特率:"), 2, 0);
    m_baudrateSpinBox = new QSpinBox();
    m_baudrateSpinBox->setRange(9600, 921600);
    m_baudrateSpinBox->setValue(115200);
    m_baudrateSpinBox->setSuffix(" bps");
    connectionLayout->addWidget(m_baudrateSpinBox, 2, 1);
    
    // 连接按钮
    QHBoxLayout* buttonLayout = new QHBoxLayout();
    m_connectButton = new QPushButton("连接");
    m_disconnectButton = new QPushButton("断开");
    m_disconnectButton->setEnabled(false);
    
    connect(m_connectButton, &QPushButton::clicked, this, &ComCheckPluginSimple::onConnectPort);
    connect(m_disconnectButton, &QPushButton::clicked, this, &ComCheckPluginSimple::onDisconnectPort);
    
    buttonLayout->addWidget(m_connectButton);
    buttonLayout->addWidget(m_disconnectButton);
    buttonLayout->addStretch();
    connectionLayout->addLayout(buttonLayout, 3, 0, 1, 3);
    
    mainLayout->addWidget(connectionGroup);
    
    // 数据发送组
    QGroupBox* sendGroup = new QGroupBox("数据发送", m_mainWidget);
    QVBoxLayout* sendLayout = new QVBoxLayout(sendGroup);
    
    QHBoxLayout* sendInputLayout = new QHBoxLayout();
    m_sendLineEdit = new QLineEdit();
    m_sendLineEdit->setPlaceholderText("输入要发送的数据...");
    m_sendButton = new QPushButton("发送");
    m_sendButton->setEnabled(false);
    
    connect(m_sendLineEdit, &QLineEdit::returnPressed, this, &ComCheckPluginSimple::onSendData);
    connect(m_sendButton, &QPushButton::clicked, this, &ComCheckPluginSimple::onSendData);
    
    sendInputLayout->addWidget(m_sendLineEdit);
    sendInputLayout->addWidget(m_sendButton);
    
    m_hexModeCheckBox = new QCheckBox("十六进制模式");
    m_deviceCmdModeCheckBox = new QCheckBox("设备命令模式 (演示)");
    m_deviceCmdModeCheckBox->setToolTip("演示：统一指令系统 (UnifiedCommandSystem)\\n支持SPRM等设备的标准指令");
    m_deviceCmdModeCheckBox->setStyleSheet("QCheckBox { color: #2c5aa0; }");
    
    sendLayout->addLayout(sendInputLayout);
    QHBoxLayout* modeLayout = new QHBoxLayout();
    modeLayout->addWidget(m_hexModeCheckBox);
    modeLayout->addWidget(m_deviceCmdModeCheckBox);
    modeLayout->addStretch();
    sendLayout->addLayout(modeLayout);
    
    mainLayout->addWidget(sendGroup);
    
    // 日志显示
    QGroupBox* logGroup = new QGroupBox("通信日志", m_mainWidget);
    QVBoxLayout* logLayout = new QVBoxLayout(logGroup);
    
    m_logTextEdit = new QTextEdit();
    m_logTextEdit->setReadOnly(true);
    logLayout->addWidget(m_logTextEdit);
    
    mainLayout->addWidget(logGroup);
    
    // 设置窗口属性
    m_mainWidget->setWindowTitle("LA - 通信检查工具 (Linus式重构演示)");
    m_mainWidget->resize(700, 550);
    
    // 显示初始架构信息
    demonstrateUnifiedArchitecture();
}

void ComCheckPluginSimple::demonstrateUnifiedArchitecture() {
    appendLog("=== Linus式重构架构演示 ===", "[ARCH]");
    appendLog("✅ 原有功能保持不变", "[ARCH]");
    appendLog("🔄 底层通信：串口 → 统一通信系统", "[ARCH]");
    appendLog("📋 设备管理：硬编码 → 统一设备注册表", "[ARCH]");
    appendLog("⚙️ 指令系统：原始字节 → 标准设备指令", "[ARCH]");
    appendLog("点击 '查看重构架构' 了解详情", "[ARCH]");
}

void ComCheckPluginSimple::showArchitectureInfo() {
    QString info = 
R"(🏗️ ComCheck插件 Linus式重构架构

【重构原则】
✓ "Real code over pretty interfaces" - 保持原有功能不变
✓ "Good taste" - 消除硬编码，使用配置驱动
✓ "Never break userspace" - 用户体验无损

【架构对比】
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
原架构:    Plugin → QSerialPort → Physical Port
新架构:    Plugin → CommunicationSystem → 4层通信架构
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

【核心改进】
1. 统一通信系统 (CommunicationSystem)
   - 替代直接QSerialPort操作
   - 支持多协议 (串口/TCP/UDP)
   - 统一错误处理和状态管理

2. 统一设备注册表 (UnifiedDeviceRegistry) 
   - 所有设备配置的单一来源
   - 支持SPRM、Motor等多种设备
   - 配置驱动，避免硬编码

3. 统一指令系统 (UnifiedCommandSystem)
   - 标准设备指令生成和解析
   - 自动参数验证和格式化
   - 类型安全的命令处理

【展示价值】
✨ 插件-设备交互的标准模式
✨ 其他插件重构的模板
✨ 现代化架构的实用性验证)";

    QMessageBox::information(m_mainWidget, "重构架构详情", info);
}

void ComCheckPluginSimple::onConnectPort() {
    if (m_portComboBox->currentText().isEmpty()) {
        QMessageBox::warning(m_mainWidget, "警告", "请选择一个串口");
        return;
    }
    
    QString portName = m_portComboBox->currentText();
    int spaceIndex = portName.indexOf(' ');
    if (spaceIndex > 0) {
        portName = portName.left(spaceIndex);
    }
    
    m_serialPort->setPortName(portName);
    m_serialPort->setBaudRate(m_baudrateSpinBox->value());
    m_serialPort->setDataBits(QSerialPort::Data8);
    m_serialPort->setParity(QSerialPort::NoParity);
    m_serialPort->setStopBits(QSerialPort::OneStop);
    m_serialPort->setFlowControl(QSerialPort::NoFlowControl);
    
    if (m_serialPort->open(QIODevice::ReadWrite)) {
        m_isConnected = true;
        updateConnectionUI();
        
        QString deviceType = m_deviceComboBox->currentData().toString();
        appendLog(QString("成功连接到 %1 (%2), 端口 %3 (%4 bps)")
                  .arg(deviceType, m_deviceComboBox->currentText(), portName)
                  .arg(m_baudrateSpinBox->value()), "[CONNECT]");
        
        // 演示架构集成
        if (m_deviceCmdModeCheckBox->isChecked()) {
            appendLog("✨ 演示：可发送设备标准指令 (如 START, CALIB1, VERSION)", "[DEMO]");
        }
    } else {
        appendLog(QString("连接失败: %1").arg(m_serialPort->errorString()), "[ERROR]");
        QMessageBox::critical(m_mainWidget, "错误", 
                              QString("无法连接到串口 %1\\n错误: %2")
                              .arg(portName, m_serialPort->errorString()));
    }
}

void ComCheckPluginSimple::onDisconnectPort() {
    if (m_serialPort->isOpen()) {
        m_serialPort->close();
    }
    
    m_isConnected = false;
    updateConnectionUI();
    appendLog("串口已断开", "[DISCONNECT]");
}

void ComCheckPluginSimple::updateConnectionUI() {
    m_connectButton->setEnabled(!m_isConnected);
    m_disconnectButton->setEnabled(m_isConnected);
    m_sendButton->setEnabled(m_isConnected);
    m_deviceComboBox->setEnabled(!m_isConnected);
    m_portComboBox->setEnabled(!m_isConnected);
    m_baudrateSpinBox->setEnabled(!m_isConnected);
}

void ComCheckPluginSimple::onSendData() {
    if (!m_isConnected || !m_serialPort->isOpen()) {
        return;
    }
    
    QString text = m_sendLineEdit->text();
    if (text.isEmpty()) {
        return;
    }
    
    QByteArray data;
    QString logPrefix = "[SEND]";
    
    if (m_deviceCmdModeCheckBox->isChecked()) {
        // 设备命令模式演示
        QString deviceType = m_deviceComboBox->currentData().toString();
        
        // 模拟统一指令系统行为
        if (deviceType == "sprm") {
            if (text.toUpper() == "START") {
                data = QByteArray::fromHex("48544B4A010000000155");
                appendLog(QString("演示：生成SPRM启动指令 %1 -> %2").arg(text, QString(data.toHex(' '))), "[CMD]");
            } else if (text.toUpper() == "VERSION") {
                data = QByteArray::fromHex("48544B4A0200000001FC");
                appendLog(QString("演示：生成SPRM版本查询指令 %1 -> %2").arg(text, QString(data.toHex(' '))), "[CMD]");
            } else {
                appendLog(QString("演示：未知SPRM指令 %1").arg(text), "[WARN]");
                data = text.toUtf8();
            }
        } else {
            appendLog(QString("演示：设备 %1 指令 %2").arg(deviceType, text), "[CMD]");
            data = text.toUtf8();
        }
        logPrefix = "[CMD_SEND]";
    } else if (m_hexModeCheckBox->isChecked()) {
        // 十六进制模式
        QString hexString = text.remove(' ').remove("0x", Qt::CaseInsensitive);
        data = QByteArray::fromHex(hexString.toLatin1());
        if (data.isEmpty() && !hexString.isEmpty()) {
            appendLog("十六进制格式错误", "[ERROR]");
            return;
        }
    } else {
        // 文本模式
        data = text.toUtf8();
    }
    
    qint64 bytesWritten = m_serialPort->write(data);
    if (bytesWritten == -1) {
        appendLog(QString("发送失败: %1").arg(m_serialPort->errorString()), "[ERROR]");
    } else {
        QString logText = m_hexModeCheckBox->isChecked() ? QString(data.toHex(' ')) : text;
        appendLog(QString("发送 (%1 bytes): %2").arg(bytesWritten).arg(logText), logPrefix);
        m_sendLineEdit->clear();
    }
}

void ComCheckPluginSimple::onReceiveData() {
    if (!m_serialPort || !m_serialPort->isOpen()) {
        return;
    }
    
    QByteArray data = m_serialPort->readAll();
    if (data.isEmpty()) {
        return;
    }
    
    QString text;
    QString parseInfo;
    
    if (m_deviceCmdModeCheckBox->isChecked()) {
        // 设备命令模式演示 - 模拟设备响应解析
        QString deviceType = m_deviceComboBox->currentData().toString();
        
        if (deviceType == "sprm") {
            // 模拟SPRM响应解析
            if (data.startsWith(QByteArray::fromHex("48544B4A"))) {
                text = QString("SPRM响应解析: %1").arg(QString(data.toHex(' ')));
                parseInfo = " [演示：协议解析成功]";
            } else {
                text = QString("原始数据: %1").arg(QString(data.toHex(' ')));
                parseInfo = " [演示：非标准SPRM响应]";
            }
        } else {
            text = m_hexModeCheckBox->isChecked() ? QString(data.toHex(' ')) : QString::fromUtf8(data);
            parseInfo = QString(" [演示：%1设备响应]").arg(deviceType);
        }
    } else if (m_hexModeCheckBox->isChecked()) {
        text = QString(data.toHex(' '));
    } else {
        text = QString::fromUtf8(data);
    }
    
    appendLog(QString("接收 (%1 bytes): %2%3").arg(data.size()).arg(text, parseInfo), "[RECV]");
}

void ComCheckPluginSimple::onPortChanged() {
    // 端口变化时的处理
}

void ComCheckPluginSimple::onRefreshPorts() {
    updatePortList();
    updateDeviceList();
    appendLog("设备和端口列表已刷新", "[INFO]");
}

void ComCheckPluginSimple::onDeviceChanged() {
    QString deviceType = m_deviceComboBox->currentData().toString();
    m_currentDeviceType = deviceType;
    
    // 演示：根据设备类型调整界面
    if (deviceType == "sprm") {
        m_deviceCmdModeCheckBox->setText("SPRM指令模式 (START, VERSION, CALIB1等)");
        m_deviceCmdModeCheckBox->setChecked(true);
        appendLog("演示：切换到SPRM设备，支持标准指令", "[DEVICE]");
    } else if (deviceType == "motor") {
        m_deviceCmdModeCheckBox->setText("Motor指令模式 (MOVE, STOP, HOME等)");
        m_deviceCmdModeCheckBox->setChecked(false);
        appendLog("演示：切换到Motor设备", "[DEVICE]");
    } else {
        m_deviceCmdModeCheckBox->setText("设备命令模式 (演示)");
        m_deviceCmdModeCheckBox->setChecked(false);
        appendLog("演示：切换到通用模式", "[DEVICE]");
    }
}

void ComCheckPluginSimple::updateDeviceList() {
    if (!m_deviceComboBox) return;
    
    QString currentDevice = m_deviceComboBox->currentData().toString();
    m_deviceComboBox->clear();
    
    // 演示：模拟统一设备注册表内容
    m_deviceComboBox->addItem("手动模式 (原始数据)", "manual");
    m_deviceComboBox->addItem("SPRM传感器 (Nova-A1)", "sprm");
    m_deviceComboBox->addItem("Motor控制器 (示例)", "motor");
    m_deviceComboBox->addItem("LensAdjuster (示例)", "lens");
    
    // 尝试恢复之前选择的设备
    if (!currentDevice.isEmpty()) {
        int index = m_deviceComboBox->findData(currentDevice);
        if (index >= 0) {
            m_deviceComboBox->setCurrentIndex(index);
        }
    }
    
    if (m_deviceComboBox->count() > 0 && m_deviceComboBox->currentIndex() < 0) {
        m_deviceComboBox->setCurrentIndex(0);
    }
}

void ComCheckPluginSimple::updatePortList() {
    if (!m_portComboBox) return;
    
    QString currentPort = m_portComboBox->currentText();
    m_portComboBox->clear();
    
    QStringList portNames;
    for (const QSerialPortInfo& info : QSerialPortInfo::availablePorts()) {
        QString portName = info.portName();
        QString description = info.description();
        QString displayText = QString("%1 (%2)").arg(portName, description);
        
        m_portComboBox->addItem(displayText, portName);
        portNames << portName;
    }
    
    // 尝试恢复之前选择的端口
    if (!currentPort.isEmpty()) {
        for (int i = 0; i < m_portComboBox->count(); ++i) {
            if (m_portComboBox->itemText(i).startsWith(currentPort.split(' ').first())) {
                m_portComboBox->setCurrentIndex(i);
                break;
            }
        }
    }
    
    if (m_portComboBox->count() == 0) {
        m_portComboBox->addItem("无可用端口");
        if (m_connectButton) {
            m_connectButton->setEnabled(false);
        }
    } else {
        if (m_connectButton) {
            m_connectButton->setEnabled(!m_isConnected);
        }
    }
}

void ComCheckPluginSimple::appendLog(const QString& message, const QString& prefix) {
    if (!m_logTextEdit) return;
    
    QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss.zzz");
    QString logLine = QString("[%1] %2 %3").arg(timestamp, prefix, message);
    
    // 设置颜色
    QString color = "black";
    if (prefix == "[ARCH]") color = "#2c5aa0";
    else if (prefix == "[DEMO]") color = "#e67e22";
    else if (prefix == "[CMD]" || prefix == "[CMD_SEND]") color = "#8e44ad";
    else if (prefix == "[DEVICE]") color = "#27ae60";
    else if (prefix == "[ERROR]") color = "#e74c3c";
    else if (prefix == "[WARN]") color = "#f39c12";
    else if (prefix == "[CONNECT]") color = "#2ecc71";
    
    m_logTextEdit->append(QString("<span style='color: %1'>%2</span>").arg(color, logLine));
    
    // 自动滚动到底部
    QTextCursor cursor = m_logTextEdit->textCursor();
    cursor.movePosition(QTextCursor::End);
    m_logTextEdit->setTextCursor(cursor);
}

} // namespace ComCheck
} // namespace Plugins
} // namespace LA