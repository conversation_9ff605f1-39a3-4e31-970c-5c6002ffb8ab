#pragma once

/**
 * @file CommandSystem.h
 * @brief 指令系统实现
 * 
 * 基于静态映射表和设备注册表的指令系统实现
 */

#include "../../include/LA/Communication/Command/ICommandSystem.h"
#include "../../include/LA/Communication/Registry/IDeviceRegistry.h"
#include <QMutex>
#include <QMap>
#include <QRegularExpression>
#include <QJsonDocument>
#include <QJsonObject>

namespace LA {
namespace Communication {
namespace Command {

/**
 * @brief 指令模板引擎
 * 
 * 负责指令模板的解析和渲染
 */
class CommandTemplateEngine
{
public:
    /**
     * @brief 渲染模板
     * @param templateString 模板字符串（支持 {{parameter}} 语法）
     * @param parameters 参数映射
     * @return 渲染后的字符串
     */
    static QString renderTemplate(const QString& templateString, const QVariantMap& parameters);
    
    /**
     * @brief 解析模板，提取参数占位符
     * @param templateString 模板字符串
     * @return 参数名列表
     */
    static QStringList extractParameters(const QString& templateString);
    
    /**
     * @brief 验证模板语法
     * @param templateString 模板字符串
     * @return 是否有效
     */
    static bool validateTemplate(const QString& templateString);

private:
    static const QRegularExpression s_parameterRegex;
};

/**
 * @brief 指令解析器
 * 
 * 负责解析各种格式的响应数据
 */
class CommandParser
{
public:
    /**
     * @brief 解析JSON格式响应
     */
    static ParsedCommand parseJsonResponse(const QString& deviceId, 
                                         const QString& commandId,
                                         const QByteArray& data);
    
    /**
     * @brief 解析文本格式响应（键值对）
     */
    static ParsedCommand parseTextResponse(const QString& deviceId,
                                         const QString& commandId, 
                                         const QByteArray& data);
    
    /**
     * @brief 解析二进制格式响应
     */
    static ParsedCommand parseBinaryResponse(const QString& deviceId,
                                           const QString& commandId,
                                           const QByteArray& data);
    
    /**
     * @brief 解析自定义格式响应
     */
    static ParsedCommand parseCustomResponse(const QString& deviceId,
                                           const QString& commandId,
                                           const QByteArray& data,
                                           const QString& format);
};

/**
 * @brief 指令系统实现类
 */
class CommandSystem : public ICommandSystem
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param deviceRegistry 设备注册表
     * @param parent 父对象
     */
    explicit CommandSystem(QSharedPointer<Registry::IDeviceRegistry> deviceRegistry,
                          QObject* parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~CommandSystem() override;

    // === ICommandSystem接口实现 ===
    
    // 指令构建接口
    QSharedPointer<BuiltCommand> buildCommand(
        const QString& deviceId,
        const QString& commandId,
        const QVariantMap& parameters = QVariantMap(),
        const CommandContext& context = CommandContext()) override;

    QList<QSharedPointer<BuiltCommand>> buildBatchCommands(
        const QString& deviceId,
        const QList<QPair<QString, QVariantMap>>& commands,
        const CommandContext& context = CommandContext()) override;

    QSharedPointer<BuiltCommand> buildFromTemplate(
        const QString& deviceId,
        const QString& commandId,
        const QVariantMap& templateData) override;

    // 指令解析接口
    ParsedCommand parseResponse(
        const QString& deviceId,
        const QString& commandId,
        const QByteArray& responseData) override;

    ParsedCommand parseRawCommand(
        const QString& deviceId,
        const QByteArray& rawData) override;

    QList<ParsedCommand> autoParseData(const QByteArray& rawData) override;

    // 指令验证接口
    CommandValidation validateCommand(
        const QString& deviceId,
        const QString& commandId,
        const QVariantMap& parameters) override;

    CommandValidation validateBuiltCommand(const BuiltCommand& command) override;

    // 指令查询接口
    QList<CommandDefinition> getAvailableCommands(const QString& deviceId) override;
    CommandDefinition getCommandDefinition(const QString& deviceId, const QString& commandId) override;
    bool supportsCommand(const QString& deviceId, const QString& commandId) override;

    // 模板管理接口
    bool registerCommandTemplate(
        DeviceType deviceType,
        const QString& commandId,
        const QString& templateString) override;

    QString getCommandTemplate(DeviceType deviceType, const QString& commandId) override;

    // 统计和监控接口
    QVariantMap getCommandStatistics(const QString& deviceId = QString()) override;
    QList<BuiltCommand> getRecentCommands(const QString& deviceId, int limit = 10) override;
    void clearCommandHistory(const QString& deviceId = QString(), 
                            const QDateTime& olderThan = QDateTime()) override;

    // === 扩展功能 ===
    
    /**
     * @brief 批量注册指令模板
     * @param templates 模板映射 {DeviceType -> {CommandId -> Template}}
     * @return 成功注册的数量
     */
    int registerBatchTemplates(const QMap<DeviceType, QMap<QString, QString>>& templates);
    
    /**
     * @brief 从文件加载指令模板
     * @param filePath 模板文件路径
     * @return 是否成功
     */
    bool loadTemplatesFromFile(const QString& filePath);
    
    /**
     * @brief 保存指令模板到文件
     * @param filePath 模板文件路径
     * @return 是否成功
     */
    bool saveTemplatesToFile(const QString& filePath) const;
    
    /**
     * @brief 启用指令历史记录
     * @param enabled 是否启用
     * @param maxHistorySize 最大历史记录数
     */
    void setHistoryTracking(bool enabled, int maxHistorySize = 1000);

private:
    // === 内部方法 ===
    
    /**
     * @brief 从设备注册表获取指令定义
     */
    CommandDefinition getCommandDefinitionFromRegistry(const QString& deviceId, const QString& commandId);
    
    /**
     * @brief 获取设备类型
     */
    DeviceType getDeviceType(const QString& deviceId);
    
    /**
     * @brief 构建指令内容
     */
    QString buildCommandContent(const CommandDefinition& definition, 
                               const QVariantMap& parameters);
    
    /**
     * @brief 验证指令参数
     */
    CommandValidation validateParameters(const CommandDefinition& definition,
                                        const QVariantMap& parameters);
    
    /**
     * @brief 应用默认参数
     */
    QVariantMap applyDefaultParameters(const CommandDefinition& definition,
                                      const QVariantMap& parameters);
    
    /**
     * @brief 记录指令历史
     */
    void recordCommandHistory(const BuiltCommand& command);
    
    /**
     * @brief 更新统计信息
     */
    void updateStatistics(const QString& deviceId, const QString& commandId, bool success);
    
    /**
     * @brief 选择合适的解析器
     */
    ParsedCommand selectAndParse(const QString& deviceId, 
                                const QString& commandId,
                                const QByteArray& data);

private:
    // === 数据成员 ===
    
    QSharedPointer<Registry::IDeviceRegistry> m_deviceRegistry;  // 设备注册表
    mutable QMutex m_mutex;                                      // 线程安全锁
    
    // 模板存储
    QMap<DeviceType, QMap<QString, QString>> m_commandTemplates; // 指令模板
    QMap<QString, QString> m_globalTemplates;                    // 全局模板
    
    // 历史记录
    bool m_historyEnabled;                                        // 是否启用历史记录
    int m_maxHistorySize;                                        // 最大历史记录数
    QMap<QString, QList<BuiltCommand>> m_commandHistory;         // 指令历史 {deviceId -> commands}
    
    // 统计信息
    QMap<QString, QVariantMap> m_deviceStats;                   // 设备统计 {deviceId -> stats}
    QVariantMap m_globalStats;                                  // 全局统计
    QDateTime m_statsStartTime;                                 // 统计开始时间
    
    // 配置
    static const int DEFAULT_TIMEOUT = 5000;                    // 默认超时时间
    static const int DEFAULT_MAX_RETRIES = 3;                   // 默认重试次数
    static const int DEFAULT_HISTORY_SIZE = 1000;               // 默认历史记录大小
};

/**
 * @brief 指令系统工厂实现
 */
class CommandSystemFactory : public ICommandSystemFactory
{
public:
    QSharedPointer<ICommandSystem> createCommandSystem(
        QSharedPointer<Registry::IDeviceRegistry> deviceRegistry) override;
};

} // namespace Command
} // namespace Communication
} // namespace LA