#include <QCoreApplication>
#include <QDebug>
#include "src/Devices/sprm/SprmDevice.h"

int main(int argc, char *argv[]) {
    QCoreApplication app(argc, argv);
    
    qDebug() << "SPRM Device Four-Layer Architecture Example";
    
    // 创建SPRM设备
    auto device = LA::Device::Devices::SprmDeviceFactory::createDevice("SPRM-A1");
    if (!device) {
        qCritical() << "Failed to create device";
        return 1;
    }
    
    qDebug() << "Device created:" << device->getDeviceType();
    qDebug() << "Model:" << device->getDeviceModel();
    
    // 获取设备规格
    auto specs = device->getDeviceSpecifications();
    qDebug() << "Device specifications:" << specs;
    
    // 初始化设备（使用默认配置）
    QVariantMap config;
    config["basic_info"] = QVariantMap{{"model", "SPRM-A1"}};
    config["hardware_specs"] = QVariantMap{{"laser_power", 5.0}};
    config["communication"] = QVariantMap{{"protocol", "RS485"}};
    
    if (device->initialize(config)) {
        qDebug() << "Device initialized successfully";
        
        // 获取设备状态
        auto status = device->getDeviceStatus();
        qDebug() << "Device status:" << status;
    } else {
        qWarning() << "Device initialization failed";
    }
    
    qDebug() << "Example completed";
    return 0;
}
