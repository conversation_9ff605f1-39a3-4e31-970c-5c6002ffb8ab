#pragma once

#include "LA/DeviceManagement/Core/DeviceManagementTypes.h"
#include "LA/DeviceManagement/Matching/IPortScanner.h"
#include "../Core/SimpleTypes.h"  // 使用简化类型
#include <QMap>
#include <QObject>
#include <QString>
#include <memory>


namespace LA {
namespace DeviceManagement {
namespace Registration {

// 使用简化类型 - 确保类型一致性
using LA::DeviceManagement::Core::ConnectionStatus;
using LA::DeviceManagement::Core::DeviceInfo;
using LA::DeviceManagement::Core::PortInfo;

/**
 * @brief 设备注册结果
 */
struct RegistrationResult {
    bool    success = false;
    QString deviceId;
    QString portId;
    QString errorMessage;

    bool isValid() const {
        return success && !deviceId.isEmpty() && !portId.isEmpty();
    }
};

/**
 * @brief 设备注册管理器接口 - Linus式单一职责
 *
 * Linus: "注册管理器只做一件事：管理设备-端口的注册关系和生命周期"
 * ✅ 负责: 设备端口注册、注销、生命周期协调
 * ❌ 不涉及: 端口扫描、设备探测、匹配计算
 *
 * 职责边界:
 * - 设备端口映射关系管理
 * - 注册/注销流程协调
 * - 生命周期同步（开启/关闭）
 * - 注册状态跟踪和查询
 */
class IRegistrationManager {
  public:
    virtual ~IRegistrationManager() = default;

    // ====== 核心注册功能 - 最小接口 ======

    /**
     * @brief 注册设备到指定端口
     * @param deviceInfo 设备信息
     * @param portInfo 端口信息
     * @return 注册结果
     */
    virtual RegistrationResult registerDevicePort(const DeviceInfo &deviceInfo, const PortInfo &portInfo) = 0;

    /**
     * @brief 注销设备注册
     * @param deviceId 设备ID
     * @return 是否成功
     */
    virtual bool unregisterDevice(const QString &deviceId) = 0;

    /**
     * @brief 注销端口上的所有设备
     * @param portId 端口ID
     * @return 注销的设备数量
     */
    virtual int unregisterPort(const QString &portId) = 0;

    // ====== 生命周期协调 ======

    /**
     * @brief 同步设备和端口的生命周期状态
     * @param deviceId 设备ID
     * @param portId 端口ID
     * @param targetState 目标状态
     * @return 是否成功
     */
    virtual bool synchronizeLifecycle(const QString &deviceId, const QString &portId, ConnectionStatus targetState) = 0;

    /**
     * @brief 开启设备连接（设备和端口同时开启）
     * @param deviceId 设备ID
     * @return 是否成功
     */
    virtual bool openDevice(const QString &deviceId) = 0;

    /**
     * @brief 关闭设备连接（设备和端口同时关闭）
     * @param deviceId 设备ID
     * @return 是否成功
     */
    virtual bool closeDevice(const QString &deviceId) = 0;

    // ====== 查询接口 ======

    /**
     * @brief 获取设备对应的端口ID
     * @param deviceId 设备ID
     * @return 端口ID，空字符串表示未注册
     */
    virtual QString getDevicePort(const QString &deviceId) const = 0;

    /**
     * @brief 获取端口上注册的设备ID列表
     * @param portId 端口ID
     * @return 设备ID列表
     */
    virtual QStringList getPortDevices(const QString &portId) const = 0;

    /**
     * @brief 检查设备是否已注册
     * @param deviceId 设备ID
     * @return 是否已注册
     */
    virtual bool isDeviceRegistered(const QString &deviceId) const = 0;

    /**
     * @brief 检查端口是否被占用
     * @param portId 端口ID
     * @return 是否被占用
     */
    virtual bool isPortOccupied(const QString &portId) const = 0;

    /**
     * @brief 获取所有设备-端口映射
     * @return 设备ID到端口ID的映射
     */
    virtual QMap<QString, QString> getAllMappings() const = 0;

    // ====== 状态查询 ======

    /**
     * @brief 获取注册的设备总数
     * @return 设备数量
     */
    virtual int getRegisteredDeviceCount() const = 0;

    /**
     * @brief 获取占用的端口总数
     * @return 端口数量
     */
    virtual int getOccupiedPortCount() const = 0;
};

}  // namespace Registration
}  // namespace DeviceManagement
}  // namespace LA