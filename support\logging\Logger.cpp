#include "Logger.h"
#include "formatter/ILogFormatter.h"
#include "output/ILogOutput.h"
#include <QRegularExpression>
#include <QCoreApplication>
#include <QThreadPool>
#include <QMutexLocker>
#include <QDebug>
#include <QThread>
#include <memory>
#include <map>
#include <algorithm>

namespace LA {
namespace Support {
namespace Logging {

//=====================================================================
// Logger Implementation
//=====================================================================

Logger::Logger(const QString& name)
    : ILogger()
    , m_name(name)
    , m_flushTimer(new QTimer(this))
    , m_initialized(false)
    , m_totalLogCount(0)
    , m_errorLogCount(0)
    , m_warningLogCount(0)
    , m_lastFlushTime(0)
    , m_creationTime(QDateTime::currentDateTime())
{
    connect(m_flushTimer, &QTimer::timeout, this, &Logger::performPeriodicFlush);
}

Logger::~Logger() {
    shutdown();
}

bool Logger::initialize(const ConfigParameters& config) {
    QMutexLocker locker(&m_mutex);
    
    if (m_initialized) {
        return true;
    }
    
    try {
        // 从配置参数初始化LoggerConfig
        LoggerConfig loggerConfig;
        if (config.contains("minLevel")) {
            loggerConfig.minLevel = static_cast<LogLevel>(config.value("minLevel").toInt());
        }
        if (config.contains("enableConsole")) {
            loggerConfig.enableConsole = config.value("enableConsole").toBool();
        }
        if (config.contains("enableFile")) {
            loggerConfig.enableFile = config.value("enableFile").toBool();
        }
        if (config.contains("logDirectory")) {
            loggerConfig.logDirectory = config.value("logDirectory").toString();
        }
        
        initializeFromConfig(loggerConfig);
        setupDefaultHandlers();
        setupAsyncProcessing();
        
        // 启动定期刷新
        if (m_config.flushInterval > 0) {
            m_flushTimer->start(m_config.flushInterval);
        }
        
        m_initialized = true;
        return true;
        
    } catch (const std::exception& e) {
        qCritical() << "Logger initialization failed:" << e.what();
        return false;
    }
}

void Logger::shutdown() {
    QMutexLocker locker(&m_mutex);
    
    if (!m_initialized) {
        return;
    }
    
    try {
        // 停止定期刷新
        m_flushTimer->stop();
        
        // 异步处理已简化 - 不再使用
        
        // 刷新所有处理器
        flush();
        
        // 清理处理器
        for (auto& handler : m_handlers) {
            handler->close();
        }
        m_handlers.clear();
        m_filters.clear();
        
        m_initialized = false;
        
    } catch (const std::exception& e) {
        qCritical() << "Logger shutdown failed:" << e.what();
    }
}

bool Logger::isInitialized() const {
    return m_initialized;
}

StatusInfoList Logger::getStatus() const {
    StatusInfoList status;
    
    status.append({
        "name", m_name,
        "Logger Name", "日志记录器名称"
    });
    
    status.append({
        "initialized", m_initialized ? "Yes" : "No",
        "Initialized", "是否已初始化"
    });
    
    status.append({
        "handlers", QString::number(m_handlers.size()),
        "Handler Count", "处理器数量"
    });
    
    status.append({
        "total_logs", QString::number(m_totalLogCount),
        "Total Logs", "总日志数"
    });
    
    status.append({
        "error_logs", QString::number(m_errorLogCount),
        "Error Logs", "错误日志数"
    });
    
    return status;
}

VersionInfo Logger::getVersion() const {
    VersionInfo version;
    version.major = 1;
    version.minor = 0;
    version.patch = 0;
    version.build = "stable";
    return version;
}

void Logger::setConfig(const LoggerConfig& config) {
    QMutexLocker locker(&m_mutex);
    m_config = config;
    
    if (m_initialized) {
        // 重新应用配置
        setupDefaultHandlers();
        
        // 更新刷新间隔
        if (m_config.flushInterval > 0) {
            m_flushTimer->start(m_config.flushInterval);
        } else {
            m_flushTimer->stop();
        }
    }
}

LoggerConfig Logger::getConfig() const {
    QMutexLocker locker(&m_mutex);
    return m_config;
}

void Logger::addHandler(std::shared_ptr<ILogHandler> handler) {
    if (!handler) return;
    
    QMutexLocker locker(&m_mutex);
    m_handlers.push_back(handler);
}

void Logger::removeHandler(std::shared_ptr<ILogHandler> handler) {
    if (!handler) return;
    
    QMutexLocker locker(&m_mutex);
    m_handlers.erase(
        std::remove(m_handlers.begin(), m_handlers.end(), handler),
        m_handlers.end()
    );
}

void Logger::addFilter(std::shared_ptr<ILogFilter> filter) {
    if (!filter) return;
    
    QMutexLocker locker(&m_mutex);
    m_filters.push_back(filter);
}

void Logger::removeFilter(std::shared_ptr<ILogFilter> filter) {
    if (!filter) return;
    
    QMutexLocker locker(&m_mutex);
    m_filters.erase(
        std::remove(m_filters.begin(), m_filters.end(), filter),
        m_filters.end()
    );
}

void Logger::log(LogLevel level, LogType type, const QString& message, const LogContext& context) {
    if (!isLevelEnabled(level) || !isTypeEnabled(type)) {
        return;
    }
    
    LogEntry entry(level, type, message, enrichContext(context));
    
    // 简化为同步处理
    processLogEntry(entry);
}

void Logger::trace(LogType type, const QString& message, const LogContext& context) {
    log(LogLevel::TRACE, type, message, context);
}

void Logger::debug(LogType type, const QString& message, const LogContext& context) {
    log(LogLevel::DEBUG, type, message, context);
}

void Logger::info(LogType type, const QString& message, const LogContext& context) {
    log(LogLevel::INFO, type, message, context);
}

void Logger::warning(LogType type, const QString& message, const LogContext& context) {
    log(LogLevel::WARNING, type, message, context);
}

void Logger::error(LogType type, const QString& message, const LogContext& context) {
    log(LogLevel::ERROR, type, message, context);
}

void Logger::fatal(LogType type, const QString& message, const LogContext& context) {
    log(LogLevel::FATAL, type, message, context);
}

void Logger::flush() {
    QMutexLocker locker(&m_mutex);
    
    for (auto& handler : m_handlers) {
        handler->flush();
    }
    
    m_lastFlushTime = QDateTime::currentMSecsSinceEpoch();
}

bool Logger::isLevelEnabled(LogLevel level) const {
    return level >= m_config.minLevel;
}

bool Logger::isTypeEnabled(LogType type) const {
    if (m_config.enabledTypes.isEmpty()) {
        return true;  // 如果没有指定类型过滤，则允许所有类型
    }
    
    QString typeStr = logTypeToString(type);
    return m_config.enabledTypes.contains(typeStr);
}

QString Logger::getName() const {
    return m_name;
}

void Logger::setParent(std::shared_ptr<Logger> parent) {
    QMutexLocker locker(&m_mutex);
    m_parent = parent;
}

std::shared_ptr<Logger> Logger::getParent() const {
    QMutexLocker locker(&m_mutex);
    return m_parent.lock();
}

void Logger::addChild(std::shared_ptr<Logger> child) {
    if (!child) return;
    
    QMutexLocker locker(&m_mutex);
    m_children.push_back(child);
}

void Logger::removeChild(std::shared_ptr<Logger> child) {
    if (!child) return;
    
    QMutexLocker locker(&m_mutex);
    m_children.erase(
        std::remove_if(m_children.begin(), m_children.end(),
                      [child](const std::weak_ptr<Logger>& weak) {
                          return weak.lock() == child;
                      }),
        m_children.end()
    );
}

QVariantMap Logger::getStatistics() const {
    QMutexLocker locker(&m_mutex);
    
    QVariantMap stats;
    stats["name"] = m_name;
    stats["total_logs"] = static_cast<qulonglong>(m_totalLogCount);
    stats["error_logs"] = static_cast<qulonglong>(m_errorLogCount);
    stats["warning_logs"] = static_cast<qulonglong>(m_warningLogCount);
    stats["last_flush_time"] = m_lastFlushTime;
    stats["creation_time"] = m_creationTime;
    stats["handler_count"] = static_cast<int>(m_handlers.size());
    stats["filter_count"] = static_cast<int>(m_filters.size());
    stats["child_count"] = static_cast<int>(m_children.size());
    
    return stats;
}

void Logger::processAsyncQueue() {
    // 由AsyncLogWorker调用
}

void Logger::performPeriodicFlush() {
    flush();
}

void Logger::processLogEntry(const LogEntry& entry) {
    if (!shouldProcess(entry)) {
        return;
    }
    
    // 更新统计信息
    updateStatistics(entry);
    
    // 处理日志条目
    for (auto& handler : m_handlers) {
        try {
            handler->handle(entry);
        } catch (const std::exception& e) {
            // 处理器异常不应该影响其他处理器
            qWarning() << "Log handler error:" << e.what();
        }
    }
    
    // 发送信号
    emit logRecorded(entry);
}

bool Logger::shouldProcess(const LogEntry& entry) const {
    // 应用所有过滤器
    for (const auto& filter : m_filters) {
        if (!filter->filter(entry)) {
            return false;
        }
    }
    
    return true;
}

void Logger::updateStatistics(const LogEntry& entry) {
    m_totalLogCount++;
    
    switch (entry.level) {
        case LogLevel::ERROR:
        case LogLevel::FATAL:
            m_errorLogCount++;
            break;
        case LogLevel::WARNING:
            m_warningLogCount++;
            break;
        default:
            break;
    }
}

LogContext Logger::enrichContext(const LogContext& context) const {
    LogContext enriched = context;
    
    // 自动填充缺失的上下文信息
    if (enriched.timestamp.isNull()) {
        enriched.timestamp = QDateTime::currentDateTime();
    }
    
    if (enriched.threadId.isEmpty()) {
        enriched.threadId = QString("0x%1").arg(
            reinterpret_cast<quintptr>(QThread::currentThreadId()), 0, 16);
    }
    
    if (enriched.processId.isEmpty()) {
        enriched.processId = QString::number(QCoreApplication::applicationPid());
    }
    
    return enriched;
}

QString Logger::generateLogId() const {
    static qint64 counter = 0;
    return QString("%1-%2-%3")
        .arg(m_name)
        .arg(QDateTime::currentMSecsSinceEpoch())
        .arg(counter++);
}

void Logger::initializeFromConfig(const LoggerConfig& config) {
    m_config = config;
}

void Logger::setupDefaultHandlers() {
    // 清除现有处理器
    m_handlers.clear();
    
    // 根据配置添加默认处理器
    if (m_config.enableConsole) {
        auto consoleHandler = std::make_shared<ConsoleHandler>();
        addHandler(consoleHandler);
    }
    
    if (m_config.enableFile) {
        QString filename = QString("%1/%2")
            .arg(m_config.logDirectory)
            .arg(m_config.logFilePattern);
        
        if (m_config.enableRotation) {
            auto rotatingHandler = std::make_shared<RotatingFileHandler>(
                filename, m_config.maxFileSize, m_config.maxBackupCount);
            addHandler(rotatingHandler);
        } else {
            auto fileHandler = std::make_shared<FileHandler>(filename);
            addHandler(fileHandler);
        }
    }
}

void Logger::setupAsyncProcessing() {
    // 异步处理已简化 - 不再使用
}

// AsyncLogWorker implementation removed for simplification

// Simple stub implementations - classes declared in ILogOutput.h
ConsoleHandler::ConsoleHandler(std::shared_ptr<ILogFormatter>) {}
ConsoleHandler::~ConsoleHandler() {}
void ConsoleHandler::handle(const LogEntry& entry) {
    qDebug() << QString("[%1] %2")
                .arg(static_cast<int>(entry.level))
                .arg(entry.message);
}
void ConsoleHandler::flush() {}
void ConsoleHandler::close() {}
void ConsoleHandler::setFormatter(std::shared_ptr<ILogFormatter>) {}
std::shared_ptr<ILogFormatter> ConsoleHandler::getFormatter() const { return nullptr; }
void ConsoleHandler::setOutputStream(FILE*) {}

FileHandler::FileHandler(const QString&, std::shared_ptr<ILogFormatter>) {}
FileHandler::~FileHandler() {}
void FileHandler::handle(const LogEntry&) {}
void FileHandler::flush() {}
void FileHandler::close() {}
void FileHandler::setFormatter(std::shared_ptr<ILogFormatter>) {}
std::shared_ptr<ILogFormatter> FileHandler::getFormatter() const { return nullptr; }
void FileHandler::setFilename(const QString&) {}
QString FileHandler::getFilename() const { return QString(); }
void FileHandler::setAppendMode(bool) {}
bool FileHandler::isAppendMode() const { return true; }
void FileHandler::setAutoFlushInterval(int) {}
int FileHandler::getAutoFlushInterval() const { return 0; }

RotatingFileHandler::RotatingFileHandler(const QString&, qint64, int, std::shared_ptr<ILogFormatter>) {}
RotatingFileHandler::~RotatingFileHandler() {}
void RotatingFileHandler::handle(const LogEntry&) {}
void RotatingFileHandler::flush() {}
void RotatingFileHandler::close() {}
void RotatingFileHandler::setFormatter(std::shared_ptr<ILogFormatter>) {}
std::shared_ptr<ILogFormatter> RotatingFileHandler::getFormatter() const { return nullptr; }
void RotatingFileHandler::setMaxFileSize(qint64) {}
qint64 RotatingFileHandler::getMaxFileSize() const { return 0; }
void RotatingFileHandler::setMaxBackupCount(int) {}
int RotatingFileHandler::getMaxBackupCount() const { return 0; }
void RotatingFileHandler::rotate() {}

//=====================================================================
// Filter Implementations
//=====================================================================

LevelFilter::LevelFilter(LogLevel minLevel, LogLevel maxLevel)
    : m_minLevel(minLevel), m_maxLevel(maxLevel) {
}

bool LevelFilter::filter(const LogEntry& entry) const {
    return entry.level >= m_minLevel && entry.level <= m_maxLevel;
}

void LevelFilter::setMinLevel(LogLevel level) {
    m_minLevel = level;
}

void LevelFilter::setMaxLevel(LogLevel level) {
    m_maxLevel = level;
}

LogLevel LevelFilter::getMinLevel() const {
    return m_minLevel;
}

LogLevel LevelFilter::getMaxLevel() const {
    return m_maxLevel;
}

TypeFilter::TypeFilter(const QList<LogType>& allowedTypes)
    : m_allowedTypes(allowedTypes) {
}

bool TypeFilter::filter(const LogEntry& entry) const {
    return m_allowedTypes.contains(entry.type);
}

void TypeFilter::setAllowedTypes(const QList<LogType>& types) {
    m_allowedTypes = types;
}

QList<LogType> TypeFilter::getAllowedTypes() const {
    return m_allowedTypes;
}

void TypeFilter::addAllowedType(LogType type) {
    if (!m_allowedTypes.contains(type)) {
        m_allowedTypes.append(type);
    }
}

void TypeFilter::removeAllowedType(LogType type) {
    m_allowedTypes.removeAll(type);
}

ComponentFilter::ComponentFilter(const QStringList& allowedComponents)
    : m_allowedComponents(allowedComponents) {
}

bool ComponentFilter::filter(const LogEntry& entry) const {
    return m_allowedComponents.contains(entry.context.component);
}

void ComponentFilter::setAllowedComponents(const QStringList& components) {
    m_allowedComponents = components;
}

QStringList ComponentFilter::getAllowedComponents() const {
    return m_allowedComponents;
}

void ComponentFilter::addAllowedComponent(const QString& component) {
    if (!m_allowedComponents.contains(component)) {
        m_allowedComponents.append(component);
    }
}

void ComponentFilter::removeAllowedComponent(const QString& component) {
    m_allowedComponents.removeAll(component);
}

RegexFilter::RegexFilter(const QString& pattern, bool include)
    : m_pattern(pattern), m_includeMode(include) {
    m_regex.setPattern(pattern);
}

bool RegexFilter::filter(const LogEntry& entry) const {
    bool matches = m_regex.match(entry.message).hasMatch();
    return m_includeMode ? matches : !matches;
}

void RegexFilter::setPattern(const QString& pattern) {
    m_pattern = pattern;
    m_regex.setPattern(pattern);
}

QString RegexFilter::getPattern() const {
    return m_pattern;
}

void RegexFilter::setIncludeMode(bool include) {
    m_includeMode = include;
}

bool RegexFilter::isIncludeMode() const {
    return m_includeMode;
}

//=====================================================================
// Utility Functions
//=====================================================================

QString logLevelToString(LogLevel level) {
    switch (level) {
        case LogLevel::TRACE:   return "TRACE";
        case LogLevel::DEBUG:   return "DEBUG";
        case LogLevel::INFO:    return "INFO";
        case LogLevel::WARNING: return "WARNING";
        case LogLevel::ERROR:   return "ERROR";
        case LogLevel::FATAL:   return "FATAL";
        default:                return "UNKNOWN";
    }
}

LogLevel stringToLogLevel(const QString& levelStr) {
    QString upper = levelStr.toUpper();
    if (upper == "TRACE")   return LogLevel::TRACE;
    if (upper == "DEBUG")   return LogLevel::DEBUG;
    if (upper == "INFO")    return LogLevel::INFO;
    if (upper == "WARNING") return LogLevel::WARNING;
    if (upper == "ERROR")   return LogLevel::ERROR;
    if (upper == "FATAL")   return LogLevel::FATAL;
    return LogLevel::INFO;  // 默认级别
}

QString logTypeToString(LogType type) {
    switch (type) {
        case LogType::DEFAULT:          return "DEFAULT";
        case LogType::INIT:             return "INIT";
        case LogType::COMM:             return "COMM";
        case LogType::COMM_ACK:         return "COMM_ACK";
        case LogType::PROCESS_STATUS:   return "PROCESS_STATUS";
        case LogType::PROCESS_DATA:     return "PROCESS_DATA";
        case LogType::RESULT_DATA:      return "RESULT_DATA";
        case LogType::ERROR_LOG:        return "ERROR_LOG";
        case LogType::PERFORMANCE:      return "PERFORMANCE";
        case LogType::SECURITY:         return "SECURITY";
        case LogType::AUDIT:            return "AUDIT";
        case LogType::CUSTOM:           return "CUSTOM";
        default:                        return "UNKNOWN";
    }
}

LogType stringToLogType(const QString& typeStr) {
    QString upper = typeStr.toUpper();
    if (upper == "DEFAULT")         return LogType::DEFAULT;
    if (upper == "INIT")            return LogType::INIT;
    if (upper == "COMM")            return LogType::COMM;
    if (upper == "COMM_ACK")        return LogType::COMM_ACK;
    if (upper == "PROCESS_STATUS")  return LogType::PROCESS_STATUS;
    if (upper == "PROCESS_DATA")    return LogType::PROCESS_DATA;
    if (upper == "RESULT_DATA")     return LogType::RESULT_DATA;
    if (upper == "ERROR_LOG")       return LogType::ERROR_LOG;
    if (upper == "PERFORMANCE")     return LogType::PERFORMANCE;
    if (upper == "SECURITY")        return LogType::SECURITY;
    if (upper == "AUDIT")           return LogType::AUDIT;
    if (upper == "CUSTOM")          return LogType::CUSTOM;
    return LogType::DEFAULT;  // 默认类型
}

}  // namespace Logging
}  // namespace Support
}  // namespace LA