#pragma once

/**
 * @file NetworkConnection.h
 * @brief 网络连接实现 - Linus式最小实现
 * 
 * 遵循Linus设计原则：
 * - 实现新的IConnection接口
 * - 最小化功能，专注数据传输
 * - 基于Qt Network的稳定实现
 * - Layer 1实现：纯粹的read/write操作
 */

#include "IConnection.h"
#include <QTcpSocket>
#include <QUdpSocket>
#include <QHostAddress>
#include <QMutex>

namespace LA {
namespace Communication {
namespace Connection {

/**
 * @brief TCP连接的Linus式实现
 * 
 * 严格遵循IConnection接口，只实现TCP数据传输功能
 */
class TcpConnection : public IConnection {
    Q_OBJECT

public:
    explicit TcpConnection(QObject* parent = nullptr);
    virtual ~TcpConnection();

    // === IConnection接口实现 ===
    
    SimpleResult open(const ConnectionConfig& config) override;
    SimpleResult close() override;
    ConnectionStatus status() const override;
    bool isOpen() const override;
    
    qint64 write(const QByteArray& data) override;
    QByteArray read(qint64 maxBytes = -1) override;
    qint64 bytesAvailable() const override;
    bool waitForReadyRead(int timeout = 5000) override;
    bool waitForBytesWritten(int timeout = 5000) override;
    
    DeviceStatistics getStatistics() const override;
    QString errorString() const override;
    void resetStatistics() override;

    // === TCP特定功能（最小化） ===
    
    /**
     * @brief 获取远程地址
     * @return 远程主机地址
     */
    QHostAddress getRemoteAddress() const;
    
    /**
     * @brief 获取远程端口
     * @return 远程端口号
     */
    quint16 getRemotePort() const;

private slots:
    void onConnected();
    void onDisconnected();
    void onReadyRead();
    void onErrorOccurred(QAbstractSocket::SocketError error);

private:
    QTcpSocket* m_socket;
    ConnectionConfig m_config;
    ConnectionStatus m_status;
    DeviceStatistics m_statistics;
    QString m_lastError;
    mutable QMutex m_mutex;
    
    void updateStatistics(qint64 bytesRead = 0, qint64 bytesWritten = 0);
    void setStatus(ConnectionStatus newStatus);
};

/**
 * @brief UDP连接的Linus式实现
 * 
 * 严格遵循IConnection接口，只实现UDP数据传输功能
 */
class UdpConnection : public IConnection {
    Q_OBJECT

public:
    explicit UdpConnection(QObject* parent = nullptr);
    virtual ~UdpConnection();

    // === IConnection接口实现 ===
    
    SimpleResult open(const ConnectionConfig& config) override;
    SimpleResult close() override;
    ConnectionStatus status() const override;
    bool isOpen() const override;
    
    qint64 write(const QByteArray& data) override;
    QByteArray read(qint64 maxBytes = -1) override;
    qint64 bytesAvailable() const override;
    bool waitForReadyRead(int timeout = 5000) override;
    bool waitForBytesWritten(int timeout = 5000) override;
    
    DeviceStatistics getStatistics() const override;
    QString errorString() const override;
    void resetStatistics() override;

    // === UDP特定功能（最小化） ===
    
    /**
     * @brief 获取远程地址
     * @return 远程主机地址
     */
    QHostAddress getRemoteAddress() const;
    
    /**
     * @brief 获取远程端口
     * @return 远程端口号
     */
    quint16 getRemotePort() const;

private slots:
    void onReadyRead();
    void onErrorOccurred(QAbstractSocket::SocketError error);

private:
    QUdpSocket* m_socket;
    ConnectionConfig m_config;
    ConnectionStatus m_status;
    DeviceStatistics m_statistics;
    QString m_lastError;
    QHostAddress m_remoteAddress;
    quint16 m_remotePort;
    mutable QMutex m_mutex;
    
    void updateStatistics(qint64 bytesRead = 0, qint64 bytesWritten = 0);
    void setStatus(ConnectionStatus newStatus);
};

} // namespace Connection
} // namespace Communication
} // namespace LA