#pragma once

/**
 * @file Version.h
 * @brief LA Algorithm Precision Library版本信息
 */

#define LA_ALGORITHM_PRECISION_VERSION_MAJOR @LA_ALGORITHM_PRECISION_VERSION_MAJOR@
#define LA_ALGORITHM_PRECISION_VERSION_MINOR @LA_ALGORITHM_PRECISION_VERSION_MINOR@
#define LA_ALGORITHM_PRECISION_VERSION_PATCH @LA_ALGORITHM_PRECISION_VERSION_PATCH@
#define LA_ALGORITHM_PRECISION_VERSION "@LA_ALGORITHM_PRECISION_VERSION@"

namespace LA {
namespace Algorithm {
namespace Precision {

/**
 * @brief 获取版本字符串
 */
inline const char* getVersionString() {
    return LA_ALGORITHM_PRECISION_VERSION;
}

/**
 * @brief 获取主版本号
 */
inline int getMajorVersion() {
    return LA_ALGORITHM_PRECISION_VERSION_MAJOR;
}

/**
 * @brief 获取次版本号  
 */
inline int getMinorVersion() {
    return LA_ALGORITHM_PRECISION_VERSION_MINOR;
}

/**
 * @brief 获取补丁版本号
 */
inline int getPatchVersion() {
    return LA_ALGORITHM_PRECISION_VERSION_PATCH;
}

} // namespace Precision
} // namespace Algorithm  
} // namespace LA