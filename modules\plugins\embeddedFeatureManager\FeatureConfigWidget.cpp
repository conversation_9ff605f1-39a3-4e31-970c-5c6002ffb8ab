#include "FeatureConfigWidget.h"
#include "PermissionManager.h"

#include <QJsonDocument>
#include <QJsonValue>
#include <QFileDialog>
#include <QMessageBox>
#include <QHeaderView>
#include <QScrollArea>
#include <QFormLayout>
#include <QLineEdit>
#include <QSpinBox>
#include <QDoubleSpinBox>
#include <QCheckBox>
#include <QTextEdit>
#include <QGroupBox>
#include <QApplication>
#include <QMimeData>
#include <QDrag>
#include <QDebug>

FeatureConfigWidget::FeatureConfigWidget(QWidget *parent)
    : QWidget(parent)
    , m_mainSplitter(nullptr)
    , m_leftPanel(nullptr)
    , m_rightPanel(nullptr)
    , m_toolBar(nullptr)
    , m_statusBar(nullptr)
    , m_targetSelector(nullptr)
    , m_statusLabel(nullptr)
    , m_permissionLabel(nullptr)
    , m_featureTree(nullptr)
    , m_propertyPanel(nullptr)
    , m_permissionManager(nullptr)
    , m_isLoading(false)
    , m_hasUnsavedChanges(false)
{
    setupUI();
    setupConnections();
    
    // 设置默认target
    m_currentTarget = "DEFAULT_TARGET";
    
    qDebug() << "FeatureConfigWidget initialized";
}

FeatureConfigWidget::~FeatureConfigWidget()
{
    qDebug() << "FeatureConfigWidget destroyed";
}

void FeatureConfigWidget::setConfigData(const QJsonObject& configData)
{
    m_isLoading = true;
    m_configData = configData;
    
    // 更新target选择器
    if (m_targetSelector) {
        m_targetSelector->clear();
        QJsonArray targets = configData.value("targets").toArray();
        for (const auto& target : targets) {
            QString targetName = target.toObject().value("target_name").toString();
            m_targetSelector->addItem(targetName);
        }
        
        // 选择第一个target作为默认
        if (!targets.isEmpty()) {
            m_currentTarget = targets.first().toObject().value("target_name").toString();
            m_targetSelector->setCurrentText(m_currentTarget);
        }
    }
    
    updateFeatureTree();
    updateStatusInfo();
    
    m_isLoading = false;
    m_hasUnsavedChanges = false;
    
    qDebug() << "Config data loaded with" << configData.value("features").toArray().size() << "features";
}

QJsonObject FeatureConfigWidget::getConfigData() const
{
    QJsonObject result = m_configData;
    
    // 更新features数据
    result["features"] = saveFeaturesToJson();
    
    return result;
}

void FeatureConfigWidget::setPermissionManager(PermissionManager* permissionManager)
{
    m_permissionManager = permissionManager;
    updatePermissionState();
}

void FeatureConfigWidget::refreshUI()
{
    updateFeatureTree();
    updatePropertyPanel();
    updateStatusInfo();
}

void FeatureConfigWidget::updatePermissionState()
{
    if (!m_permissionManager) {
        return;
    }
    
    bool canEdit = checkEditPermission();
    bool canDelete = checkDeletePermission();
    bool canSync = checkSyncPermission();
    
    // 更新按钮状态
    m_addFeatureBtn->setEnabled(canEdit);
    m_removeFeatureBtn->setEnabled(canDelete);
    m_featureUpBtn->setEnabled(canEdit);
    m_featureDownBtn->setEnabled(canEdit);
    m_syncBtn->setEnabled(canSync);
    
    // 更新树控件编辑状态
    if (m_featureTree) {
        m_featureTree->setDragDropMode(canEdit ? QAbstractItemView::InternalMove : QAbstractItemView::NoDragDrop);
    }
    
    // 更新属性面板状态
    if (m_propertyPanel) {
        m_propertyPanel->setReadOnly(!canEdit);
    }
    
    // 更新权限标签
    if (m_permissionLabel && m_permissionManager) {
        QString roleText = m_permissionManager->getCurrentUserRole();
        m_permissionLabel->setText(QString("Role: %1").arg(roleText));
    }
}

void FeatureConfigWidget::onAddFeature()
{
    if (!checkEditPermission()) {
        QMessageBox::warning(this, "Permission Denied", "You don't have permission to add features.");
        return;
    }
    
    // 创建新功能项
    QJsonObject newFeature;
    newFeature["id"] = QString("new_feature_%1").arg(QDateTime::currentMSecsSinceEpoch());
    newFeature["name"] = "New Feature";
    newFeature["type"] = "bool";
    newFeature["enabled"] = true;
    newFeature["description"] = "";
    
    // 添加target特定的值
    QJsonObject perTarget;
    perTarget[m_currentTarget] = true;
    newFeature["per_target"] = perTarget;
    
    // 添加到树中
    QTreeWidgetItem* newItem = createFeatureItem(newFeature);
    m_featureTree->addTopLevelItem(newItem);
    m_featureTree->setCurrentItem(newItem);
    
    m_hasUnsavedChanges = true;
    emit configChanged();
}

void FeatureConfigWidget::onRemoveFeature()
{
    if (!checkDeletePermission()) {
        QMessageBox::warning(this, "Permission Denied", "You don't have permission to remove features.");
        return;
    }
    
    QTreeWidgetItem* current = m_featureTree->currentItem();
    if (!current) {
        return;
    }
    
    int result = QMessageBox::question(this, "Confirm Deletion", 
        "Are you sure you want to delete the selected feature?",
        QMessageBox::Yes | QMessageBox::No, QMessageBox::No);
        
    if (result == QMessageBox::Yes) {
        delete current;
        m_hasUnsavedChanges = true;
        emit configChanged();
    }
}

void FeatureConfigWidget::onFeatureUp()
{
    if (!checkEditPermission()) return;
    
    QTreeWidgetItem* current = m_featureTree->currentItem();
    if (!current) return;
    
    QTreeWidgetItem* parent = current->parent();
    int index = parent ? parent->indexOfChild(current) : m_featureTree->indexOfTopLevelItem(current);
    
    if (index > 0) {
        if (parent) {
            QTreeWidgetItem* item = parent->takeChild(index);
            parent->insertChild(index - 1, item);
        } else {
            QTreeWidgetItem* item = m_featureTree->takeTopLevelItem(index);
            m_featureTree->insertTopLevelItem(index - 1, item);
        }
        m_featureTree->setCurrentItem(current);
        m_hasUnsavedChanges = true;
        emit configChanged();
    }
}

void FeatureConfigWidget::onFeatureDown()
{
    if (!checkEditPermission()) return;
    
    QTreeWidgetItem* current = m_featureTree->currentItem();
    if (!current) return;
    
    QTreeWidgetItem* parent = current->parent();
    int index = parent ? parent->indexOfChild(current) : m_featureTree->indexOfTopLevelItem(current);
    int maxIndex = parent ? parent->childCount() - 1 : m_featureTree->topLevelItemCount() - 1;
    
    if (index < maxIndex) {
        if (parent) {
            QTreeWidgetItem* item = parent->takeChild(index);
            parent->insertChild(index + 1, item);
        } else {
            QTreeWidgetItem* item = m_featureTree->takeTopLevelItem(index);
            m_featureTree->insertTopLevelItem(index + 1, item);
        }
        m_featureTree->setCurrentItem(current);
        m_hasUnsavedChanges = true;
        emit configChanged();
    }
}

void FeatureConfigWidget::onExpandAll()
{
    m_featureTree->expandAll();
}

void FeatureConfigWidget::onCollapseAll()
{
    m_featureTree->collapseAll();
}

void FeatureConfigWidget::onSaveConfig()
{
    QString fileName = QFileDialog::getSaveFileName(this, "Save Configuration", 
        QString(), "JSON Files (*.json)");
    if (!fileName.isEmpty()) {
        // 通知插件保存配置
        emit syncRequested();
    }
}

void FeatureConfigWidget::onLoadConfig()
{
    if (m_hasUnsavedChanges) {
        int result = QMessageBox::question(this, "Unsaved Changes",
            "You have unsaved changes. Load new configuration anyway?",
            QMessageBox::Yes | QMessageBox::No, QMessageBox::No);
        if (result != QMessageBox::Yes) {
            return;
        }
    }
    
    QString fileName = QFileDialog::getOpenFileName(this, "Load Configuration", 
        QString(), "JSON Files (*.json)");
    if (!fileName.isEmpty()) {
        // 通知插件加载配置
        // TODO: 实现加载信号
    }
}

void FeatureConfigWidget::onSyncToHeader()
{
    if (!checkSyncPermission()) {
        QMessageBox::warning(this, "Permission Denied", "You don't have permission to sync files.");
        return;
    }
    
    emit syncRequested();
}

void FeatureConfigWidget::onImportFromHeader()
{
    if (!checkEditPermission()) {
        QMessageBox::warning(this, "Permission Denied", "You don't have permission to import configurations.");
        return;
    }
    
    QString fileName = QFileDialog::getOpenFileName(this, "Import from Header", 
        QString(), "Header Files (*.h)");
    if (!fileName.isEmpty()) {
        // TODO: 实现头文件导入信号
    }
}

void FeatureConfigWidget::onFeatureSelectionChanged()
{
    updatePropertyPanel();
    
    QTreeWidgetItem* current = m_featureTree->currentItem();
    bool hasSelection = (current != nullptr);
    
    m_removeFeatureBtn->setEnabled(hasSelection && checkDeletePermission());
    m_featureUpBtn->setEnabled(hasSelection && checkEditPermission());
    m_featureDownBtn->setEnabled(hasSelection && checkEditPermission());
}

void FeatureConfigWidget::onFeatureItemChanged(QTreeWidgetItem* item, int column)
{
    if (m_isLoading) return;
    
    m_hasUnsavedChanges = true;
    emit configChanged();
}

void FeatureConfigWidget::onFeaturePropertyChanged()
{
    if (m_isLoading) return;
    
    QTreeWidgetItem* current = m_featureTree->currentItem();
    if (!current || !m_propertyPanel) return;
    
    // 从属性面板获取更新后的功能数据
    QJsonObject updatedFeature = m_propertyPanel->getFeature();
    updateFeatureItem(current, updatedFeature);
    
    m_hasUnsavedChanges = true;
    emit configChanged();
}

void FeatureConfigWidget::onTargetChanged(const QString& targetName)
{
    if (m_currentTarget != targetName) {
        m_currentTarget = targetName;
        updateFeatureTree();
        updatePropertyPanel();
        
        if (m_propertyPanel) {
            m_propertyPanel->setTarget(targetName);
        }
    }
}

void FeatureConfigWidget::setupUI()
{
    setWindowTitle("Feature Configuration Manager");
    
    // 主布局
    QVBoxLayout* mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(0, 0, 0, 0);
    mainLayout->setSpacing(0);
    
    // 工具栏
    setupToolBar();
    mainLayout->addWidget(m_toolBar);
    
    // 主分割器
    m_mainSplitter = new QSplitter(Qt::Horizontal);
    mainLayout->addWidget(m_mainSplitter);
    
    // 左侧面板 - 功能树
    m_leftPanel = new QWidget;
    QVBoxLayout* leftLayout = new QVBoxLayout(m_leftPanel);
    
    // 功能树控件
    m_featureTree = new FeatureTreeWidget;
    m_featureTree->setHeaderLabels(QStringList() << "Feature" << "Type" << "Value" << "Status");
    m_featureTree->header()->setStretchLastSection(false);
    m_featureTree->header()->setSectionResizeMode(0, QHeaderView::ResizeToContents);
    m_featureTree->header()->setSectionResizeMode(1, QHeaderView::ResizeToContents);
    m_featureTree->header()->setSectionResizeMode(2, QHeaderView::ResizeToContents);
    m_featureTree->header()->setSectionResizeMode(3, QHeaderView::Stretch);
    
    leftLayout->addWidget(m_featureTree);
    
    // 功能树操作按钮
    QHBoxLayout* treeButtonLayout = new QHBoxLayout;
    m_addFeatureBtn = new QPushButton("Add");
    m_removeFeatureBtn = new QPushButton("Remove");
    m_featureUpBtn = new QPushButton("Up");
    m_featureDownBtn = new QPushButton("Down");
    m_expandAllBtn = new QPushButton("Expand All");
    m_collapseAllBtn = new QPushButton("Collapse All");
    
    treeButtonLayout->addWidget(m_addFeatureBtn);
    treeButtonLayout->addWidget(m_removeFeatureBtn);
    treeButtonLayout->addSeparator();
    treeButtonLayout->addWidget(m_featureUpBtn);
    treeButtonLayout->addWidget(m_featureDownBtn);
    treeButtonLayout->addSeparator();
    treeButtonLayout->addWidget(m_expandAllBtn);
    treeButtonLayout->addWidget(m_collapseAllBtn);
    treeButtonLayout->addStretch();
    
    leftLayout->addLayout(treeButtonLayout);
    
    m_mainSplitter->addWidget(m_leftPanel);
    
    // 右侧面板 - 属性编辑
    m_rightPanel = new QWidget;
    QVBoxLayout* rightLayout = new QVBoxLayout(m_rightPanel);
    
    QLabel* propertyLabel = new QLabel("Feature Properties:");
    LA::Themes::ThemeManager::instance().applyThemeToWidget(propertyLabel, LA::Themes::ThemeManager::ComponentType::Label);
    QFont labelFont = LA::Themes::ThemeManager::instance().getFont(LA::Themes::ThemeManager::FontRole::Bold);
    propertyLabel->setFont(labelFont);
    propertyLabel->setContentsMargins(LA::Themes::ThemeManager::instance().getMetric(LA::Themes::ThemeManager::Metric::PaddingSmall), 
                                      LA::Themes::ThemeManager::instance().getMetric(LA::Themes::ThemeManager::Metric::PaddingSmall), 
                                      LA::Themes::ThemeManager::instance().getMetric(LA::Themes::ThemeManager::Metric::PaddingSmall), 
                                      LA::Themes::ThemeManager::instance().getMetric(LA::Themes::ThemeManager::Metric::PaddingSmall));
    rightLayout->addWidget(propertyLabel);
    
    m_propertyPanel = new FeaturePropertyPanel;
    rightLayout->addWidget(m_propertyPanel);
    
    m_mainSplitter->addWidget(m_rightPanel);
    
    // 设置分割器比例
    m_mainSplitter->setStretchFactor(0, 2);
    m_mainSplitter->setStretchFactor(1, 1);
    
    // 状态栏
    setupStatusBar();
    mainLayout->addWidget(m_statusBar);
}

void FeatureConfigWidget::setupToolBar()
{
    m_toolBar = new QToolBar;
    m_toolBar->setToolButtonStyle(Qt::ToolButtonTextBesideIcon);
    
    // Target选择器
    m_toolBar->addWidget(new QLabel("Target:"));
    m_targetSelector = new QComboBox;
    m_targetSelector->setMinimumWidth(150);
    m_toolBar->addWidget(m_targetSelector);
    m_toolBar->addSeparator();
    
    // 文件操作
    m_saveBtn = new QPushButton("Save");
    m_loadBtn = new QPushButton("Load");
    m_syncBtn = new QPushButton("Sync to Header");
    m_importBtn = new QPushButton("Import from Header");
    
    m_toolBar->addWidget(m_saveBtn);
    m_toolBar->addWidget(m_loadBtn);
    m_toolBar->addSeparator();
    m_toolBar->addWidget(m_syncBtn);
    m_toolBar->addWidget(m_importBtn);
    
    m_toolBar->addSeparator();
    
    // 权限信息
    m_permissionLabel = new QLabel("Role: Unknown");
    LA::Themes::ThemeManager::instance().applyThemeToWidget(m_permissionLabel, LA::Themes::ThemeManager::ComponentType::Label);
    m_permissionLabel->setStyleSheet(QString("color: %1; font-weight: bold;")
                                   .arg(LA::Themes::ThemeManager::instance().getSemanticColorString(LA::Themes::ThemeManager::ColorRole::Primary)));
    m_toolBar->addWidget(m_permissionLabel);
}

void FeatureConfigWidget::setupStatusBar()
{
    m_statusBar = new QStatusBar;
    m_statusLabel = new QLabel("Ready");
    m_statusBar->addWidget(m_statusLabel);
    
    // 添加右侧状态信息
    QLabel* changeLabel = new QLabel;
    m_statusBar->addPermanentWidget(changeLabel);
}

void FeatureConfigWidget::setupConnections()
{
    // Target选择器
    connect(m_targetSelector, QOverload<const QString&>::of(&QComboBox::currentTextChanged),
            this, &FeatureConfigWidget::onTargetChanged);
    
    // 功能树操作按钮
    connect(m_addFeatureBtn, &QPushButton::clicked, this, &FeatureConfigWidget::onAddFeature);
    connect(m_removeFeatureBtn, &QPushButton::clicked, this, &FeatureConfigWidget::onRemoveFeature);
    connect(m_featureUpBtn, &QPushButton::clicked, this, &FeatureConfigWidget::onFeatureUp);
    connect(m_featureDownBtn, &QPushButton::clicked, this, &FeatureConfigWidget::onFeatureDown);
    connect(m_expandAllBtn, &QPushButton::clicked, this, &FeatureConfigWidget::onExpandAll);
    connect(m_collapseAllBtn, &QPushButton::clicked, this, &FeatureConfigWidget::onCollapseAll);
    
    // 文件操作按钮
    connect(m_saveBtn, &QPushButton::clicked, this, &FeatureConfigWidget::onSaveConfig);
    connect(m_loadBtn, &QPushButton::clicked, this, &FeatureConfigWidget::onLoadConfig);
    connect(m_syncBtn, &QPushButton::clicked, this, &FeatureConfigWidget::onSyncToHeader);
    connect(m_importBtn, &QPushButton::clicked, this, &FeatureConfigWidget::onImportFromHeader);
    
    // 功能树事件
    if (m_featureTree) {
        connect(m_featureTree, &QTreeWidget::currentItemChanged, 
                this, &FeatureConfigWidget::onFeatureSelectionChanged);
        connect(m_featureTree, &QTreeWidget::itemChanged,
                this, &FeatureConfigWidget::onFeatureItemChanged);
        connect(m_featureTree, &FeatureTreeWidget::featuresMoved,
                this, [this]() {
                    m_hasUnsavedChanges = true;
                    emit configChanged();
                });
    }
    
    // 属性面板事件
    if (m_propertyPanel) {
        connect(m_propertyPanel, &FeaturePropertyPanel::propertyChanged,
                this, &FeatureConfigWidget::onFeaturePropertyChanged);
    }
}

void FeatureConfigWidget::updateFeatureTree()
{
    if (!m_featureTree) return;
    
    m_isLoading = true;
    m_featureTree->clear();
    
    QJsonArray features = m_configData.value("features").toArray();
    loadFeaturesFromJson(features);
    
    m_featureTree->expandAll();
    m_isLoading = false;
}

void FeatureConfigWidget::updatePropertyPanel()
{
    if (!m_propertyPanel) return;
    
    QTreeWidgetItem* current = m_featureTree->currentItem();
    if (current) {
        QJsonObject feature = getFeatureFromItem(current);
        m_propertyPanel->setFeature(feature);
        m_propertyPanel->setTarget(m_currentTarget);
    } else {
        m_propertyPanel->clear();
    }
}

void FeatureConfigWidget::updateStatusInfo()
{
    if (!m_statusLabel) return;
    
    QJsonArray features = m_configData.value("features").toArray();
    int featureCount = features.size();
    
    QString statusText = QString("Features: %1 | Target: %2")
                        .arg(featureCount)
                        .arg(m_currentTarget);
    
    if (m_hasUnsavedChanges) {
        statusText += " | *Modified*";
    }
    
    m_statusLabel->setText(statusText);
}

void FeatureConfigWidget::loadFeaturesFromJson(const QJsonArray& features)
{
    for (const auto& featureValue : features) {
        QJsonObject feature = featureValue.toObject();
        QTreeWidgetItem* item = createFeatureItem(feature);
        m_featureTree->addTopLevelItem(item);
        
        // 递归加载子功能项
        QJsonArray children = feature.value("children").toArray();
        if (!children.isEmpty()) {
            loadChildFeatures(item, children);
        }
    }
}

void FeatureConfigWidget::loadChildFeatures(QTreeWidgetItem* parentItem, const QJsonArray& children)
{
    for (const auto& childValue : children) {
        QJsonObject child = childValue.toObject();
        QTreeWidgetItem* childItem = createFeatureItem(child);
        parentItem->addChild(childItem);
        
        // 递归处理子功能项的子功能项
        QJsonArray grandChildren = child.value("children").toArray();
        if (!grandChildren.isEmpty()) {
            loadChildFeatures(childItem, grandChildren);
        }
    }
}

QJsonArray FeatureConfigWidget::saveFeaturesToJson() const
{
    QJsonArray features;
    
    for (int i = 0; i < m_featureTree->topLevelItemCount(); ++i) {
        QTreeWidgetItem* item = m_featureTree->topLevelItem(i);
        QJsonObject feature = getFeatureFromItem(item);
        
        // 递归保存子功能项
        QJsonArray children;
        saveChildFeatures(item, children);
        if (!children.isEmpty()) {
            feature["children"] = children;
        }
        
        features.append(feature);
    }
    
    return features;
}

void FeatureConfigWidget::saveChildFeatures(const QTreeWidgetItem* parentItem, QJsonArray& children) const
{
    for (int i = 0; i < parentItem->childCount(); ++i) {
        const QTreeWidgetItem* childItem = parentItem->child(i);
        QJsonObject child = getFeatureFromItem(childItem);
        
        // 递归处理子功能项
        QJsonArray grandChildren;
        saveChildFeatures(childItem, grandChildren);
        if (!grandChildren.isEmpty()) {
            child["children"] = grandChildren;
        }
        
        children.append(child);
    }
}

QTreeWidgetItem* FeatureConfigWidget::createFeatureItem(const QJsonObject& feature)
{
    QTreeWidgetItem* item = new QTreeWidgetItem;
    updateFeatureItem(item, feature);
    return item;
}

void FeatureConfigWidget::updateFeatureItem(QTreeWidgetItem* item, const QJsonObject& feature)
{
    if (!item) return;
    
    QString id = feature.value("id").toString();
    QString name = feature.value("name").toString();
    QString type = feature.value("type").toString("bool");
    
    // 获取当前target的值
    QJsonObject perTarget = feature.value("per_target").toObject();
    QJsonValue currentValue = perTarget.value(m_currentTarget);
    
    QString valueStr;
    if (type == "bool") {
        valueStr = currentValue.toBool() ? "true" : "false";
    } else if (type == "number") {
        valueStr = QString::number(currentValue.toDouble());
    } else {
        valueStr = currentValue.toString();
    }
    
    // 状态信息
    bool enabled = feature.value("enabled").toBool(true);
    QString status = enabled ? "Enabled" : "Disabled";
    
    item->setText(0, name.isEmpty() ? id : name);
    item->setText(1, type);
    item->setText(2, valueStr);
    item->setText(3, status);
    
    // 设置项目数据
    item->setData(0, Qt::UserRole, feature);
    
    // 设置图标和样式
    if (!enabled) {
        for (int i = 0; i < 4; ++i) {
            QFont font = item->font(i);
            font.setItalic(true);
            item->setFont(i, font);
            item->setForeground(i, QColor(128, 128, 128));
        }
    }
}

QJsonObject FeatureConfigWidget::getFeatureFromItem(const QTreeWidgetItem* item) const
{
    if (!item) return QJsonObject();
    
    return item->data(0, Qt::UserRole).toJsonObject();
}

bool FeatureConfigWidget::checkEditPermission() const
{
    if (!m_permissionManager) return true;
    return m_permissionManager->hasPermission(PermissionManager::EDIT_FEATURES);
}

bool FeatureConfigWidget::checkDeletePermission() const
{
    if (!m_permissionManager) return true;
    return m_permissionManager->hasPermission(PermissionManager::DELETE_FEATURES);
}

bool FeatureConfigWidget::checkSyncPermission() const
{
    if (!m_permissionManager) return true;
    return m_permissionManager->hasPermission(PermissionManager::SYNC_FILES);
}

// FeatureTreeWidget implementation
FeatureTreeWidget::FeatureTreeWidget(QWidget* parent)
    : QTreeWidget(parent)
{
    setDragDropMode(QAbstractItemView::InternalMove);
    setDefaultDropAction(Qt::MoveAction);
    setDragEnabled(true);
    setAcceptDrops(true);
    setDropIndicatorShown(true);
}

void FeatureTreeWidget::dragEnterEvent(QDragEnterEvent* event)
{
    if (event->mimeData()->hasFormat("application/x-qabstractitemmodeldatalist")) {
        event->acceptProposedAction();
    }
}

void FeatureTreeWidget::dragMoveEvent(QDragMoveEvent* event)
{
    QTreeWidgetItem* dropItem = itemAt(event->pos());
    if (dropItem && canDropOn(currentItem(), dropItem)) {
        event->acceptProposedAction();
    } else {
        event->ignore();
    }
}

void FeatureTreeWidget::dropEvent(QDropEvent* event)
{
    QTreeWidgetItem* dropItem = itemAt(event->pos());
    QTreeWidgetItem* dragItem = currentItem();
    
    if (dragItem && dropItem && canDropOn(dragItem, dropItem)) {
        // 执行移动操作
        moveFeature(dragItem, dropItem, 0);
        event->acceptProposedAction();
        emit featuresMoved();
    } else {
        event->ignore();
    }
}

void FeatureTreeWidget::startDrag(Qt::DropActions supportedActions)
{
    if (currentItem()) {
        QTreeWidget::startDrag(supportedActions);
    }
}

bool FeatureTreeWidget::canDropOn(QTreeWidgetItem* item, QTreeWidgetItem* target) const
{
    if (!item || !target || item == target) {
        return false;
    }
    
    // 检查是否会造成循环依赖
    QTreeWidgetItem* parent = target->parent();
    while (parent) {
        if (parent == item) {
            return false;
        }
        parent = parent->parent();
    }
    
    return true;
}

void FeatureTreeWidget::moveFeature(QTreeWidgetItem* item, QTreeWidgetItem* newParent, int index)
{
    // 从原位置移除
    QTreeWidgetItem* oldParent = item->parent();
    if (oldParent) {
        oldParent->removeChild(item);
    } else {
        int oldIndex = indexOfTopLevelItem(item);
        takeTopLevelItem(oldIndex);
    }
    
    // 添加到新位置
    if (newParent) {
        newParent->insertChild(index, item);
    } else {
        insertTopLevelItem(index, item);
    }
}

// FeaturePropertyPanel implementation  
FeaturePropertyPanel::FeaturePropertyPanel(QWidget* parent)
    : QWidget(parent)
    , m_layout(nullptr)
    , m_scrollContent(nullptr)
    , m_readOnly(false)
    , m_updating(false)
{
    setupUI();
}

void FeaturePropertyPanel::setFeature(const QJsonObject& feature)
{
    if (m_updating) return;
    
    m_updating = true;
    m_currentFeature = feature;
    updateUI();
    m_updating = false;
}

QJsonObject FeaturePropertyPanel::getFeature() const
{
    // 从编辑器收集当前值
    QJsonObject result = m_currentFeature;
    
    for (auto it = m_propertyEditors.begin(); it != m_propertyEditors.end(); ++it) {
        QString key = it.key();
        QWidget* editor = it.value();
        
        if (auto lineEdit = qobject_cast<QLineEdit*>(editor)) {
            result[key] = lineEdit->text();
        } else if (auto spinBox = qobject_cast<QSpinBox*>(editor)) {
            result[key] = spinBox->value();
        } else if (auto doubleSpinBox = qobject_cast<QDoubleSpinBox*>(editor)) {
            result[key] = doubleSpinBox->value();
        } else if (auto checkBox = qobject_cast<QCheckBox*>(editor)) {
            result[key] = checkBox->isChecked();
        } else if (auto textEdit = qobject_cast<QTextEdit*>(editor)) {
            result[key] = textEdit->toPlainText();
        }
    }
    
    return result;
}

void FeaturePropertyPanel::setTarget(const QString& target)
{
    m_currentTarget = target;
    updateUI();
}

void FeaturePropertyPanel::setReadOnly(bool readOnly)
{
    m_readOnly = readOnly;
    
    // 更新所有编辑器的只读状态
    for (QWidget* editor : m_propertyEditors) {
        if (auto lineEdit = qobject_cast<QLineEdit*>(editor)) {
            lineEdit->setReadOnly(readOnly);
        } else if (auto spinBox = qobject_cast<QSpinBox*>(editor)) {
            spinBox->setReadOnly(readOnly);
        } else if (auto doubleSpinBox = qobject_cast<QDoubleSpinBox*>(editor)) {
            doubleSpinBox->setReadOnly(readOnly);
        } else if (auto checkBox = qobject_cast<QCheckBox*>(editor)) {
            checkBox->setEnabled(!readOnly);
        } else if (auto textEdit = qobject_cast<QTextEdit*>(editor)) {
            textEdit->setReadOnly(readOnly);
        }
    }
}

void FeaturePropertyPanel::clear()
{
    m_currentFeature = QJsonObject();
    updateUI();
}

void FeaturePropertyPanel::onPropertyValueChanged()
{
    if (m_updating) return;
    emit propertyChanged();
}

void FeaturePropertyPanel::setupUI()
{
    // 创建滚动区域
    QScrollArea* scrollArea = new QScrollArea;
    scrollArea->setWidgetResizable(true);
    scrollArea->setFrameShape(QFrame::NoFrame);
    
    m_scrollContent = new QWidget;
    m_layout = new QVBoxLayout(m_scrollContent);
    m_layout->addStretch();
    
    scrollArea->setWidget(m_scrollContent);
    
    QVBoxLayout* mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(0, 0, 0, 0);
    mainLayout->addWidget(scrollArea);
}

void FeaturePropertyPanel::updateUI()
{
    // 清除现有的编辑器
    for (QWidget* editor : m_propertyEditors) {
        editor->deleteLater();
    }
    m_propertyEditors.clear();
    
    // 清除布局中的所有项目（除了最后的stretch）
    QLayoutItem* item;
    while (m_layout->count() > 1 && (item = m_layout->takeAt(0))) {
        if (item->widget()) {
            item->widget()->deleteLater();
        }
        delete item;
    }
    
    if (m_currentFeature.isEmpty()) {
        return;
    }
    
    createPropertyEditors(m_currentFeature);
}

void FeaturePropertyPanel::createPropertyEditors(const QJsonObject& feature)
{
    QFormLayout* formLayout = new QFormLayout;
    
    // 基本属性编辑器
    for (auto it = feature.begin(); it != feature.end(); ++it) {
        QString key = it.key();
        QJsonValue value = it.value();
        
        if (key == "per_target" || key == "children") {
            continue; // 这些字段需要特殊处理
        }
        
        QWidget* editor = nullptr;
        
        if (value.isBool()) {
            QCheckBox* checkBox = new QCheckBox;
            checkBox->setChecked(value.toBool());
            connect(checkBox, &QCheckBox::toggled, this, &FeaturePropertyPanel::onPropertyValueChanged);
            editor = checkBox;
        } else if (value.isDouble()) {
            QDoubleSpinBox* spinBox = new QDoubleSpinBox;
            spinBox->setRange(-999999.0, 999999.0);
            spinBox->setDecimals(6);
            spinBox->setValue(value.toDouble());
            connect(spinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
                    this, &FeaturePropertyPanel::onPropertyValueChanged);
            editor = spinBox;
        } else if (value.isString()) {
            QString stringValue = value.toString();
            if (stringValue.length() > 50 || stringValue.contains('\n')) {
                QTextEdit* textEdit = new QTextEdit;
                textEdit->setMaximumHeight(100);
                textEdit->setPlainText(stringValue);
                connect(textEdit, &QTextEdit::textChanged, this, &FeaturePropertyPanel::onPropertyValueChanged);
                editor = textEdit;
            } else {
                QLineEdit* lineEdit = new QLineEdit;
                lineEdit->setText(stringValue);
                connect(lineEdit, &QLineEdit::textChanged, this, &FeaturePropertyPanel::onPropertyValueChanged);
                editor = lineEdit;
            }
        }
        
        if (editor) {
            formLayout->addRow(key + ":", editor);
            m_propertyEditors[key] = editor;
        }
    }
    
    // 添加target特定值编辑器
    if (feature.contains("per_target") && !m_currentTarget.isEmpty()) {
        QJsonObject perTarget = feature.value("per_target").toObject();
        QJsonValue targetValue = perTarget.value(m_currentTarget);
        
        if (!targetValue.isUndefined()) {
            QGroupBox* targetGroup = new QGroupBox(QString("Target: %1").arg(m_currentTarget));
            QFormLayout* targetLayout = new QFormLayout(targetGroup);
            
            QWidget* targetEditor = nullptr;
            
            if (targetValue.isBool()) {
                QCheckBox* checkBox = new QCheckBox;
                checkBox->setChecked(targetValue.toBool());
                connect(checkBox, &QCheckBox::toggled, this, &FeaturePropertyPanel::onPropertyValueChanged);
                targetEditor = checkBox;
            } else if (targetValue.isDouble()) {
                QDoubleSpinBox* spinBox = new QDoubleSpinBox;
                spinBox->setRange(-999999.0, 999999.0);
                spinBox->setDecimals(6);
                spinBox->setValue(targetValue.toDouble());
                connect(spinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
                        this, &FeaturePropertyPanel::onPropertyValueChanged);
                targetEditor = spinBox;
            } else if (targetValue.isString()) {
                QLineEdit* lineEdit = new QLineEdit;
                lineEdit->setText(targetValue.toString());
                connect(lineEdit, &QLineEdit::textChanged, this, &FeaturePropertyPanel::onPropertyValueChanged);
                targetEditor = lineEdit;
            }
            
            if (targetEditor) {
                targetLayout->addRow("Value:", targetEditor);
                m_propertyEditors[QString("per_target.%1").arg(m_currentTarget)] = targetEditor;
            }
            
            m_layout->insertWidget(m_layout->count() - 1, targetGroup);
        }
    }
    
    // 添加基本属性表单
    QGroupBox* basicGroup = new QGroupBox("Basic Properties");
    basicGroup->setLayout(formLayout);
    m_layout->insertWidget(m_layout->count() - 1, basicGroup);
    
    // 设置只读状态
    setReadOnly(m_readOnly);
}