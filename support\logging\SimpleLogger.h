#pragma once

#include <QString>
#include <QDebug>
#include <QDateTime>

namespace LA {
namespace Support {
namespace Logging {

/**
 * @brief 简化的日志系统 - 最小可用实现
 */
class SimpleLogger {
public:
    enum Level {
        TRACE = 0,
        DEBUG = 1,
        INFO = 2,
        WARNING = 3,
        ERROR = 4,
        FATAL = 5
    };

    static void log(Level level, const QString& message) {
        QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss.zzz");
        QString levelStr = levelToString(level);
        qDebug() << QString("[%1] [%2] %3").arg(timestamp, levelStr, message);
    }

    static void trace(const QString& message) { log(TRACE, message); }
    static void debug(const QString& message) { log(DEBUG, message); }
    static void info(const QString& message) { log(INFO, message); }
    static void warning(const QString& message) { log(WARNING, message); }
    static void error(const QString& message) { log(ERROR, message); }
    static void fatal(const QString& message) { log(FATAL, message); }

private:
    static QString levelToString(Level level) {
        switch (level) {
            case TRACE: return "TRACE";
            case DEBUG: return "DEBUG";
            case INFO: return "INFO";
            case WARNING: return "WARN";
            case ERROR: return "ERROR";
            case FATAL: return "FATAL";
            default: return "UNKNOWN";
        }
    }
};

}  // namespace Logging
}  // namespace Support
}  // namespace LA

// 简化的宏定义 - 替代qLog.h
#define LA_LOG_TRACE(msg) LA::Support::Logging::SimpleLogger::trace(msg)
#define LA_LOG_DEBUG(msg) LA::Support::Logging::SimpleLogger::debug(msg)  
#define LA_LOG_INFO(msg) LA::Support::Logging::SimpleLogger::info(msg)
#define LA_LOG_WARNING(msg) LA::Support::Logging::SimpleLogger::warning(msg)
#define LA_LOG_ERROR(msg) LA::Support::Logging::SimpleLogger::error(msg)
#define LA_LOG_FATAL(msg) LA::Support::Logging::SimpleLogger::fatal(msg)