#pragma once

/**
 * @file ICommandSystem.h
 * @brief 指令系统接口定义
 * 
 * 指令系统遵循**最小模块原则**，专注于指令的生成和解析，不涉及：
 * - 设备管理（由DeviceRegistry负责）
 * - 数据传输（由IConnection负责）
 * - 协议编解码（由IProtocol负责）
 * 
 * 基于**单一来源原则**，从DeviceRegistry获取设备指令定义，
 * 使用**声明式映射**的静态指令模板进行指令构建。
 */

#include "../DataStructures/DeviceMappingTables.h"
#include <QString>
#include <QVariantMap>
#include <QDateTime>
#include <QSharedPointer>

// 前向声明
namespace LA {
namespace Communication {
namespace Registry {
    class IDeviceRegistry;
}
}
}
#include <QObject>

namespace LA {
namespace Communication {
namespace Command {

using namespace DataStructures;

/**
 * @brief 指令执行结果
 */
struct CommandResult {
    bool success;                   // 执行是否成功
    QString commandId;              // 指令ID
    QString deviceId;               // 目标设备ID
    QVariantMap responseData;       // 响应数据
    QString errorMessage;           // 错误信息
    int errorCode = 0;              // 错误代码
    qint64 executionTime = 0;       // 执行时间(ms)
    QDateTime timestamp;            // 时间戳
    
    CommandResult() : success(false), timestamp(QDateTime::currentDateTime()) {}
    CommandResult(bool ok, const QString& cmdId = "", const QString& devId = "")
        : success(ok), commandId(cmdId), deviceId(devId), timestamp(QDateTime::currentDateTime()) {}
};

/**
 * @brief 指令执行上下文
 */
struct CommandContext {
    QString deviceId;               // 目标设备ID
    DeviceType deviceType;          // 设备类型
    QString sessionId;              // 会话ID（用于跟踪）
    QVariantMap metadata;           // 上下文元数据
    int priority = 0;               // 指令优先级
    int timeout = 5000;             // 超时时间(ms)
    bool requiresResponse = true;   // 是否需要响应
    
    CommandContext() : deviceType(DeviceType::Unknown) {}
};

/**
 * @brief 构建的指令对象
 */
struct BuiltCommand {
    QString commandId;              // 指令ID
    QString deviceId;               // 目标设备ID
    DeviceType deviceType;          // 设备类型
    
    // 指令内容
    QString rawCommand;             // 原始指令字符串
    QByteArray binaryCommand;       // 二进制指令数据
    QVariantMap parameters;         // 指令参数
    
    // 执行属性
    CommandDefinition definition;   // 指令定义（来自映射表）
    CommandContext context;         // 执行上下文
    
    // 状态信息
    QDateTime createdTime;          // 创建时间
    QString createdBy;              // 创建者
    
    BuiltCommand() : deviceType(DeviceType::Unknown), createdTime(QDateTime::currentDateTime()) {}
};

/**
 * @brief 指令解析结果
 */
struct ParsedCommand {
    bool isValid;                   // 解析是否成功
    QString commandId;              // 识别出的指令ID
    QString deviceId;               // 源设备ID
    DeviceType deviceType;          // 设备类型
    
    // 解析出的数据
    QVariantMap parsedData;         // 解析出的数据字段
    QVariantMap rawFields;          // 原始字段
    
    // 解析信息
    QString parseMethod;            // 解析方法
    QDateTime parseTime;            // 解析时间
    QString errorMessage;           // 解析错误信息
    
    ParsedCommand() : isValid(false), deviceType(DeviceType::Unknown), parseTime(QDateTime::currentDateTime()) {}
};

/**
 * @brief 指令验证结果
 */
struct CommandValidation {
    bool isValid;                   // 验证是否通过
    QStringList errors;             // 错误列表
    QStringList warnings;           // 警告列表
    QStringList missingParameters;  // 缺失参数
    QStringList invalidParameters;  // 无效参数
    
    CommandValidation() : isValid(true) {}
};

/**
 * @brief 指令系统接口
 * 
 * 负责指令的生成、解析和验证，作为设备通信的指令层
 */
class ICommandSystem : public QObject
{
    Q_OBJECT

public:
    virtual ~ICommandSystem() = default;

    // === 指令构建接口 ===
    
    /**
     * @brief 构建指令
     * @param deviceId 目标设备ID
     * @param commandId 指令ID
     * @param parameters 指令参数
     * @param context 执行上下文
     * @return 构建的指令对象
     */
    virtual QSharedPointer<BuiltCommand> buildCommand(
        const QString& deviceId,
        const QString& commandId,
        const QVariantMap& parameters = QVariantMap(),
        const CommandContext& context = CommandContext()) = 0;

    /**
     * @brief 构建批量指令
     * @param deviceId 目标设备ID
     * @param commands 指令列表 [{commandId, parameters}]
     * @param context 执行上下文
     * @return 构建的指令列表
     */
    virtual QList<QSharedPointer<BuiltCommand>> buildBatchCommands(
        const QString& deviceId,
        const QList<QPair<QString, QVariantMap>>& commands,
        const CommandContext& context = CommandContext()) = 0;

    /**
     * @brief 根据模板构建指令
     * @param deviceId 目标设备ID
     * @param commandId 指令ID
     * @param templateData 模板数据
     * @return 构建的指令对象
     */
    virtual QSharedPointer<BuiltCommand> buildFromTemplate(
        const QString& deviceId,
        const QString& commandId,
        const QVariantMap& templateData) = 0;

    // === 指令解析接口 ===
    
    /**
     * @brief 解析响应数据
     * @param deviceId 源设备ID
     * @param commandId 对应的指令ID（可选，用于上下文）
     * @param responseData 响应数据
     * @return 解析结果
     */
    virtual ParsedCommand parseResponse(
        const QString& deviceId,
        const QString& commandId,
        const QByteArray& responseData) = 0;

    /**
     * @brief 解析原始指令
     * @param deviceId 源设备ID
     * @param rawData 原始数据
     * @return 解析结果
     */
    virtual ParsedCommand parseRawCommand(
        const QString& deviceId,
        const QByteArray& rawData) = 0;

    /**
     * @brief 尝试自动识别并解析数据
     * @param rawData 原始数据
     * @return 解析结果列表（可能匹配多个设备）
     */
    virtual QList<ParsedCommand> autoParseData(const QByteArray& rawData) = 0;

    // === 指令验证接口 ===
    
    /**
     * @brief 验证指令参数
     * @param deviceId 设备ID
     * @param commandId 指令ID
     * @param parameters 参数
     * @return 验证结果
     */
    virtual CommandValidation validateCommand(
        const QString& deviceId,
        const QString& commandId,
        const QVariantMap& parameters) = 0;

    /**
     * @brief 验证构建的指令
     * @param command 构建的指令
     * @return 验证结果
     */
    virtual CommandValidation validateBuiltCommand(const BuiltCommand& command) = 0;

    // === 指令查询接口 ===
    
    /**
     * @brief 获取设备支持的指令列表
     * @param deviceId 设备ID
     * @return 指令定义列表
     */
    virtual QList<CommandDefinition> getAvailableCommands(const QString& deviceId) = 0;

    /**
     * @brief 获取指令定义
     * @param deviceId 设备ID
     * @param commandId 指令ID
     * @return 指令定义
     */
    virtual CommandDefinition getCommandDefinition(const QString& deviceId, const QString& commandId) = 0;

    /**
     * @brief 检查设备是否支持指令
     * @param deviceId 设备ID
     * @param commandId 指令ID
     * @return 是否支持
     */
    virtual bool supportsCommand(const QString& deviceId, const QString& commandId) = 0;

    // === 模板管理接口 ===
    
    /**
     * @brief 注册指令模板
     * @param deviceType 设备类型
     * @param commandId 指令ID
     * @param templateString 模板字符串
     * @return 是否成功
     */
    virtual bool registerCommandTemplate(
        DeviceType deviceType,
        const QString& commandId,
        const QString& templateString) = 0;

    /**
     * @brief 获取指令模板
     * @param deviceType 设备类型
     * @param commandId 指令ID
     * @return 模板字符串
     */
    virtual QString getCommandTemplate(DeviceType deviceType, const QString& commandId) = 0;

    // === 统计和监控接口 ===
    
    /**
     * @brief 获取指令执行统计
     * @param deviceId 设备ID（空表示所有设备）
     * @return 统计信息
     */
    virtual QVariantMap getCommandStatistics(const QString& deviceId = QString()) = 0;

    /**
     * @brief 获取最近的指令历史
     * @param deviceId 设备ID
     * @param limit 返回数量限制
     * @return 指令历史列表
     */
    virtual QList<BuiltCommand> getRecentCommands(const QString& deviceId, int limit = 10) = 0;

    /**
     * @brief 清除指令历史
     * @param deviceId 设备ID（空表示所有设备）
     * @param olderThan 清除早于指定时间的记录
     */
    virtual void clearCommandHistory(const QString& deviceId = QString(), 
                                   const QDateTime& olderThan = QDateTime()) = 0;

signals:
    /**
     * @brief 指令构建完成信号
     * @param command 构建的指令
     */
    void commandBuilt(const BuiltCommand& command);

    /**
     * @brief 指令解析完成信号
     * @param parsed 解析结果
     */
    void commandParsed(const ParsedCommand& parsed);

    /**
     * @brief 指令验证失败信号
     * @param deviceId 设备ID
     * @param commandId 指令ID
     * @param validation 验证结果
     */
    void commandValidationFailed(const QString& deviceId, const QString& commandId, const CommandValidation& validation);

    /**
     * @brief 模板注册信号
     * @param deviceType 设备类型
     * @param commandId 指令ID
     */
    void templateRegistered(DeviceType deviceType, const QString& commandId);
};

/**
 * @brief 指令系统工厂接口
 */
class ICommandSystemFactory
{
public:
    virtual ~ICommandSystemFactory() = default;
    
    /**
     * @brief 创建指令系统实例
     * @param deviceRegistry 设备注册表（作为单一来源）
     * @return 指令系统智能指针
     */
    virtual QSharedPointer<ICommandSystem> createCommandSystem(
        QSharedPointer<Registry::IDeviceRegistry> deviceRegistry) = 0;
};

} // namespace Command
} // namespace Communication
} // namespace LA