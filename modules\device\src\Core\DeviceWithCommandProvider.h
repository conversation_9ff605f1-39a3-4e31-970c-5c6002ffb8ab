/**
 * @file DeviceWithCommandProvider.h
 * @brief 演示四层架构中指令组件的使用方式
 * 
 * 职责：
 * - 展示指令提供者如何作为独立组件集成到四层架构
 * - 演示配置驱动的指令系统
 * - 对比传统静态注册与新架构的差异
 */

#pragma once

#include "Device.h"
#include "../command/ICommandProvider.h"
#include <memory>

namespace LA::Device::Core {

/**
 * @brief 带指令提供者的设备类 - 四层架构演示
 * 
 * 展示如何将指令系统作为独立组件集成：
 * - Driver层使用指令提供者生成硬件指令
 * - Capability层使用指令提供者执行业务功能  
 * - Strategy层可以影响指令参数
 * - Script层可以动态调整指令行为
 */
class DeviceWithCommandProvider : public Device {
    Q_OBJECT

public:
    explicit DeviceWithCommandProvider(QObject* parent = nullptr);
    virtual ~DeviceWithCommandProvider() = default;

    // === 指令提供者管理 ===
    
    /**
     * @brief 设置指令提供者
     * @param provider 指令提供者实例
     */
    void setCommandProvider(std::unique_ptr<Command::ICommandProvider> provider);
    
    /**
     * @brief 获取指令提供者
     * @return 指令提供者指针
     */
    Command::ICommandProvider* getCommandProvider() const;
    
    /**
     * @brief 从配置创建指令提供者
     * @param deviceType 设备类型
     * @param commandConfig 指令配置
     * @return 是否成功
     */
    bool createCommandProvider(const QString& deviceType, const QVariantMap& commandConfig);

    // === 四层架构中的指令使用演示 ===
    
    /**
     * @brief Driver层使用指令提供者
     * @param command 指令名称
     * @param params 参数
     * @return 生成的指令
     */
    QByteArray generateDriverCommand(const QString& command, const QVariantMap& params = {});
    
    /**
     * @brief Capability层使用指令提供者
     * @param capability 能力名称
     * @param action 动作
     * @param params 参数
     * @return 执行结果
     */
    QVariantMap executeCapabilityWithCommands(const QString& capability, 
                                             const QString& action,
                                             const QVariantMap& params = {});
    
    /**
     * @brief Strategy层影响指令参数
     * @param strategy 策略名称
     * @param originalParams 原始参数
     * @return 调整后的参数
     */
    QVariantMap applyStrategyToCommandParams(const QString& strategy, 
                                           const QVariantMap& originalParams);
    
    /**
     * @brief Script层动态调整指令
     * @param scriptEvent 脚本事件
     * @param commandId 指令ID
     * @param params 参数
     * @return 调整后的指令参数
     */
    QVariantMap scriptAdjustCommand(const QString& scriptEvent,
                                   const QString& commandId,
                                   const QVariantMap& params);

    // === 配置驱动的指令系统演示 ===
    
    /**
     * @brief 从配置文件加载指令定义
     * @param configFile 配置文件路径
     * @return 是否成功
     */
    bool loadCommandConfiguration(const QString& configFile);
    
    /**
     * @brief 获取当前指令配置
     * @return 指令配置
     */
    QVariantMap getCommandConfiguration() const;
    
    /**
     * @brief 热更新指令配置
     * @param newConfig 新配置
     * @return 是否成功
     */
    bool updateCommandConfiguration(const QVariantMap& newConfig);

Q_SIGNALS:
    void commandGenerated(const QString& commandId, const QByteArray& command);
    void commandExecuted(const QString& commandId, const QVariantMap& result);
    void commandConfigurationChanged(const QVariantMap& newConfig);

private slots:
    void onCommandProviderError(const QString& error);
    void onCommandResponse(const Command::CommandResult& result);

private:
    std::unique_ptr<Command::ICommandProvider> m_commandProvider;
    QVariantMap m_commandConfiguration;
    
    /**
     * @brief 验证指令提供者
     * @return 是否有效
     */
    bool validateCommandProvider();
    
    /**
     * @brief 连接指令提供者信号
     */
    void connectCommandProviderSignals();
    
    /**
     * @brief 断开指令提供者信号
     */
    void disconnectCommandProviderSignals();
};

} // namespace LA::Device::Core

// =============================================================================
// 对比分析：传统静态注册 vs 新四层架构
// =============================================================================

/**
 * 传统方式 (UnifiedCommandSystem)：
 * 
 * ```cpp
 * // 静态方法调用，紧耦合
 * QByteArray command = UnifiedCommandSystem::generateCommand(deviceId, "START_MEASURE", params);
 * CommandResult result = UnifiedCommandSystem::parseResponse(deviceId, response);
 * 
 * // 问题：
 * - 全局状态，难以测试
 * - 设备与指令系统强耦合
 * - 无法运行时替换指令实现
 * - 不符合四层架构的组合原则
 * ```
 * 
 * 新四层架构方式：
 * 
 * ```cpp
 * // 组件化使用，松耦合
 * auto commandProvider = Command::CommandProviderFactory::createProvider("SPRM", config);
 * device->setCommandProvider(std::move(commandProvider));
 * 
 * // 各层独立使用指令提供者
 * QByteArray command = device->generateDriverCommand("START_MEASURE", params);
 * QVariantMap result = device->executeCapabilityWithCommands("ranging", "start", params);
 * 
 * // 优势：
 * - 组件化设计，易于测试
 * - 松耦合，可替换实现
 * - 配置驱动，支持热更新
 * - 符合四层架构组合原则
 * - 每层都可以独立使用指令服务
 * ```
 */