#pragma once

/**
 * @file IDataFlowManager.h
 * @brief 数据流管理器接口定义
 * 
 * DataFlowManager遵循**组合协调原则**，作为数据流的核心协调器，不重复实现功能：
 * - 协调DeviceRegistry（设备管理）
 * - 协调CommandSystem（指令系统）  
 * - 协调IConnection（数据连接）
 * - 协调IProtocol（协议处理）
 * 
 * 基于**最小模块原则**，专注于组合和协调，提供：
 * - 设备到连接的映射管理
 * - 协议到设备的绑定管理  
 * - 端到端的数据流处理
 * - 多设备并发通信协调
 * - 统一的错误处理和状态管理
 */

#include "../Registry/IDeviceRegistry.h"
#include "../Command/ICommandSystem.h"
#include "../Connection/IConnection.h"
#include "../Protocol/IProtocol.h"
#include "../DataStructures/DeviceMappingTables.h"
#include <QString>
#include <QObject>
#include <QSharedPointer>
#include <QVariantMap>
#include <QDateTime>

namespace LA {
namespace Communication {
namespace DataFlow {

using namespace Registry;
using namespace Command;
using namespace Connection;
using namespace Protocol;
using namespace DataStructures;

/**
 * @brief 数据流状态
 */
enum class DataFlowState {
    Idle = 0,           // 空闲
    Initializing,       // 初始化中
    Ready,              // 就绪
    Processing,         // 处理中
    Error,              // 错误
    Stopping,           // 停止中
    Stopped             // 已停止
};

/**
 * @brief 数据流配置
 */
struct DataFlowConfig {
    QString flowId;                     // 流ID
    QString deviceId;                   // 设备ID
    QString connectionId;               // 连接ID
    QString protocolId;                 // 协议ID
    
    // 流控制配置
    bool autoStart = true;              // 自动启动
    bool autoReconnect = true;          // 自动重连
    int maxRetries = 3;                 // 最大重试次数
    int retryInterval = 1000;           // 重试间隔(ms)
    
    // 性能配置
    int maxConcurrentOperations = 10;   // 最大并发操作数
    int operationTimeout = 5000;        // 操作超时(ms)
    int bufferSize = 8192;              // 缓冲区大小
    
    DataFlowConfig() = default;
    explicit DataFlowConfig(const QString& id, const QString& devId, 
                           const QString& connId, const QString& protId)
        : flowId(id), deviceId(devId), connectionId(connId), protocolId(protId) {}
};

/**
 * @brief 数据流操作结果
 */
struct DataFlowResult {
    bool success;                       // 操作是否成功
    QString flowId;                     // 流ID
    QString operationType;              // 操作类型
    QVariantMap resultData;             // 结果数据
    QString errorMessage;               // 错误信息
    int errorCode = 0;                  // 错误代码
    qint64 processingTime = 0;          // 处理时间(ms)
    QDateTime timestamp;                // 时间戳
    
    DataFlowResult() : success(false), timestamp(QDateTime::currentDateTime()) {}
    DataFlowResult(bool ok, const QString& id = "", const QString& op = "")
        : success(ok), flowId(id), operationType(op), timestamp(QDateTime::currentDateTime()) {}
};

/**
 * @brief 数据流统计信息
 */
struct DataFlowStatistics {
    QString flowId;                     // 流ID
    quint64 operationsProcessed = 0;    // 处理的操作数
    quint64 operationsSucceeded = 0;    // 成功的操作数
    quint64 operationsFailed = 0;       // 失败的操作数
    quint64 bytesProcessed = 0;         // 处理的字节数
    QDateTime startTime;                // 开始时间
    QDateTime lastActivity;             // 最后活动时间
    double averageProcessingTime = 0.0; // 平均处理时间(ms)
    double throughput = 0.0;            // 吞吐量(ops/sec)
    
    DataFlowStatistics() : startTime(QDateTime::currentDateTime()),
                          lastActivity(QDateTime::currentDateTime()) {}
    explicit DataFlowStatistics(const QString& id) : flowId(id),
        startTime(QDateTime::currentDateTime()), lastActivity(QDateTime::currentDateTime()) {}
};

/**
 * @brief 数据流管理器接口
 * 
 * 作为组合协调器，统一管理设备通信的完整数据流
 */
class IDataFlowManager : public QObject
{
    Q_OBJECT

public:
    virtual ~IDataFlowManager() = default;

    // === 初始化和配置接口 ===
    
    /**
     * @brief 初始化数据流管理器
     * @param deviceRegistry 设备注册表
     * @param commandSystem 指令系统
     * @param connectionFactory 连接工厂
     * @param protocolFactory 协议工厂
     * @return 是否成功
     */
    virtual bool initialize(
        QSharedPointer<IDeviceRegistry> deviceRegistry,
        QSharedPointer<ICommandSystem> commandSystem,
        QSharedPointer<IConnectionFactory> connectionFactory,
        QSharedPointer<IProtocolFactory> protocolFactory) = 0;
    
    /**
     * @brief 创建数据流
     * @param config 数据流配置
     * @return 操作结果
     */
    virtual DataFlowResult createDataFlow(const DataFlowConfig& config) = 0;
    
    /**
     * @brief 删除数据流
     * @param flowId 流ID
     * @return 操作结果
     */
    virtual DataFlowResult removeDataFlow(const QString& flowId) = 0;
    
    /**
     * @brief 获取所有数据流ID
     */
    virtual QStringList getAllDataFlowIds() const = 0;
    
    /**
     * @brief 获取数据流配置
     * @param flowId 流ID
     * @return 配置信息
     */
    virtual DataFlowConfig getDataFlowConfig(const QString& flowId) const = 0;

    // === 数据流控制接口 ===
    
    /**
     * @brief 启动数据流
     * @param flowId 流ID
     * @return 操作结果
     */
    virtual DataFlowResult startDataFlow(const QString& flowId) = 0;
    
    /**
     * @brief 停止数据流
     * @param flowId 流ID
     * @return 操作结果
     */
    virtual DataFlowResult stopDataFlow(const QString& flowId) = 0;
    
    /**
     * @brief 重启数据流
     * @param flowId 流ID
     * @return 操作结果
     */
    virtual DataFlowResult restartDataFlow(const QString& flowId) = 0;
    
    /**
     * @brief 获取数据流状态
     * @param flowId 流ID
     * @return 状态
     */
    virtual DataFlowState getDataFlowState(const QString& flowId) const = 0;
    
    /**
     * @brief 检查数据流是否活跃
     * @param flowId 流ID
     * @return 是否活跃
     */
    virtual bool isDataFlowActive(const QString& flowId) const = 0;

    // === 数据操作接口 ===
    
    /**
     * @brief 发送指令
     * @param flowId 流ID
     * @param commandId 指令ID
     * @param parameters 参数
     * @param context 上下文
     * @return 操作结果
     */
    virtual DataFlowResult sendCommand(
        const QString& flowId,
        const QString& commandId,
        const QVariantMap& parameters = QVariantMap(),
        const CommandContext& context = CommandContext()) = 0;
    
    /**
     * @brief 发送批量指令
     * @param flowId 流ID
     * @param commands 指令列表 [{commandId, parameters}]
     * @return 操作结果列表
     */
    virtual QList<DataFlowResult> sendBatchCommands(
        const QString& flowId,
        const QList<QPair<QString, QVariantMap>>& commands) = 0;
    
    /**
     * @brief 发送原始数据
     * @param flowId 流ID
     * @param data 原始数据
     * @param frameType 帧类型
     * @return 操作结果
     */
    virtual DataFlowResult sendRawData(
        const QString& flowId,
        const QByteArray& data,
        FrameType frameType = FrameType::Request) = 0;
    
    /**
     * @brief 读取数据
     * @param flowId 流ID
     * @param timeout 超时时间(ms)
     * @return 操作结果
     */
    virtual DataFlowResult readData(const QString& flowId, int timeout = 5000) = 0;

    // === 设备和协议管理接口 ===
    
    /**
     * @brief 绑定设备到数据流
     * @param flowId 流ID
     * @param deviceId 设备ID
     * @return 操作结果
     */
    virtual DataFlowResult bindDevice(const QString& flowId, const QString& deviceId) = 0;
    
    /**
     * @brief 解绑设备
     * @param flowId 流ID
     * @return 操作结果
     */
    virtual DataFlowResult unbindDevice(const QString& flowId) = 0;
    
    /**
     * @brief 获取数据流绑定的设备ID
     * @param flowId 流ID
     * @return 设备ID
     */
    virtual QString getBoundDeviceId(const QString& flowId) const = 0;
    
    /**
     * @brief 更新连接配置
     * @param flowId 流ID
     * @param connectionConfig 连接配置
     * @return 操作结果
     */
    virtual DataFlowResult updateConnectionConfig(
        const QString& flowId,
        const SimpleConnectionConfig& connectionConfig) = 0;
    
    /**
     * @brief 更新协议配置
     * @param flowId 流ID
     * @param protocolConfig 协议配置
     * @return 操作结果
     */
    virtual DataFlowResult updateProtocolConfig(
        const QString& flowId,
        const SimpleProtocolConfig& protocolConfig) = 0;

    // === 监控和统计接口 ===
    
    /**
     * @brief 获取数据流统计信息
     * @param flowId 流ID（空表示所有流）
     * @return 统计信息
     */
    virtual QList<DataFlowStatistics> getStatistics(const QString& flowId = QString()) const = 0;
    
    /**
     * @brief 重置统计信息
     * @param flowId 流ID（空表示所有流）
     */
    virtual void resetStatistics(const QString& flowId = QString()) = 0;
    
    /**
     * @brief 获取活跃的数据流数量
     */
    virtual int getActiveDataFlowCount() const = 0;
    
    /**
     * @brief 获取总的处理操作数
     */
    virtual quint64 getTotalOperationsProcessed() const = 0;
    
    /**
     * @brief 获取管理器状态信息
     */
    virtual QVariantMap getManagerStatus() const = 0;

    // === 高级功能接口 ===
    
    /**
     * @brief 设置数据流优先级
     * @param flowId 流ID
     * @param priority 优先级（数字越大优先级越高）
     * @return 操作结果
     */
    virtual DataFlowResult setDataFlowPriority(const QString& flowId, int priority) = 0;
    
    /**
     * @brief 暂停数据流
     * @param flowId 流ID
     * @return 操作结果
     */
    virtual DataFlowResult pauseDataFlow(const QString& flowId) = 0;
    
    /**
     * @brief 恢复数据流
     * @param flowId 流ID
     * @return 操作结果
     */
    virtual DataFlowResult resumeDataFlow(const QString& flowId) = 0;
    
    /**
     * @brief 检查数据流健康状态
     * @param flowId 流ID
     * @return 健康状态详情
     */
    virtual QVariantMap checkDataFlowHealth(const QString& flowId) = 0;
    
    /**
     * @brief 执行数据流诊断
     * @param flowId 流ID
     * @return 诊断报告
     */
    virtual QVariantMap runDataFlowDiagnostics(const QString& flowId) = 0;

signals:
    /**
     * @brief 数据流状态变化信号
     * @param flowId 流ID
     * @param state 新状态
     * @param previousState 之前状态
     */
    void dataFlowStateChanged(const QString& flowId, DataFlowState state, DataFlowState previousState);
    
    /**
     * @brief 数据流创建信号
     * @param flowId 流ID
     * @param config 配置
     */
    void dataFlowCreated(const QString& flowId, const DataFlowConfig& config);
    
    /**
     * @brief 数据流删除信号
     * @param flowId 流ID
     */
    void dataFlowRemoved(const QString& flowId);
    
    /**
     * @brief 操作完成信号
     * @param result 操作结果
     */
    void operationCompleted(const DataFlowResult& result);
    
    /**
     * @brief 数据接收信号
     * @param flowId 流ID
     * @param data 接收的数据
     * @param frameType 帧类型
     */
    void dataReceived(const QString& flowId, const QVariantMap& data, FrameType frameType);
    
    /**
     * @brief 数据发送信号
     * @param flowId 流ID
     * @param data 发送的数据
     * @param result 发送结果
     */
    void dataSent(const QString& flowId, const QByteArray& data, const TransferResult& result);
    
    /**
     * @brief 错误发生信号
     * @param flowId 流ID
     * @param errorMessage 错误信息
     * @param errorCode 错误代码
     */
    void errorOccurred(const QString& flowId, const QString& errorMessage, int errorCode);
    
    /**
     * @brief 统计信息更新信号
     * @param flowId 流ID
     * @param statistics 统计信息
     */
    void statisticsUpdated(const QString& flowId, const DataFlowStatistics& statistics);
};

/**
 * @brief 数据流管理器工厂接口
 * 
 * 用于创建数据流管理器实例
 */
class IDataFlowManagerFactory
{
public:
    virtual ~IDataFlowManagerFactory() = default;
    
    /**
     * @brief 创建数据流管理器
     * @param managerId 管理器ID
     * @return 管理器智能指针
     */
    virtual QSharedPointer<IDataFlowManager> createDataFlowManager(
        const QString& managerId = "default") = 0;
    
    /**
     * @brief 获取默认配置
     * @return 默认配置
     */
    virtual QVariantMap getDefaultManagerConfig() const = 0;
    
    /**
     * @brief 验证配置
     * @param config 配置
     * @return 是否有效
     */
    virtual bool validateManagerConfig(const QVariantMap& config) const = 0;
};

} // namespace DataFlow
} // namespace Communication
} // namespace LA