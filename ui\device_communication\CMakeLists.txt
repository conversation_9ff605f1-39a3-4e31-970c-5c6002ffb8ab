# Device Communication UI Library
# Linus式设计：UI层专门负责用户界面，不包含业务逻辑

cmake_minimum_required(VERSION 3.16)
project(LA_DeviceCommunicationUI VERSION 1.0.0 LANGUAGES CXX)

# C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Qt配置
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)

# 查找Qt依赖 - UI库可以依赖Widgets
find_package(Qt5 REQUIRED COMPONENTS Core Widgets Network)

# UI组件源文件 - 包含现有文件和新增的适配器
set(UI_SOURCES
    src/widgets/DevicePortWidget.cpp
    # src/widgets/PortListWidget.cpp  # 暂时移除，需要重构接口匹配
    src/panels/DeviceCommunicationPanel.cpp
    src/adapters/LegacyDeviceUiAdapter.cpp  # 新增：遗留UI适配器
)

# UI组件头文件 - 包含现有文件和新增的适配器
set(UI_HEADERS
    include/LA/UI/DeviceCommunication/widgets/DevicePortWidget.h
    # include/LA/UI/DeviceCommunication/widgets/PortListWidget.h  # 暂时移除，需要重构接口匹配
    include/LA/UI/DeviceCommunication/panels/DeviceCommunicationPanel.h
    include/LA/UI/DeviceCommunication/adapters/LegacyDeviceUiAdapter.h  # 新增：遗留UI适配器
)

# 创建UI库
add_library(LA_device_communication_ui_lib SHARED
    ${UI_SOURCES}
    ${UI_HEADERS}
)

# 链接依赖 - Linus: "UI depends on mechanism, not the other way around"
target_link_libraries(LA_device_communication_ui_lib
    PUBLIC
        Qt5::Core
        Qt5::Widgets
        Qt5::Network
        
        # 依赖机制层（纯逻辑库）
        LA_communication_lib        # 通信机制
        LA_device_management_lib    # Linus: "UI层需要协调器"
        
        # UI基础设施
        LA_rightsidebar_lib        # 右侧边栏框架
        LA_themes_lib              # 主题系统
        
    # PRIVATE
    #     LA::Foundation  # 暂时注释，等待Foundation库实现
)

# 设置包含目录
target_include_directories(LA_device_communication_ui_lib
    PUBLIC
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
        $<INSTALL_INTERFACE:include>
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}/src
        
        # 访问机制层接口
        ${CMAKE_SOURCE_DIR}/infrastructure/communication/include
        ${CMAKE_SOURCE_DIR}/modules/device_management/include  # 新增：设备管理接口
        ${CMAKE_SOURCE_DIR}/support/foundation
        
        # UI框架接口
        ${CMAKE_SOURCE_DIR}/core/rightSidebar/include
        ${CMAKE_SOURCE_DIR}/ui/themes/include
)

# 包含LA库工具函数
include(${CMAKE_CURRENT_SOURCE_DIR}/../../cmake/LALibraryUtils.cmake)

# 设置库属性
set_target_properties(LA_device_communication_ui_lib PROPERTIES
    OUTPUT_NAME "LA_device_communication_ui"
    VERSION ${PROJECT_VERSION}
    SOVERSION 1
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
    CXX_EXTENSIONS OFF
    AUTOMOC ON
    AUTOUIC ON
    AUTORCC ON
)

# 注册为LA共享库，确保拷贝到bin目录
register_la_shared_library(LA_device_communication_ui_lib)

# 输出配置信息
message(STATUS "Device Communication UI Library Configuration:")
message(STATUS "  Library: LA_device_communication_ui_lib")
message(STATUS "  Version: ${PROJECT_VERSION}")
message(STATUS "  Purpose: UI layer for device and communication management")
message(STATUS "  Dependencies:")
message(STATUS "    - Qt5::Widgets (UI framework)")
message(STATUS "    - LA_communication_lib (communication mechanisms)")
message(STATUS "    - LA_device_management_lib (device management mechanisms) [PENDING]")
message(STATUS "    - LA_rightsidebar_lib (UI framework)")
message(STATUS "  Features:")
message(STATUS "    - Device-Port unified view")
message(STATUS "    - Auto-connection interface")
message(STATUS "    - Real-time status display")
message(STATUS "    - Right sidebar integration")
message(STATUS "    - Theme system support")

# Linus: "Make it easy to exclude UI when building server applications"
if(BUILD_UI_COMPONENTS)
    message(STATUS "  UI components: ENABLED")
else()
    message(STATUS "  UI components: DISABLED (server build)")
endif()