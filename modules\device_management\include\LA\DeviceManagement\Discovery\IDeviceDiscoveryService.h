#pragma once

#include "../Core/DeviceManagementTypes.h"
#include <QObject>
#include <QTimer>
#include <QMutex>
#include <memory>

namespace LA {
namespace DeviceManagement {
namespace Discovery {

/**
 * @brief 设备发现服务接口
 * 
 * 提供设备发现、监控和自动注册功能
 */
class IDeviceDiscoveryService : public QObject {
    Q_OBJECT

public:
    explicit IDeviceDiscoveryService(QObject* parent = nullptr) : QObject(parent) {}
    virtual ~IDeviceDiscoveryService() = default;

    /**
     * @brief 初始化发现服务
     * @param config 发现配置
     * @return 初始化是否成功
     */
    virtual bool initialize(const DiscoveryConfig& config = DiscoveryConfig()) = 0;

    /**
     * @brief 关闭发现服务
     */
    virtual void shutdown() = 0;

    /**
     * @brief 启动设备发现
     * @return 启动是否成功
     */
    virtual bool startDiscovery() = 0;

    /**
     * @brief 停止设备发现
     */
    virtual void stopDiscovery() = 0;

    /**
     * @brief 执行一次性设备扫描
     * @return 发现的设备数量
     */
    virtual int performScan() = 0;

    /**
     * @brief 添加发现策略
     * @param strategy 发现策略
     * @return 添加是否成功
     */
    virtual bool addDiscoveryStrategy(std::shared_ptr<IDiscoveryStrategy> strategy) = 0;

    /**
     * @brief 移除发现策略
     * @param strategyId 策略ID
     * @return 移除是否成功
     */
    virtual bool removeDiscoveryStrategy(const QString& strategyId) = 0;

    /**
     * @brief 获取发现的设备列表
     * @return 发现的设备信息列表
     */
    virtual QList<DeviceInfo> getDiscoveredDevices() const = 0;

    /**
     * @brief 获取指定类型的已发现设备
     * @param deviceType 设备类型
     * @return 该类型的设备列表
     */
    virtual QList<DeviceInfo> getDiscoveredDevicesByType(const QString& deviceType) const = 0;

    /**
     * @brief 检查设备是否已发现
     * @param deviceId 设备ID
     * @return 是否已发现
     */
    virtual bool isDeviceDiscovered(const QString& deviceId) const = 0;

    /**
     * @brief 获取发现统计信息
     * @return 统计信息
     */
    virtual QVariantMap getDiscoveryStatistics() const = 0;

    /**
     * @brief 设置发现配置
     * @param config 发现配置
     */
    virtual void setDiscoveryConfig(const DiscoveryConfig& config) = 0;

    /**
     * @brief 获取发现配置
     * @return 发现配置
     */
    virtual DiscoveryConfig getDiscoveryConfig() const = 0;

    /**
     * @brief 获取服务状态
     * @return 服务状态
     */
    virtual ServiceState getServiceState() const = 0;

signals:
    /**
     * @brief 设备发现信号
     * @param device 发现的设备信息
     */
    void deviceDiscovered(const DeviceInfo& device);

    /**
     * @brief 设备丢失信号
     * @param deviceId 丢失的设备ID
     */
    void deviceLost(const QString& deviceId);

    /**
     * @brief 发现完成信号
     * @param deviceCount 发现的设备数量
     */
    void discoveryCompleted(int deviceCount);

    /**
     * @brief 发现错误信号
     * @param error 错误信息
     */
    void discoveryError(const QString& error);

    /**
     * @brief 服务状态改变信号
     * @param state 新状态
     */
    void serviceStateChanged(ServiceState state);
};

/**
 * @brief 设备发现策略接口
 */
class IDiscoveryStrategy : public QObject {
    Q_OBJECT

public:
    explicit IDiscoveryStrategy(QObject* parent = nullptr) : QObject(parent) {}
    virtual ~IDiscoveryStrategy() = default;

    /**
     * @brief 获取策略ID
     * @return 策略ID
     */
    virtual QString getStrategyId() const = 0;

    /**
     * @brief 获取策略名称
     * @return 策略名称
     */
    virtual QString getStrategyName() const = 0;

    /**
     * @brief 获取支持的设备类型
     * @return 支持的设备类型列表
     */
    virtual QStringList getSupportedDeviceTypes() const = 0;

    /**
     * @brief 初始化策略
     * @param config 策略配置
     * @return 初始化是否成功
     */
    virtual bool initialize(const QVariantMap& config = QVariantMap()) = 0;

    /**
     * @brief 执行设备发现
     * @return 发现的设备列表
     */
    virtual QList<DeviceInfo> discoverDevices() = 0;

    /**
     * @brief 验证设备是否仍可用
     * @param device 设备信息
     * @return 是否仍可用
     */
    virtual bool verifyDevice(const DeviceInfo& device) = 0;

    /**
     * @brief 获取策略统计信息
     * @return 统计信息
     */
    virtual QVariantMap getStatistics() const = 0;

signals:
    /**
     * @brief 设备发现信号
     * @param device 发现的设备
     */
    void deviceFound(const DeviceInfo& device);

    /**
     * @brief 策略错误信号
     * @param error 错误信息
     */
    void strategyError(const QString& error);
};

/**
 * @brief 创建设备发现服务
 * @param parent 父对象
 * @return 设备发现服务实例
 */
std::shared_ptr<IDeviceDiscoveryService> createDeviceDiscoveryService(QObject* parent = nullptr);

} // namespace Discovery
} // namespace DeviceManagement
} // namespace LA