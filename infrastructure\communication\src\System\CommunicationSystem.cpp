#include <LA/Communication/System/CommunicationSystem.h>
#include <LA/Communication/Manager/ICommunicationManager.h>
#include <LA/Communication/PortManagement/IPortManager.h>
#include <LA/Communication/Protocol/IProtocol.h>
#include <LA/Communication/Connection/IConnection.h>
#include <LA/Communication/Command/ICommandHandler.h>
#include <LA/Communication/Session/ICommunicationSession.h>
#include <QDebug>
#include <QMutexLocker>
#include <QDateTime>
#include <QTimer>

namespace LA {
namespace Communication {
namespace System {

/**
 * @brief Linus式通信系统实现
 * 
 * 系统级整合所有Layer 1-3组件的实现
 */

CommunicationSystem::CommunicationSystem(QObject* parent)
    : QObject(parent)
    , m_initialized(false)
{
    resetSystemStatistics();
}

CommunicationSystem::~CommunicationSystem() {
    if (m_initialized) {
        shutdown();
    }
}

// === 系统生命周期 ===

SimpleResult CommunicationSystem::initialize(const ConfigParameters& config) {
    QMutexLocker locker(&m_mutex);
    
    if (m_initialized) {
        return SimpleResult::success(true);
    }
    
    m_lastError.clear();
    m_systemConfig = config;
    
    // 初始化核心组件
    initializeComponents();
    
    // 初始化通信管理器
    if (m_communicationManager) {
        auto result = m_communicationManager->initialize(config);
        if (!result.isSuccess) {
            m_lastError = QString("Failed to initialize communication manager: %1").arg(result.message);
            return SimpleResult::failure(m_lastError);
        }
    }
    
    // 初始化端口管理器
    if (m_portManager) {
        auto result = m_portManager->initialize(config);
        if (!result.isSuccess) {
            m_lastError = QString("Failed to initialize port manager: %1").arg(result.message);
            return SimpleResult::failure(m_lastError);
        }
    }
    
    // 连接管理器信号
    connectManagerSignals();
    
    m_initialized = true;
    emit systemStatusChanged(true);
    
    qDebug() << "Communication system initialized successfully";
    return SimpleResult::success(true);
}

SimpleResult CommunicationSystem::shutdown() {
    QMutexLocker locker(&m_mutex);
    
    if (!m_initialized) {
        return SimpleResult::success(true);
    }
    
    // 断开信号连接
    disconnectManagerSignals();
    
    // 关闭所有连接
    if (m_communicationManager) {
        m_communicationManager->closeAllSessions();
        m_communicationManager->shutdown();
    }
    
    // 关闭端口管理器
    if (m_portManager) {
        m_portManager->shutdown();
    }
    
    // 清理组件
    shutdownComponents();
    
    m_initialized = false;
    emit systemStatusChanged(false);
    
    qDebug() << "Communication system shutdown completed";
    return SimpleResult::success(true);
}

bool CommunicationSystem::isInitialized() const {
    QMutexLocker locker(&m_mutex);
    return m_initialized;
}

// === 高级通信接口 ===

SimpleResult CommunicationSystem::createConnection(const QString& connectionId,
                                                  const QString& portName,
                                                  ProtocolType protocolType,
                                                  const ConfigParameters& config) {
    QMutexLocker locker(&m_mutex);
    
    if (!m_initialized) {
        m_lastError = "System not initialized";
        return SimpleResult::failure(m_lastError);
    }
    
    if (!m_communicationManager) {
        m_lastError = "Communication manager not available";
        return SimpleResult::failure(m_lastError);
    }
    
    if (connectionId.isEmpty()) {
        m_lastError = "Empty connection ID";
        return SimpleResult::failure(m_lastError);
    }
    
    // 准备会话配置
    ConfigParameters sessionConfig = config;
    sessionConfig["portName"] = portName;
    sessionConfig["protocolType"] = static_cast<int>(protocolType);
    
    // 确定会话类型
    QString sessionType = "basic";
    if (config.contains("sessionType")) {
        sessionType = config["sessionType"].toString();
    }
    
    // 创建会话
    auto result = m_communicationManager->createSession(connectionId, sessionType, sessionConfig);
    if (!result.isSuccess) {
        m_lastError = QString("Failed to create session '%1': %2").arg(connectionId, result.message);
        return SimpleResult::failure(m_lastError);
    }
    
    updateSystemStatistics();
    emit connectionCreated(connectionId);
    
    qDebug() << "Created connection:" << connectionId << "on port:" << portName;
    return SimpleResult::success(true);
}

qint64 CommunicationSystem::sendData(const QString& connectionId, const QByteArray& data) {
    QMutexLocker locker(&m_mutex);
    
    if (!m_initialized || !m_communicationManager) {
        return -1;
    }
    
    auto session = m_communicationManager->getSession(connectionId);
    if (!session) {
        m_lastError = QString("Connection '%1' not found").arg(connectionId);
        return -1;
    }
    
    qint64 bytesSent = session->sendRawData(data);
    if (bytesSent > 0) {
        updateSystemStatistics();
    }
    
    return bytesSent;
}

SimpleResult CommunicationSystem::sendMessage(const QString& connectionId, const QVariantMap& message) {
    QMutexLocker locker(&m_mutex);
    
    if (!m_initialized || !m_communicationManager) {
        m_lastError = "System not initialized or manager not available";
        return SimpleResult::failure(m_lastError);
    }
    
    auto session = m_communicationManager->getSession(connectionId);
    if (!session) {
        m_lastError = QString("Connection '%1' not found").arg(connectionId);
        return SimpleResult::failure(m_lastError);
    }
    
    auto result = session->sendMessage(message);
    if (result.isSuccess) {
        updateSystemStatistics();
    }
    
    return result;
}

CommandResult CommunicationSystem::executeCommand(const QString& connectionId, const QVariantMap& command) {
    QMutexLocker locker(&m_mutex);
    
    CommandResult result;
    result.success = false;
    
    if (!m_initialized || !m_communicationManager) {
        result.errorMessage = "System not initialized or manager not available";
        return result;
    }
    
    auto session = m_communicationManager->getSession(connectionId);
    if (!session) {
        result.errorMessage = QString("Connection '%1' not found").arg(connectionId);
        return result;
    }
    
    result = session->executeCommand(command);
    if (result.success) {
        updateSystemStatistics();
    }
    
    return result;
}

SimpleResult CommunicationSystem::closeConnection(const QString& connectionId) {
    QMutexLocker locker(&m_mutex);
    
    if (!m_initialized || !m_communicationManager) {
        m_lastError = "System not initialized or manager not available";
        return SimpleResult::failure(m_lastError);
    }
    
    // 先关闭会话，再销毁
    auto session = m_communicationManager->getSession(connectionId);
    if (session) {
        session->closeSession();
    }
    
    auto result = m_communicationManager->destroySession(connectionId);
    if (result.isSuccess) {
        updateSystemStatistics();
    }
    
    return result;
}

// === 系统管理接口 ===

PortInfoList CommunicationSystem::getAvailablePorts() {
    QMutexLocker locker(&m_mutex);
    
    if (!m_initialized || !m_portManager) {
        return PortInfoList();
    }
    
    return m_portManager->getAvailablePorts();
}

QStringList CommunicationSystem::getSupportedProtocols() {
    QMutexLocker locker(&m_mutex);
    
    if (!m_initialized || !m_protocolFactory) {
        return QStringList();
    }
    
    return m_protocolFactory->getSupportedProtocolTypes();
}

QStringList CommunicationSystem::getActiveConnections() {
    QMutexLocker locker(&m_mutex);
    
    if (!m_initialized || !m_communicationManager) {
        return QStringList();
    }
    
    return m_communicationManager->getAllSessionIds();
}

ConnectionStatus CommunicationSystem::getConnectionStatus(const QString& connectionId) {
    QMutexLocker locker(&m_mutex);
    
    if (!m_initialized || !m_communicationManager) {
        return ConnectionStatus::Disconnected;
    }
    
    auto session = m_communicationManager->getSession(connectionId);
    if (!session) {
        return ConnectionStatus::Disconnected;
    }
    
    return session->getSessionStatus();
}

// === 监控和诊断 ===

DeviceStatistics CommunicationSystem::getSystemStatistics() {
    QMutexLocker locker(&m_mutex);
    return m_systemStats;
}

DeviceStatistics CommunicationSystem::getConnectionStatistics(const QString& connectionId) {
    QMutexLocker locker(&m_mutex);
    
    if (!m_initialized || !m_communicationManager) {
        return DeviceStatistics();
    }
    
    return m_communicationManager->getSessionStatistics(connectionId);
}

SimpleResult CommunicationSystem::performSystemHealthCheck() {
    QMutexLocker locker(&m_mutex);
    
    if (!m_initialized) {
        m_lastError = "System not initialized";
        return SimpleResult::failure(m_lastError);
    }
    
    // 检查核心组件
    if (!m_communicationManager || !m_portManager) {
        m_lastError = "Core components not available";
        return SimpleResult::failure(m_lastError);
    }
    
    // 执行管理器健康检查
    auto managerResult = m_communicationManager->performHealthCheck();
    if (!managerResult.isSuccess) {
        m_lastError = QString("Manager health check failed: %1").arg(managerResult.message);
        return SimpleResult::failure(m_lastError);
    }
    
    // 检查端口管理器
    auto portResult = m_portManager->refreshPorts();
    if (!portResult.isSuccess) {
        m_lastError = QString("Port manager check failed: %1").arg(portResult.message);
        return SimpleResult::failure(m_lastError);
    }
    
    qDebug() << "System health check completed successfully";
    return SimpleResult::success(true);
}

QString CommunicationSystem::getSystemStatusReport() {
    QMutexLocker locker(&m_mutex);
    
    int totalConnections = 0;
    int activeConnections = 0;
    int errorConnections = 0;
    
    if (m_communicationManager) {
        totalConnections = m_communicationManager->getSessionCount();
        activeConnections = m_communicationManager->getSessionsByStatus(ConnectionStatus::Connected).size();
        errorConnections = m_communicationManager->getSessionsByStatus(ConnectionStatus::Error).size();
    }
    
    int availablePorts = 0;
    if (m_portManager) {
        availablePorts = m_portManager->getAvailablePorts().size();
    }
    
    qint64 uptime = QDateTime::currentMSecsSinceEpoch() - m_systemStats.lastActivity;
    
    return QString("Communication System Status Report:\n"
                   "  System Initialized: %1\n"
                   "  Total Connections: %2\n"
                   "  Active Connections: %3\n"
                   "  Error Connections: %4\n"
                   "  Available Ports: %5\n"
                   "  System Uptime: %6ms\n"
                   "  Bytes Sent: %7\n"
                   "  Bytes Received: %8\n"
                   "  Packets Sent: %9\n"
                   "  Packets Received: %10")
           .arg(m_initialized ? "Yes" : "No")
           .arg(totalConnections)
           .arg(activeConnections)
           .arg(errorConnections)
           .arg(availablePorts)
           .arg(uptime)
           .arg(m_systemStats.bytesSent)
           .arg(m_systemStats.bytesReceived)
           .arg(m_systemStats.packetsSent)
           .arg(m_systemStats.packetsReceived);
}

QString CommunicationSystem::errorString() const {
    QMutexLocker locker(&m_mutex);
    return m_lastError;
}

// === 配置管理 ===

SimpleResult CommunicationSystem::setSystemConfig(const ConfigParameters& config) {
    QMutexLocker locker(&m_mutex);
    
    m_systemConfig = config;
    
    // 应用配置到组件
    if (m_communicationManager) {
        m_communicationManager->setGlobalConfig(config);
    }
    
    return SimpleResult::success(true);
}

ConfigParameters CommunicationSystem::getSystemConfig() const {
    QMutexLocker locker(&m_mutex);
    return m_systemConfig;
}

void CommunicationSystem::resetSystemStatistics() {
    QMutexLocker locker(&m_mutex);
    m_systemStats = DeviceStatistics();
    m_systemStats.lastActivity = QDateTime::currentMSecsSinceEpoch();
    m_lastError.clear();
}

// === 私有槽函数 ===

void CommunicationSystem::onConnectionStatusChanged(const QString& connectionId, ConnectionStatus status) {
    emit connectionStatusChanged(connectionId, status);
}

void CommunicationSystem::onManagerError(const QString& error) {
    emit systemError(QString("Manager error: %1").arg(error));
}

// === 私有辅助方法 ===

void CommunicationSystem::initializeComponents() {
    // 这里应该创建具体的组件实例
    // 实际实现中会使用工厂模式创建各种组件
    // 现在先创建空指针，等待工厂实现
    
    qDebug() << "Initializing communication system components...";
    
    // TODO: 使用工厂创建组件实例
    // m_communicationManager = ManagerFactory::createManager("basic");
    // m_portManager = PortManagerFactory::createPortManager("linus");
    // m_protocolFactory = ProtocolFactory::createFactory();
    // m_commandHandlerFactory = CommandHandlerFactory::createFactory();
    // m_sessionFactory = SessionFactory::createFactory();
}

void CommunicationSystem::shutdownComponents() {
    // 清理所有组件
    m_communicationManager.reset();
    m_portManager.reset();
    m_protocolFactory.reset();
    m_commandHandlerFactory.reset();
    m_sessionFactory.reset();
    
    qDebug() << "Communication system components shutdown";
}

void CommunicationSystem::connectManagerSignals() {
    if (m_communicationManager) {
        connect(m_communicationManager.get(), &Manager::ICommunicationManager::sessionStatusChanged,
                this, &CommunicationSystem::onConnectionStatusChanged);
        connect(m_communicationManager.get(), &Manager::ICommunicationManager::managerError,
                this, &CommunicationSystem::onManagerError);
    }
}

void CommunicationSystem::disconnectManagerSignals() {
    if (m_communicationManager) {
        disconnect(m_communicationManager.get(), nullptr, this, nullptr);
    }
}

void CommunicationSystem::updateSystemStatistics() {
    m_systemStats.lastActivity = QDateTime::currentMSecsSinceEpoch();
    
    // 聚合所有会话的统计信息
    if (m_communicationManager) {
        DeviceStatistics managerStats = m_communicationManager->getManagerStatistics();
        // 这里可以聚合统计信息，简化实现暂时不做
    }
}

// === 单例实现 ===

CommunicationSystem* CommunicationSystemInstance::s_instance = nullptr;
QMutex CommunicationSystemInstance::s_mutex;

CommunicationSystem& CommunicationSystemInstance::instance() {
    QMutexLocker locker(&s_mutex);
    
    if (!s_instance) {
        s_instance = new CommunicationSystem();
    }
    
    return *s_instance;
}

void CommunicationSystemInstance::destroyInstance() {
    QMutexLocker locker(&s_mutex);
    
    if (s_instance) {
        delete s_instance;
        s_instance = nullptr;
    }
}

} // namespace System
} // namespace Communication
} // namespace LA

// Removed #include "CommunicationSystem.moc" - not needed