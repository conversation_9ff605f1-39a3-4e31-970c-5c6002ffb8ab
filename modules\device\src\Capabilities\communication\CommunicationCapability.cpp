/**
 * @file CommunicationCapability.cpp
 * @brief 通信能力适配器实现 - 设备与基础设施通信模块桥接
 * 
 * Linus式实现原则：
 * - "Talk is cheap, show me the code" - 实际可工作的通信适配器
 * - "Never break userspace" - 完全兼容现有设备接口
 * - "最小模块原则" - 只做通信适配，不包含其他逻辑
 */

#include "CommunicationCapability.h"
#include <QDebug>
#include <QDateTime>

namespace LA::Device::Capability {

CommunicationCapability::CommunicationCapability(QObject* parent)
    : IDeviceCapability(parent)
    , m_initialized(false)
    , m_connected(false)
    , m_commandsSent(0)
    , m_responsesReceived(0)
    , m_bytesTransmitted(0)
    , m_bytesReceived(0)
    , m_errorCount(0)
    , m_statusTimer(nullptr)
{
    qDebug() << "[CommunicationCapability] Creating communication capability adapter";
    
    // 初始化状态监控定时器
    m_statusTimer = new QTimer(this);
    connect(m_statusTimer, &QTimer::timeout, this, &CommunicationCapability::performStatusCheck);
    
    initializeCommunicationStatistics();
}

CommunicationCapability::~CommunicationCapability() {
    shutdown();
    qDebug() << "[CommunicationCapability] Communication capability destroyed";
}

bool CommunicationCapability::initialize(const QVariantMap& config) {
    if (m_initialized) {
        qDebug() << "[CommunicationCapability] Already initialized";
        return true;
    }

    qDebug() << "[CommunicationCapability] Initializing with config:" << config;

    // 保存配置
    m_communicationConfig = config;
    
    // 验证基础设施服务
    if (!validateInfrastructureServices()) {
        qCritical() << "[CommunicationCapability] Infrastructure services not available";
        return false;
    }

    // 配置通信会话
    if (m_session) {
        // 构建会话配置
        ConfigParameters sessionConfig;
        if (config.contains("port_name")) {
            sessionConfig["port_name"] = config["port_name"];
        }
        if (config.contains("baud_rate")) {
            sessionConfig["baud_rate"] = config["baud_rate"];
        }
        if (config.contains("timeout")) {
            sessionConfig["timeout"] = config["timeout"];
        }
        
        // 连接会话信号
        connect(m_session.get(), &Communication::Session::ICommunicationSession::sessionStatusChanged,
                this, &CommunicationCapability::onSessionStatusChanged);
        connect(m_session.get(), &Communication::Session::ICommunicationSession::dataReceived,
                this, &CommunicationCapability::onSessionDataReceived);
        connect(m_session.get(), &Communication::Session::ICommunicationSession::messageReceived,
                this, &CommunicationCapability::onSessionMessageReceived);
        connect(m_session.get(), &Communication::Session::ICommunicationSession::sessionError,
                this, &CommunicationCapability::onSessionError);
        connect(m_session.get(), &Communication::Session::ICommunicationSession::commandExecuted,
                this, &CommunicationCapability::onCommandExecuted);
    }

    m_initialized = true;
    qDebug() << "[CommunicationCapability] Communication capability initialized successfully";
    
    emit capabilityReady();
    return true;
}

QVariantMap CommunicationCapability::executeCapability(const QString& action, const QVariantMap& params) {
    if (!m_initialized) {
        return createErrorResult("Communication capability not initialized");
    }

    qDebug() << "[CommunicationCapability] Executing action:" << action << "with params:" << params;

    if (action == "establish_connection") {
        return establishConnection(params);
    } else if (action == "close_connection") {
        return closeConnection();
    } else if (action == "send_command") {
        QString command = params.value("command", "").toString();
        QVariantMap cmdParams = params.value("params", QVariantMap()).toMap();
        return sendDeviceCommand(command, cmdParams);
    } else if (action == "send_raw_data") {
        QByteArray data = params.value("data", QByteArray()).toByteArray();
        return sendRawData(data);
    } else if (action == "receive_response") {
        int timeout = params.value("timeout", 5000).toInt();
        return receiveResponse(timeout);
    } else if (action == "execute_command_and_wait") {
        QString command = params.value("command", "").toString();
        QVariantMap cmdParams = params.value("params", QVariantMap()).toMap();
        int timeout = params.value("timeout", 5000).toInt();
        return executeCommandAndWait(command, cmdParams, timeout);
    } else if (action == "get_status") {
        return getConnectionStatus();
    } else if (action == "get_statistics") {
        return getCommunicationStatistics();
    } else if (action == "get_device_info") {
        return getDeviceInfo();
    } else {
        return createErrorResult(QString("Unknown action: %1").arg(action));
    }
}

void CommunicationCapability::shutdown() {
    if (!m_initialized) {
        return;
    }

    qDebug() << "[CommunicationCapability] Shutting down communication capability";

    // 停止状态监控
    stopStatusMonitoring();
    
    // 关闭连接
    if (m_connected && m_session) {
        m_session->closeSession();
    }
    
    // 断开信号连接
    if (m_session) {
        disconnect(m_session.get(), nullptr, this, nullptr);
    }

    m_initialized = false;
    m_connected = false;
    
    qDebug() << "[CommunicationCapability] Communication capability shut down";
}

void CommunicationCapability::setInfrastructureServices(const QVariantMap& services) {
    qDebug() << "[CommunicationCapability] Setting infrastructure services:" << services.keys();

    // 注入通信会话服务
    if (services.contains("communication_session")) {
        QVariant sessionVariant = services["communication_session"];
        m_session = sessionVariant.value<std::shared_ptr<Communication::Session::ICommunicationSession>>();
        qDebug() << "[CommunicationCapability] Communication session service injected";
    }
    
    // 注入协议服务
    if (services.contains("protocol_handler")) {
        QVariant protocolVariant = services["protocol_handler"];
        m_protocol = protocolVariant.value<std::shared_ptr<Communication::Protocol::IProtocol>>();
        qDebug() << "[CommunicationCapability] Protocol handler service injected";
    }
    
    // 注入命令提供者
    if (services.contains("command_provider")) {
        QVariant providerVariant = services["command_provider"];
        m_commandProvider = providerVariant.value<std::shared_ptr<Command::ICommandProvider>>();
        qDebug() << "[CommunicationCapability] Command provider service injected";
    }
}

QVariantMap CommunicationCapability::establishConnection(const QVariantMap& connectionConfig) {
    if (!m_session) {
        return createErrorResult("Communication session not available");
    }

    qDebug() << "[CommunicationCapability] Establishing connection with config:" << connectionConfig;

    // 构建基础设施模块的连接配置
    ConfigParameters sessionConfig;
    for (auto it = connectionConfig.constBegin(); it != connectionConfig.constEnd(); ++it) {
        sessionConfig[it.key()] = it.value();
    }

    // 调用基础设施通信模块建立连接
    SimpleResult result = m_session->openSession(sessionConfig);
    
    if (result.success) {
        m_connected = true;
        startStatusMonitoring();
        updateStatistics("connection_established", 0, true);
        
        emit connectionEstablished();
        emit communicationStatusChanged("connected");
        
        qDebug() << "[CommunicationCapability] Connection established successfully";
        return createSuccessResult({{"connection_status", "established"}});
    } else {
        updateStatistics("connection_failed", 0, false);
        emit connectionError(result.message);
        
        qCritical() << "[CommunicationCapability] Failed to establish connection:" << result.message;
        return createErrorResult(result.message);
    }
}

QVariantMap CommunicationCapability::closeConnection() {
    if (!m_session || !m_connected) {
        return createErrorResult("No active connection to close");
    }

    qDebug() << "[CommunicationCapability] Closing connection";

    SimpleResult result = m_session->closeSession();
    
    if (result.success) {
        m_connected = false;
        stopStatusMonitoring();
        
        emit connectionClosed();
        emit communicationStatusChanged("disconnected");
        
        qDebug() << "[CommunicationCapability] Connection closed successfully";
        return createSuccessResult({{"connection_status", "closed"}});
    } else {
        qCritical() << "[CommunicationCapability] Failed to close connection:" << result.message;
        return createErrorResult(result.message);
    }
}

QVariantMap CommunicationCapability::sendDeviceCommand(const QString& command, const QVariantMap& params) {
    if (!m_connected || !m_session || !m_commandProvider) {
        return createErrorResult("Communication not available");
    }

    qDebug() << "[CommunicationCapability] Sending device command:" << command;

    try {
        // 验证命令
        if (!m_commandProvider->validateCommand(command, params)) {
            return createErrorResult(QString("Invalid command: %1").arg(command));
        }
        
        // 生成命令数据
        QByteArray commandData = m_commandProvider->generateCommand(command, params);
        if (commandData.isEmpty()) {
            return createErrorResult("Failed to generate command data");
        }
        
        // 发送原始命令数据
        qint64 bytesSent = m_session->sendRawData(commandData);
        if (bytesSent <= 0) {
            updateStatistics("command_send_failed", commandData.size(), false);
            return createErrorResult("Failed to send command data");
        }
        
        // 更新统计
        updateStatistics("command_sent", bytesSent, true);
        m_commandsSent++;
        
        emit commandSent(command, params);
        
        QVariantMap result = createSuccessResult();
        result["command"] = command;
        result["bytes_sent"] = bytesSent;
        result["timestamp"] = QDateTime::currentDateTime().toString();
        
        qDebug() << "[CommunicationCapability] Command sent successfully:" << bytesSent << "bytes";
        return result;
        
    } catch (const std::exception& e) {
        QString error = QString("Exception sending command: %1").arg(e.what());
        updateStatistics("command_exception", 0, false);
        return createErrorResult(error);
    }
}

QVariantMap CommunicationCapability::receiveResponse(int timeout) {
    if (!m_connected || !m_session) {
        return createErrorResult("Communication not available");
    }

    qDebug() << "[CommunicationCapability] Receiving response with timeout:" << timeout;

    try {
        // 接收原始数据
        QByteArray responseData = m_session->receiveRawData();
        
        if (responseData.isEmpty()) {
            return createErrorResult("No response data received");
        }
        
        // 解析响应数据
        QVariantMap parsedResponse;
        if (m_commandProvider) {
            Command::CommandResult cmdResult = m_commandProvider->parseResponse(responseData);
            parsedResponse["success"] = cmdResult.success;
            parsedResponse["data"] = cmdResult.data;
            parsedResponse["command"] = cmdResult.command;
            if (!cmdResult.error.isEmpty()) {
                parsedResponse["error"] = cmdResult.error;
            }
        } else {
            // 没有命令提供者时，返回原始数据
            parsedResponse["raw_data"] = responseData;
            parsedResponse["success"] = true;
        }
        
        // 更新统计
        updateStatistics("response_received", responseData.size(), true);
        m_responsesReceived++;
        
        emit responseReceived(parsedResponse);
        
        QVariantMap result = createSuccessResult(parsedResponse);
        result["bytes_received"] = responseData.size();
        result["timestamp"] = QDateTime::currentDateTime().toString();
        
        qDebug() << "[CommunicationCapability] Response received successfully:" << responseData.size() << "bytes";
        return result;
        
    } catch (const std::exception& e) {
        QString error = QString("Exception receiving response: %1").arg(e.what());
        updateStatistics("response_exception", 0, false);
        return createErrorResult(error);
    }
}

QVariantMap CommunicationCapability::executeCommandAndWait(const QString& command, const QVariantMap& params, int timeout) {
    qDebug() << "[CommunicationCapability] Executing command and waiting:" << command << "timeout:" << timeout;

    // 发送命令
    QVariantMap sendResult = sendDeviceCommand(command, params);
    if (!sendResult["success"].toBool()) {
        return sendResult;  // 发送失败，直接返回
    }
    
    // 等待响应
    QVariantMap receiveResult = receiveResponse(timeout);
    if (!receiveResult["success"].toBool()) {
        return receiveResult;  // 接收失败，直接返回
    }
    
    // 合并发送和接收结果
    QVariantMap completeResult = createSuccessResult();
    completeResult["command"] = command;
    completeResult["send_result"] = sendResult;
    completeResult["receive_result"] = receiveResult;
    completeResult["execution_time"] = QDateTime::currentMSecsSinceEpoch();
    
    // 如果有解析数据，提取到顶层
    if (receiveResult.contains("data")) {
        completeResult["response_data"] = receiveResult["data"];
    }
    
    qDebug() << "[CommunicationCapability] Command executed and response received successfully";
    return completeResult;
}

QVariantMap CommunicationCapability::sendRawData(const QByteArray& data) {
    if (!m_connected || !m_session) {
        return createErrorResult("Communication not available");
    }

    qDebug() << "[CommunicationCapability] Sending raw data:" << data.size() << "bytes";

    qint64 bytesSent = m_session->sendRawData(data);
    
    if (bytesSent > 0) {
        updateStatistics("raw_data_sent", bytesSent, true);
        
        QVariantMap result = createSuccessResult();
        result["bytes_sent"] = bytesSent;
        result["data_size"] = data.size();
        
        emit rawDataReceived(data);  // 发送数据事件
        return result;
    } else {
        updateStatistics("raw_data_send_failed", data.size(), false);
        return createErrorResult("Failed to send raw data");
    }
}

QVariantMap CommunicationCapability::getConnectionStatus() const {
    QVariantMap status;
    status["initialized"] = m_initialized;
    status["connected"] = m_connected;
    status["session_available"] = (m_session != nullptr);
    status["protocol_available"] = (m_protocol != nullptr);
    status["command_provider_available"] = (m_commandProvider != nullptr);
    
    if (m_session) {
        status["session_status"] = static_cast<int>(m_session->getSessionStatus());
        status["session_open"] = m_session->isSessionOpen();
    }
    
    status["last_activity"] = m_lastActivity.toString();
    status["last_error"] = m_lastError;
    
    return status;
}

QVariantMap CommunicationCapability::getCommunicationStatistics() const {
    QVariantMap stats;
    stats["commands_sent"] = m_commandsSent;
    stats["responses_received"] = m_responsesReceived;
    stats["bytes_transmitted"] = m_bytesTransmitted;
    stats["bytes_received"] = m_bytesReceived;
    stats["error_count"] = m_errorCount;
    stats["uptime_seconds"] = m_lastActivity.secsTo(QDateTime::currentDateTime());
    
    // 计算成功率
    if (m_commandsSent > 0) {
        double successRate = (double)(m_commandsSent - m_errorCount) / m_commandsSent * 100.0;
        stats["success_rate_percent"] = successRate;
    }
    
    return stats;
}

QVariantMap CommunicationCapability::getDeviceInfo() const {
    return m_deviceInfo;
}

void CommunicationCapability::setCommandProvider(std::shared_ptr<Command::ICommandProvider> provider) {
    m_commandProvider = provider;
    qDebug() << "[CommunicationCapability] Command provider set";
}

bool CommunicationCapability::updateCommunicationConfig(const QVariantMap& config) {
    qDebug() << "[CommunicationCapability] Updating communication config:" << config;
    
    // 合并配置
    for (auto it = config.constBegin(); it != config.constEnd(); ++it) {
        m_communicationConfig[it.key()] = it.value();
    }
    
    // 如果已连接，应用新配置
    if (m_connected && m_session) {
        // 这里可以重新配置会话（如果支持动态配置）
        qDebug() << "[CommunicationCapability] Configuration updated during active session";
    }
    
    return true;
}

// === 基础设施模块事件处理 ===

void CommunicationCapability::onSessionStatusChanged(ConnectionStatus status) {
    QString statusStr;
    switch (status) {
        case ConnectionStatus::Disconnected:
            statusStr = "disconnected";
            m_connected = false;
            break;
        case ConnectionStatus::Connecting:
            statusStr = "connecting";
            break;
        case ConnectionStatus::Connected:
            statusStr = "connected";
            m_connected = true;
            break;
        case ConnectionStatus::Error:
            statusStr = "error";
            m_connected = false;
            updateStatistics("status_error", 0, false);
            break;
    }
    
    qDebug() << "[CommunicationCapability] Session status changed to:" << statusStr;
    emit communicationStatusChanged(statusStr);
}

void CommunicationCapability::onSessionDataReceived(const QByteArray& data) {
    qDebug() << "[CommunicationCapability] Session data received:" << data.size() << "bytes";
    
    updateStatistics("data_received", data.size(), true);
    m_lastActivity = QDateTime::currentDateTime();
    
    emit rawDataReceived(data);
}

void CommunicationCapability::onSessionMessageReceived(const QVariantMap& message) {
    qDebug() << "[CommunicationCapability] Session message received:" << message;
    
    updateStatistics("message_received", 0, true);
    m_lastActivity = QDateTime::currentDateTime();
    
    emit responseReceived(message);
}

void CommunicationCapability::onSessionError(const QString& error) {
    qCritical() << "[CommunicationCapability] Session error:" << error;
    
    m_lastError = error;
    updateStatistics("session_error", 0, false);
    
    emit capabilityError(error);
    emit connectionError(error);
}

void CommunicationCapability::onCommandExecuted(const QVariantMap& command, const CommandResult& result) {
    qDebug() << "[CommunicationCapability] Command executed:" << command << "Result:" << result.success;
    
    if (result.success) {
        updateStatistics("command_executed", 0, true);
    } else {
        updateStatistics("command_failed", 0, false);
    }
    
    // 将CommandResult转换为QVariantMap
    QVariantMap resultMap;
    resultMap["success"] = result.success;
    resultMap["command"] = result.command;
    resultMap["data"] = result.data;
    resultMap["error"] = result.error;
    resultMap["timestamp"] = result.timestamp.toString();
    
    emit responseReceived(resultMap);
}

// === 内部工具方法 ===

bool CommunicationCapability::validateInfrastructureServices() const {
    bool valid = true;
    
    if (!m_session) {
        qWarning() << "[CommunicationCapability] Communication session service not available";
        valid = false;
    }
    
    // 协议和命令提供者是可选的
    if (!m_protocol) {
        qDebug() << "[CommunicationCapability] Protocol handler not available (optional)";
    }
    
    if (!m_commandProvider) {
        qDebug() << "[CommunicationCapability] Command provider not available (optional)";
    }
    
    return valid;
}

void CommunicationCapability::initializeCommunicationStatistics() {
    m_commandsSent = 0;
    m_responsesReceived = 0;
    m_bytesTransmitted = 0;
    m_bytesReceived = 0;
    m_errorCount = 0;
    m_lastActivity = QDateTime::currentDateTime();
    
    qDebug() << "[CommunicationCapability] Communication statistics initialized";
}

void CommunicationCapability::updateStatistics(const QString& operation, qint64 bytes, bool success) {
    if (operation.contains("sent") || operation.contains("transmitted")) {
        m_bytesTransmitted += bytes;
    } else if (operation.contains("received")) {
        m_bytesReceived += bytes;
    }
    
    if (!success) {
        m_errorCount++;
    }
    
    m_lastActivity = QDateTime::currentDateTime();
}

QVariantMap CommunicationCapability::createErrorResult(const QString& error) const {
    QVariantMap result;
    result["success"] = false;
    result["error"] = error;
    result["capability"] = getCapabilityId();
    result["timestamp"] = QDateTime::currentDateTime().toString();
    return result;
}

QVariantMap CommunicationCapability::createSuccessResult(const QVariantMap& data) const {
    QVariantMap result = data;
    result["success"] = true;
    result["capability"] = getCapabilityId();
    result["timestamp"] = QDateTime::currentDateTime().toString();
    return result;
}

void CommunicationCapability::startStatusMonitoring() {
    if (m_statusTimer && !m_statusTimer->isActive()) {
        m_statusTimer->start(5000);  // 每5秒检查一次状态
        qDebug() << "[CommunicationCapability] Status monitoring started";
    }
}

void CommunicationCapability::stopStatusMonitoring() {
    if (m_statusTimer) {
        m_statusTimer->stop();
        qDebug() << "[CommunicationCapability] Status monitoring stopped";
    }
}

void CommunicationCapability::performStatusCheck() {
    if (m_session && m_connected) {
        ConnectionStatus status = m_session->getSessionStatus();
        if (status != ConnectionStatus::Connected) {
            qWarning() << "[CommunicationCapability] Status check detected connection issue";
            onSessionStatusChanged(status);
        }
    }
}

// === CommunicationCapabilityFactory实现 ===

std::unique_ptr<CommunicationCapability> CommunicationCapabilityFactory::createCapability(const QString& deviceType, const QVariantMap& config) {
    Q_UNUSED(deviceType)
    
    qDebug() << "[CommunicationCapabilityFactory] Creating communication capability for:" << deviceType;
    
    auto capability = std::make_unique<CommunicationCapability>();
    
    if (!config.isEmpty()) {
        if (!capability->initialize(config)) {
            qCritical() << "[CommunicationCapabilityFactory] Failed to initialize capability";
            return nullptr;
        }
    }
    
    return capability;
}

std::unique_ptr<CommunicationCapability> CommunicationCapabilityFactory::createWithServices(
    const QString& deviceType,
    std::shared_ptr<Communication::Session::ICommunicationSession> session,
    std::shared_ptr<Communication::Protocol::IProtocol> protocol,
    std::shared_ptr<Command::ICommandProvider> commandProvider) {
    
    qDebug() << "[CommunicationCapabilityFactory] Creating capability with injected services for:" << deviceType;
    
    auto capability = std::make_unique<CommunicationCapability>();
    
    // 注入基础设施服务
    QVariantMap services;
    services["communication_session"] = QVariant::fromValue(session);
    services["protocol_handler"] = QVariant::fromValue(protocol);
    services["command_provider"] = QVariant::fromValue(commandProvider);
    
    capability->setInfrastructureServices(services);
    
    return capability;
}

} // namespace LA::Device::Capability