#ifndef LA_DEVICE_MANAGEMENT_DEVICE_MANAGEMENT_ORCHESTRATOR_H
#define LA_DEVICE_MANAGEMENT_DEVICE_MANAGEMENT_ORCHESTRATOR_H

#include "Core/DeviceManagementTypes.h"  // 包含类型定义
// #include "LA/Communication/PortManagement/PortDiscoveryService.h"  // 暂时注释掉
#include <QObject>
#include <QString>
#include <QStringList>
#include <QTimer>
#include <QVariantMap>
#include <QDateTime>
#include <QMutex>
#include <memory>

namespace LA {
namespace DeviceManagement {

// Linus: "使用具体的分层架构服务"
namespace Discovery {
class DeviceDiscoveryService;
}  // namespace Discovery

// 暂时保留前向声明，等待实现
class DevicePortMatcher;
class RegistrationManager;
class LifecycleController;
class DeviceInstanceManager;  // 前向声明

// 使用统一的类型定义，避免重复定义
#include "Core/DeviceManagementTypes.h"

/**
 * @brief 设备发现结果结构
 */
struct DeviceDiscoveryResult {
    QString     deviceType;       // 设备类型
    QString     portName;         // 端口名称
    QVariantMap deviceInfo;       // 设备信息
    QVariantMap portInfo;         // 端口信息
    double      matchConfidence;  // 匹配置信度 (0.0-1.0)
    QDateTime   discoveryTime;    // 发现时间

    DeviceDiscoveryResult() : matchConfidence(0.0), discoveryTime(QDateTime::currentDateTime()) {
    }
};

/**
 * @brief 设备管理流程编排器 - 双层架构的协调层
 *
 * 遵循Linus式双层架构设计：
 * 1. 上层：编排各个服务组件，实现完整的设备管理流程
 * 2. 下层：DeviceInstanceManager专注于实例管理
 * 3. 协调：设备发现、端口匹配、注册、生命周期管理
 */
class DeviceManagementOrchestrator : public QObject {
    Q_OBJECT

  public:
    explicit DeviceManagementOrchestrator(QObject *parent = nullptr);
    virtual ~DeviceManagementOrchestrator();

    // ====== 初始化和清理 ======
    bool initialize();
    void shutdown();
    bool isInitialized() const;

    // ====== 设备发现和注册流程 ======
    void startDeviceDiscovery();
    void stopDeviceDiscovery();
    bool isDiscoveryActive() const;

    // ====== 自动设备管理 ======
    QString autoCreateDevice(const QString &deviceType, const QString &portName = QString());
    bool    autoConnectDevice(const QString &instanceId);
    bool    autoDisconnectDevice(const QString &instanceId);

    // ====== 手动设备管理 ======
    QString createDevice(const QString &deviceType, const QVariantMap &config);
    bool    connectDevice(const QString &instanceId, const QString &portName);
    bool    disconnectDevice(const QString &instanceId);
    bool    removeDevice(const QString &instanceId);

    // ====== 批量操作 ======
    QStringList createMultipleDevices(const QString &deviceType, int count);
    bool        connectAllDevices();
    bool        disconnectAllDevices();
    void        removeAllDevices();

    // ====== 查询接口 ======
    QStringList                  getAvailablePorts() const;
    QStringList                  getConnectedDevices() const;
    QStringList                  getDisconnectedDevices() const;
    QList<DeviceDiscoveryResult> getDiscoveryResults() const;

    // ====== 状态查询 ======
    DeviceInstanceInfo getDeviceInfo(const QString &instanceId) const;
    QVariantMap        getDeviceStatus(const QString &instanceId) const;
    QString            getDevicePort(const QString &instanceId) const;
    bool               isDeviceConnected(const QString &instanceId) const;

    // ====== 配置管理 ======
    bool setDiscoveryInterval(int intervalMs);
    int  getDiscoveryInterval() const;
    bool setAutoConnect(bool enabled);
    bool isAutoConnectEnabled() const;

    // ====== 错误处理 ======
    QString     getLastError() const;
    QStringList getErrorHistory() const;
    void        clearErrorHistory();

    // ====== 获取子组件 ======
    DeviceInstanceManager *getInstanceManager() const;

  signals:
    void deviceDiscovered(const DeviceDiscoveryResult &result);
    void deviceAutoCreated(const QString &instanceId);
    void deviceConnected(const QString &instanceId);
    void deviceDisconnected(const QString &instanceId);
    void deviceRemoved(const QString &instanceId);
    void discoveryStarted();
    void discoveryStopped();
    void orchestratorError(const QString &error);

  private slots:
    void onDiscoveryTimer();
    void onDeviceDiscovered(const DeviceDiscoveryResult &result);
    void onInstanceCreated(const QString &instanceId);
    void onInstanceDestroyed(const QString &instanceId);
    void onInstanceConnected(const QString &instanceId);
    void onInstanceDisconnected(const QString &instanceId);
    void onInstanceError(const QString &instanceId, const QString &error);

  private:
    // ====== 初始化子组件 ======
    bool initializeComponents();
    void shutdownComponents();

    // ====== 发现流程 ======
    void performDiscovery();
    void processDiscoveryResults(const QList<DeviceDiscoveryResult> &results);
    void handleAutoCreation(const DeviceDiscoveryResult &result);

    // ====== 内部验证 ======
    bool validateInstanceId(const QString &instanceId) const;
    bool validateDeviceType(const QString &deviceType) const;
    bool validatePortName(const QString &portName) const;

    // ====== 错误处理 ======
    void setLastError(const QString &error);
    void addErrorToHistory(const QString &error);

  private:
    // ====== 核心组件 ======
    // std::unique_ptr<DeviceInstanceManager>  m_instanceManager;  // 暂时禁用
    std::unique_ptr<Discovery::DeviceDiscoveryService> m_discoveryService;
    // std::unique_ptr<LA::Communication::PortManagement::PortDiscoveryService>   m_portDiscoveryService;  // 暂时禁用
    // std::unique_ptr<DevicePortMatcher>      m_portMatcher;  // 暂时禁用
    // std::unique_ptr<RegistrationManager>    m_registrationManager;  // 暂时禁用
    // std::unique_ptr<LifecycleController>    m_lifecycleController;  // 暂时禁用

    // ====== 发现定时器 ======
    std::unique_ptr<QTimer> m_discoveryTimer;
    int                     m_discoveryInterval;  // 发现间隔（毫秒）
    bool                    m_discoveryActive;    // 发现是否激活

    // ====== 配置选项 ======
    bool m_autoConnectEnabled;  // 是否启用自动连接
    bool m_initialized;         // 是否已初始化

    // ====== 状态管理 ======
    QList<DeviceDiscoveryResult> m_discoveryResults;  // 发现结果
    QMap<QString, QString>       m_devicePortMap;     // 设备-端口映射
    QString                      m_lastError;         // 最后错误
    QStringList                  m_errorHistory;      // 错误历史

    // ====== 线程安全 ======
    mutable QMutex m_mutex;
};

} // namespace DeviceManagement  
} // namespace LA

// 注册元类型以支持信号槽
Q_DECLARE_METATYPE(LA::DeviceManagement::DeviceDiscoveryResult)

#endif  // LA_DEVICE_MANAGEMENT_DEVICE_MANAGEMENT_ORCHESTRATOR_H
