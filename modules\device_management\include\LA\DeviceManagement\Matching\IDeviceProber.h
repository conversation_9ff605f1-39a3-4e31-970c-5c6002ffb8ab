#pragma once

#include <QByteArray>
#include <QString>
#include <QVariantMap>

namespace LA {
namespace DeviceManagement {
namespace Matching {

/**
 * @brief 探测配置
 */
struct DeviceProbeConfig {
    QStringList deviceTypes;        // 要探测的设备类型列表
    int timeoutMs = 1000;          // 超时时间(毫秒)
    int retryCount = 3;            // 重试次数
    bool enableBroadcast = false;  // 是否启用广播探测
    QVariantMap customParams;      // 自定义参数
};

/**
 * @brief 探测结果
 */
struct ProbeResult {
    QString portName;
    QByteArray response;      // 设备响应数据
    bool isSuccessful;        // 是否探测成功
    QString errorMessage;     // 错误信息(如果有)
    int responseTimeMs;       // 响应时间(毫秒)
    
    bool isValid() const { return isSuccessful && !response.isEmpty(); }
};

/**
 * @brief 设备探测器接口 - 纯粹的设备探测
 * 
 * Linus: "只做一件事：发送探测指令并接收响应"
 * ✅ 负责: 发送探测指令、接收响应数据、超时处理
 * ❌ 不涉及: 响应解析、设备识别、匹配逻辑
 */
class IDeviceProber {
public:
    virtual ~IDeviceProber() = default;
    
    /**
     * @brief 探测指定端口上的设备
     * @param portName 端口名称
     * @param config 探测配置
     * @return 探测结果
     */
    virtual ProbeResult probeDevice(const QString& portName, const DeviceProbeConfig& config) = 0;
    
    /**
     * @brief 发送单个探测指令
     * @param portName 端口名称
     * @param command 探测指令
     * @param timeoutMs 超时时间
     * @return 是否发送成功
     */
    virtual bool sendProbeCommand(const QString& portName, const QByteArray& command, int timeoutMs = 1000) = 0;
    
    /**
     * @brief 接收探测响应
     * @param portName 端口名称
     * @param timeoutMs 超时时间
     * @return 响应数据
     */
    virtual QByteArray receiveProbeResponse(const QString& portName, int timeoutMs = 1000) = 0;
    
    /**
     * @brief 取消正在进行的探测
     * @param portName 端口名称
     */
    virtual void cancelProbe(const QString& portName) = 0;
};

} // namespace Matching
} // namespace DeviceManagement
} // namespace LA