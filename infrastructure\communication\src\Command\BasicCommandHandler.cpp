#include <LA/Communication/Command/ICommandHandler.h>
#include <QDebug>
#include <QMutexLocker>
#include <QDateTime>
#include <QRegularExpression>

namespace LA {
namespace Communication {
namespace Command {

/**
 * @brief 基础命令处理器实现
 * 
 * 实现基本的设备控制命令处理功能
 */
class BasicCommandHandler : public ICommandHandler {
    Q_OBJECT

public:
    explicit BasicCommandHandler(QObject* parent = nullptr);
    virtual ~BasicCommandHandler() = default;

    // === 命令处理核心 ===
    CommandResult executeCommand(const QVariantMap& command) override;
    bool supportsCommand(const QString& commandType) const override;
    QStringList getSupportedCommands() const override;

    // === 命令验证 ===
    SimpleResult validateCommand(const QVariantMap& command) override;
    QVariantMap getCommandRequirements(const QString& commandType) const override;

    // === 状态查询 ===
    QString errorString() const override;
    DeviceStatistics getStatistics() const override;
    void resetStatistics() override;
    bool isAvailable() const override;

private:
    mutable QMutex m_mutex;
    DeviceStatistics m_statistics;
    QString m_lastError;
    bool m_isAvailable;
    
    // 支持的命令类型
    static const QStringList SUPPORTED_COMMANDS;
    
    // 具体命令执行方法
    CommandResult executeReadCommand(const QVariantMap& command);
    CommandResult executeWriteCommand(const QVariantMap& command);
    CommandResult executeStatusCommand(const QVariantMap& command);
    CommandResult executeResetCommand(const QVariantMap& command);
    CommandResult executeConfigCommand(const QVariantMap& command);
    
    void updateStatistics(bool success);
    bool isValidCommandId(const QString& commandId) const;
};

// 支持的命令定义
const QStringList BasicCommandHandler::SUPPORTED_COMMANDS = {
    "READ",      // 读取数据命令
    "WRITE",     // 写入数据命令
    "STATUS",    // 状态查询命令
    "RESET",     // 重置命令
    "CONFIG"     // 配置命令
};

BasicCommandHandler::BasicCommandHandler(QObject* parent)
    : ICommandHandler(parent)
    , m_isAvailable(true)
{
    resetStatistics();
}

// === 命令处理核心 ===

CommandResult BasicCommandHandler::executeCommand(const QVariantMap& command) {
    QMutexLocker locker(&m_mutex);
    
    m_lastError.clear();
    
    // 验证命令
    auto validationResult = validateCommand(command);
    if (!validationResult.isSuccess) {
        updateStatistics(false);
        CommandResult result;
        result.success = false;
        result.errorMessage = validationResult.message;
        emit commandError(command, result.errorMessage);
        return result;
    }
    
    QString commandType = command["type"].toString().toUpper();
    
    // 分发到具体的命令执行方法
    CommandResult result;
    
    if (commandType == "READ") {
        result = executeReadCommand(command);
    } else if (commandType == "WRITE") {
        result = executeWriteCommand(command);
    } else if (commandType == "STATUS") {
        result = executeStatusCommand(command);
    } else if (commandType == "RESET") {
        result = executeResetCommand(command);
    } else if (commandType == "CONFIG") {
        result = executeConfigCommand(command);
    } else {
        result.success = false;
        result.errorMessage = QString("Unsupported command type: %1").arg(commandType);
        updateStatistics(false);
        emit commandError(command, result.errorMessage);
        return result;
    }
    
    updateStatistics(result.success);
    
    if (result.success) {
        emit commandExecuted(command, result);
    } else {
        emit commandError(command, result.errorMessage);
    }
    
    return result;
}

bool BasicCommandHandler::supportsCommand(const QString& commandType) const {
    return SUPPORTED_COMMANDS.contains(commandType.toUpper());
}

QStringList BasicCommandHandler::getSupportedCommands() const {
    return SUPPORTED_COMMANDS;
}

// === 命令验证 ===

SimpleResult BasicCommandHandler::validateCommand(const QVariantMap& command) {
    // 检查必需字段
    if (!command.contains("type")) {
        m_lastError = "Missing 'type' field";
        return SimpleResult::failure(m_lastError);
    }
    
    if (!command.contains("id")) {
        m_lastError = "Missing 'id' field";
        return SimpleResult::failure(m_lastError);
    }
    
    QString commandType = command["type"].toString().toUpper();
    if (!supportsCommand(commandType)) {
        m_lastError = QString("Unsupported command type: %1").arg(commandType);
        return SimpleResult::failure(m_lastError);
    }
    
    QString commandId = command["id"].toString();
    if (!isValidCommandId(commandId)) {
        m_lastError = "Invalid command ID format";
        return SimpleResult::failure(m_lastError);
    }
    
    // 验证命令特定参数
    if (commandType == "READ" || commandType == "WRITE") {
        if (!command.contains("address")) {
            m_lastError = QString("%1 command requires 'address' parameter").arg(commandType);
            return SimpleResult::failure(m_lastError);
        }
    }
    
    if (commandType == "WRITE") {
        if (!command.contains("data")) {
            m_lastError = "WRITE command requires 'data' parameter";
            return SimpleResult::failure(m_lastError);
        }
    }
    
    if (commandType == "CONFIG") {
        if (!command.contains("config")) {
            m_lastError = "CONFIG command requires 'config' parameter";
            return SimpleResult::failure(m_lastError);
        }
    }
    
    return SimpleResult::success(true);
}

QVariantMap BasicCommandHandler::getCommandRequirements(const QString& commandType) const {
    QVariantMap requirements;
    QString type = commandType.toUpper();
    
    // 通用必需参数
    requirements["required"] = QStringList() << "type" << "id";
    
    // 命令特定参数
    if (type == "READ") {
        QStringList required = requirements["required"].toStringList();
        required << "address";
        requirements["required"] = required;
        requirements["optional"] = QStringList() << "length" << "timeout";
    } else if (type == "WRITE") {
        QStringList required = requirements["required"].toStringList();
        required << "address" << "data";
        requirements["required"] = required;
        requirements["optional"] = QStringList() << "timeout";
    } else if (type == "STATUS") {
        requirements["optional"] = QStringList() << "detailed";
    } else if (type == "RESET") {
        requirements["optional"] = QStringList() << "hard" << "timeout";
    } else if (type == "CONFIG") {
        QStringList required = requirements["required"].toStringList();
        required << "config";
        requirements["required"] = required;
        requirements["optional"] = QStringList() << "validate";
    }
    
    return requirements;
}

// === 状态查询 ===

QString BasicCommandHandler::errorString() const {
    QMutexLocker locker(&m_mutex);
    return m_lastError;
}

DeviceStatistics BasicCommandHandler::getStatistics() const {
    QMutexLocker locker(&m_mutex);
    return m_statistics;
}

void BasicCommandHandler::resetStatistics() {
    QMutexLocker locker(&m_mutex);
    m_statistics = DeviceStatistics();
    m_statistics.lastActivity = QDateTime::currentMSecsSinceEpoch();
    m_lastError.clear();
}

bool BasicCommandHandler::isAvailable() const {
    QMutexLocker locker(&m_mutex);
    return m_isAvailable;
}

// === 具体命令执行方法 ===

CommandResult BasicCommandHandler::executeReadCommand(const QVariantMap& command) {
    CommandResult result;
    result.commandId = command["id"].toString();
    result.commandType = "READ";
    result.timestamp = QDateTime::currentMSecsSinceEpoch();
    
    QString address = command["address"].toString();
    int length = command.value("length", 1).toInt();
    
    // 模拟读取操作
    QByteArray data;
    for (int i = 0; i < length; i++) {
        data.append(static_cast<char>(0x55 + i)); // 模拟数据
    }
    
    result.success = true;
    result.data["address"] = address;
    result.data["length"] = length;
    result.data["data"] = data;
    result.message = QString("Read %1 bytes from address %2").arg(length).arg(address);
    
    qDebug() << "Executed READ command:" << result.message;
    
    return result;
}

CommandResult BasicCommandHandler::executeWriteCommand(const QVariantMap& command) {
    CommandResult result;
    result.commandId = command["id"].toString();
    result.commandType = "WRITE";
    result.timestamp = QDateTime::currentMSecsSinceEpoch();
    
    QString address = command["address"].toString();
    QByteArray data = command["data"].toByteArray();
    
    // 模拟写入操作
    result.success = true;
    result.data["address"] = address;
    result.data["length"] = data.size();
    result.data["written"] = data.size();
    result.message = QString("Wrote %1 bytes to address %2").arg(data.size()).arg(address);
    
    qDebug() << "Executed WRITE command:" << result.message;
    
    return result;
}

CommandResult BasicCommandHandler::executeStatusCommand(const QVariantMap& command) {
    CommandResult result;
    result.commandId = command["id"].toString();
    result.commandType = "STATUS";
    result.timestamp = QDateTime::currentMSecsSinceEpoch();
    
    bool detailed = command.value("detailed", false).toBool();
    
    result.success = true;
    result.data["status"] = "online";
    result.data["available"] = m_isAvailable;
    result.data["uptime"] = QDateTime::currentMSecsSinceEpoch() - m_statistics.lastActivity;
    
    if (detailed) {
        result.data["statistics"] = QVariantMap{
            {"packetsReceived", m_statistics.packetsReceived},
            {"packetsSent", m_statistics.packetsSent},
            {"errorsCount", m_statistics.errorsCount},
            {"lastActivity", m_statistics.lastActivity}
        };
    }
    
    result.message = "Status query completed";
    
    qDebug() << "Executed STATUS command:" << result.message;
    
    return result;
}

CommandResult BasicCommandHandler::executeResetCommand(const QVariantMap& command) {
    CommandResult result;
    result.commandId = command["id"].toString();
    result.commandType = "RESET";
    result.timestamp = QDateTime::currentMSecsSinceEpoch();
    
    bool hard = command.value("hard", false).toBool();
    
    // 执行重置操作
    if (hard) {
        resetStatistics();
        result.message = "Hard reset completed";
    } else {
        // 软重置只清除错误状态
        m_lastError.clear();
        result.message = "Soft reset completed";
    }
    
    result.success = true;
    result.data["resetType"] = hard ? "hard" : "soft";
    
    qDebug() << "Executed RESET command:" << result.message;
    
    return result;
}

CommandResult BasicCommandHandler::executeConfigCommand(const QVariantMap& command) {
    CommandResult result;
    result.commandId = command["id"].toString();
    result.commandType = "CONFIG";
    result.timestamp = QDateTime::currentMSecsSinceEpoch();
    
    QVariantMap config = command["config"].toMap();
    bool validate = command.value("validate", true).toBool();
    
    // 模拟配置操作
    if (validate) {
        // 验证配置参数
        for (auto it = config.begin(); it != config.end(); ++it) {
            qDebug() << "Setting config:" << it.key() << "=" << it.value();
        }
    }
    
    result.success = true;
    result.data["configCount"] = config.size();
    result.data["validated"] = validate;
    result.message = QString("Applied %1 configuration parameters").arg(config.size());
    
    qDebug() << "Executed CONFIG command:" << result.message;
    
    return result;
}

// === 私有辅助方法 ===

void BasicCommandHandler::updateStatistics(bool success) {
    qint64 currentTime = QDateTime::currentMSecsSinceEpoch();
    m_statistics.lastActivity = currentTime;
    
    if (success) {
        m_statistics.packetsReceived++;
    } else {
        m_statistics.errorsCount++;
    }
}

bool BasicCommandHandler::isValidCommandId(const QString& commandId) const {
    // 验证命令ID格式：字母、数字、下划线、连字符，长度1-32
    QRegularExpression regex("^[a-zA-Z0-9_-]{1,32}$");
    return regex.match(commandId).hasMatch();
}

} // namespace Command
} // namespace Communication
} // namespace LA

#include "BasicCommandHandler.moc"