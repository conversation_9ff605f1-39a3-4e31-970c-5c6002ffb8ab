#pragma once

#include "IConfigFileHandler.h"
#include <QObject>
#include <QSettings>
#include <QTextStream>

namespace LA {
namespace Support {
namespace Config {

/**
 * @brief INI 配置文件处理器
 * 
 * 专门处理INI格式的配置文件，提供：
 * - INI文件的读写操作
 * - 分组（Section）支持
 * - 注释保持和生成
 * - 键值对验证
 * - 特殊值类型处理（数组、布尔值等）
 */
class IniConfigFileHandler : public QObject {
    Q_OBJECT

public:
    explicit IniConfigFileHandler(QObject* parent = nullptr);
    ~IniConfigFileHandler() override;

    /**
     * @brief 加载INI配置文件
     */
    static Result<QVariantMap> loadIniFile(const QString& filePath, 
                                          const ConfigFileOptions& options = ConfigFileOptions());

    /**
     * @brief 保存INI配置文件
     */
    static SimpleResult saveIniFile(const QString& filePath, 
                                   const QVariantMap& data,
                                   const ConfigFileOptions& options = ConfigFileOptions());

    /**
     * @brief 解析INI内容
     */
    static Result<QVariantMap> parseIniContent(const QByteArray& content, 
                                              const ConfigFileOptions& options = ConfigFileOptions());

    /**
     * @brief 序列化为INI内容
     */
    static ByteArrayResult serializeIniContent(const QVariantMap& data, 
                                              const ConfigFileOptions& options = ConfigFileOptions());

    /**
     * @brief 验证INI数据
     */
    static SimpleResult validateIniData(const QVariantMap& data, 
                                       const QVariantMap& schema = QVariantMap());

    /**
     * @brief 生成带注释的INI内容
     */
    static QString formatIniWithComments(const QVariantMap& data, 
                                        const ConfigFileOptions& options = ConfigFileOptions());

    /**
     * @brief 合并INI对象
     */
    static QVariantMap mergeIniObjects(const QVariantMap& base, 
                                      const QVariantMap& overlay,
                                      bool mergeSections = true);

    /**
     * @brief 展平INI对象（移除分组结构）
     */
    static QVariantMap flattenIniObject(const QVariantMap& data, 
                                       const QString& separator = ".");

    /**
     * @brief 反展平INI对象（重建分组结构）
     */
    static QVariantMap unflattenIniObject(const QVariantMap& flatData, 
                                         const QString& separator = ".");

    /**
     * @brief 获取INI节列表
     */
    static QStringList getIniSections(const QVariantMap& data);

    /**
     * @brief 获取指定节的键列表
     */
    static QStringList getIniSectionKeys(const QVariantMap& data, 
                                        const QString& section);

    /**
     * @brief 获取INI值（支持节.键格式）
     */
    static QVariant getIniValue(const QVariantMap& data, 
                               const QString& key, 
                               const QString& section = QString());

    /**
     * @brief 设置INI值（支持节.键格式）
     */
    static bool setIniValue(QVariantMap& data, 
                           const QString& key, 
                           const QVariant& value,
                           const QString& section = QString());

    /**
     * @brief 删除INI键
     */
    static bool removeIniKey(QVariantMap& data, 
                            const QString& key, 
                            const QString& section = QString());

    /**
     * @brief 删除INI节
     */
    static bool removeIniSection(QVariantMap& data, 
                                const QString& section);

    /**
     * @brief 检查INI键是否存在
     */
    static bool hasIniKey(const QVariantMap& data, 
                         const QString& key, 
                         const QString& section = QString());

    /**
     * @brief 检查INI节是否存在
     */
    static bool hasIniSection(const QVariantMap& data, 
                             const QString& section);

    /**
     * @brief 转换为QSettings兼容格式
     */
    static QVariantMap toQSettingsFormat(const QVariantMap& data);

    /**
     * @brief 从QSettings兼容格式转换
     */
    static QVariantMap fromQSettingsFormat(const QVariantMap& settingsData);

private:
    // 内部辅助方法
    static QString escapeIniValue(const QString& value);
    static QString unescapeIniValue(const QString& value);
    static QString escapeIniKey(const QString& key);
    static QString unescapeIniKey(const QString& key);
    
    // 值类型转换
    static QString variantToIniString(const QVariant& value);
    static QVariant iniStringToVariant(const QString& str, const QString& type = QString());
    
    // 特殊类型处理
    static QString listToIniString(const QVariantList& list);
    static QVariantList iniStringToList(const QString& str);
    static QString mapToIniString(const QVariantMap& map);
    static QVariantMap iniStringToMap(const QString& str);
    
    // 注释处理
    static void preserveComments(const QString& originalContent, 
                                QString& newContent);
    static QString extractComments(const QString& content, 
                                  const QString& section, 
                                  const QString& key);
    
    // 解析辅助方法
    static QPair<QString, QString> parseKeyValue(const QString& line);
    static QString parseSection(const QString& line);
    static bool isCommentLine(const QString& line);
    static bool isSectionLine(const QString& line);
    static bool isKeyValueLine(const QString& line);
    
    // 格式化辅助方法
    static void formatIniSection(QString& result, 
                                const QString& sectionName, 
                                const QVariantMap& sectionData,
                                const ConfigFileOptions& options);
    static void formatIniKeyValue(QString& result, 
                                 const QString& key, 
                                 const QVariant& value,
                                 const ConfigFileOptions& options);

signals:
    /**
     * @brief INI解析错误信号
     */
    void iniParseError(const QString& filePath, int lineNumber, const QString& error);

    /**
     * @brief INI验证错误信号
     */
    void iniValidationError(const QString& filePath, const QString& error);

    /**
     * @brief INI文件保存完成信号
     */
    void iniFileSaved(const QString& filePath);
};

// IniConfigFileParser类定义在ConfigFileHandler.h中，避免重复定义
class IniConfigFileParser;

/**
 * @brief INI配置文件工具类
 */
class IniConfigUtils {
public:
    /**
     * @brief 检查INI文件是否有效
     */
    static bool isValidIniFile(const QString& filePath);

    /**
     * @brief 获取INI文件编码
     */
    static ConfigFileEncoding detectIniEncoding(const QString& filePath);

    /**
     * @brief 转换INI文件编码
     */
    static SimpleResult convertIniEncoding(const QString& filePath, 
                                          ConfigFileEncoding targetEncoding);

    /**
     * @brief 修复INI文件格式
     */
    static SimpleResult repairIniFile(const QString& filePath, 
                                     const QString& backupPath = QString());

    /**
     * @brief 压缩INI文件（移除空白和注释）
     */
    static SimpleResult compressIniFile(const QString& filePath);

    /**
     * @brief 比较两个INI文件
     */
    static Result<QVariantMap> compareIniFiles(const QString& file1, 
                                              const QString& file2);

    /**
     * @brief 生成INI文件统计信息
     */
    static QVariantMap generateIniStatistics(const QString& filePath);

    /**
     * @brief 将INI文件转换为其他格式
     */
    static SimpleResult convertIniToJson(const QString& iniPath, 
                                        const QString& jsonPath);

    /**
     * @brief 将其他格式转换为INI文件
     */
    static SimpleResult convertJsonToIni(const QString& jsonPath, 
                                        const QString& iniPath);
};

/**
 * @brief INI节数据结构
 */
struct IniSection {
    QString name;
    QString comment;
    QVariantMap data;
    QStringList keyOrder; // 保持键的顺序
    
    IniSection() = default;
    IniSection(const QString& sectionName) : name(sectionName) {}
};

/**
 * @brief INI文档数据结构
 */
struct IniDocument {
    QList<IniSection> sections;
    QStringList sectionOrder; // 保持节的顺序
    QString headerComment;
    QString footerComment;
    
    /**
     * @brief 获取节
     */
    IniSection* getSection(const QString& name);
    const IniSection* getSection(const QString& name) const;
    
    /**
     * @brief 添加节
     */
    void addSection(const IniSection& section);
    
    /**
     * @brief 删除节
     */
    bool removeSection(const QString& name);
    
    /**
     * @brief 转换为QVariantMap
     */
    QVariantMap toVariantMap() const;
    
    /**
     * @brief 从QVariantMap构造
     */
    static IniDocument fromVariantMap(const QVariantMap& data);
};

}  // namespace Config
}  // namespace Support
}  // namespace LA