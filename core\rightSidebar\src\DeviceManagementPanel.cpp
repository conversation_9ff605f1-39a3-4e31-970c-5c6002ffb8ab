#include "LA/RightSidebar/DeviceManagementPanel.h"
#include <LA/DeviceManagement/IDeviceRegistry.h>
#include <LA/DeviceManagement/Discovery/IDeviceDiscoveryService.h>
#include <LA/Communication/PortManagement/IPortManager.h>
#include <LA/Foundation/Core/CommonTypes.h>

#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QSplitter>
#include <QTabWidget>
#include <QTreeWidget>
#include <QTableWidget>
#include <QGroupBox>
#include <QPushButton>
#include <QLabel>
#include <QComboBox>
#include <QProgressBar>
#include <QTimer>
#include <QHeaderView>
#include <QMessageBox>
#include <QDialog>
#include <QDialogButtonBox>
#include <QApplication>
#include <QStyle>
#include <QDebug>

namespace LA {
namespace RightSidebar {

DeviceManagementPanel::DeviceManagementPanel(QWidget* parent)
    : RightSidebarPanel(parent)
    , m_mainSplitter(nullptr)
    , m_tabWidget(nullptr)
    , m_deviceGroup(nullptr)
    , m_deviceTree(nullptr)
    , m_startDiscoveryBtn(nullptr)
    , m_stopDiscoveryBtn(nullptr)
    , m_refreshDevicesBtn(nullptr)
    , m_deviceCountLabel(nullptr)
    , m_discoveryProgress(nullptr)
    , m_portGroup(nullptr)
    , m_portTable(nullptr)
    , m_refreshPortsBtn(nullptr)
    , m_portCountLabel(nullptr)
    , m_connectionGroup(nullptr)
    , m_deviceCombo(nullptr)
    , m_portCombo(nullptr)
    , m_connectBtn(nullptr)
    , m_disconnectBtn(nullptr)
    , m_autoMatchBtn(nullptr)
    , m_connectionStatus(nullptr)
    , m_refreshTimer(nullptr)
    , m_discoveryRunning(false)
{
    setupUI();
    connectSignals();
    
    // 创建刷新定时器
    m_refreshTimer = new QTimer(this);
    m_refreshTimer->setInterval(5000); // 5秒刷新一次
    connect(m_refreshTimer, &QTimer::timeout, this, &DeviceManagementPanel::onRefreshTimer);
    m_refreshTimer->start();
    
    qDebug() << "DeviceManagementPanel: 构造完成";
}

DeviceManagementPanel::~DeviceManagementPanel()
{
    if (m_refreshTimer) {
        m_refreshTimer->stop();
    }
    qDebug() << "DeviceManagementPanel: 析构完成";
}

void DeviceManagementPanel::setDeviceRegistry(std::shared_ptr<LA::DeviceManagement::IDeviceRegistry> registry)
{
    m_deviceRegistry = registry;
    if (m_deviceRegistry) {
        qDebug() << "DeviceManagementPanel: 设备注册表已设置";
        updateDeviceList();
    }
}

void DeviceManagementPanel::setDeviceDiscoveryService(std::shared_ptr<LA::DeviceManagement::Discovery::IDeviceDiscoveryService> service)
{
    m_discoveryService = service;
    if (m_discoveryService) {
        qDebug() << "DeviceManagementPanel: 设备发现服务已设置";
        
        // 连接设备发现信号
        connect(m_discoveryService.get(), &LA::DeviceManagement::Discovery::IDeviceDiscoveryService::deviceDiscovered,
                this, &DeviceManagementPanel::onDeviceDiscovered);
        connect(m_discoveryService.get(), &LA::DeviceManagement::Discovery::IDeviceDiscoveryService::deviceLost,
                this, &DeviceManagementPanel::onDeviceLost);
        connect(m_discoveryService.get(), &LA::DeviceManagement::Discovery::IDeviceDiscoveryService::discoveryCompleted,
                this, &DeviceManagementPanel::onDiscoveryCompleted);
        connect(m_discoveryService.get(), &LA::DeviceManagement::Discovery::IDeviceDiscoveryService::discoveryError,
                this, &DeviceManagementPanel::onDiscoveryError);
    }
}

void DeviceManagementPanel::setPortManager(std::shared_ptr<LA::Communication::PortManagement::IPortManager> manager)
{
    m_portManager = manager;
    if (m_portManager) {
        qDebug() << "DeviceManagementPanel: 端口管理器已设置";
        updatePortList();
    }
}

QString DeviceManagementPanel::getPanelId() const
{
    return "device_management";
}

QString DeviceManagementPanel::getPanelTitle() const
{
    return tr("设备管理");
}

QString DeviceManagementPanel::getPanelDescription() const
{
    return tr("设备发现、端口管理和连接控制");
}

QIcon DeviceManagementPanel::getPanelIcon() const
{
    return QApplication::style()->standardIcon(QStyle::SP_ComputerIcon);
}

bool DeviceManagementPanel::canCollapse() const
{
    return true;
}

void DeviceManagementPanel::refreshContent()
{
    qDebug() << "DeviceManagementPanel: 刷新内容";
    updateDeviceList();
    updatePortList();
    updateConnectionStatus();
    updateStatistics();
}

void DeviceManagementPanel::setupUI()
{
    // 创建主布局
    QVBoxLayout* mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(5, 5, 5, 5);
    mainLayout->setSpacing(5);
    
    // 创建标签页组件
    m_tabWidget = new QTabWidget();
    
    // 添加设备发现标签页
    QWidget* deviceTab = createDeviceDiscoveryArea();
    m_tabWidget->addTab(deviceTab, QIcon(), tr("设备发现"));
    
    // 添加端口管理标签页
    QWidget* portTab = createPortManagementArea();
    m_tabWidget->addTab(portTab, QIcon(), tr("端口管理"));
    
    // 添加连接控制标签页
    QWidget* connectionTab = createConnectionControlArea();
    m_tabWidget->addTab(connectionTab, QIcon(), tr("连接控制"));
    
    mainLayout->addWidget(m_tabWidget);
    
    // 设置紧凑布局
    setMaximumWidth(400);
    setMinimumWidth(300);
}

QWidget* DeviceManagementPanel::createDeviceDiscoveryArea()
{
    QWidget* widget = new QWidget();
    QVBoxLayout* layout = new QVBoxLayout(widget);
    layout->setContentsMargins(5, 5, 5, 5);
    layout->setSpacing(5);
    
    // 创建控制按钮组
    QHBoxLayout* btnLayout = new QHBoxLayout();
    m_startDiscoveryBtn = new QPushButton(tr("开始发现"));
    m_stopDiscoveryBtn = new QPushButton(tr("停止发现"));
    m_refreshDevicesBtn = new QPushButton(tr("刷新"));
    
    m_startDiscoveryBtn->setMaximumWidth(80);
    m_stopDiscoveryBtn->setMaximumWidth(80);
    m_refreshDevicesBtn->setMaximumWidth(60);
    
    btnLayout->addWidget(m_startDiscoveryBtn);
    btnLayout->addWidget(m_stopDiscoveryBtn);
    btnLayout->addWidget(m_refreshDevicesBtn);
    btnLayout->addStretch();
    
    layout->addLayout(btnLayout);
    
    // 创建进度条
    m_discoveryProgress = new QProgressBar();
    m_discoveryProgress->setVisible(false);
    layout->addWidget(m_discoveryProgress);
    
    // 创建设备统计信息
    QHBoxLayout* statsLayout = new QHBoxLayout();
    m_deviceCountLabel = new QLabel(tr("设备数: 0"));
    statsLayout->addWidget(m_deviceCountLabel);
    statsLayout->addStretch();
    layout->addLayout(statsLayout);
    
    // 创建设备树形视图
    m_deviceTree = new QTreeWidget();
    m_deviceTree->setHeaderLabels(QStringList() << tr("设备名称") << tr("类型") << tr("状态"));
    m_deviceTree->setRootIsDecorated(true);
    m_deviceTree->setAlternatingRowColors(true);
    m_deviceTree->header()->setStretchLastSection(true);
    m_deviceTree->setColumnWidth(0, 150);
    m_deviceTree->setColumnWidth(1, 80);
    
    layout->addWidget(m_deviceTree);
    
    return widget;
}

QWidget* DeviceManagementPanel::createPortManagementArea()
{
    QWidget* widget = new QWidget();
    QVBoxLayout* layout = new QVBoxLayout(widget);
    layout->setContentsMargins(5, 5, 5, 5);
    layout->setSpacing(5);
    
    // 创建控制按钮组
    QHBoxLayout* btnLayout = new QHBoxLayout();
    m_refreshPortsBtn = new QPushButton(tr("刷新端口"));
    m_refreshPortsBtn->setMaximumWidth(80);
    btnLayout->addWidget(m_refreshPortsBtn);
    btnLayout->addStretch();
    layout->addLayout(btnLayout);
    
    // 创建端口统计信息
    QHBoxLayout* statsLayout = new QHBoxLayout();
    m_portCountLabel = new QLabel(tr("端口数: 0"));
    statsLayout->addWidget(m_portCountLabel);
    statsLayout->addStretch();
    layout->addLayout(statsLayout);
    
    // 创建端口表格视图
    m_portTable = new QTableWidget();
    m_portTable->setColumnCount(4);
    m_portTable->setHorizontalHeaderLabels(QStringList() << tr("端口名") << tr("类型") << tr("状态") << tr("描述"));
    m_portTable->setAlternatingRowColors(true);
    m_portTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_portTable->verticalHeader()->setVisible(false);
    
    // 设置列宽
    m_portTable->setColumnWidth(0, 80);
    m_portTable->setColumnWidth(1, 60);
    m_portTable->setColumnWidth(2, 60);
    m_portTable->horizontalHeader()->setStretchLastSection(true);
    
    layout->addWidget(m_portTable);
    
    return widget;
}

QWidget* DeviceManagementPanel::createConnectionControlArea()
{
    QWidget* widget = new QWidget();
    QVBoxLayout* layout = new QVBoxLayout(widget);
    layout->setContentsMargins(5, 5, 5, 5);
    layout->setSpacing(5);
    
    // 创建设备选择组
    QGroupBox* deviceSelectGroup = new QGroupBox(tr("设备选择"));
    QVBoxLayout* deviceSelectLayout = new QVBoxLayout(deviceSelectGroup);
    
    m_deviceCombo = new QComboBox();
    m_deviceCombo->setPlaceholderText(tr("选择设备..."));
    deviceSelectLayout->addWidget(m_deviceCombo);
    
    layout->addWidget(deviceSelectGroup);
    
    // 创建端口选择组
    QGroupBox* portSelectGroup = new QGroupBox(tr("端口选择"));
    QVBoxLayout* portSelectLayout = new QVBoxLayout(portSelectGroup);
    
    m_portCombo = new QComboBox();
    m_portCombo->setPlaceholderText(tr("选择端口..."));
    portSelectLayout->addWidget(m_portCombo);
    
    layout->addWidget(portSelectGroup);
    
    // 创建连接控制按钮组
    QGroupBox* controlGroup = new QGroupBox(tr("连接控制"));
    QVBoxLayout* controlLayout = new QVBoxLayout(controlGroup);
    
    QHBoxLayout* btnLayout = new QHBoxLayout();
    m_connectBtn = new QPushButton(tr("连接"));
    m_disconnectBtn = new QPushButton(tr("断开"));
    m_autoMatchBtn = new QPushButton(tr("自动匹配"));
    
    btnLayout->addWidget(m_connectBtn);
    btnLayout->addWidget(m_disconnectBtn);
    controlLayout->addLayout(btnLayout);
    controlLayout->addWidget(m_autoMatchBtn);
    
    layout->addWidget(controlGroup);
    
    // 创建连接状态显示
    QGroupBox* statusGroup = new QGroupBox(tr("连接状态"));
    QVBoxLayout* statusLayout = new QVBoxLayout(statusGroup);
    
    m_connectionStatus = new QLabel(tr("未连接"));
    m_connectionStatus->setAlignment(Qt::AlignCenter);
    m_connectionStatus->setStyleSheet("QLabel { background-color: #f0f0f0; border: 1px solid #ccc; padding: 5px; }");
    statusLayout->addWidget(m_connectionStatus);
    
    layout->addWidget(statusGroup);
    
    layout->addStretch();
    
    return widget;
}

void DeviceManagementPanel::connectSignals()
{
    // 设备发现按钮信号
    connect(m_startDiscoveryBtn, &QPushButton::clicked, this, &DeviceManagementPanel::startDeviceDiscovery);
    connect(m_stopDiscoveryBtn, &QPushButton::clicked, this, &DeviceManagementPanel::stopDeviceDiscovery);
    connect(m_refreshDevicesBtn, &QPushButton::clicked, this, &DeviceManagementPanel::updateDeviceList);
    
    // 端口管理按钮信号
    connect(m_refreshPortsBtn, &QPushButton::clicked, this, &DeviceManagementPanel::refreshPortList);
    
    // 连接控制按钮信号
    connect(m_connectBtn, &QPushButton::clicked, this, &DeviceManagementPanel::connectSelectedDevice);
    connect(m_disconnectBtn, &QPushButton::clicked, this, &DeviceManagementPanel::disconnectSelectedDevice);
    connect(m_autoMatchBtn, &QPushButton::clicked, this, &DeviceManagementPanel::autoMatchDevicesAndPorts);
    
    // 设备树双击信号
    connect(m_deviceTree, &QTreeWidget::itemDoubleClicked, this, &DeviceManagementPanel::onDeviceDoubleClicked);
    
    // 端口表双击信号
    connect(m_portTable, &QTableWidget::itemDoubleClicked, this, &DeviceManagementPanel::onPortDoubleClicked);
    
    // 选择变化信号
    connect(m_deviceTree, &QTreeWidget::itemSelectionChanged, this, &DeviceManagementPanel::onDeviceSelectionChanged);
    connect(m_portTable, &QTableWidget::itemSelectionChanged, this, &DeviceManagementPanel::onPortSelectionChanged);
    connect(m_deviceCombo, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &DeviceManagementPanel::onConnectionStatusChanged);
    connect(m_portCombo, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &DeviceManagementPanel::onConnectionStatusChanged);
}

void DeviceManagementPanel::startDeviceDiscovery()
{
    qDebug() << "DeviceManagementPanel: 开始设备发现";
    
    if (!m_discoveryService) {
        QMessageBox::warning(this, tr("错误"), tr("设备发现服务未初始化"));
        return;
    }
    
    if (m_discoveryService->startDiscovery()) {
        m_discoveryRunning = true;
        m_startDiscoveryBtn->setEnabled(false);
        m_stopDiscoveryBtn->setEnabled(true);
        m_discoveryProgress->setVisible(true);
        m_discoveryProgress->setRange(0, 0); // 无限进度条
        
        qDebug() << "DeviceManagementPanel: 设备发现已启动";
    } else {
        QMessageBox::warning(this, tr("错误"), tr("启动设备发现失败"));
    }
}

void DeviceManagementPanel::stopDeviceDiscovery()
{
    qDebug() << "DeviceManagementPanel: 停止设备发现";
    
    if (!m_discoveryService) {
        return;
    }
    
    m_discoveryService->stopDiscovery();
    m_discoveryRunning = false;
    m_startDiscoveryBtn->setEnabled(true);
    m_stopDiscoveryBtn->setEnabled(false);
    m_discoveryProgress->setVisible(false);
    
    qDebug() << "DeviceManagementPanel: 设备发现已停止";
}

void DeviceManagementPanel::refreshPortList()
{
    qDebug() << "DeviceManagementPanel: 刷新端口列表";
    updatePortList();
}

void DeviceManagementPanel::connectSelectedDevice()
{
    QString deviceId = m_deviceCombo->currentData().toString();
    QString portName = m_portCombo->currentData().toString();
    
    if (deviceId.isEmpty() || portName.isEmpty()) {
        QMessageBox::warning(this, tr("警告"), tr("请选择设备和端口"));
        return;
    }
    
    if (connectDeviceToPort(deviceId, portName)) {
        updateConnectionStatus();
        QMessageBox::information(this, tr("成功"), tr("设备连接成功"));
    } else {
        QMessageBox::warning(this, tr("错误"), tr("设备连接失败"));
    }
}

void DeviceManagementPanel::disconnectSelectedDevice()
{
    QString deviceId = m_deviceCombo->currentData().toString();
    
    if (deviceId.isEmpty()) {
        QMessageBox::warning(this, tr("警告"), tr("请选择设备"));
        return;
    }
    
    if (disconnectDeviceFromPort(deviceId)) {
        updateConnectionStatus();
        QMessageBox::information(this, tr("成功"), tr("设备断开成功"));
    } else {
        QMessageBox::warning(this, tr("错误"), tr("设备断开失败"));
    }
}

void DeviceManagementPanel::autoMatchDevicesAndPorts()
{
    qDebug() << "DeviceManagementPanel: 自动匹配设备和端口";
    
    if (!m_deviceRegistry || !m_portManager) {
        QMessageBox::warning(this, tr("错误"), tr("服务未初始化"));
        return;
    }
    
    int matchedCount = 0;
    
    // 获取所有设备和端口
    QStringList deviceIds = m_deviceRegistry->getAllDeviceIds();
    // 注意：这里需要端口管理器提供获取所有端口的接口
    
    for (const QString& deviceId : deviceIds) {
        // 检查设备是否已连接
        if (m_devicePortMap.contains(deviceId)) {
            continue;
        }
        
        // 尝试为设备找到合适的端口
        // 这里需要根据设备类型匹配对应的端口类型
        // 实际实现会更复杂，需要考虑端口状态、兼容性等
        
        matchedCount++;
    }
    
    updateConnectionStatus();
    QMessageBox::information(this, tr("自动匹配"), tr("已匹配 %1 个设备").arg(matchedCount));
}

// 实现槽函数
void DeviceManagementPanel::onDeviceDiscovered(const LA::Foundation::Core::DeviceInfo& device)
{
    qDebug() << "DeviceManagementPanel: 发现设备:" << device.deviceName;
    addDeviceToTree(device);
    updateStatistics();
    
    // 更新下拉框
    m_deviceCombo->addItem(device.deviceName, device.deviceId);
}

void DeviceManagementPanel::onDeviceLost(const QString& deviceId)
{
    qDebug() << "DeviceManagementPanel: 丢失设备:" << deviceId;
    removeDeviceFromTree(deviceId);
    updateStatistics();
    
    // 从下拉框移除
    for (int i = 0; i < m_deviceCombo->count(); ++i) {
        if (m_deviceCombo->itemData(i).toString() == deviceId) {
            m_deviceCombo->removeItem(i);
            break;
        }
    }
}

void DeviceManagementPanel::onDiscoveryCompleted(int deviceCount)
{
    qDebug() << "DeviceManagementPanel: 发现完成，设备数:" << deviceCount;
    m_discoveryProgress->setRange(0, 1);
    m_discoveryProgress->setValue(1);
    QTimer::singleShot(1000, [this]() {
        if (!m_discoveryRunning) {
            m_discoveryProgress->setVisible(false);
        } else {
            m_discoveryProgress->setRange(0, 0);
        }
    });
}

void DeviceManagementPanel::onDiscoveryError(const QString& error)
{
    qWarning() << "DeviceManagementPanel: 发现错误:" << error;
    QMessageBox::warning(this, tr("发现错误"), error);
}

void DeviceManagementPanel::onDeviceSelectionChanged()
{
    QTreeWidgetItem* item = m_deviceTree->currentItem();
    if (item && item->parent()) { // 确保是设备项而不是类别项
        QString deviceId = item->data(0, Qt::UserRole).toString();
        
        // 在下拉框中选择对应设备
        for (int i = 0; i < m_deviceCombo->count(); ++i) {
            if (m_deviceCombo->itemData(i).toString() == deviceId) {
                m_deviceCombo->setCurrentIndex(i);
                break;
            }
        }
    }
}

void DeviceManagementPanel::onPortSelectionChanged()
{
    int row = m_portTable->currentRow();
    if (row >= 0) {
        QTableWidgetItem* item = m_portTable->item(row, 0);
        if (item) {
            QString portName = item->text();
            
            // 在下拉框中选择对应端口
            for (int i = 0; i < m_portCombo->count(); ++i) {
                if (m_portCombo->itemData(i).toString() == portName) {
                    m_portCombo->setCurrentIndex(i);
                    break;
                }
            }
        }
    }
}

void DeviceManagementPanel::onDeviceDoubleClicked(QTreeWidgetItem* item, int column)
{
    Q_UNUSED(column)
    
    if (!item || !item->parent()) {
        return;
    }
    
    QString deviceId = item->data(0, Qt::UserRole).toString();
    if (deviceId.isEmpty()) {
        return;
    }
    
    // 获取设备信息并显示详细对话框
    if (m_deviceRegistry && m_deviceRegistry->isDeviceRegistered(deviceId)) {
        // 这里需要从注册表获取设备详细信息
        // 然后显示设备信息对话框
        qDebug() << "DeviceManagementPanel: 显示设备详细信息:" << deviceId;
    }
}

void DeviceManagementPanel::onPortDoubleClicked(QTableWidgetItem* item)
{
    if (!item) {
        return;
    }
    
    int row = item->row();
    QTableWidgetItem* portItem = m_portTable->item(row, 0);
    if (!portItem) {
        return;
    }
    
    QString portName = portItem->text();
    qDebug() << "DeviceManagementPanel: 显示端口详细信息:" << portName;
    
    // 显示端口信息对话框
}

void DeviceManagementPanel::onConnectionStatusChanged()
{
    updateConnectionStatus();
}

void DeviceManagementPanel::onRefreshTimer()
{
    // 定期刷新状态
    updateStatistics();
}

void DeviceManagementPanel::updateDeviceList()
{
    qDebug() << "DeviceManagementPanel: 更新设备列表";
    
    m_deviceTree->clear();
    m_deviceCombo->clear();
    
    if (!m_deviceRegistry) {
        return;
    }
    
    QStringList deviceIds = m_deviceRegistry->getAllDeviceIds();
    
    // 创建按类型分类的树形结构
    QMap<QString, QTreeWidgetItem*> categoryItems;
    
    for (const QString& deviceId : deviceIds) {
        if (m_deviceRegistry->isDeviceRegistered(deviceId)) {
            // 这里需要从注册表获取设备信息
            // 暂时创建模拟数据
            LA::Foundation::Core::DeviceInfo device;
            device.deviceId = deviceId;
            device.deviceName = QString("Device_%1").arg(deviceId);
            device.deviceType = LA::Foundation::Core::DeviceType::Virtual;
            
            addDeviceToTree(device);
            m_deviceCombo->addItem(device.deviceName, device.deviceId);
        }
    }
    
    updateStatistics();
}

void DeviceManagementPanel::updatePortList()
{
    qDebug() << "DeviceManagementPanel: 更新端口列表";
    
    m_portTable->setRowCount(0);
    m_portCombo->clear();
    
    if (!m_portManager) {
        return;
    }
    
    // 这里需要端口管理器提供获取所有端口的接口
    // 暂时创建模拟数据
    for (int i = 1; i <= 3; ++i) {
        LA::Foundation::Core::PortInfo port;
        port.portName = QString("COM%1").arg(i);
        port.portType = LA::Foundation::Core::PortType::Serial;
        port.status = LA::Foundation::Core::PortStatus::Available;
        port.description = QString("Serial Port %1").arg(i);
        
        addPortToTable(port);
        m_portCombo->addItem(port.portName, port.portName);
    }
    
    updateStatistics();
}

void DeviceManagementPanel::updateConnectionStatus()
{
    QString deviceId = m_deviceCombo->currentData().toString();
    QString portName = m_portCombo->currentData().toString();
    
    bool canConnect = !deviceId.isEmpty() && !portName.isEmpty() && 
                     !m_devicePortMap.contains(deviceId) &&
                     isDevicePortCompatible(deviceId, portName);
    
    bool isConnected = m_devicePortMap.contains(deviceId);
    
    m_connectBtn->setEnabled(canConnect && !isConnected);
    m_disconnectBtn->setEnabled(isConnected);
    
    if (isConnected) {
        QString connectedPort = m_devicePortMap.value(deviceId);
        m_connectionStatus->setText(tr("已连接到 %1").arg(connectedPort));
        m_connectionStatus->setStyleSheet("QLabel { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 5px; }");
    } else {
        m_connectionStatus->setText(tr("未连接"));
        m_connectionStatus->setStyleSheet("QLabel { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 5px; }");
    }
}

void DeviceManagementPanel::updateStatistics()
{
    int deviceCount = m_deviceTree->topLevelItemCount();
    int portCount = m_portTable->rowCount();
    
    m_deviceCountLabel->setText(tr("设备数: %1").arg(deviceCount));
    m_portCountLabel->setText(tr("端口数: %1").arg(portCount));
}

void DeviceManagementPanel::addDeviceToTree(const LA::Foundation::Core::DeviceInfo& device)
{
    QString categoryName;
    switch (device.deviceType) {
        case LA::Foundation::Core::DeviceType::Serial:
            categoryName = tr("串口设备");
            break;
        case LA::Foundation::Core::DeviceType::Network:
            categoryName = tr("网络设备");
            break;
        case LA::Foundation::Core::DeviceType::Virtual:
            categoryName = tr("虚拟设备");
            break;
        default:
            categoryName = tr("其他设备");
            break;
    }
    
    // 查找或创建类别项
    QTreeWidgetItem* categoryItem = nullptr;
    for (int i = 0; i < m_deviceTree->topLevelItemCount(); ++i) {
        QTreeWidgetItem* item = m_deviceTree->topLevelItem(i);
        if (item->text(0) == categoryName) {
            categoryItem = item;
            break;
        }
    }
    
    if (!categoryItem) {
        categoryItem = new QTreeWidgetItem(m_deviceTree);
        categoryItem->setText(0, categoryName);
        categoryItem->setIcon(0, createDeviceIcon(device));
        categoryItem->setExpanded(true);
    }
    
    // 添加设备项
    QTreeWidgetItem* deviceItem = new QTreeWidgetItem(categoryItem);
    deviceItem->setText(0, device.deviceName);
    deviceItem->setText(1, QString::number(static_cast<int>(device.deviceType)));
    deviceItem->setText(2, tr("可用"));
    deviceItem->setData(0, Qt::UserRole, device.deviceId);
    deviceItem->setIcon(0, createDeviceIcon(device));
}

void DeviceManagementPanel::removeDeviceFromTree(const QString& deviceId)
{
    for (int i = 0; i < m_deviceTree->topLevelItemCount(); ++i) {
        QTreeWidgetItem* categoryItem = m_deviceTree->topLevelItem(i);
        for (int j = 0; j < categoryItem->childCount(); ++j) {
            QTreeWidgetItem* deviceItem = categoryItem->child(j);
            if (deviceItem->data(0, Qt::UserRole).toString() == deviceId) {
                delete deviceItem;
                
                // 如果类别下没有设备了，删除类别
                if (categoryItem->childCount() == 0) {
                    delete categoryItem;
                }
                return;
            }
        }
    }
}

void DeviceManagementPanel::addPortToTable(const LA::Foundation::Core::PortInfo& port)
{
    int row = m_portTable->rowCount();
    m_portTable->insertRow(row);
    
    m_portTable->setItem(row, 0, new QTableWidgetItem(port.portName));
    m_portTable->setItem(row, 1, new QTableWidgetItem(QString::number(static_cast<int>(port.portType))));
    
    QString statusText;
    switch (port.status) {
        case LA::Foundation::Core::PortStatus::Available:
            statusText = tr("可用");
            break;
        case LA::Foundation::Core::PortStatus::Occupied:
            statusText = tr("占用");
            break;
        case LA::Foundation::Core::PortStatus::Error:
            statusText = tr("错误");
            break;
        default:
            statusText = tr("未知");
            break;
    }
    
    QTableWidgetItem* statusItem = new QTableWidgetItem(statusText);
    statusItem->setIcon(createPortStatusIcon(static_cast<int>(port.status)));
    m_portTable->setItem(row, 2, statusItem);
    
    m_portTable->setItem(row, 3, new QTableWidgetItem(port.description));
}

void DeviceManagementPanel::removePortFromTable(const QString& portName)
{
    for (int row = 0; row < m_portTable->rowCount(); ++row) {
        QTableWidgetItem* item = m_portTable->item(row, 0);
        if (item && item->text() == portName) {
            m_portTable->removeRow(row);
            break;
        }
    }
}

QString DeviceManagementPanel::getSelectedDeviceId() const
{
    QTreeWidgetItem* item = m_deviceTree->currentItem();
    if (item && item->parent()) {
        return item->data(0, Qt::UserRole).toString();
    }
    return QString();
}

QString DeviceManagementPanel::getSelectedPortName() const
{
    int row = m_portTable->currentRow();
    if (row >= 0) {
        QTableWidgetItem* item = m_portTable->item(row, 0);
        if (item) {
            return item->text();
        }
    }
    return QString();
}

bool DeviceManagementPanel::isDevicePortCompatible(const QString& deviceId, const QString& portName) const
{
    // 简化的兼容性检查
    // 实际实现需要根据设备类型和端口类型进行详细检查
    Q_UNUSED(deviceId)
    Q_UNUSED(portName)
    return true;
}

bool DeviceManagementPanel::connectDeviceToPort(const QString& deviceId, const QString& portName)
{
    qDebug() << "DeviceManagementPanel: 连接设备" << deviceId << "到端口" << portName;
    
    // 检查兼容性
    if (!isDevicePortCompatible(deviceId, portName)) {
        return false;
    }
    
    // 检查端口是否可用
    if (m_portDeviceMap.contains(portName)) {
        return false;
    }
    
    // 建立连接映射
    m_devicePortMap[deviceId] = portName;
    m_portDeviceMap[portName] = deviceId;
    
    // 这里需要调用实际的连接逻辑
    // 例如：m_portManager->connectDevice(deviceId, portName);
    
    return true;
}

bool DeviceManagementPanel::disconnectDeviceFromPort(const QString& deviceId)
{
    qDebug() << "DeviceManagementPanel: 断开设备" << deviceId;
    
    if (!m_devicePortMap.contains(deviceId)) {
        return false;
    }
    
    QString portName = m_devicePortMap.value(deviceId);
    
    // 移除连接映射
    m_devicePortMap.remove(deviceId);
    m_portDeviceMap.remove(portName);
    
    // 这里需要调用实际的断开逻辑
    // 例如：m_portManager->disconnectDevice(deviceId);
    
    return true;
}

QIcon DeviceManagementPanel::createDeviceIcon(const LA::Foundation::Core::DeviceInfo& device) const
{
    switch (device.deviceType) {
        case LA::Foundation::Core::DeviceType::Serial:
            return QApplication::style()->standardIcon(QStyle::SP_DriveHDIcon);
        case LA::Foundation::Core::DeviceType::Network:
            return QApplication::style()->standardIcon(QStyle::SP_DriveNetIcon);
        case LA::Foundation::Core::DeviceType::Virtual:
            return QApplication::style()->standardIcon(QStyle::SP_ComputerIcon);
        default:
            return QApplication::style()->standardIcon(QStyle::SP_FileIcon);
    }
}

QIcon DeviceManagementPanel::createPortStatusIcon(int status) const
{
    switch (status) {
        case static_cast<int>(LA::Foundation::Core::PortStatus::Available):
            return QApplication::style()->standardIcon(QStyle::SP_DialogApplyButton);
        case static_cast<int>(LA::Foundation::Core::PortStatus::Occupied):
            return QApplication::style()->standardIcon(QStyle::SP_DialogCancelButton);
        case static_cast<int>(LA::Foundation::Core::PortStatus::Error):
            return QApplication::style()->standardIcon(QStyle::SP_MessageBoxCritical);
        default:
            return QApplication::style()->standardIcon(QStyle::SP_MessageBoxQuestion);
    }
}

} // namespace RightSidebar
} // namespace LA