#ifndef IMAGEPROCESSING_BILINEARINTERPOLATION_H
#define IMAGEPROCESSING_BILINEARINTERPOLATION_H

#include "../interfaces/IInterpolation.h"
#include "../common/ValidationUtils.h"
#include <QDebug>
#include <QtMath>

namespace ImageProcessing {

/**
 * @brief 双线性插值算法实现
 * 
 * 实现高质量的双线性插值算法，支持图像放大和缩小
 * 基于原有my_interPolation::bilinear_interpolation的逻辑重构
 */
class BilinearInterpolation : public IInterpolation {
public:
    /**
     * @brief 构造函数
     */
    BilinearInterpolation();

    /**
     * @brief 析构函数
     */
    ~BilinearInterpolation() override = default;

    // IInterpolation接口实现
    bool interpolate(const ImageDataU32& src, ImageDataU32& dst) override;
    void setParameters(const InterpolationParams& params) override;
    InterpolationParams getParameters() const override;
    QString getAlgorithmName() const override;
    QString getDescription() const override;
    bool isSupported(uint32_t srcWidth, uint32_t srcHeight,
                    uint32_t dstWidth, uint32_t dstHeight) const override;
    uint32_t estimateProcessingTime(uint32_t srcWidth, uint32_t srcHeight,
                                  uint32_t dstWidth, uint32_t dstHeight) const override;
    void reset() override;
    QString getVersion() const override;
    bool isThreadSafe() const override;

private:
    InterpolationParams params_;    ///< 插值参数

    /**
     * @brief 计算源图像中的索引位置（缩小时使用偏移）
     * @param dstIndex 目标图像索引
     * @param srcBoardLen 源图像边长
     * @param dstBoardLen 目标图像边长
     * @param offset 偏移量
     * @return 源图像中的浮点索引
     */
    float calculateSourceIndex(uint32_t dstIndex, uint32_t srcBoardLen, 
                              uint32_t dstBoardLen, float offset) const;

    /**
     * @brief 计算源图像中的索引位置（放大时使用）
     * @param dstIndex 目标图像索引
     * @param srcBoardLen 源图像边长
     * @param dstBoardLen 目标图像边长
     * @return 源图像中的浮点索引
     */
    float calculateSourceIndexExpand(uint32_t dstIndex, uint32_t srcBoardLen, 
                                    uint32_t dstBoardLen) const;

    /**
     * @brief 线性插值计算
     * @param rate 插值比率 (0.0 - 1.0)
     * @param pointF 前端点值
     * @param pointB 后端点值
     * @return 插值结果
     */
    uint32_t linearInterpolation(float rate, uint32_t pointF, uint32_t pointB) const;

    /**
     * @brief 双线性插值计算
     * @param rateX X方向插值比率
     * @param rateY Y方向插值比率
     * @param f1 左上角点值
     * @param f2 左下角点值
     * @param f3 右下角点值
     * @param f4 右上角点值
     * @return 双线性插值结果
     */
    uint32_t bilinearCalculation(float rateX, float rateY,
                                uint32_t f1, uint32_t f2, 
                                uint32_t f3, uint32_t f4) const;

    /**
     * @brief 处理边界点（顶点）
     * @param src 源图像
     * @param dst 目标图像
     */
    void processCornerPoints(const ImageDataU32& src, ImageDataU32& dst) const;

    /**
     * @brief 处理边界线（边缘）
     * @param src 源图像
     * @param dst 目标图像
     */
    void processBoundaryLines(const ImageDataU32& src, ImageDataU32& dst) const;

    /**
     * @brief 处理内部区域
     * @param src 源图像
     * @param dst 目标图像
     */
    void processInteriorRegion(const ImageDataU32& src, ImageDataU32& dst) const;

    /**
     * @brief 获取安全的像素值（边界处理）
     * @param src 源图像
     * @param x X坐标
     * @param y Y坐标
     * @return 像素值
     */
    uint32_t getSafePixelValue(const ImageDataU32& src, int x, int y) const;

    /**
     * @brief 验证插值参数
     * @param src 源图像
     * @param dst 目标图像
     * @throws ProcessingException 如果参数无效
     */
    void validateInterpolationInputs(const ImageDataU32& src, const ImageDataU32& dst) const;

    /**
     * @brief 记录调试信息
     * @param message 调试信息
     */
    void logDebug(const QString& message) const;
};

} // namespace ImageProcessing

#endif // IMAGEPROCESSING_BILINEARINTERPOLATION_H
