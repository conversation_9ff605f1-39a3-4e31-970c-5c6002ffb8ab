@echo off
echo ========================================
echo LA Theme System Test Script
echo ========================================
echo.

echo Testing LA Application with Theme System...
echo.

cd /d "%~dp0..\build\Debug"

echo Checking application file...
if exist "bin\CSPC_LA_function.exe" (
    echo [OK] Application exists: CSPC_LA_function.exe
) else (
    echo [ERROR] Application not found: CSPC_LA_function.exe
    goto :error
)

echo.
echo Checking theme library...
if exist "bin\libLA_themes.dll" (
    echo [OK] Theme library exists: libLA_themes.dll
) else (
    echo [ERROR] Theme library not found: libLA_themes.dll
    goto :error
)

echo.
echo Checking all required libraries...
set "missing_libs="

for %%L in (
    "libLA_editview.dll"
    "libLA_rightsidebar.dll" 
    "libLA_sidebar.dll"
    "libLA_themes.dll"
    "Qt5Core.dll"
    "Qt5Widgets.dll"
    "Qt5Gui.dll"
) do (
    if exist "bin\%%~L" (
        echo [OK] %%~L
    ) else (
        echo [ERROR] Missing: %%~L
        set "missing_libs=1"
    )
)

if defined missing_libs (
    echo.
    echo [ERROR] Some required libraries are missing!
    goto :error
)

echo.
echo ========================================
echo All checks passed! 
echo ========================================
echo.

echo Starting application to test theme system...
echo.
echo Instructions:
echo 1. The application should start with the default Industrial theme
echo 2. Try the "主题" menu to switch between themes:
echo    - 工业蓝主题 (Industrial Blue)
echo    - 现代主题 (Modern)
echo    - 亮色主题 (Light)
echo    - 暗色主题 (Dark)
echo 3. Try the "暗色模式" toggle
echo 4. Check if the UI colors change properly
echo.

echo Press any key to start the application...
pause >nul

start "" "bin\CSPC_LA_function.exe"

echo.
echo Application started! Check the theme menu functionality.
echo Press any key to exit this script...
pause >nul

goto :end

:error
echo.
echo ========================================
echo Test failed! Please check the errors above.
echo ========================================
pause
exit /b 1

:end
echo.
echo Theme system test completed.
exit /b 0
