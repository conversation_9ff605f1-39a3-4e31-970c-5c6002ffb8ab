#include <LA/Communication/Thread/ICommunicationThreadLinus.h>
#include <QMutex>
#include <QMutexLocker>
#include <QQueue>
#include <QWaitCondition>
#include <QDateTime>
#include <QTimer>
#include <QDebug>

namespace LA {
namespace Communication {
namespace Thread {

/**
 * @brief Linus式通信线程实现
 * 
 * 严格遵循ICommunicationThreadLinus接口，只实现基础的异步数据传输功能
 */
class CommunicationThreadLinus : public ICommunicationThreadLinus {
    Q_OBJECT

public:
    explicit CommunicationThreadLinus(QObject* parent = nullptr);
    virtual ~CommunicationThreadLinus();

    // === 线程控制 ===
    SimpleResult startThread() override;
    SimpleResult stopThread() override;
    bool isThreadRunning() const override;

    // === 数据传输 ===
    SimpleResult sendData(const QByteArray& data) override;
    QByteArray receiveData(qint64 maxBytes = -1) override;
    qint64 bytesAvailable() const override;
    SimpleResult clearSendQueue() override;
    SimpleResult clearReceiveBuffer() override;

    // === 状态查询 ===
    DeviceStatistics getStatistics() const override;
    QString errorString() const override;
    void resetStatistics() override;
    int getSendQueueSize() const override;
    int getReceiveBufferSize() const override;

    // === 配置管理 ===
    SimpleResult setThreadPriority(QThread::Priority priority) override;
    SimpleResult setProcessingInterval(int intervalMs) override;
    SimpleResult setMaxQueueSize(int maxSize) override;

protected:
    void run() override;

private:
    mutable QMutex m_mutex;
    QWaitCondition m_condition;
    
    bool m_stopRequested;
    bool m_isRunning;
    
    // 数据队列
    QQueue<QByteArray> m_sendQueue;
    QQueue<QByteArray> m_receiveQueue;
    
    // 配置参数
    int m_processingIntervalMs;
    int m_maxQueueSize;
    
    // 统计信息
    DeviceStatistics m_statistics;
    QString m_lastError;
    
    // 内部方法
    void updateStatistics(qint64 bytesSent = 0, qint64 bytesReceived = 0);
    void processQueues();
    void emitStatisticsUpdate();
};

CommunicationThreadLinus::CommunicationThreadLinus(QObject* parent)
    : ICommunicationThreadLinus(parent)
    , m_stopRequested(false)
    , m_isRunning(false)
    , m_processingIntervalMs(10)  // 10ms默认间隔
    , m_maxQueueSize(1000)        // 默认最大队列大小
{
    resetStatistics();
}

CommunicationThreadLinus::~CommunicationThreadLinus() {
    if (isRunning()) {
        stopThread();
        wait(3000); // 等待3秒让线程退出
    }
}

// === 线程控制 ===

SimpleResult CommunicationThreadLinus::startThread() {
    QMutexLocker locker(&m_mutex);
    
    if (m_isRunning) {
        return SimpleResult::success(true);
    }
    
    m_stopRequested = false;
    
    // 启动Qt线程
    start();
    
    // 等待线程真正开始运行
    if (!m_condition.wait(&m_mutex, 5000)) { // 5秒超时
        m_lastError = "Thread failed to start within timeout";
        return SimpleResult::failure(m_lastError);
    }
    
    if (!m_isRunning) {
        m_lastError = "Thread failed to start";
        return SimpleResult::failure(m_lastError);
    }
    
    emit threadStateChanged(true);
    return SimpleResult::success(true);
}

SimpleResult CommunicationThreadLinus::stopThread() {
    QMutexLocker locker(&m_mutex);
    
    if (!m_isRunning) {
        return SimpleResult::success(true);
    }
    
    m_stopRequested = true;
    m_condition.wakeAll();
    
    locker.unlock();
    
    // 等待线程退出
    if (!wait(3000)) { // 3秒超时
        terminate(); // 强制终止
        wait(1000);  // 再等1秒
        m_lastError = "Thread terminated forcefully";
    }
    
    locker.relock();
    m_isRunning = false;
    
    emit threadStateChanged(false);
    return SimpleResult::success(true);
}

bool CommunicationThreadLinus::isThreadRunning() const {
    QMutexLocker locker(&m_mutex);
    return m_isRunning;
}

// === 数据传输 ===

SimpleResult CommunicationThreadLinus::sendData(const QByteArray& data) {
    QMutexLocker locker(&m_mutex);
    
    if (!m_isRunning) {
        m_lastError = "Thread not running";
        return SimpleResult::failure(m_lastError);
    }
    
    if (data.isEmpty()) {
        m_lastError = "Empty data";
        return SimpleResult::failure(m_lastError);
    }
    
    if (m_sendQueue.size() >= m_maxQueueSize) {
        m_lastError = "Send queue full";
        return SimpleResult::failure(m_lastError);
    }
    
    m_sendQueue.enqueue(data);
    m_condition.wakeAll(); // 唤醒处理线程
    
    return SimpleResult::success(true);
}

QByteArray CommunicationThreadLinus::receiveData(qint64 maxBytes) {
    QMutexLocker locker(&m_mutex);
    
    if (m_receiveQueue.isEmpty()) {
        return QByteArray();
    }
    
    QByteArray result;
    qint64 totalBytes = 0;
    
    while (!m_receiveQueue.isEmpty() && (maxBytes == -1 || totalBytes < maxBytes)) {
        QByteArray data = m_receiveQueue.dequeue();
        
        if (maxBytes != -1 && totalBytes + data.size() > maxBytes) {
            // 只取需要的部分，剩余部分放回队列
            qint64 remainingBytes = maxBytes - totalBytes;
            result.append(data.left(remainingBytes));
            m_receiveQueue.prepend(data.mid(remainingBytes));
            break;
        } else {
            result.append(data);
            totalBytes += data.size();
        }
    }
    
    return result;
}

qint64 CommunicationThreadLinus::bytesAvailable() const {
    QMutexLocker locker(&m_mutex);
    
    qint64 totalBytes = 0;
    for (const auto& data : m_receiveQueue) {
        totalBytes += data.size();
    }
    
    return totalBytes;
}

SimpleResult CommunicationThreadLinus::clearSendQueue() {
    QMutexLocker locker(&m_mutex);
    
    m_sendQueue.clear();
    return SimpleResult::success(true);
}

SimpleResult CommunicationThreadLinus::clearReceiveBuffer() {
    QMutexLocker locker(&m_mutex);
    
    m_receiveQueue.clear();
    return SimpleResult::success(true);
}

// === 状态查询 ===

DeviceStatistics CommunicationThreadLinus::getStatistics() const {
    QMutexLocker locker(&m_mutex);
    return m_statistics;
}

QString CommunicationThreadLinus::errorString() const {
    QMutexLocker locker(&m_mutex);
    return m_lastError;
}

void CommunicationThreadLinus::resetStatistics() {
    QMutexLocker locker(&m_mutex);
    m_statistics = DeviceStatistics();
    m_statistics.lastActivity = QDateTime::currentMSecsSinceEpoch();
    m_lastError.clear();
}

int CommunicationThreadLinus::getSendQueueSize() const {
    QMutexLocker locker(&m_mutex);
    return m_sendQueue.size();
}

int CommunicationThreadLinus::getReceiveBufferSize() const {
    QMutexLocker locker(&m_mutex);
    return m_receiveQueue.size();
}

// === 配置管理 ===

SimpleResult CommunicationThreadLinus::setThreadPriority(QThread::Priority priority) {
    setPriority(priority);
    return SimpleResult::success(true);
}

SimpleResult CommunicationThreadLinus::setProcessingInterval(int intervalMs) {
    QMutexLocker locker(&m_mutex);
    
    if (intervalMs < 1 || intervalMs > 10000) {
        m_lastError = "Invalid processing interval (1-10000ms)";
        return SimpleResult::failure(m_lastError);
    }
    
    m_processingIntervalMs = intervalMs;
    return SimpleResult::success(true);
}

SimpleResult CommunicationThreadLinus::setMaxQueueSize(int maxSize) {
    QMutexLocker locker(&m_mutex);
    
    if (maxSize < 1 || maxSize > 100000) {
        m_lastError = "Invalid max queue size (1-100000)";
        return SimpleResult::failure(m_lastError);
    }
    
    m_maxQueueSize = maxSize;
    return SimpleResult::success(true);
}

// === 线程主循环 ===

void CommunicationThreadLinus::run() {
    {
        QMutexLocker locker(&m_mutex);
        m_isRunning = true;
        m_condition.wakeAll(); // 通知startThread()线程已开始
    }
    
    qDebug() << "CommunicationThreadLinus started";
    
    while (true) {
        {
            QMutexLocker locker(&m_mutex);
            
            if (m_stopRequested) {
                break;
            }
            
            // 处理队列
            processQueues();
            
            // 等待新数据或停止信号
            m_condition.wait(&m_mutex, m_processingIntervalMs);
        }
    }
    
    {
        QMutexLocker locker(&m_mutex);
        m_isRunning = false;
    }
    
    qDebug() << "CommunicationThreadLinus stopped";
}

// === 私有辅助方法 ===

void CommunicationThreadLinus::updateStatistics(qint64 bytesSent, qint64 bytesReceived) {
    qint64 currentTime = QDateTime::currentMSecsSinceEpoch();
    
    if (bytesSent > 0) {
        m_statistics.bytesSent += bytesSent;
        m_statistics.packetsSent++;
        m_statistics.lastActivity = currentTime;
    }
    
    if (bytesReceived > 0) {
        m_statistics.bytesReceived += bytesReceived;
        m_statistics.packetsReceived++;
        m_statistics.lastActivity = currentTime;
    }
}

void CommunicationThreadLinus::processQueues() {
    // 处理发送队列 - 在实际实现中，这里会与具体的连接对象交互
    while (!m_sendQueue.isEmpty()) {
        QByteArray data = m_sendQueue.dequeue();
        
        // 模拟数据发送（实际实现中会调用连接对象的write方法）
        // 这里我们只是更新统计信息并发出信号
        updateStatistics(data.size(), 0);
        emit dataSent(data);
    }
    
    // 处理接收数据 - 在实际实现中，这里会从连接对象读取数据
    // 由于这是一个基础线程实现，具体的数据接收需要在子类中实现
    // 或者通过外部调用addReceivedData()方法来添加接收到的数据
    
    // 定期发出统计更新
    static qint64 lastStatsUpdate = 0;
    qint64 currentTime = QDateTime::currentMSecsSinceEpoch();
    if (currentTime - lastStatsUpdate > 1000) { // 每秒更新一次
        emitStatisticsUpdate();
        lastStatsUpdate = currentTime;
    }
}

void CommunicationThreadLinus::emitStatisticsUpdate() {
    emit statisticsUpdated(m_statistics);
}

/**
 * @brief Linus式通信线程工厂实现
 */
class CommunicationThreadLinusFactory : public ICommunicationThreadLinusFactory {
public:
    virtual ~CommunicationThreadLinusFactory() = default;
    
    std::shared_ptr<ICommunicationThreadLinus> createCommunicationThread() override {
        return std::make_shared<CommunicationThreadLinus>();
    }
    
    bool isSupported() const override {
        return true; // Qt框架总是支持线程
    }
};

} // namespace Thread
} // namespace Communication
} // namespace LA

#include "CommunicationThreadLinus.moc"