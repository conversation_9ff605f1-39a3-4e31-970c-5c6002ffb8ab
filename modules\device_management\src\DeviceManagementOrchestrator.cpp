#include "LA/DeviceManagement/DeviceManagementOrchestrator.h"
// Linus: "集成新的分层架构服务"
#include "LA/DeviceManagement/Discovery/DeviceDiscoveryService.h"
// #include "LA/Communication/PortManagement/PortDiscoveryService.h"  // 暂时禁用，文件不存在
#include <QDebug>
#include <QMutexLocker>

namespace LA::DeviceManagement {

DeviceManagementOrchestrator::DeviceManagementOrchestrator(QObject *parent)
    : QObject(parent),
      m_discoveryInterval(5000)  // 默认5秒
      ,
      m_discoveryActive(false),
      m_autoConnectEnabled(true),
      m_initialized(false) {
    // 注册元类型以支持信号槽
    qRegisterMetaType<DeviceDiscoveryResult>("DeviceDiscoveryResult");

    qDebug() << "DeviceManagementOrchestrator created";
}

DeviceManagementOrchestrator::~DeviceManagementOrchestrator() {
    shutdown();
    qDebug() << "DeviceManagementOrchestrator destroyed";
}

bool DeviceManagementOrchestrator::initialize() {
    QMutexLocker locker(&m_mutex);

    if (m_initialized) {
        qWarning() << "DeviceManagementOrchestrator already initialized";
        return true;
    }

    // 初始化核心组件
    if (!initializeComponents()) {
        setLastError("Failed to initialize components");
        return false;
    }

    m_initialized = true;
    qDebug() << "DeviceManagementOrchestrator initialized successfully";

    return true;
}

void DeviceManagementOrchestrator::shutdown() {
    QMutexLocker locker(&m_mutex);

    if (!m_initialized) {
        return;
    }

    // 停止发现
    if (m_discoveryActive) {
        stopDeviceDiscovery();
    }

    // 关闭所有组件
    shutdownComponents();

    m_initialized = false;
    qDebug() << "DeviceManagementOrchestrator shutdown completed";
}

bool DeviceManagementOrchestrator::isInitialized() const {
    QMutexLocker locker(&m_mutex);
    return m_initialized;
}

void DeviceManagementOrchestrator::startDeviceDiscovery() {
    QMutexLocker locker(&m_mutex);

    if (!m_initialized) {
        setLastError("Orchestrator not initialized");
        return;
    }

    if (m_discoveryActive) {
        qWarning() << "Device discovery already active";
        return;
    }

    // 创建发现定时器（单次执行，不重复）
    if (!m_discoveryTimer) {
        m_discoveryTimer = std::make_unique<QTimer>(this);
        m_discoveryTimer->setSingleShot(true);  // 设置为单次执行
        connect(m_discoveryTimer.get(), &QTimer::timeout, this, &DeviceManagementOrchestrator::onDiscoveryTimer);
    }

    m_discoveryActive = true;
    emit discoveryStarted();
    qDebug() << "Device discovery started (single-shot mode)";

    // 立即执行一次发现
    performDiscovery();

    // 启动单次定时器，用于清理状态
    m_discoveryTimer->start(1000);  // 1秒后清理发现状态
}

void DeviceManagementOrchestrator::stopDeviceDiscovery() {
    QMutexLocker locker(&m_mutex);

    if (!m_discoveryActive) {
        return;
    }

    if (m_discoveryTimer) {
        m_discoveryTimer->stop();
    }

    m_discoveryActive = false;

    emit discoveryStopped();
    qDebug() << "Device discovery stopped";
}

bool DeviceManagementOrchestrator::isDiscoveryActive() const {
    QMutexLocker locker(&m_mutex);
    return m_discoveryActive;
}

QString DeviceManagementOrchestrator::autoCreateDevice(const QString &deviceType, const QString &portName) {
    QMutexLocker locker(&m_mutex);
    m_lastError.clear();

    if (!validateDeviceType(deviceType)) {
        setLastError(QString("Invalid device type: %1").arg(deviceType));
        return QString();
    }

    // 创建设备配置
    QVariantMap config;
    if (!portName.isEmpty()) {
        config["portName"]    = portName;
        config["autoConnect"] = true;
    }

    // Linus: "存根实现，等待DeviceInstanceManager集成"
    qDebug() << "DeviceManagementOrchestrator::autoCreateDevice() - 存根实现";
    qDebug() << "  设备类型:" << deviceType << "端口:" << portName;

    setLastError("DeviceInstanceManager not implemented yet");
    return QString();  // 返回空字符串表示失败
}

bool DeviceManagementOrchestrator::autoConnectDevice(const QString &instanceId) {
    QMutexLocker locker(&m_mutex);

    // Linus: "存根实现，等待DeviceInstanceManager集成"
    qDebug() << "DeviceManagementOrchestrator::autoConnectDevice() - 存根实现";
    qDebug() << "  实例ID:" << instanceId;

    setLastError("DeviceInstanceManager not implemented yet");
    return false;  // 返回false表示失败
}

bool DeviceManagementOrchestrator::autoDisconnectDevice(const QString &instanceId) {
    // Linus: "存根实现，等待DeviceInstanceManager集成"
    qDebug() << "DeviceManagementOrchestrator::autoDisconnectDevice() - 存根实现";
    qDebug() << "  实例ID:" << instanceId;

    setLastError("DeviceInstanceManager not implemented yet");
    return false;
}

QString DeviceManagementOrchestrator::createDevice(const QString &deviceType, const QVariantMap &config) {
    // Linus: "存根实现，等待DeviceInstanceManager集成"
    qDebug() << "DeviceManagementOrchestrator::createDevice() - 存根实现";
    qDebug() << "  设备类型:" << deviceType;

    setLastError("DeviceInstanceManager not implemented yet");
    return QString();
}

bool DeviceManagementOrchestrator::connectDevice(const QString &instanceId, const QString &portName) {
    // Linus: "存根实现，等待DeviceInstanceManager集成"
    qDebug() << "DeviceManagementOrchestrator::connectDevice() - 存根实现";
    qDebug() << "  实例ID:" << instanceId << "端口:" << portName;

    setLastError("DeviceInstanceManager not implemented yet");
    return false;
}

bool DeviceManagementOrchestrator::disconnectDevice(const QString &instanceId) {
    // Linus: "存根实现"
    qDebug() << "DeviceManagementOrchestrator::disconnectDevice() - 存根实现:" << instanceId;
    setLastError("DeviceInstanceManager not implemented yet");
    return false;
}

bool DeviceManagementOrchestrator::removeDevice(const QString &instanceId) {
    // Linus: "存根实现"
    qDebug() << "DeviceManagementOrchestrator::removeDevice() - 存根实现:" << instanceId;
    setLastError("DeviceInstanceManager not implemented yet");
    return false;
}

QStringList DeviceManagementOrchestrator::getAvailablePorts() const {
    // Linus: "使用Discovery服务获取可用端口"
    // 暂时禁用端口发现服务，直接返回空列表
    qDebug() << "DeviceManagementOrchestrator::getAvailablePorts() - 端口发现服务暂时禁用";
    return QStringList();
    
    /*
    if (m_portDiscoveryService) {
        auto        ports = m_portDiscoveryService->discoverPorts();
        QStringList portNames;
        for (const auto &port : ports) {
            portNames.append(port.portName);
        }
        qDebug() << "DeviceManagementOrchestrator::getAvailablePorts() - 发现" << portNames.size() << "个端口";
        return portNames;
    } else {
        qDebug() << "DeviceManagementOrchestrator::getAvailablePorts() - 端口发现服务未就绪";
        return QStringList();
    }
    */
}

QStringList DeviceManagementOrchestrator::getConnectedDevices() const {
    // Linus: "存根实现"
    qDebug() << "DeviceManagementOrchestrator::getConnectedDevices() - 存根实现";
    return QStringList();  // 返回空列表
}

QStringList DeviceManagementOrchestrator::getDisconnectedDevices() const {
    // Linus: "存根实现"
    qDebug() << "DeviceManagementOrchestrator::getDisconnectedDevices() - 存根实现";
    return QStringList();  // 返回空列表
}

QList<DeviceDiscoveryResult> DeviceManagementOrchestrator::getDiscoveryResults() const {
    // Linus: "存根实现"
    qDebug() << "DeviceManagementOrchestrator::getDiscoveryResults() - 存根实现";
    return QList<DeviceDiscoveryResult>();  // 返回空列表
}

DeviceInstanceInfo DeviceManagementOrchestrator::getDeviceInfo(const QString &instanceId) const {
    // Linus: "存根实现"
    qDebug() << "DeviceManagementOrchestrator::getDeviceInfo() - 存根实现:" << instanceId;
    return DeviceInstanceInfo();  // 返回默认构造的对象
}

QString DeviceManagementOrchestrator::getDevicePort(const QString &instanceId) const {
    QMutexLocker locker(&m_mutex);
    return m_devicePortMap.value(instanceId, QString());
}

bool DeviceManagementOrchestrator::isDeviceConnected(const QString &instanceId) const {
    // Linus: "存根实现"
    qDebug() << "DeviceManagementOrchestrator::isDeviceConnected() - 存根实现:" << instanceId;
    return false;  // 返回false表示未连接
}

DeviceInstanceManager *DeviceManagementOrchestrator::getInstanceManager() const {
    // Linus: "存根实现"
    qDebug() << "DeviceManagementOrchestrator::getInstanceManager() - 存根实现";
    return nullptr;  // 返回空指针
}

QString DeviceManagementOrchestrator::getLastError() const {
    QMutexLocker locker(&m_mutex);
    return m_lastError;
}

bool DeviceManagementOrchestrator::initializeComponents() {
    qDebug() << "DeviceManagementOrchestrator: 初始化Linus式分层架构组件";

    // Linus: "暂时禁用DeviceInstanceManager，专注于Discovery服务"
    // m_instanceManager = std::make_unique<DeviceInstanceManager>(this);

    // Linus: "初始化Discovery层服务"
    try {
        // 创建设备发现服务
        m_discoveryService = std::make_unique<Discovery::DeviceDiscoveryService>(this);
        if (!m_discoveryService) {
            setLastError("Failed to create DeviceDiscoveryService");
            return false;
        }

        // 端口发现服务暂时禁用
        // m_portDiscoveryService = std::make_unique<LA::Communication::PortManagement::PortDiscoveryService>(this);
        // if (!m_portDiscoveryService) {
        //     setLastError("Failed to create PortDiscoveryService");
        //     return false;
        // }

        // 连接Discovery服务信号
        connect(m_discoveryService.get(), &Discovery::DeviceDiscoveryService::deviceDiscovered, this, [this](const Discovery::DeviceDiscoveryResult &result) {
            qDebug() << "DeviceManagementOrchestrator: 设备发现" << result.deviceName;
            // TODO: 转换为DeviceManagementOrchestrator的DeviceDiscoveryResult格式
        });

        // 端口发现服务信号连接暂时禁用
        // connect(m_portDiscoveryService.get(), &LA::Communication::PortManagement::PortDiscoveryService::portDiscovered, this, [this](const LA::Communication::PortManagement::PortDiscoveryResult &result) {
        //     qDebug() << "DeviceManagementOrchestrator: 端口发现" << result.portName;
        // });

        qDebug() << "DeviceManagementOrchestrator: Discovery服务初始化成功";

    } catch (const std::exception &e) {
        setLastError(QString("Failed to initialize Discovery services: %1").arg(e.what()));
        return false;
    } catch (...) {
        setLastError("Failed to initialize Discovery services: unknown error");
        return false;
    }

    return true;
}

void DeviceManagementOrchestrator::shutdownComponents() {
    qDebug() << "DeviceManagementOrchestrator: 关闭分层架构组件";

    // 断开Discovery服务连接
    if (m_discoveryService) {
        disconnect(m_discoveryService.get(), nullptr, this, nullptr);
    }
    // 端口发现服务暂时禁用
    // if (m_portDiscoveryService) {
    //     disconnect(m_portDiscoveryService.get(), nullptr, this, nullptr);
    // }

    // 断开实例管理器连接（如果启用）
    // if (m_instanceManager) {
    //     disconnect(m_instanceManager.get(), nullptr, this, nullptr);
    // }

    // 重置组件
    m_discoveryTimer.reset();
    m_discoveryService.reset();
    // m_portDiscoveryService.reset();  // 暂时禁用
    // m_instanceManager.reset();  // 暂时禁用

    qDebug() << "DeviceManagementOrchestrator: 组件关闭完成";
}

void DeviceManagementOrchestrator::performDiscovery() {
    qDebug() << "DeviceManagementOrchestrator: 执行设备和端口发现";

    // Linus: "使用分层架构的Discovery服务"
    if (m_discoveryService) {
        qDebug() << "DeviceManagementOrchestrator: 启动设备发现";
        auto devices = m_discoveryService->discoverDevices();
        qDebug() << "DeviceManagementOrchestrator: 发现" << devices.size() << "个设备";
    }

    // 端口发现服务暂时禁用
    // if (m_portDiscoveryService) {
    //     qDebug() << "DeviceManagementOrchestrator: 启动端口发现";
    //     auto ports = m_portDiscoveryService->discoverPorts();
    //     qDebug() << "DeviceManagementOrchestrator: 发现" << ports.size() << "个端口";
    // }
}

bool DeviceManagementOrchestrator::validateInstanceId(const QString &instanceId) const {
    // Linus: "存根实现，简单验证"
    return !instanceId.isEmpty();
}

bool DeviceManagementOrchestrator::validateDeviceType(const QString &deviceType) const {
    // Linus: "存根实现，简单验证"
    return !deviceType.isEmpty();
}

bool DeviceManagementOrchestrator::validatePortName(const QString &portName) const {
    // TODO: 实现端口名称验证
    return !portName.isEmpty();
}

void DeviceManagementOrchestrator::setLastError(const QString &error) {
    m_lastError = error;
    addErrorToHistory(error);
    emit orchestratorError(error);
}

void DeviceManagementOrchestrator::addErrorToHistory(const QString &error) {
    m_errorHistory.append(QString("[%1] %2").arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss")).arg(error));

    // 限制错误历史长度
    if (m_errorHistory.size() > 100) {
        m_errorHistory.removeFirst();
    }
}

void DeviceManagementOrchestrator::onDiscoveryTimer() {
    // 定时器触发时，清理发现状态
    qDebug() << "DeviceManagementOrchestrator: 发现定时器触发，清理发现状态";

    QMutexLocker locker(&m_mutex);
    m_discoveryActive = false;
    emit discoveryStopped();

    qDebug() << "DeviceManagementOrchestrator: 发现状态已清理";
}

void DeviceManagementOrchestrator::onInstanceCreated(const QString &instanceId) {
    qDebug() << "Instance created:" << instanceId;
}

void DeviceManagementOrchestrator::onInstanceDestroyed(const QString &instanceId) {
    // 清理端口映射
    m_devicePortMap.remove(instanceId);
    qDebug() << "Instance destroyed:" << instanceId;
}

void DeviceManagementOrchestrator::onInstanceConnected(const QString &instanceId) {
    qDebug() << "Instance connected:" << instanceId;
}

void DeviceManagementOrchestrator::onInstanceDisconnected(const QString &instanceId) {
    qDebug() << "Instance disconnected:" << instanceId;
}

void DeviceManagementOrchestrator::onInstanceError(const QString &instanceId, const QString &error) {
    QString fullError = QString("Instance %1 error: %2").arg(instanceId, error);
    setLastError(fullError);
    qWarning() << fullError;
}

// ====== 缺少的私有槽方法实现 ======

void DeviceManagementOrchestrator::onDeviceDiscovered(const DeviceDiscoveryResult &result) {
    // Linus: "设备发现事件处理"
    qDebug() << "DeviceManagementOrchestrator: 设备发现事件" << result.deviceType;
    emit deviceDiscovered(result);
}

}  // namespace LA::DeviceManagement
