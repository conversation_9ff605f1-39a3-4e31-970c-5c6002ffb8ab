#pragma once

#include "IPortScanner.h"
#include <QSerialPortInfo>
#include <QTcpSocket>

namespace LA {
namespace DeviceManagement {
namespace Matching {

/**
 * @brief 系统端口扫描器 - IPortScanner的具体实现
 * 
 * Linus: "只做一件事：扫描系统端口"
 * 实现了端口发现的具体逻辑，支持串口和网络端口
 */
class SystemPortScanner : public IPortScanner {
public:
    SystemPortScanner() = default;
    virtual ~SystemPortScanner() = default;

    // ====== IPortScanner接口实现 ======
    QList<PortInfo> scanAvailablePorts() override;
    PortInfo getPortInfo(const QString& portName) override;
    bool isPortAvailable(const QString& portName) override;

private:
    // ====== 具体实现 - 每个方法只做一件事 ======
    QList<PortInfo> scanSerialPorts();
    QList<PortInfo> scanNetworkPorts();
    PortInfo createPortInfoFromSerial(const QSerialPortInfo& serialInfo);
    PortInfo createPortInfoFromNetwork(const QString& address, int port);
    bool testSerialPortAvailability(const QString& portName);
    bool testNetworkPortAvailability(const QString& address, int port);
};

} // namespace Matching
} // namespace DeviceManagement
} // namespace LA