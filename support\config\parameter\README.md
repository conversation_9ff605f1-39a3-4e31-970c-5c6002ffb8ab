# 参数管理模块 (Parameter Management Module)

## 概述

参数管理模块提供了完整的应用程序参数管理功能，支持参数定义、验证、存储、历史记录和备份等功能。

## 功能特性

### 核心功能
- **参数定义管理** - 支持动态定义和管理参数类型
- **类型安全** - 强类型参数系统，支持多种数据类型
- **验证机制** - 完整的参数值验证规则支持
- **权限控制** - 细粒度的参数访问权限管理
- **作用域管理** - 支持全局、应用、模块等多种作用域

### 高级功能
- **历史记录** - 完整的参数变更历史追踪
- **备份恢复** - 自动和手动参数配置备份
- **导入导出** - 支持JSON、XML等格式的配置导入导出
- **文件监控** - 自动检测配置文件变更并重新加载
- **加密存储** - 支持敏感参数的加密存储

## 使用方法

### 基本使用

```cpp
#include <LA/Support/Config/Parameter/ParameterManager.h>

using namespace LA::Support::Config;

// 创建参数管理器
auto factory = std::make_unique<ParameterManagerFactory>();
auto manager = factory->createParameterManager();

// 定义参数
ParameterDefinition definition;
definition.key = "app_timeout";
definition.name = "Application Timeout";
definition.description = "Timeout value in seconds";
definition.type = ParameterType::INT;
definition.defaultValue = 30;
definition.validation.minValue = 1;
definition.validation.maxValue = 300;

manager->defineParameter(definition);

// 设置参数值
manager->setParameter("app_timeout", 60, "user", "User preference");

// 获取参数值
int timeout = manager->getParameter("app_timeout", 30).toInt();
```

### 高级使用

```cpp
// 参数查询
ParameterQuery query;
query.categories << "network" << "security";
query.types << ParameterType::STRING << ParameterType::INT;

auto result = manager->queryParameters(query);
if (result.success) {
    for (const auto& param : result.data) {
        qDebug() << "Parameter:" << param.key << param.description;
    }
}

// 参数历史查询
auto historyResult = manager->getParameterHistory("app_timeout", 10);
if (historyResult.success) {
    for (const auto& record : historyResult.data) {
        qDebug() << "Changed from" << record.oldValue 
                 << "to" << record.newValue 
                 << "at" << record.timestamp;
    }
}

// 备份参数
auto backupResult = manager->backupParameters("manual_backup");
if (backupResult.success) {
    qDebug() << "Backup created:" << backupResult.data;
}
```

## 参数类型系统

### 支持的参数类型

| 类型 | 描述 | 示例 |
|------|------|------|
| `BOOL` | 布尔值 | true, false |
| `INT` | 整数 | 42, -10 |
| `DOUBLE` | 浮点数 | 3.14, -2.5 |
| `STRING` | 字符串 | "Hello World" |
| `LIST` | 列表 | ["a", "b", "c"] |
| `MAP` | 映射 | {"key": "value"} |
| `DATETIME` | 日期时间 | 2024-01-01T12:00:00 |
| `CUSTOM` | 自定义类型 | 用户定义的复杂类型 |

### 访问权限

| 权限 | 描述 |
|------|------|
| `READ_ONLY` | 只读参数 |
| `READ_WRITE` | 读写参数 |
| `WRITE_ONLY` | 只写参数 |
| `ADMIN_ONLY` | 仅管理员可访问 |

### 作用域

| 作用域 | 描述 |
|--------|------|
| `GLOBAL` | 全局作用域 |
| `APPLICATION` | 应用程序作用域 |
| `MODULE` | 模块作用域 |
| `COMPONENT` | 组件作用域 |
| `USER` | 用户作用域 |
| `SESSION` | 会话作用域 |
| `TEMPORARY` | 临时作用域 |

## 验证规则

```cpp
ParameterValidationRule rule;
rule.name = "Port Range Validation";
rule.description = "Valid port range 1-65535";
rule.minValue = 1;
rule.maxValue = 65535;
rule.required = true;
rule.defaultValue = 8080;

definition.validation = rule;
```

## 配置选项

### 初始化配置

```cpp
ConfigParameters config;
config["storage_directory"] = "/path/to/config";
config["max_history_size"] = 1000;
config["backup_interval"] = 60;  // 分钟
config["max_backups"] = 10;
config["auto_save"] = true;
config["watch_files"] = true;
config["encrypt_storage"] = false;

manager->initialize(config);
```

## 信号和事件

参数管理器提供以下信号：

```cpp
// 参数定义信号
connect(manager, &IParameterManager::parameterDefined,
        this, &MyClass::onParameterDefined);

// 参数值变更信号
connect(manager, &IParameterManager::parameterChanged,
        this, &MyClass::onParameterChanged);

// 参数重置信号
connect(manager, &IParameterManager::parameterReset,
        this, &MyClass::onParameterReset);

// 验证失败信号
connect(manager, &IParameterManager::parameterValidationFailed,
        this, &MyClass::onValidationFailed);
```

## 文件格式

### 参数配置文件 (parameters.json)

```json
{
  "app_timeout": {
    "name": "Application Timeout",
    "description": "Timeout value in seconds",
    "type": "INT",
    "defaultValue": 30,
    "value": 60,
    "isDefault": false,
    "timestamp": "2024-01-01T12:00:00Z"
  }
}
```

### 参数定义文件 (definitions.json)

```json
{
  "app_timeout": {
    "key": "app_timeout",
    "name": "Application Timeout",
    "description": "Timeout value in seconds",
    "type": "INT",
    "access": "READ_WRITE",
    "scope": "APPLICATION",
    "defaultValue": 30,
    "validation": {
      "minValue": 1,
      "maxValue": 300,
      "required": true
    }
  }
}
```

## 最佳实践

### 1. 参数命名规范
- 使用有意义的参数名称
- 采用一致的命名风格 (如snake_case)
- 使用分类前缀 (如network_timeout, ui_theme)

### 2. 参数验证
- 为所有参数设置合适的验证规则
- 提供有意义的错误信息
- 使用合理的默认值

### 3. 权限管理
- 仔细设置参数访问权限
- 敏感参数使用适当的权限控制
- 考虑参数的生命周期和作用域

### 4. 备份策略
- 定期备份重要参数配置
- 在关键操作前创建备份
- 保留合理数量的历史备份

## 故障排除

### 常见问题

1. **参数定义失败**
   - 检查参数键是否唯一
   - 验证参数类型是否正确
   - 确认验证规则是否合理

2. **参数设置失败**
   - 检查参数是否已定义
   - 验证参数值是否符合验证规则
   - 确认参数访问权限

3. **文件加载失败**
   - 检查文件路径和权限
   - 验证JSON格式是否正确
   - 确认存储目录是否存在

### 调试技巧

```cpp
// 启用详细日志
qSetMessagePattern("[%{time}] %{category} %{type}: %{message}");

// 检查管理器状态
auto status = manager->getStatus();
for (const auto& info : status) {
    qDebug() << info.first << ":" << info.second;
}

// 获取统计信息
auto stats = manager->getStatistics();
for (const auto& stat : stats) {
    qDebug() << stat.first << ":" << stat.second;
}
```

## 扩展和自定义

### 自定义参数类型

```cpp
class CustomParameterType : public QObject {
    Q_OBJECT
    
public:
    static QVariant fromString(const QString& str);
    static QString toString(const QVariant& value);
    static bool validate(const QVariant& value);
};
```

### 自定义验证器

```cpp
class CustomValidator {
public:
    static bool validateCustomRule(const ParameterDefinition& def, 
                                  const QVariant& value);
};
```

## 性能考虑

- 参数访问是线程安全的，但频繁访问可能影响性能
- 大量参数时考虑使用缓存机制
- 历史记录会占用内存，定期清理过期记录
- 文件监控会消耗系统资源，按需启用

## 版本历史

- v1.0.0 - 初始版本，包含基本参数管理功能
- 计划功能：插件式验证器、远程参数同步、配置模板

## 相关模块

- [配置文件处理模块](../file/README.md) - 提供配置文件读写功能
- [配置验证模块](../validation/README.md) - 提供高级验证功能
- [基础类型模块](../../foundation/README.md) - 提供基础类型定义