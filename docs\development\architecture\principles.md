# LA项目设计原则

## 核心设计原则

### 1. 单一职责原则 (Single Responsibility Principle)
每个模块、类、函数只负责一个特定功能，职责明确且不重叠。

**应用指导**：
- 模块边界清晰，避免功能混杂
- 接口设计简洁，功能专一
- 易于测试、维护和扩展

### 2. 单一来源原则 (Single Source of Truth)
每类数据和逻辑只在一个地方定义和维护，避免重复和不一致。

**应用实例**：
- **设备定义**: DeviceRegistry作为设备信息的单一来源
- **协议规则**: 协议处理逻辑集中在各Protocol实现中
- **映射关系**: 设备-协议-端口的映射关系统一定义

### 3. 最小模块原则 (Minimal Module Principle)
模块保持最小可用状态，专注单一职责，可组合成更大功能。

**应用实例**：
- **IConnection**: 只负责数据传输
- **IProtocol**: 只负责数据编解码
- **IDeviceRegistry**: 只负责设备注册和查询
- **组合器**: DataFlowManager将最小模块组合成完整功能

### 4. 结构体映射原则 (Struct Mapping Principle)
用结构体清晰地定义对象属性，明确映射各信息的关联关系。

**设计模式**：
```cpp
// 属性映射：完整描述对象的所有相关信息
struct DeviceAttributes {
    QString deviceId;                           // 设备标识
    DeviceType deviceType;                      // 设备类型
    CommunicationAttributes communication;      // 通信属性关联
    DeviceCapabilities capabilities;            // 设备能力映射
};

// 关联映射：明确组件间的关系
struct CommunicationAttributes {
    ProtocolConfig protocol;                    // 协议配置
    ConnectionConfig connection;                // 连接配置
    CommunicationStats statistics;             // 统计信息
};
```

### 5. 声明式映射原则 (Declarative Mapping Principle)
*（优化原"静态表格关系"原则）*

明确的对应关系通过声明式方式定义，支持编译期优化，允许运行时扩展。

**实现方式**：
- **静态映射表**: 编译期确定的核心映射关系
- **动态注册**: 支持插件运行时注册新的映射
- **配置驱动**: 允许通过配置文件调整映射关系

```cpp
// 静态核心映射（编译期优化）
static const QMap<DeviceType, QList<ProtocolType>> CORE_DEVICE_PROTOCOL_MAP = {
    {DeviceType::YJSensor, {ProtocolType::Custom_YJSensor, ProtocolType::Raw}},
    {DeviceType::ModbusDevice, {ProtocolType::Modbus_RTU, ProtocolType::Modbus_TCP}}
};

// 动态扩展映射（运行时注册）
class DeviceMappingRegistry {
public:
    void registerDeviceProtocol(DeviceType device, ProtocolType protocol);
    QList<ProtocolType> getSupportedProtocols(DeviceType device) const;
};
```
所有映射关系放进单一命名空间或统一 `registry.h`/`registry.cpp`，加上 `static_assert` 做一致性检查
### 6. 模块可共享原则

对于通用的模块，需要设计成可共享的库，便于在其他项目中复用。

## 架构原则

### 组合优于继承 (Composition over Inheritance)
通过依赖注入和组合模式实现功能复用，避免深层继承树。
**继承层数上限**（不超过 3 层），除非有极强理由
### 接口隔离原则 (Interface Segregation Principle)  
- 接口保持精简，客户端不依赖不需要的接口。
- 抽象接口必须基于当前已有的多个实现，否则先直接用具体实现。
- 对频繁调用、性能敏感的路径，不要因“依赖倒置”增加多层虚函数调用。
### 依赖倒置原则 (Dependency Inversion Principle)
高层模块不依赖低层模块，都依赖于抽象接口。

## 数据流设计原则

### 职责分离
连接、协议、指令、设备各司其职，边界清晰。

### 最小接口
接口只暴露必要的功能，隐藏实现细节。

### 可测试性
每个组件都可独立测试，支持模拟和桩对象。

### 无状态设计
尽量设计无状态的组件，状态管理集中化。

## 配置驱动原则


## 实践指导

### 模块设计检查清单
- [ ] 模块职责是否单一明确？
- [ ] 是否存在数据和逻辑的重复定义？
- [ ] 模块是否可以独立测试？
- [ ] 接口是否足够简洁？
- [ ] 是否易于扩展和维护？

### 代码审查重点
- 检查是否违反单一来源原则
- 验证模块间的依赖关系是否合理
- 确认映射关系是否集中管理
- 评估组件的可组合性
- 出错时是否能从日志、断点、单步跟踪快速找到问题源头？
### 重构指导
当发现以下情况时考虑重构：
- 同一逻辑在多处重复实现
- 模块承担了多个不相关的职责
- 映射关系散布在多个文件中
- 组件难以独立测试

---

*这些设计原则是LA项目架构的基础，在开发过程中应当严格遵循。*