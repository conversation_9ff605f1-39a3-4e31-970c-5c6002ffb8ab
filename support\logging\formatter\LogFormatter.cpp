#include "ILogFormatter.h"
#include <QJsonDocument>
#include <QJsonObject>
#include <QRegularExpression>
#include <QXmlStreamWriter>

namespace LA {
namespace Support {
namespace Logging {

//=====================================================================
// SimpleTextFormatter Implementation
//=====================================================================

SimpleTextFormatter::SimpleTextFormatter(const QString& pattern)
    : m_pattern(pattern)
    , m_timeFormat("yyyy-MM-dd hh:mm:ss.zzz")
    , m_colorEnabled(false) {
}

QString SimpleTextFormatter::format(const LogEntry& entry) {
    QString result = m_pattern;
    
    // 替换时间占位符
    if (result.contains("%{time}")) {
        QString timeStr = entry.context.timestamp.toString(m_timeFormat);
        result.replace("%{time}", timeStr);
    }
    
    // 替换级别占位符
    if (result.contains("%{level}")) {
        QString levelStr = formatLevel(entry.level);
        if (m_colorEnabled) {
            levelStr = getColorCode(entry.level) + levelStr + resetColorCode();
        }
        result.replace("%{level}", levelStr);
    }
    
    // 替换类型占位符
    if (result.contains("%{type}")) {
        QString typeStr = formatType(entry.type);
        result.replace("%{type}", typeStr);
    }
    
    // 替换消息占位符
    if (result.contains("%{message}")) {
        result.replace("%{message}", entry.message);
    }
    
    // 替换源文件占位符
    if (result.contains("%{file}")) {
        result.replace("%{file}", entry.context.file);
    }
    
    // 替换函数名占位符
    if (result.contains("%{function}")) {
        result.replace("%{function}", entry.context.function);
    }
    
    // 替换行号占位符
    if (result.contains("%{line}")) {
        result.replace("%{line}", QString::number(entry.context.line));
    }
    
    // 替换组件名占位符
    if (result.contains("%{component}")) {
        result.replace("%{component}", entry.context.component);
    }
    
    // 替换模块名占位符
    if (result.contains("%{module}")) {
        result.replace("%{module}", entry.context.module);
    }
    
    // 替换线程ID占位符
    if (result.contains("%{thread}")) {
        result.replace("%{thread}", entry.context.threadId);
    }
    
    // 替换进程ID占位符
    if (result.contains("%{process}")) {
        result.replace("%{process}", entry.context.processId);
    }
    
    return result;
}

void SimpleTextFormatter::setPattern(const QString& pattern) {
    m_pattern = pattern;
}

QString SimpleTextFormatter::getPattern() const {
    return m_pattern;
}

void SimpleTextFormatter::setTimeFormat(const QString& format) {
    m_timeFormat = format;
}

QString SimpleTextFormatter::getTimeFormat() const {
    return m_timeFormat;
}

void SimpleTextFormatter::setColorEnabled(bool enabled) {
    m_colorEnabled = enabled;
}

bool SimpleTextFormatter::isColorEnabled() const {
    return m_colorEnabled;
}

QString SimpleTextFormatter::formatLevel(LogLevel level) const {
    switch (level) {
        case LogLevel::TRACE:   return "TRACE";
        case LogLevel::DEBUG:   return "DEBUG";
        case LogLevel::INFO:    return "INFO ";
        case LogLevel::WARNING: return "WARN ";
        case LogLevel::ERROR:   return "ERROR";
        case LogLevel::FATAL:   return "FATAL";
        default:                return "UNKNW";
    }
}

QString SimpleTextFormatter::formatType(LogType type) const {
    switch (type) {
        case LogType::DEFAULT:          return "DEF";
        case LogType::INIT:             return "INI";
        case LogType::COMM:             return "COM";
        case LogType::COMM_ACK:         return "ACK";
        case LogType::PROCESS_STATUS:   return "STA";
        case LogType::PROCESS_DATA:     return "DAT";
        case LogType::RESULT_DATA:      return "RES";
        case LogType::ERROR_LOG:        return "ERR";
        case LogType::PERFORMANCE:      return "PER";
        case LogType::SECURITY:         return "SEC";
        case LogType::AUDIT:            return "AUD";
        case LogType::CUSTOM:           return "CUS";
        default:                        return "UNK";
    }
}

QString SimpleTextFormatter::getColorCode(LogLevel level) const {
    switch (level) {
        case LogLevel::TRACE:   return "\033[37m";      // 白色
        case LogLevel::DEBUG:   return "\033[36m";      // 青色
        case LogLevel::INFO:    return "\033[32m";      // 绿色
        case LogLevel::WARNING: return "\033[33m";      // 黄色
        case LogLevel::ERROR:   return "\033[31m";      // 红色
        case LogLevel::FATAL:   return "\033[35m";      // 紫色
        default:                return "\033[0m";       // 重置
    }
}

QString SimpleTextFormatter::resetColorCode() const {
    return "\033[0m";
}

//=====================================================================
// JsonFormatter Implementation
//=====================================================================

JsonFormatter::JsonFormatter()
    : m_timeFormat("yyyy-MM-ddThh:mm:ss.zzz")
    , m_prettyPrint(false) {
}

QString JsonFormatter::format(const LogEntry& entry) {
    QJsonObject json;
    
    // 基本信息
    json["timestamp"] = entry.context.timestamp.toString(m_timeFormat);
    json["level"] = logLevelToString(entry.level);
    json["type"] = logTypeToString(entry.type);
    json["message"] = entry.message;
    
    // 上下文信息
    QJsonObject context;
    if (!entry.context.file.isEmpty()) {
        context["file"] = entry.context.file;
    }
    if (!entry.context.function.isEmpty()) {
        context["function"] = entry.context.function;
    }
    if (entry.context.line > 0) {
        context["line"] = entry.context.line;
    }
    if (!entry.context.component.isEmpty()) {
        context["component"] = entry.context.component;
    }
    if (!entry.context.module.isEmpty()) {
        context["module"] = entry.context.module;
    }
    if (!entry.context.threadId.isEmpty()) {
        context["thread"] = entry.context.threadId;
    }
    if (!entry.context.processId.isEmpty()) {
        context["process"] = entry.context.processId;
    }
    
    // 添加元数据
    if (!entry.context.metadata.isEmpty()) {
        QJsonObject metadata;
        for (auto it = entry.context.metadata.begin(); 
             it != entry.context.metadata.end(); ++it) {
            metadata[it.key()] = QJsonValue::fromVariant(it.value());
        }
        context["metadata"] = metadata;
    }
    
    if (!context.isEmpty()) {
        json["context"] = context;
    }
    
    QJsonDocument doc(json);
    return doc.toJson(m_prettyPrint ? QJsonDocument::Indented : QJsonDocument::Compact);
}

void JsonFormatter::setPattern(const QString& pattern) {
    // JSON格式化器不使用模式字符串
    Q_UNUSED(pattern)
}

QString JsonFormatter::getPattern() const {
    return "JSON";
}

void JsonFormatter::setTimeFormat(const QString& format) {
    m_timeFormat = format;
}

QString JsonFormatter::getTimeFormat() const {
    return m_timeFormat;
}

void JsonFormatter::setColorEnabled(bool enabled) {
    // JSON格式化器不支持颜色
    Q_UNUSED(enabled)
}

bool JsonFormatter::isColorEnabled() const {
    return false;
}

void JsonFormatter::setPrettyPrint(bool enabled) {
    m_prettyPrint = enabled;
}

bool JsonFormatter::isPrettyPrint() const {
    return m_prettyPrint;
}

//=====================================================================
// XmlFormatter Implementation
//=====================================================================

XmlFormatter::XmlFormatter()
    : m_timeFormat("yyyy-MM-ddThh:mm:ss.zzz")
    , m_rootElementName("LogEntry") {
}

QString XmlFormatter::format(const LogEntry& entry) {
    QString xml;
    QXmlStreamWriter writer(&xml);
    
    writer.setAutoFormatting(true);
    writer.writeStartDocument();
    
    writer.writeStartElement(m_rootElementName);
    
    // 基本信息
    writer.writeTextElement("timestamp", entry.context.timestamp.toString(m_timeFormat));
    writer.writeTextElement("level", logLevelToString(entry.level));
    writer.writeTextElement("type", logTypeToString(entry.type));
    writer.writeTextElement("message", escapeXml(entry.message));
    
    // 上下文信息
    if (!entry.context.file.isEmpty() || !entry.context.function.isEmpty() ||
        entry.context.line > 0 || !entry.context.component.isEmpty() ||
        !entry.context.module.isEmpty() || !entry.context.threadId.isEmpty() ||
        !entry.context.processId.isEmpty() || !entry.context.metadata.isEmpty()) {
        
        writer.writeStartElement("context");
        
        if (!entry.context.file.isEmpty()) {
            writer.writeTextElement("file", escapeXml(entry.context.file));
        }
        if (!entry.context.function.isEmpty()) {
            writer.writeTextElement("function", escapeXml(entry.context.function));
        }
        if (entry.context.line > 0) {
            writer.writeTextElement("line", QString::number(entry.context.line));
        }
        if (!entry.context.component.isEmpty()) {
            writer.writeTextElement("component", escapeXml(entry.context.component));
        }
        if (!entry.context.module.isEmpty()) {
            writer.writeTextElement("module", escapeXml(entry.context.module));
        }
        if (!entry.context.threadId.isEmpty()) {
            writer.writeTextElement("thread", escapeXml(entry.context.threadId));
        }
        if (!entry.context.processId.isEmpty()) {
            writer.writeTextElement("process", escapeXml(entry.context.processId));
        }
        
        // 元数据
        if (!entry.context.metadata.isEmpty()) {
            writer.writeStartElement("metadata");
            for (auto it = entry.context.metadata.begin();
                 it != entry.context.metadata.end(); ++it) {
                writer.writeTextElement(it.key(), escapeXml(it.value().toString()));
            }
            writer.writeEndElement(); // metadata
        }
        
        writer.writeEndElement(); // context
    }
    
    writer.writeEndElement(); // root element
    writer.writeEndDocument();
    
    return xml;
}

void XmlFormatter::setPattern(const QString& pattern) {
    // XML格式化器不使用模式字符串
    Q_UNUSED(pattern)
}

QString XmlFormatter::getPattern() const {
    return "XML";
}

void XmlFormatter::setTimeFormat(const QString& format) {
    m_timeFormat = format;
}

QString XmlFormatter::getTimeFormat() const {
    return m_timeFormat;
}

void XmlFormatter::setColorEnabled(bool enabled) {
    // XML格式化器不支持颜色
    Q_UNUSED(enabled)
}

bool XmlFormatter::isColorEnabled() const {
    return false;
}

void XmlFormatter::setRootElementName(const QString& name) {
    m_rootElementName = name;
}

QString XmlFormatter::getRootElementName() const {
    return m_rootElementName;
}

QString XmlFormatter::escapeXml(const QString& text) const {
    QString escaped = text;
    escaped.replace("&", "&amp;");
    escaped.replace("<", "&lt;");
    escaped.replace(">", "&gt;");
    escaped.replace("\"", "&quot;");
    escaped.replace("'", "&apos;");
    return escaped;
}

}  // namespace Logging
}  // namespace Support
}  // namespace LA