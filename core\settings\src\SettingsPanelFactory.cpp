#include "LA/Settings/SettingsPanelFactory.h"
#include "LA/Settings/panels/SettingsSidebarPanel.h"
#include <QDebug>

namespace LA {
namespace Settings {

std::unique_ptr<LA::SideBar::ISidebarPanel> SettingsPanelFactory::createSettingsPanel(QWidget *parent) {
    try {
        auto panel = std::make_unique<SettingsSidebarPanel>(parent);
        if (panel->initializePanel()) {
            qDebug() << "Settings sidebar panel created successfully";
            return std::move(panel);
        } else {
            qWarning() << "Failed to initialize settings sidebar panel";
            return nullptr;
        }
    } catch (const std::exception &e) {
        qCritical() << "Exception creating settings panel:" << e.what();
        return nullptr;
    }
}

QStringList SettingsPanelFactory::getSupportedPanels() {
    return QStringList() << QStringLiteral("settings");
}

bool SettingsPanelFactory::supportsPanel(const QString &panelId) {
    return panelId == QStringLiteral("settings");
}

QString SettingsPanelFactory::getPanelDisplayName(const QString &panelId) {
    if (panelId == QStringLiteral("settings")) {
        return QObject::tr("设置");
    }
    return QString();
}

QString SettingsPanelFactory::getPanelIconPath(const QString &panelId) {
    if (panelId == QStringLiteral("settings")) {
        return QStringLiteral(":/icons/settings.png");
    }
    return QString();
}

}  // namespace Settings
}  // namespace LA
