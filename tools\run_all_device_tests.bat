@echo off
REM 运行所有设备类型的指令测试
REM 遍历所有配置文件并执行测试

echo ===============================================
echo    Running All Device Command Tests
echo ===============================================

setlocal EnableDelayedExpansion
set SCRIPT_DIR=%~dp0
set CONFIG_DIR=%SCRIPT_DIR%..\tests\config\devices
set FAILED_TESTS=0
set TOTAL_TESTS=0

echo [INFO] Scanning for device configurations...

REM 遍历所有设备配置文件
for %%f in ("%CONFIG_DIR%\*_test_config.json") do (
    set CONFIG_FILE=%%~nf
    set DEVICE_TYPE=!CONFIG_FILE:_test_config=!
    
    echo.
    echo [TEST] Running tests for device: !DEVICE_TYPE!
    echo -----------------------------------------------
    
    call "%SCRIPT_DIR%\run_device_command_tests.bat" !DEVICE_TYPE!
    set /a TOTAL_TESTS+=1
    
    if !ERRORLEVEL! neq 0 (
        echo [FAILED] !DEVICE_TYPE! tests failed!
        set /a FAILED_TESTS+=1
    ) else (
        echo [PASSED] !DEVICE_TYPE! tests passed!
    )
)

echo.
echo ===============================================
echo           Test Summary
echo ===============================================
echo Total device types tested: %TOTAL_TESTS%
echo Failed device tests: %FAILED_TESTS%
set /a PASSED_TESTS=%TOTAL_TESTS%-%FAILED_TESTS%
echo Passed device tests: %PASSED_TESTS%

if %FAILED_TESTS% equ 0 (
    echo [SUCCESS] All device command tests passed!
    exit /b 0
) else (
    echo [FAILURE] Some device command tests failed!
    exit /b 1
)