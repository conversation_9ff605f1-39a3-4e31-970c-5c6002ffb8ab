/**
 * @file ICommandProvider.h
 * @brief 指令提供者接口 - 四层架构中的独立指令组件
 * 
 * 职责：
 * - 提供设备指令的生成和解析服务
 * - 作为独立组件可被各层使用
 * - 遵循单一职责和最小模块原则
 */

#pragma once

#include <QObject>
#include <QString>
#include <QVariantMap>
#include <QByteArray>
#include <QDateTime>

namespace LA::Device::Command {

/**
 * @brief 指令结果结构
 */
struct CommandResult {
    bool success = false;
    QString command;
    QVariantMap data;
    QString error;
    QDateTime timestamp;
};

/**
 * @brief 指令定义结构
 */
struct CommandDefinition {
    QString commandId;
    uint16_t commandCode;
    QString description;
    QStringList requiredParams;
    QStringList optionalParams;
    QString protocolType; // "HTKJ", "Modbus", "Custom"
    int timeoutMs = 3000;
};

/**
 * @brief 指令提供者接口 - 四层架构独立组件
 * 
 * 设计原则：
 * - 单一职责：只管指令生成和解析
 * - 组件化：可被Driver、Capability、Strategy、Script层使用
 * - 配置驱动：指令定义来自配置文件
 */
class ICommandProvider : public QObject {
    Q_OBJECT

public:
    explicit ICommandProvider(QObject* parent = nullptr) : QObject(parent) {}
    virtual ~ICommandProvider() = default;

    // === 核心指令接口 ===
    
    /**
     * @brief 生成设备指令
     * @param commandId 指令ID
     * @param params 指令参数
     * @return 生成的指令字节流
     */
    virtual QByteArray generateCommand(const QString& commandId, const QVariantMap& params = {}) = 0;
    
    /**
     * @brief 解析设备响应
     * @param responseData 响应数据
     * @return 解析结果
     */
    virtual CommandResult parseResponse(const QByteArray& responseData) = 0;
    
    /**
     * @brief 验证指令参数
     * @param commandId 指令ID
     * @param params 参数
     * @return 是否有效
     */
    virtual bool validateCommand(const QString& commandId, const QVariantMap& params = {}) = 0;

    // === 指令定义查询 ===
    
    /**
     * @brief 获取支持的指令列表
     * @return 指令ID列表
     */
    virtual QStringList getSupportedCommands() const = 0;
    
    /**
     * @brief 获取指令定义
     * @param commandId 指令ID
     * @return 指令定义
     */
    virtual CommandDefinition getCommandDefinition(const QString& commandId) const = 0;
    
    /**
     * @brief 是否支持指定指令
     * @param commandId 指令ID
     * @return 是否支持
     */
    virtual bool supportsCommand(const QString& commandId) const = 0;

    // === 配置管理 ===
    
    /**
     * @brief 从配置加载指令定义
     * @param configData 配置数据
     * @return 是否成功
     */
    virtual bool loadConfiguration(const QVariantMap& configData) = 0;
    
    /**
     * @brief 获取提供者信息
     * @return 提供者信息
     */
    virtual QVariantMap getProviderInfo() const = 0;

Q_SIGNALS:
    void commandGenerated(const QString& commandId, const QByteArray& command);
    void responseReceived(const CommandResult& result);
    void errorOccurred(const QString& error);
};

/**
 * @brief 指令提供者工厂
 */
class CommandProviderFactory {
public:
    /**
     * @brief 创建指令提供者
     * @param deviceType 设备类型
     * @param configData 配置数据
     * @return 指令提供者实例
     */
    static std::unique_ptr<ICommandProvider> createProvider(const QString& deviceType, 
                                                           const QVariantMap& configData = {});
    
    /**
     * @brief 注册指令提供者类型
     * @param deviceType 设备类型
     * @param creator 创建函数
     */
    using ProviderCreator = std::function<std::unique_ptr<ICommandProvider>(const QVariantMap&)>;
    static void registerProvider(const QString& deviceType, ProviderCreator creator);
    
    /**
     * @brief 获取支持的设备类型
     * @return 设备类型列表
     */
    static QStringList getSupportedDeviceTypes();

private:
    static QMap<QString, ProviderCreator> s_creators;
};

} // namespace LA::Device::Command