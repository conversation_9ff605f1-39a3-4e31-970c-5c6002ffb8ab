#pragma once

/**
 * @file ICommandHandler.h
 * @brief Linus式命令处理器接口定义 - 最小接口设计
 * 
 * 遵循Linus Torvalds的设计哲学：
 * - "Good programmers worry about data structures" - 基于CommonTypes.h的数据结构
 * - 最小接口原则 - 只负责命令处理，不涉及协议编解码、数据传输
 * - 单一职责 - 纯粹的命令执行和响应生成
 * - Layer 2接口 - 依赖Layer 1，为Layer 3提供命令抽象
 */

#include <QObject>
#include <QString>
#include <QVariantMap>
#include "support/foundation/core/CommonTypes.h"

namespace LA {
namespace Communication {
namespace Command {

// 使用Foundation层的标准类型
using namespace LA::Foundation::Core;

/**
 * @brief 纯粹的命令处理器接口
 * 
 * Linus式设计原则:
 * - 最小接口: 只提供必需的命令处理功能
 * - 无业务逻辑: 不涉及协议编解码、数据传输、设备管理
 * - 可测试性: 每个方法都可以独立测试
 * - 可组合性: 可与协议层、连接层组合使用
 */
class ICommandHandler : public QObject {
    Q_OBJECT

public:
    explicit ICommandHandler(QObject* parent = nullptr) : QObject(parent) {}
    virtual ~ICommandHandler() = default;

    // === 命令处理核心 ===
    
    /**
     * @brief 执行命令
     * @param command 命令数据映射
     * @return 执行结果，包含响应数据
     */
    virtual CommandResult executeCommand(const QVariantMap& command) = 0;
    
    /**
     * @brief 检查是否支持指定命令
     * @param commandType 命令类型
     * @return 是否支持
     */
    virtual bool supportsCommand(const QString& commandType) const = 0;
    
    /**
     * @brief 获取支持的命令列表
     * @return 支持的命令类型列表
     */
    virtual QStringList getSupportedCommands() const = 0;

    // === 命令验证 ===
    
    /**
     * @brief 验证命令格式
     * @param command 命令数据映射
     * @return 验证结果
     */
    virtual SimpleResult validateCommand(const QVariantMap& command) = 0;
    
    /**
     * @brief 获取命令参数要求
     * @param commandType 命令类型
     * @return 参数要求映射
     */
    virtual QVariantMap getCommandRequirements(const QString& commandType) const = 0;

    // === 状态查询 ===
    
    /**
     * @brief 获取最后错误信息
     * @return 错误描述
     */
    virtual QString errorString() const = 0;
    
    /**
     * @brief 获取统计信息
     * @return 统计数据结构
     */
    virtual DeviceStatistics getStatistics() const = 0;
    
    /**
     * @brief 重置统计信息
     */
    virtual void resetStatistics() = 0;
    
    /**
     * @brief 检查处理器是否可用
     * @return 是否可用
     */
    virtual bool isAvailable() const = 0;

signals:
    /**
     * @brief 命令执行完成信号
     * @param command 原始命令
     * @param result 执行结果
     */
    void commandExecuted(const QVariantMap& command, const CommandResult& result);
    
    /**
     * @brief 命令执行错误信号
     * @param command 原始命令
     * @param error 错误描述
     */
    void commandError(const QVariantMap& command, const QString& error);
    
    /**
     * @brief 异步命令进度信号
     * @param commandId 命令ID
     * @param progress 进度百分比 (0-100)
     */
    void commandProgress(const QString& commandId, int progress);
};

/**
 * @brief 命令处理器工厂接口
 * 
 * 用于创建不同类型的命令处理器
 */
class ICommandHandlerFactory {
public:
    virtual ~ICommandHandlerFactory() = default;
    
    /**
     * @brief 创建命令处理器实例
     * @param type 处理器类型
     * @return 命令处理器实例的智能指针
     */
    virtual std::shared_ptr<ICommandHandler> createCommandHandler(const QString& type) = 0;
    
    /**
     * @brief 检查是否支持指定处理器类型
     * @param type 处理器类型
     * @return 是否支持
     */
    virtual bool supportsHandlerType(const QString& type) const = 0;
    
    /**
     * @brief 获取支持的处理器类型列表
     * @return 支持的类型列表
     */
    virtual QStringList getSupportedHandlerTypes() const = 0;
};

} // namespace Command
} // namespace Communication
} // namespace LA