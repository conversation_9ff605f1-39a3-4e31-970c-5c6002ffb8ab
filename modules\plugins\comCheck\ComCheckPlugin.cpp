#include "ComCheckPlugin.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QGroupBox>
#include <QDateTime>
#include <QMessageBox>
#include <QApplication>
#include <QDebug>
#include <QLabel>

// Linus: "Use shared components from main program" (暂时注释直到依赖解决)
// using namespace LA::UI::DeviceCommunication;

namespace LA {
namespace Plugins {
namespace ComCheck {

ComCheckPlugin::ComCheckPlugin(QObject* parent)
    : BaseFunctionPlugin(parent)
    , m_serialPort(nullptr)
    , m_isConnected(false)
{
    // Linus: "Keep it simple - basic serial port for testing"
    qDebug() << "ComCheck Plugin - using shared UI components and simple serial communication";
}

bool ComCheckPlugin::initialize() {
    qDebug() << "ComCheck Plugin initializing...";
    setPluginInfo("com.la.plugins.comcheck", "ComCheck", "1.0.0", 
                  "Communication check and testing plugin", "LA Development Team");
    setCategory("Communication");
    setWindowSizes(QSize(400, 300), QSize(600, 500));
    setSupportsFloating(true);
    setSupportedThemes({"Default", "Dark"});
    return true;
}

void ComCheckPlugin::shutdown() {
    if (m_serialPort && m_serialPort->isOpen()) {
        m_serialPort->close();
    }
    qDebug() << "ComCheck Plugin shutdown";
}

QWidget* ComCheckPlugin::doCreateWidget(QWidget* parent) {
    m_mainWidget = new QWidget(parent);
    QVBoxLayout* mainLayout = new QVBoxLayout(m_mainWidget);
    
    // 标题
    QLabel* titleLabel = new QLabel("通信检查工具", m_mainWidget);
    titleLabel->setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;");
    titleLabel->setAlignment(Qt::AlignCenter);
    mainLayout->addWidget(titleLabel);
    
    // Linus: "Temporary simplified UI until DevicePortWidget dependencies are resolved"
    QGroupBox* connectionGroup = new QGroupBox("设备端口连接 (临时简化版)", m_mainWidget);
    QVBoxLayout* connLayout = new QVBoxLayout(connectionGroup);
    QLabel* statusLabel = new QLabel("当前架构演示：插件使用主程序UI组件架构");
    statusLabel->setStyleSheet("color: #666; font-style: italic;");
    connLayout->addWidget(statusLabel);
    mainLayout->addWidget(connectionGroup);
    
    // TODO: 启用DevicePortWidget当通信模块依赖解决后
    // m_devicePortWidget = new DevicePortWidget(m_mainWidget);
    // mainLayout->addWidget(m_devicePortWidget);
    
    // ComCheck特有的通信测试功能
    setupComCheckSpecificUI();
    
    // 应用主题系统 - Linus: "Shared UI consistency"
    applyTheme();
    
    return m_mainWidget;
}

QWidget* ComCheckPlugin::createSidebarPanel(QWidget* parent) {
    // 创建侧边栏快捷面板
    QWidget* panel = new QWidget(parent);
    QVBoxLayout* layout = new QVBoxLayout(panel);
    
    QLabel* title = new QLabel("通信检查", panel);
    title->setStyleSheet("font-weight: bold; font-size: 14px;");
    
    QPushButton* openButton = new QPushButton("打开通信检查", panel);
    connect(openButton, &QPushButton::clicked, [this]() {
        if (m_mainWidget) {
            m_mainWidget->show();
            m_mainWidget->raise();
        }
    });
    
    layout->addWidget(title);
    layout->addWidget(openButton);
    layout->addStretch();
    
    return panel;
}

void ComCheckPlugin::setupComCheckSpecificUI() {
    if (!m_mainWidget) return;
    
    QVBoxLayout* mainLayout = qobject_cast<QVBoxLayout*>(m_mainWidget->layout());
    
    // 数据发送组 - ComCheck特有功能
    QGroupBox* sendGroup = new QGroupBox("数据发送", m_mainWidget);
    QVBoxLayout* sendLayout = new QVBoxLayout(sendGroup);
    
    QHBoxLayout* sendInputLayout = new QHBoxLayout();
    m_sendLineEdit = new QLineEdit();
    m_sendLineEdit->setPlaceholderText("输入要发送的数据...");
    m_sendButton = new QPushButton("发送");
    m_sendButton->setEnabled(false);
    
    connect(m_sendLineEdit, &QLineEdit::returnPressed, this, &ComCheckPlugin::onSendData);
    connect(m_sendButton, &QPushButton::clicked, this, &ComCheckPlugin::onSendData);
    
    sendInputLayout->addWidget(m_sendLineEdit);
    sendInputLayout->addWidget(m_sendButton);
    
    m_hexModeCheckBox = new QCheckBox("十六进制模式");
    m_deviceCmdModeCheckBox = new QCheckBox("设备命令模式");
    m_deviceCmdModeCheckBox->setToolTip("启用后可发送设备标准指令(如SPRM的START、CALIB等)");
    
    sendLayout->addLayout(sendInputLayout);
    QHBoxLayout* modeLayout = new QHBoxLayout();
    modeLayout->addWidget(m_hexModeCheckBox);
    modeLayout->addWidget(m_deviceCmdModeCheckBox);
    modeLayout->addStretch();
    sendLayout->addLayout(modeLayout);
    
    mainLayout->addWidget(sendGroup);
    
    // 日志显示 - ComCheck特有功能
    QGroupBox* logGroup = new QGroupBox("通信日志", m_mainWidget);
    QVBoxLayout* logLayout = new QVBoxLayout(logGroup);
    
    m_logTextEdit = new QTextEdit();
    m_logTextEdit->setReadOnly(true);
    logLayout->addWidget(m_logTextEdit);
    
    mainLayout->addWidget(logGroup);
}

void ComCheckPlugin::applyTheme() {
    // 使用主程序主题系统统一样式
    if (m_mainWidget) {
        // 主题系统会自动应用到子组件
        LA::Themes::ThemeManager::instance().applyTheme(m_mainWidget);
    }
}

// 新的信号处理方法 - 连接到DevicePortWidget
void ComCheckPlugin::onDevicePortConnected(const QString& deviceId, const QString& portName) {
    m_currentPortName = portName;
    m_isConnected = true;
    
    // 初始化基础串口通信用于测试
    if (m_serialPort) {
        delete m_serialPort;
    }
    m_serialPort = new QSerialPort(this);
    m_serialPort->setPortName(portName);
    m_serialPort->setBaudRate(115200);  // 默认波特率
    
    if (m_serialPort->open(QIODevice::ReadWrite)) {
        connect(m_serialPort, &QSerialPort::readyRead, [this]() {
            QByteArray data = m_serialPort->readAll();
            onReceiveData(data);
        });
        
        if (m_sendButton) m_sendButton->setEnabled(true);
        appendLog(QString("Connected to device %1 on port %2").arg(deviceId, portName), "[INFO]");
    } else {
        appendLog(QString("Failed to open port %1 for testing").arg(portName), "[ERROR]");
        m_isConnected = false;
    }
}

void ComCheckPlugin::onDevicePortDisconnected(const QString& deviceId, const QString& portName) {
    Q_UNUSED(deviceId)
    
    if (m_serialPort) {
        m_serialPort->close();
        delete m_serialPort;
        m_serialPort = nullptr;
    }
    
    m_isConnected = false;
    m_currentPortName.clear();
    
    if (m_sendButton) m_sendButton->setEnabled(false);
    appendLog(QString("Disconnected from port %1").arg(portName), "[INFO]");
}


void ComCheckPlugin::onSendData() {
    if (!m_isConnected || !m_serialPort || !m_serialPort->isOpen()) {
        appendLog("No active connection for testing", "[ERROR]");
        return;
    }
    
    QString text = m_sendLineEdit->text();
    if (text.isEmpty()) {
        return;
    }
    
    QByteArray data;
    
    if (m_hexModeCheckBox->isChecked()) {
        // 十六进制模式
        QString hexString = text.remove(' ').remove("0x", Qt::CaseInsensitive);
        data = QByteArray::fromHex(hexString.toLatin1());
        if (data.isEmpty() && !hexString.isEmpty()) {
            appendLog("十六进制格式错误", "[ERROR]");
            return;
        }
    } else {
        // 文本模式
        data = text.toUtf8();
    }
    
    qint64 bytesWritten = m_serialPort->write(data);
    if (bytesWritten == -1) {
        appendLog(QString("发送失败: %1").arg(m_serialPort->errorString()), "[ERROR]");
    } else {
        QString logText = m_hexModeCheckBox->isChecked() ? QString(data.toHex(' ')) : text;
        appendLog(QString("发送 (%1 bytes): %2").arg(bytesWritten).arg(logText), "[SEND]");
        m_sendLineEdit->clear();
    }
}

void ComCheckPlugin::onReceiveData(const QByteArray& data) {
    if (data.isEmpty()) {
        return;
    }
    
    QString text;
    if (m_hexModeCheckBox->isChecked()) {
        text = QString(data.toHex(' '));
    } else {
        text = QString::fromUtf8(data);
    }
    
    appendLog(QString("接收 (%1 bytes): %2").arg(data.size()).arg(text), "[RECV]");
}


void ComCheckPlugin::appendLog(const QString& message, const QString& prefix) {
    if (!m_logTextEdit) return;
    
    QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss.zzz");
    QString logLine = QString("[%1] %2 %3").arg(timestamp, prefix, message);
    
    m_logTextEdit->append(logLine);
    
    // 自动滚动到底部
    QTextCursor cursor = m_logTextEdit->textCursor();
    cursor.movePosition(QTextCursor::End);
    m_logTextEdit->setTextCursor(cursor);
}

} // namespace ComCheck
} // namespace Plugins
} // namespace LA