#pragma once

#include "RightSidebarPanel.h"
#include <QSplitter>
#include <QTabWidget>
#include <QTreeWidget>
#include <QTableWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGroupBox>
#include <QPushButton>
#include <QLabel>
#include <QComboBox>
#include <QProgressBar>
#include <QTimer>
#include <memory>

// 前向声明
namespace LA {
namespace DeviceManagement {
class IDeviceRegistry;
namespace Discovery {
class IDeviceDiscoveryService;
}
}
namespace Communication {
namespace PortManagement {
class IPortManager;
}
namespace DeviceMatching {
class IDeviceMatchingService;
}
}
namespace Foundation {
namespace Core {
struct DeviceInfo;
struct PortInfo;
}
}
}

namespace LA {
namespace RightSidebar {

/**
 * @brief 设备管理面板
 * 
 * 集成设备发现、端口管理、设备-端口关联的统一管理界面
 * 右侧边栏显示，支持设备发现、端口监控、连接管理等功能
 */
class DeviceManagementPanel : public RightSidebarPanel {
    Q_OBJECT

public:
    explicit DeviceManagementPanel(QWidget* parent = nullptr);
    virtual ~DeviceManagementPanel();

    // 设置依赖服务
    void setDeviceRegistry(std::shared_ptr<LA::DeviceManagement::IDeviceRegistry> registry);
    void setDeviceDiscoveryService(std::shared_ptr<LA::DeviceManagement::Discovery::IDeviceDiscoveryService> service);
    void setPortManager(std::shared_ptr<LA::Communication::PortManagement::IPortManager> manager);
    void setDeviceMatchingService(std::shared_ptr<LA::Communication::DeviceMatching::IDeviceMatchingService> service);

    // RightSidebarPanel 接口实现
    QString getPanelId() const override;
    QString getPanelTitle() const override;
    QString getPanelDescription() const override;
    QIcon getPanelIcon() const override;
    bool canCollapse() const override;
    void refreshContent() override;

public slots:
    /**
     * @brief 启动设备发现
     */
    void startDeviceDiscovery();
    
    /**
     * @brief 停止设备发现
     */
    void stopDeviceDiscovery();
    
    /**
     * @brief 刷新端口列表
     */
    void refreshPortList();
    
    /**
     * @brief 连接选中的设备到端口
     */
    void connectSelectedDevice();
    
    /**
     * @brief 断开选中设备的连接
     */
    void disconnectSelectedDevice();
    
    /**
     * @brief 自动匹配设备和端口
     */
    void autoMatchDevicesAndPorts();

private slots:
    // 设备发现相关槽函数
    void onDeviceDiscovered(const LA::Foundation::Core::DeviceInfo& device);
    void onDeviceLost(const QString& deviceId);
    void onDiscoveryCompleted(int deviceCount);
    void onDiscoveryError(const QString& error);
    
    // 端口管理相关槽函数
    void onPortAdded(const LA::Foundation::Core::PortInfo& port);
    void onPortRemoved(const QString& portName);
    void onPortStatusChanged(const QString& portName, int status);
    
    // UI事件槽函数
    void onDeviceSelectionChanged();
    void onPortSelectionChanged();
    void onDeviceDoubleClicked(QTreeWidgetItem* item, int column);
    void onPortDoubleClicked(QTableWidgetItem* item);
    void onConnectionStatusChanged();
    void onRefreshTimer();

private:
    /**
     * @brief 初始化UI界面
     */
    void setupUI();
    
    /**
     * @brief 创建设备发现区域
     */
    QWidget* createDeviceDiscoveryArea();
    
    /**
     * @brief 创建端口管理区域
     */
    QWidget* createPortManagementArea();
    
    /**
     * @brief 创建连接控制区域
     */
    QWidget* createConnectionControlArea();
    
    /**
     * @brief 连接信号槽
     */
    void connectSignals();
    
    /**
     * @brief 更新设备列表
     */
    void updateDeviceList();
    
    /**
     * @brief 更新端口列表
     */
    void updatePortList();
    
    /**
     * @brief 更新连接状态显示
     */
    void updateConnectionStatus();
    
    /**
     * @brief 更新统计信息
     */
    void updateStatistics();
    
    /**
     * @brief 添加设备到树形视图
     */
    void addDeviceToTree(const LA::Foundation::Core::DeviceInfo& device);
    
    /**
     * @brief 从树形视图移除设备
     */
    void removeDeviceFromTree(const QString& deviceId);
    
    /**
     * @brief 添加端口到表格视图
     */
    void addPortToTable(const LA::Foundation::Core::PortInfo& port);
    
    /**
     * @brief 从表格视图移除端口
     */
    void removePortFromTable(const QString& portName);
    
    /**
     * @brief 获取选中的设备ID
     */
    QString getSelectedDeviceId() const;
    
    /**
     * @brief 获取选中的端口名称
     */
    QString getSelectedPortName() const;
    
    /**
     * @brief 检查设备和端口是否兼容
     */
    bool isDevicePortCompatible(const QString& deviceId, const QString& portName) const;
    
    /**
     * @brief 执行设备-端口连接
     */
    bool connectDeviceToPort(const QString& deviceId, const QString& portName);
    
    /**
     * @brief 断开设备-端口连接
     */
    bool disconnectDeviceFromPort(const QString& deviceId);
    
    /**
     * @brief 创建设备图标
     */
    QIcon createDeviceIcon(const LA::Foundation::Core::DeviceInfo& device) const;
    
    /**
     * @brief 创建端口状态图标
     */
    QIcon createPortStatusIcon(int status) const;

private:
    // 依赖服务
    std::shared_ptr<LA::DeviceManagement::IDeviceRegistry> m_deviceRegistry;
    std::shared_ptr<LA::DeviceManagement::Discovery::IDeviceDiscoveryService> m_discoveryService;
    std::shared_ptr<LA::Communication::PortManagement::IPortManager> m_portManager;
    
    // UI组件
    QSplitter*      m_mainSplitter;         ///< 主分割器
    QTabWidget*     m_tabWidget;            ///< 标签页组件
    
    // 设备发现区域
    QGroupBox*      m_deviceGroup;          ///< 设备组框
    QTreeWidget*    m_deviceTree;           ///< 设备树形视图
    QPushButton*    m_startDiscoveryBtn;    ///< 开始发现按钮
    QPushButton*    m_stopDiscoveryBtn;     ///< 停止发现按钮
    QPushButton*    m_refreshDevicesBtn;    ///< 刷新设备按钮
    QLabel*         m_deviceCountLabel;     ///< 设备数量标签
    QProgressBar*   m_discoveryProgress;    ///< 发现进度条
    
    // 端口管理区域
    QGroupBox*      m_portGroup;            ///< 端口组框
    QTableWidget*   m_portTable;            ///< 端口表格视图
    QPushButton*    m_refreshPortsBtn;      ///< 刷新端口按钮
    QLabel*         m_portCountLabel;       ///< 端口数量标签
    
    // 连接控制区域
    QGroupBox*      m_connectionGroup;      ///< 连接组框
    QComboBox*      m_deviceCombo;          ///< 设备选择下拉框
    QComboBox*      m_portCombo;            ///< 端口选择下拉框
    QPushButton*    m_connectBtn;           ///< 连接按钮
    QPushButton*    m_disconnectBtn;        ///< 断开连接按钮
    QPushButton*    m_autoMatchBtn;         ///< 自动匹配按钮
    QLabel*         m_connectionStatus;     ///< 连接状态标签
    
    // 状态管理
    QTimer*         m_refreshTimer;         ///< 刷新定时器
    bool            m_discoveryRunning;     ///< 发现服务是否运行
    
    // 设备-端口映射关系
    QMap<QString, QString>   m_devicePortMap;    ///< 设备ID到端口名称的映射
    QMap<QString, QString>   m_portDeviceMap;    ///< 端口名称到设备ID的映射
};

/**
 * @brief 设备信息对话框
 * 
 * 显示详细的设备信息，包括参数、统计数据等
 */
class DeviceInfoDialog : public QDialog {
    Q_OBJECT

public:
    explicit DeviceInfoDialog(const LA::Foundation::Core::DeviceInfo& device, QWidget* parent = nullptr);

private:
    void setupUI();
    void populateDeviceInfo();
    
private:
    LA::Foundation::Core::DeviceInfo m_deviceInfo;
    QTabWidget* m_tabWidget;
    QTableWidget* m_basicInfoTable;
    QTableWidget* m_parametersTable;
    QTableWidget* m_capabilitiesTable;
};

/**
 * @brief 端口信息对话框
 * 
 * 显示详细的端口信息，包括配置、统计数据等
 */
class PortInfoDialog : public QDialog {
    Q_OBJECT

public:
    explicit PortInfoDialog(const LA::Foundation::Core::PortInfo& port, QWidget* parent = nullptr);

private:
    void setupUI();
    void populatePortInfo();
    
private:
    LA::Foundation::Core::PortInfo m_portInfo;
    QTabWidget* m_tabWidget;
    QTableWidget* m_basicInfoTable;
    QTableWidget* m_configTable;
    QTableWidget* m_statisticsTable;
};

} // namespace RightSidebar
} // namespace LA