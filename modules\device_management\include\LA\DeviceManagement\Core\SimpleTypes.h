#pragma once

#include <QString>
#include <QVariant>
#include <QStringList>
#include <QMap>

namespace LA {
namespace DeviceManagement {
namespace Core {

// 简化的设备管理类型定义
// 用于临时解决Foundation layer依赖问题

/**
 * @brief 设备类型枚举
 */
enum class DeviceType {
    Unknown = 0,
    SPRM = 1,
    Motor = 2,
    Sensor = 3,
    TestBoard = 4
};

/**
 * @brief 端口类型枚举
 */
enum class PortType {
    Unknown = 0,
    Serial = 1,
    TCP = 2,
    UDP = 3,
    USB = 4
};

/**
 * @brief 端口状态枚举
 */
enum class PortStatus {
    Unknown = 0,
    Available = 1,
    InUse = 2,
    Busy = 3,        // 添加Busy状态
    Error = 4,
    Offline = 5
};

/**
 * @brief 连接状态枚举
 */
enum class ConnectionStatus {
    Unknown = 0,
    Connected = 1,
    Disconnected = 2,
    Connecting = 3,
    Disconnecting = 4,
    Error = 5
};

/**
 * @brief 设备信息结构
 */
struct DeviceInfo {
    QString deviceId;           // 设备ID
    QString deviceName;         // 设备名称
    DeviceType deviceType;      // 设备类型
    QString manufacturer;       // 制造商
    QString model;              // 型号
    QString deviceModel;        // 设备型号（别名）
    QString version;            // 版本
    QString serialNumber;       // 序列号
    QStringList capabilities;   // 设备能力列表
    QVariantMap properties;     // 扩展属性

    DeviceInfo() : deviceType(DeviceType::Unknown) {}
    
    bool isValid() const {
        return !deviceId.isEmpty() && deviceType != DeviceType::Unknown;
    }
    
    bool operator==(const DeviceInfo& other) const {
        return deviceId == other.deviceId;
    }
    
    bool operator!=(const DeviceInfo& other) const {
        return !(*this == other);
    }
};

/**
 * @brief 端口信息结构
 */
struct PortInfo {
    QString portName;           // 端口名称
    QString displayName;        // 显示名称
    PortType portType;          // 端口类型
    PortType type;              // 端口类型（别名）
    PortStatus status;          // 端口状态
    QString description;        // 描述
    QVariantMap properties;     // 扩展属性

    PortInfo() : portType(PortType::Unknown), type(PortType::Unknown), status(PortStatus::Unknown) {}
    
    bool isValid() const {
        return !portName.isEmpty() && portType != PortType::Unknown;
    }
    
    bool operator==(const PortInfo& other) const {
        return portName == other.portName;
    }
    
    bool operator!=(const PortInfo& other) const {
        return !(*this == other);
    }
};

/**
 * @brief 结果代码
 */
enum class ResultCode {
    Success = 0,
    Failed = 1,
    Timeout = 2,
    InvalidParameter = 3,
    NotSupported = 4,
    NotConnected = 5
};

// 配置参数类型
using ConfigParameters = QVariantMap;

// 类型别名
using DeviceInfoList = QList<DeviceInfo>;
using PortInfoList = QList<PortInfo>;

// 工具函数声明
QString deviceTypeToString(DeviceType type);
DeviceType stringToDeviceType(const QString& typeStr);
QString portTypeToString(PortType type);
PortType stringToPortType(const QString& typeStr);
QString portStatusToString(PortStatus status);
PortStatus stringToPortStatus(const QString& statusStr);
QString connectionStatusToString(ConnectionStatus status);
ConnectionStatus stringToConnectionStatus(const QString& statusStr);

} // namespace Core
} // namespace DeviceManagement
} // namespace LA