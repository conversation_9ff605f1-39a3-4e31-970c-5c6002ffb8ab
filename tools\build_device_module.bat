@echo off
rem 设备模块专用构建脚本
rem 强制使用MinGW编译器，避免系统默认Visual Studio干扰

setlocal

echo ========================================
echo 设备模块构建脚本 (Device Module Builder)
echo ========================================
echo.

set MODULE_DIR=modules\device
set BUILD_DIR=%MODULE_DIR%\build

echo 1. 检查模块目录...
if not exist "%MODULE_DIR%" (
    echo 错误：找不到设备模块目录 %MODULE_DIR%
    exit /b 1
)

echo 2. 设置编译环境变量...
set CMAKE_GENERATOR=Ninja
set CMAKE_C_COMPILER=D:/Programs/Qt/Qt5.14.2/Tools/mingw730_64/bin/gcc.exe
set CMAKE_CXX_COMPILER=D:/Programs/Qt/Qt5.14.2/Tools/mingw730_64/bin/g++.exe
set CMAKE_PREFIX_PATH=D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64

echo 3. 清理旧构建目录...
if exist "%BUILD_DIR%" rmdir /s /q "%BUILD_DIR%"
mkdir "%BUILD_DIR%"

echo 4. 配置CMake...
cd "%MODULE_DIR%"
cd build

cmake -G "%CMAKE_GENERATOR%" ^
      -DCMAKE_C_COMPILER="%CMAKE_C_COMPILER%" ^
      -DCMAKE_CXX_COMPILER="%CMAKE_CXX_COMPILER%" ^
      -DCMAKE_PREFIX_PATH="%CMAKE_PREFIX_PATH%" ^
      -DCMAKE_BUILD_TYPE=Release ^
      ..

if errorlevel 1 (
    echo 错误：CMake配置失败
    cd ..\..
    exit /b 1
)

echo 5. 开始构建...
ninja

if errorlevel 1 (
    echo 错误：构建失败
    cd ..\..
    exit /b 1
)

echo.
echo ========================================
echo 构建完成！
echo 输出目录: %MODULE_DIR%\build\
echo 使用的编译器: MinGW GCC 7.3.0
echo ========================================

cd ..\..
