#include "LoggerManager.h"
#include "Logger.h"
#include <QMutexLocker>

namespace LA {
namespace Support {
namespace Logging {

LoggerManager& LoggerManager::getInstance() {
    static LoggerManager instance;
    return instance;
}

LoggerManager::LoggerManager() : QObject(nullptr) {
    createRootLogger();
}

LoggerManager::~LoggerManager() {
    destroyAllLoggers();
}

std::shared_ptr<Logger> LoggerManager::createLogger(const QString& name, const LoggerConfig& config) {
    QMutexLocker locker(&m_mutex);
    
    auto it = m_loggers.find(name);
    if (it != m_loggers.end()) {
        return it->second;
    }
    
    auto logger = std::make_shared<Logger>(name);
    logger->setConfig(config);
    logger->initialize();
    m_loggers[name] = logger;
    
    return logger;
}

std::shared_ptr<Logger> LoggerManager::getLogger(const QString& name) {
    QMutexLocker locker(&m_mutex);
    
    auto it = m_loggers.find(name);
    if (it != m_loggers.end()) {
        return it->second;
    }
    
    // 如果不存在，使用全局配置创建新的日志记录器
    return createLogger(name, m_globalConfig);
}

std::shared_ptr<Logger> LoggerManager::getRootLogger() {
    return m_rootLogger;
}

void LoggerManager::destroyLogger(const QString& name) {
    QMutexLocker locker(&m_mutex);
    
    auto it = m_loggers.find(name);
    if (it != m_loggers.end()) {
        m_loggers.erase(it);
    }
}

void LoggerManager::destroyAllLoggers() {
    QMutexLocker locker(&m_mutex);
    
    for (auto& pair : m_loggers) {
        pair.second->shutdown();
    }
    m_loggers.clear();
    
    if (m_rootLogger) {
        m_rootLogger->shutdown();
        m_rootLogger.reset();
    }
}

void LoggerManager::setGlobalConfig(const LoggerConfig& config) {
    QMutexLocker locker(&m_mutex);
    m_globalConfig = config;
    
    // 应用到所有现有的日志记录器
    for (auto& pair : m_loggers) {
        pair.second->setConfig(config);
    }
}

LoggerConfig LoggerManager::getGlobalConfig() const {
    QMutexLocker locker(&m_mutex);
    return m_globalConfig;
}

void LoggerManager::flushAll() {
    QMutexLocker locker(&m_mutex);
    
    for (auto& pair : m_loggers) {
        pair.second->flush();
    }
    
    if (m_rootLogger) {
        m_rootLogger->flush();
    }
}

QStringList LoggerManager::getAllLoggerNames() const {
    QMutexLocker locker(&m_mutex);
    
    QStringList names;
    for (const auto& pair : m_loggers) {
        names.append(pair.first);
    }
    return names;
}

QVariantMap LoggerManager::getStatistics() const {
    QMutexLocker locker(&m_mutex);
    
    QVariantMap stats;
    stats["total_loggers"] = static_cast<int>(m_loggers.size());
    
    // 收集各个日志记录器的统计信息
    QVariantList loggerStats;
    for (const auto& pair : m_loggers) {
        loggerStats.append(pair.second->getStatistics());
    }
    stats["loggers"] = loggerStats;
    
    return stats;
}

void LoggerManager::createRootLogger() {
    m_rootLogger = std::make_shared<Logger>("root");
    m_rootLogger->initialize();
    m_loggers["root"] = m_rootLogger;
}

// LoggerFactory Implementation
std::shared_ptr<ILogger> LoggerFactory::createLogger(const QString& name, const LoggerConfig& config) {
    return LoggerManager::getInstance().createLogger(name, config);
}

std::shared_ptr<ILogger> LoggerFactory::getLogger(const QString& name) {
    return LoggerManager::getInstance().getLogger(name);
}

void LoggerFactory::destroyLogger(const QString& name) {
    LoggerManager::getInstance().destroyLogger(name);
}

}  // namespace Logging
}  // namespace Support
}  // namespace LA


