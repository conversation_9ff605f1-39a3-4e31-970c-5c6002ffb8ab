#include "LA/UI/DeviceCommunication/adapters/LegacyDeviceUiAdapter.h"
#include <LA/DeviceManagement/DeviceInstanceManager.h>
#include <QDebug>
#include <QMetaObject>

namespace LA {
namespace UI {
namespace DeviceCommunication {
namespace Adapters {

LegacyDeviceUiAdapter::LegacyDeviceUiAdapter(QObject *parent) 
    : QObject(parent), m_deviceInstanceManager(nullptr) {
    qDebug() << "[LegacyDeviceUiAdapter] 初始化Legacy设备UI适配器";
}

LegacyDeviceUiAdapter::~LegacyDeviceUiAdapter() {
    qDebug() << "[LegacyDeviceUiAdapter] 销毁Legacy设备UI适配器";
    
    // 清理所有UI组件连接
    for (auto it = m_deviceBoxes.begin(); it != m_deviceBoxes.end(); ++it) {
        cleanupComponent(it.key());
    }
    
    for (auto it = m_instanceDisplayBoxes.begin(); it != m_instanceDisplayBoxes.end(); ++it) {
        disconnect(it.key(), &QObject::destroyed, this, &LegacyDeviceUiAdapter::onUIComponentDestroyed);
    }
}

void LegacyDeviceUiAdapter::setDeviceInstanceManager(LA::DeviceManagement::DeviceInstanceManager *manager) {
    if (m_deviceInstanceManager) {
        // 断开之前的连接
        disconnect(m_deviceInstanceManager, nullptr, this, nullptr);
    }
    
    m_deviceInstanceManager = manager;
    
    if (m_deviceInstanceManager) {
        // 连接业务层信号
        connect(m_deviceInstanceManager, &LA::DeviceManagement::DeviceInstanceManager::instanceCreated,
                this, &LegacyDeviceUiAdapter::deviceInstanceCreated);
        connect(m_deviceInstanceManager, &LA::DeviceManagement::DeviceInstanceManager::instanceDestroyed,
                this, &LegacyDeviceUiAdapter::deviceInstanceDestroyed);
                
        qDebug() << "[LegacyDeviceUiAdapter] 已连接DeviceInstanceManager";
        
        // 立即更新设备列表
        updateDeviceList();
        updateInstanceList();
    }
}

void LegacyDeviceUiAdapter::registerDeviceBox(QComboBox *deviceBox, 
                                             QComboBox *portBox,
                                             QLineEdit *descriptionEdit,
                                             bool isExclusiveDevice,
                                             const QString &deviceCategory) {
    if (!deviceBox) {
        qWarning() << "[LegacyDeviceUiAdapter] 设备选择框为空";
        return;
    }
    
    qDebug() << "[LegacyDeviceUiAdapter] 注册设备选择框：" << deviceBox->objectName();
    
    // 创建UI组件信息
    UiComponentInfo info;
    info.deviceBox = deviceBox;
    info.portBox = portBox;
    info.descriptionEdit = descriptionEdit;
    info.isExclusiveDevice = isExclusiveDevice;
    info.deviceCategory = deviceCategory;
    info.instanceId = "";  // 初始时无实例
    
    m_deviceBoxes.insert(deviceBox, info);
    
    // 设置设备列表
    updateComboBoxList(deviceBox, m_availableDeviceTypes);
    
    // 连接信号
    connect(deviceBox, QOverload<const QString &>::of(&QComboBox::currentTextChanged),
            this, &LegacyDeviceUiAdapter::onDeviceSelectionChanged);
    connect(deviceBox, &QObject::destroyed, 
            this, &LegacyDeviceUiAdapter::onUIComponentDestroyed);
    
    qDebug() << "[LegacyDeviceUiAdapter] 设备选择框注册完成，当前支持的设备类型数：" << m_availableDeviceTypes.size();
}

void LegacyDeviceUiAdapter::registerInstanceDisplayBox(QComboBox *instanceBox, const QString &deviceCategory) {
    if (!instanceBox) {
        qWarning() << "[LegacyDeviceUiAdapter] 实例显示框为空";
        return;
    }
    
    qDebug() << "[LegacyDeviceUiAdapter] 注册实例显示框：" << instanceBox->objectName();
    
    m_instanceDisplayBoxes.insert(instanceBox, deviceCategory);
    
    // 设置实例列表
    updateComboBoxList(instanceBox, m_activeInstanceIds);
    
    // 连接销毁信号
    connect(instanceBox, &QObject::destroyed, 
            this, &LegacyDeviceUiAdapter::onUIComponentDestroyed);
}

bool LegacyDeviceUiAdapter::createDeviceInstance(QComboBox *deviceBox) {
    if (!deviceBox || !m_deviceInstanceManager) {
        qWarning() << "[LegacyDeviceUiAdapter] 创建设备实例失败：参数无效";
        return false;
    }
    
    auto it = m_deviceBoxes.find(deviceBox);
    if (it == m_deviceBoxes.end()) {
        qWarning() << "[LegacyDeviceUiAdapter] 设备选择框未注册";
        return false;
    }
    
    UiComponentInfo &info = it.value();
    
    // 检查是否已存在实例（独占设备）
    if (info.isExclusiveDevice && !info.instanceId.isEmpty()) {
        qDebug() << "[LegacyDeviceUiAdapter] 独占设备已存在实例：" << info.instanceId;
        return true;  // 返回成功，使用现有实例
    }
    
    // 构建设备配置
    QVariantMap config;
    config["deviceType"] = deviceBox->currentText();
    config["category"] = info.deviceCategory;
    
    if (info.descriptionEdit) {
        config["serialNumber"] = info.descriptionEdit->text();
    }
    
    if (info.portBox) {
        config["portName"] = info.portBox->currentText();
    }
    
    // 创建设备实例
    QString instanceId = m_deviceInstanceManager->createDeviceInstance(deviceBox->currentText(), config);
    
    if (instanceId.isEmpty()) {
        qWarning() << "[LegacyDeviceUiAdapter] 创建设备实例失败：" 
                   << m_deviceInstanceManager->getLastError();
        return false;
    }
    
    // 更新UI组件信息
    info.instanceId = instanceId;
    m_deviceBoxes[deviceBox] = info;
    
    qDebug() << "[LegacyDeviceUiAdapter] 成功创建设备实例：" << instanceId;
    return true;
}

bool LegacyDeviceUiAdapter::destroyDeviceInstance(QComboBox *deviceBox) {
    if (!deviceBox || !m_deviceInstanceManager) {
        return false;
    }
    
    auto it = m_deviceBoxes.find(deviceBox);
    if (it == m_deviceBoxes.end() || it->instanceId.isEmpty()) {
        return false;
    }
    
    UiComponentInfo &info = it.value();
    bool success = m_deviceInstanceManager->destroyDeviceInstance(info.instanceId);
    
    if (success) {
        info.instanceId = "";
        m_deviceBoxes[deviceBox] = info;
        qDebug() << "[LegacyDeviceUiAdapter] 成功销毁设备实例";
    }
    
    return success;
}

QString LegacyDeviceUiAdapter::getDeviceInstanceId(QComboBox *deviceBox) const {
    auto it = m_deviceBoxes.find(deviceBox);
    if (it != m_deviceBoxes.end()) {
        return it->instanceId;
    }
    return QString();
}

void LegacyDeviceUiAdapter::updateDeviceList() {
    if (!m_deviceInstanceManager) {
        return;
    }
    
    // 从业务层获取支持的设备类型
    QStringList newDeviceTypes = m_deviceInstanceManager->getSupportedDeviceTypes();
    
    if (newDeviceTypes != m_availableDeviceTypes) {
        m_availableDeviceTypes = newDeviceTypes;
        
        // 更新所有注册的设备选择框
        for (auto it = m_deviceBoxes.begin(); it != m_deviceBoxes.end(); ++it) {
            updateComboBoxList(it.key(), m_availableDeviceTypes);
        }
        
        qDebug() << "[LegacyDeviceUiAdapter] 设备类型列表已更新，数量：" << m_availableDeviceTypes.size();
    }
}

void LegacyDeviceUiAdapter::updateInstanceList() {
    if (!m_deviceInstanceManager) {
        return;
    }
    
    // 从业务层获取活动实例ID
    QStringList newInstanceIds = m_deviceInstanceManager->getActiveInstanceIds();
    
    if (newInstanceIds != m_activeInstanceIds) {
        m_activeInstanceIds = newInstanceIds;
        
        // 更新所有注册的实例显示框
        for (auto it = m_instanceDisplayBoxes.begin(); it != m_instanceDisplayBoxes.end(); ++it) {
            updateComboBoxList(it.key(), m_activeInstanceIds);
        }
        
        qDebug() << "[LegacyDeviceUiAdapter] 实例列表已更新，数量：" << m_activeInstanceIds.size();
        
        // 发送信号
        emit onInstanceListUpdated(m_activeInstanceIds);
    }
}

void LegacyDeviceUiAdapter::onDeviceSelectionChanged(const QString &deviceType) {
    QComboBox *senderBox = qobject_cast<QComboBox*>(sender());
    if (!senderBox) {
        return;
    }
    
    qDebug() << "[LegacyDeviceUiAdapter] 设备选择变更：" << deviceType;
    
    auto it = m_deviceBoxes.find(senderBox);
    if (it != m_deviceBoxes.end() && it->portBox) {
        // 这里可以根据设备类型更新端口配置
        // 但这需要依赖端口管理服务，暂时留空
        qDebug() << "[LegacyDeviceUiAdapter] TODO: 根据设备类型更新端口配置";
    }
    
    emit deviceSelectionChanged(deviceType);
}

void LegacyDeviceUiAdapter::onUIComponentDestroyed(QObject *component) {
    QComboBox *comboBox = static_cast<QComboBox*>(component);
    
    // 从设备框映射中移除
    auto deviceIt = m_deviceBoxes.find(comboBox);
    if (deviceIt != m_deviceBoxes.end()) {
        // 销毁关联的设备实例
        if (!deviceIt->instanceId.isEmpty()) {
            destroyDeviceInstance(comboBox);
        }
        m_deviceBoxes.erase(deviceIt);
        qDebug() << "[LegacyDeviceUiAdapter] 已清理设备选择框组件";
    }
    
    // 从实例显示框映射中移除
    auto instanceIt = m_instanceDisplayBoxes.find(comboBox);
    if (instanceIt != m_instanceDisplayBoxes.end()) {
        m_instanceDisplayBoxes.erase(instanceIt);
        qDebug() << "[LegacyDeviceUiAdapter] 已清理实例显示框组件";
    }
}

void LegacyDeviceUiAdapter::onInstanceListUpdated(const QStringList &instanceList) {
    // 这是一个内部信号槽，用于处理实例列表更新
    // 实际的更新逻辑已在 updateInstanceList() 中处理
}

void LegacyDeviceUiAdapter::updateComboBoxList(QComboBox *comboBox, const QStringList &items) {
    if (!comboBox) {
        return;
    }
    
    // 保存当前选中项
    QString currentText = comboBox->currentText();
    bool hasCurrentItem = items.contains(currentText);
    
    // 阻塞信号，避免不必要的事件触发
    comboBox->blockSignals(true);
    
    // 更新列表
    comboBox->clear();
    comboBox->addItems(items);
    
    // 恢复选中项或设置为第一项
    if (hasCurrentItem) {
        comboBox->setCurrentText(currentText);
    } else if (!items.isEmpty()) {
        comboBox->setCurrentIndex(0);
    }
    
    // 恢复信号
    comboBox->blockSignals(false);
}

void LegacyDeviceUiAdapter::cleanupComponent(QComboBox *component) {
    if (!component) {
        return;
    }
    
    // 断开所有连接
    disconnect(component, nullptr, this, nullptr);
    
    // 销毁关联的设备实例
    destroyDeviceInstance(component);
}

} // namespace Adapters
} // namespace DeviceCommunication
} // namespace UI
} // namespace LA