#pragma once

/**
 * @file SerialConnection.h
 * @brief 串口连接实现 - Linus式最小实现
 * 
 * 遵循Linus设计原则：
 * - 实现新的IConnection接口
 * - 最小化功能，专注数据传输
 * - 基于Qt SerialPort的稳定实现
 * - Layer 1实现：纯粹的read/write操作
 */

#include "IConnection.h"
#include <QSerialPort>
#include <QSerialPortInfo>
#include <QMutex>

namespace LA {
namespace Communication {
namespace Connection {

/**
 * @brief 串口连接的Linus式实现
 * 
 * 严格遵循IConnection接口，只实现数据传输功能
 */
class SerialConnection : public IConnection {
    Q_OBJECT

public:
    explicit SerialConnection(QObject* parent = nullptr);
    virtual ~SerialConnection();

    // === IConnection接口实现 ===
    
    SimpleResult open(const ConnectionConfig& config) override;
    SimpleResult close() override;
    ConnectionStatus status() const override;
    bool isOpen() const override;
    
    qint64 write(const QByteArray& data) override;
    QByteArray read(qint64 maxBytes = -1) override;
    qint64 bytesAvailable() const override;
    bool waitForReadyRead(int timeout = 5000) override;
    bool waitForBytesWritten(int timeout = 5000) override;
    
    DeviceStatistics getStatistics() const override;
    QString errorString() const override;
    void resetStatistics() override;

    // === 串口特定功能（最小化） ===
    
    /**
     * @brief 获取系统可用串口列表
     * @return 串口名称列表
     */
    static QStringList getAvailablePorts();
    
    /**
     * @brief 获取当前串口信息
     * @return 串口信息
     */
    QSerialPortInfo getPortInfo() const;

private slots:
    void onReadyRead();
    void onErrorOccurred(QSerialPort::SerialPortError error);

private:
    QSerialPort* m_serialPort;
    ConnectionConfig m_config;
    ConnectionStatus m_status;
    DeviceStatistics m_statistics;
    QString m_lastError;
    mutable QMutex m_mutex;
    
    void updateStatistics(qint64 bytesRead = 0, qint64 bytesWritten = 0);
    void setStatus(ConnectionStatus newStatus);
    bool applySerialConfig();
};

} // namespace Connection
} // namespace Communication
} // namespace LA