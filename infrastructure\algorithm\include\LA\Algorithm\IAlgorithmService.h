/**
 * @file IAlgorithmService.h
 * @brief Algorithm Service Interface - Placeholder
 * 
 * This is a temporary placeholder to resolve build dependencies.
 * TODO: Implement proper algorithm service interface.
 */

#pragma once

#include <QObject>
#include <QVariantMap>

namespace LA {
namespace Algorithm {

/**
 * @brief Placeholder algorithm service interface
 * 
 * This is a minimal interface to allow compilation.
 * Should be properly implemented according to requirements.
 */
class IAlgorithmService : public QObject
{
    Q_OBJECT

public:
    virtual ~IAlgorithmService() = default;
    
    /**
     * @brief Execute algorithm with parameters
     * @param algorithm Algorithm name
     * @param params Algorithm parameters
     * @return Result data
     */
    virtual QVariantMap execute(const QString& algorithm, const QVariantMap& params) = 0;
    
signals:
    /**
     * @brief Algorithm execution completed
     * @param result Execution result
     */
    void algorithmCompleted(const QVariantMap& result);
    
    /**
     * @brief Algorithm execution failed
     * @param error Error information
     */
    void algorithmFailed(const QString& error);
};

} // namespace Algorithm
} // namespace LA