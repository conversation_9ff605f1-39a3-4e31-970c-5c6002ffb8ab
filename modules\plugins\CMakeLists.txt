# LA Plugins
# 插件模块集合

cmake_minimum_required(VERSION 3.16)
project(LA_plugins VERSION 1.0.0 LANGUAGES CXX)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 查找Qt5组件
find_package(Qt5 REQUIRED COMPONENTS Core Widgets Gui Sql SerialPort PrintSupport Charts Network Xml)

# 可用的插件列表
set(AVAILABLE_PLUGINS
    # embeddedFeatureManager  # 待修复：编译错误
    # example                 # 暂时屏蔽：头文件路径问题
    # clensAdjust
    comCheck                  # 重新启用：通信检查插件
    # corePerformanceAnalysis # 暂时屏蔽：专注核心UI
    # functionTest           # 暂时屏蔽：专注核心UI
    # motorMonitor           # 暂时屏蔽：专注核心UI
    # novaCalibration        # 暂时屏蔽：专注核心UI
    # novaDemonstrate        # 暂时屏蔽：专注核心UI
    # productInfo            # 暂时屏蔽：专注核心UI
    # turnOnTime             # 暂时屏蔽：专注核心UI
)

# 选择要构建的插件（默认全部）
set(BUILD_PLUGINS "ALL" CACHE STRING "Plugins to build (ALL or specific list)")

# 确定实际要构建的插件
if(BUILD_PLUGINS STREQUAL "ALL")
    set(PLUGINS_TO_BUILD ${AVAILABLE_PLUGINS})
else()
    set(PLUGINS_TO_BUILD ${BUILD_PLUGINS})
endif()

# 添加每个选定的插件子目录
foreach(plugin ${PLUGINS_TO_BUILD})
    if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/${plugin}/CMakeLists.txt)
        add_subdirectory(${plugin})
        message(STATUS "Added plugin: ${plugin}")
    else()
        message(WARNING "Plugin directory ${plugin} does not contain CMakeLists.txt")
    endif()
endforeach()

# 创建插件集合目标
add_library(LA_plugins_collection INTERFACE)

# 设置目标属性
set_target_properties(LA_plugins_collection PROPERTIES
    OUTPUT_NAME "LA_plugins_collection"
    VERSION ${PROJECT_VERSION}
)

# 设置包含目录
target_include_directories(LA_plugins_collection
    INTERFACE
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>
        $<INSTALL_INTERFACE:include/plugins>
)

# 链接Qt依赖
target_link_libraries(LA_plugins_collection
    INTERFACE
        Qt5::Core
        Qt5::Widgets
        Qt5::Gui
)

# 导出变量供父项目使用
set(LA_PLUGINS_LIBRARY LA_plugins_collection PARENT_SCOPE)
set(LA_PLUGINS_INCLUDE_DIR ${CMAKE_CURRENT_SOURCE_DIR} PARENT_SCOPE)
set(LA_PLUGINS_VERSION ${PROJECT_VERSION} PARENT_SCOPE)

# 打印配置信息
message(STATUS "LA Plugins Configuration:")
message(STATUS "  Version: ${PROJECT_VERSION}")
message(STATUS "  C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  Available plugins: ${AVAILABLE_PLUGINS}")
message(STATUS "  Building plugins: ${PLUGINS_TO_BUILD}")

# 安装配置（可选）
if(CMAKE_INSTALL_PREFIX_INITIALIZED_TO_DEFAULT)
    set(CMAKE_INSTALL_PREFIX "${CMAKE_BINARY_DIR}/install" CACHE PATH "Default install path" FORCE)
endif()

# 创建插件信息文件（暂时注释掉，缺少模板文件）
# configure_file(
#     "${CMAKE_CURRENT_SOURCE_DIR}/../cmake/PluginInfo.cmake.in"
#     "${CMAKE_CURRENT_BINARY_DIR}/PluginInfo.cmake"
#     @ONLY
# )