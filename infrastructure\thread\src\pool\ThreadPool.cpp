#include "ThreadPool.h"
#include <QRunnable>
#include <QDebug>
#include <QDateTime>

namespace LA {
namespace Thread {

// 任务包装器类
class ThreadPool::TaskWrapper : public QRunnable
{
public:
    TaskWrapper(const Task& task, ThreadPool* pool)
        : m_task(task), m_pool(pool)
    {
        setAutoDelete(true);
    }

    void run() override
    {
        if (m_pool) {
            m_pool->executeTask(m_task);
        }
    }

private:
    Task m_task;
    ThreadPool* m_pool;
};

ThreadPool::ThreadPool(QObject* parent)
    : IThreadPool(parent)
    , m_threadPool(QThreadPool::globalInstance())
    , m_taskTimeout(30000)
    , m_isPaused(false)
    , m_completedTasks(0)
    , m_failedTasks(0)
    , m_totalTasks(0)
    , m_totalExecutionTime(0)
    , m_performanceMonitoring(false)
{
    // 创建性能监控定时器
    m_performanceTimer = new QTimer(this);
    connect(m_performanceTimer, &QTimer::timeout,
            this, &ThreadPool::updatePerformanceStatistics);
    
    qDebug() << "ThreadPool initialized with" << m_threadPool->maxThreadCount() << "threads";
}

ThreadPool::~ThreadPool()
{
    clear();
    waitForDone();
    qDebug() << "ThreadPool destroyed";
}

void ThreadPool::setMaxThreadCount(int count)
{
    m_threadPool->setMaxThreadCount(count);
    qDebug() << "ThreadPool max thread count set to" << count;
}

int ThreadPool::maxThreadCount() const
{
    return m_threadPool->maxThreadCount();
}

int ThreadPool::activeThreadCount() const
{
    return m_threadPool->activeThreadCount();
}

int ThreadPool::queuedTaskCount() const
{
    QMutexLocker locker(&m_mutex);
    return m_taskQueue.size();
}

void ThreadPool::submitTask(TaskFunction task, TaskType type, int priority, const QString& name)
{
    if (!task) {
        qWarning() << "Attempted to submit null task";
        return;
    }

    QMutexLocker locker(&m_mutex);
    
    if (m_isPaused) {
        qDebug() << "ThreadPool is paused, queueing task:" << name;
        Task newTask(task, type, priority, name);
        m_taskQueue.enqueue(newTask);
        return;
    }
    
    Task newTask(task, type, priority, name);
    
    if (!name.isEmpty()) {
        m_namedTasks[name] = newTask;
    }
    
    TaskWrapper* wrapper = new TaskWrapper(newTask, this);
    m_threadPool->start(wrapper, priority);
    
    m_totalTasks++;
    
    emit taskStarted(name, QThread::currentThread()->objectName());
    
    locker.unlock();
    updateStatistics();
}

void ThreadPool::submitTask(QRunnable* runnable, int priority)
{
    if (!runnable) {
        qWarning() << "Attempted to submit null runnable";
        return;
    }

    if (m_isPaused) {
        qWarning() << "Cannot submit QRunnable when paused";
        return;
    }
    
    m_threadPool->start(runnable, priority);
    
    QMutexLocker locker(&m_mutex);
    m_totalTasks++;
    
    locker.unlock();
    updateStatistics();
}

void ThreadPool::submitTasks(const QList<Task>& tasks)
{
    for (const Task& task : tasks) {
        submitTask(task.function, task.type, task.priority, task.name);
    }
}

void ThreadPool::submitCriticalTask(TaskFunction task, const QString& name)
{
    submitTask(task, TaskType::Critical, 10, name);
}

void ThreadPool::submitBackgroundTask(TaskFunction task, const QString& name)
{
    submitTask(task, TaskType::Background, -10, name);
}

void ThreadPool::clear()
{
    QMutexLocker locker(&m_mutex);
    
    m_threadPool->clear();
    m_taskQueue.clear();
    m_namedTasks.clear();
    
    qDebug() << "ThreadPool cleared";
}

bool ThreadPool::waitForDone(int msecs)
{
    return m_threadPool->waitForDone(msecs);
}

void ThreadPool::pause()
{
    QMutexLocker locker(&m_mutex);
    m_isPaused = true;
    
    emit threadPoolStateChanged(false);
    qDebug() << "ThreadPool paused";
}

void ThreadPool::resume()
{
    QMutexLocker locker(&m_mutex);
    m_isPaused = false;
    
    // 处理队列中的任务
    while (!m_taskQueue.isEmpty()) {
        Task task = m_taskQueue.dequeue();
        TaskWrapper* wrapper = new TaskWrapper(task, this);
        m_threadPool->start(wrapper, task.priority);
    }
    
    emit threadPoolStateChanged(true);
    qDebug() << "ThreadPool resumed";
}

bool ThreadPool::isPaused() const
{
    QMutexLocker locker(&m_mutex);
    return m_isPaused;
}

bool ThreadPool::cancelTask(const QString& taskName)
{
    QMutexLocker locker(&m_mutex);
    
    if (m_namedTasks.contains(taskName)) {
        m_namedTasks.remove(taskName);
        emit taskCancelled(taskName);
        qDebug() << "Task cancelled:" << taskName;
        return true;
    }
    
    return false;
}

void ThreadPool::cancelAllTasks()
{
    QMutexLocker locker(&m_mutex);
    
    QStringList taskNames = m_namedTasks.keys();
    m_namedTasks.clear();
    
    for (const QString& name : taskNames) {
        emit taskCancelled(name);
    }
    
    m_threadPool->clear();
    m_taskQueue.clear();
    
    qDebug() << "All tasks cancelled";
}

void ThreadPool::setTaskTimeout(int timeoutMs)
{
    QMutexLocker locker(&m_mutex);
    m_taskTimeout = timeoutMs;
}

qint64 ThreadPool::completedTaskCount() const
{
    QMutexLocker locker(&m_mutex);
    return m_completedTasks;
}

qint64 ThreadPool::failedTaskCount() const
{
    QMutexLocker locker(&m_mutex);
    return m_failedTasks;
}

qint64 ThreadPool::totalTaskCount() const
{
    QMutexLocker locker(&m_mutex);
    return m_totalTasks;
}

double ThreadPool::averageTaskExecutionTime() const
{
    QMutexLocker locker(&m_mutex);
    
    if (m_completedTasks == 0) {
        return 0.0;
    }
    
    return static_cast<double>(m_totalExecutionTime) / m_completedTasks;
}

double ThreadPool::threadPoolEfficiency() const
{
    QMutexLocker locker(&m_mutex);
    
    if (m_totalTasks == 0) {
        return 0.0;
    }
    
    return static_cast<double>(m_completedTasks) / m_totalTasks * 100.0;
}

void ThreadPool::enablePerformanceMonitoring(bool enable)
{
    QMutexLocker locker(&m_mutex);
    
    m_performanceMonitoring = enable;
    
    if (enable) {
        m_performanceTimer->start(5000); // 每5秒更新一次
    } else {
        m_performanceTimer->stop();
    }
    
    qDebug() << "Performance monitoring" << (enable ? "enabled" : "disabled");
}

bool ThreadPool::isPerformanceMonitoringEnabled() const
{
    QMutexLocker locker(&m_mutex);
    return m_performanceMonitoring;
}

void ThreadPool::executeTask(const Task& task)
{
    qint64 startTime = QDateTime::currentMSecsSinceEpoch();
    bool success = false;
    QString errorMessage;
    
    try {
        task.function();
        success = true;
        
        QMutexLocker locker(&m_mutex);
        m_completedTasks++;
        
        qint64 executionTime = QDateTime::currentMSecsSinceEpoch() - startTime;
        m_totalExecutionTime += executionTime;
        
        // 从命名任务中移除
        if (!task.name.isEmpty()) {
            m_namedTasks.remove(task.name);
        }
        
        locker.unlock();
        
        emit taskCompleted(task.name, executionTime);
        
    } catch (const std::exception& e) {
        success = false;
        errorMessage = QString::fromStdString(e.what());
        
        QMutexLocker locker(&m_mutex);
        m_failedTasks++;
        
        if (!task.name.isEmpty()) {
            m_namedTasks.remove(task.name);
        }
        
        locker.unlock();
        
        emit taskFailed(task.name, errorMessage);
        qWarning() << "Task failed" << task.name << ":" << errorMessage;
    }
    
    // 调用回调函数
    if (task.callback) {
        task.callback(success, errorMessage);
    }
}

void ThreadPool::updateStatistics()
{
    emit threadCountChanged(activeThreadCount(), maxThreadCount());
    emit queueSizeChanged(queuedTaskCount());
    
    // 检查队列过载
    int maxQueueSize = maxThreadCount() * 10; // 假设最大队列大小为线程数的10倍
    if (queuedTaskCount() > maxQueueSize) {
        emit overloadWarning(queuedTaskCount(), maxQueueSize);
    }
}

void ThreadPool::updatePerformanceStatistics()
{
    if (!m_performanceMonitoring) {
        return;
    }
    
    double efficiency = threadPoolEfficiency();
    double avgExecutionTime = averageTaskExecutionTime();
    qint64 totalTasks = totalTaskCount();
    
    emit performanceReport(efficiency, avgExecutionTime, totalTasks);
    
    qDebug() << "ThreadPool Performance:"
             << "Efficiency:" << QString::number(efficiency, 'f', 2) << "%"
             << "Avg Execution Time:" << QString::number(avgExecutionTime, 'f', 2) << "ms"
             << "Total Tasks:" << totalTasks;
}

} // namespace Thread
} // namespace LA