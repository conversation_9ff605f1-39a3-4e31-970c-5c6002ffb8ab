#ifndef LA_THREADPOOL_H
#define LA_THREADPOOL_H

#include "../../include/LA/Thread/IThreadPool.h"
#include <QThreadPool>
#include <QQueue>
#include <QMutex>
#include <QWaitCondition>
#include <QTimer>

namespace LA {
namespace Thread {

/**
 * @brief 线程池实现类
 * 
 * 基于Qt的QThreadPool实现高效的线程池管理。
 * 提供任务队列、优先级调度、性能监控等功能。
 */
class ThreadPool : public IThreadPool
{
    Q_OBJECT

public:
    explicit ThreadPool(QObject* parent = nullptr);
    ~ThreadPool() override;

    // 线程池控制
    void setMaxThreadCount(int count) override;
    int maxThreadCount() const override;
    int activeThreadCount() const override;
    int queuedTaskCount() const override;
    
    // 任务提交
    void submitTask(TaskFunction task, 
                   TaskType type = TaskType::Normal, 
                   int priority = 0,
                   const QString& name = "") override;
                   
    void submitTask(QRunnable* runnable, int priority = 0) override;
    void submitTasks(const QList<Task>& tasks) override;
    void submitCriticalTask(TaskFunction task, const QString& name = "") override;
    void submitBackgroundTask(TaskFunction task, const QString& name = "") override;
    
    // 线程池状态控制
    void clear() override;
    bool waitForDone(int msecs = -1) override;
    void pause() override;
    void resume() override;
    bool isPaused() const override;
    
    // 任务管理
    bool cancelTask(const QString& taskName) override;
    void cancelAllTasks() override;
    void setTaskTimeout(int timeoutMs) override;
    
    // 统计信息
    qint64 completedTaskCount() const override;
    qint64 failedTaskCount() const override;
    qint64 totalTaskCount() const override;
    double averageTaskExecutionTime() const override;
    double threadPoolEfficiency() const override;
    
    // 性能监控
    void enablePerformanceMonitoring(bool enable) override;
    bool isPerformanceMonitoringEnabled() const override;

private slots:
    void updatePerformanceStatistics();

private:
    class TaskWrapper;
    
    void executeTask(const Task& task);
    void updateStatistics();

private:
    QThreadPool* m_threadPool;
    mutable QMutex m_mutex;
    
    // 任务管理
    QQueue<Task> m_taskQueue;
    QMap<QString, Task> m_namedTasks;
    int m_taskTimeout;
    bool m_isPaused;
    
    // 统计信息
    qint64 m_completedTasks;
    qint64 m_failedTasks;
    qint64 m_totalTasks;
    qint64 m_totalExecutionTime;
    QTimer* m_performanceTimer;
    bool m_performanceMonitoring;
    
    Q_DISABLE_COPY(ThreadPool)
};

} // namespace Thread
} // namespace LA

#endif // LA_THREADPOOL_H