#include "LA/DeviceManagement/Discovery/DeviceDiscoveryService.h"
#include "LA/DeviceManagement/Matching/IDeviceIdentifier.h"  // 添加IDeviceIdentifier的完整定义
#include "LA/DeviceManagement/Matching/IDeviceProber.h"      // 添加IDeviceProber的完整定义
#include "LA/DeviceManagement/Matching/IPortScanner.h"       // 添加IPortScanner的完整定义
#include <QDebug>

namespace LA {
namespace DeviceManagement {
namespace Discovery {

// Foundation层类型已经通过头文件包含，无需重复using声明

DeviceDiscoveryService::DeviceDiscoveryService(QObject *parent)
    : QObject(parent), m_isDiscovering(false), m_discoveryTimeout(5000), m_discoveryTimer(new QTimer(this)) {
    m_discoveryTimer->setSingleShot(true);
    connect(m_discoveryTimer, &QTimer::timeout, this, &DeviceDiscoveryService::onDiscoveryTimeout);

    qDebug() << "DeviceDiscoveryService: Linus式设备发现服务已初始化 - 基于依赖注入的组合架构";
}

void DeviceDiscoveryService::setPortScanner(std::shared_ptr<Matching::IPortScanner> portScanner) {
    m_portScanner = portScanner;
    qDebug() << "DeviceDiscoveryService: 端口扫描器已注入";
}

void DeviceDiscoveryService::setDeviceProber(std::shared_ptr<Matching::IDeviceProber> deviceProber) {
    m_deviceProber = deviceProber;
    qDebug() << "DeviceDiscoveryService: 设备探测器已注入";
}

void DeviceDiscoveryService::setDeviceIdentifier(std::shared_ptr<Matching::IDeviceIdentifier> deviceIdentifier) {
    m_deviceIdentifier = deviceIdentifier;
    qDebug() << "DeviceDiscoveryService: 设备识别器已注入";
}

QList<DeviceDiscoveryResult> DeviceDiscoveryService::discoverDevices() {
    qDebug() << "DeviceDiscoveryService: 开始Linus式设备发现 - 基于组合架构";

    // Linus: "检查依赖，确保组合完整"
    if (!m_portScanner) {
        qWarning() << "DeviceDiscoveryService: 端口扫描器未注入，无法发现设备";
        emit discoveryError("端口扫描器未注入");
        return QList<DeviceDiscoveryResult>();
    }

    m_isDiscovering = true;
    m_discoveredDevices.clear();

    // 启动发现超时定时器
    m_discoveryTimer->start(m_discoveryTimeout);

    try {
        // Linus式职责分离: 委托给专门的模块，而非直接实现
        m_discoveredDevices = discoverDevicesFromPorts();

        m_isDiscovering = false;

        qDebug() << "DeviceDiscoveryService: 发现完成，共找到" << m_discoveredDevices.size() << "个设备";
        emit discoveryFinished();

    } catch (const QString &error) {
        m_isDiscovering = false;
        qWarning() << "DeviceDiscoveryService: 发现过程出错:" << error;
        emit discoveryError(error);
    }

    return m_discoveredDevices;
}

QList<DeviceDiscoveryResult> DeviceDiscoveryService::discoverDevicesByType(const QString &deviceType) {
    QList<DeviceDiscoveryResult> allDevices = discoverDevices();
    QList<DeviceDiscoveryResult> filteredDevices;

    for (const auto &device : allDevices) {
        if (device.deviceType == deviceType) {
            filteredDevices.append(device);
        }
    }

    qDebug() << "DeviceDiscoveryService: 按类型" << deviceType << "过滤，找到" << filteredDevices.size() << "个设备";
    return filteredDevices;
}

QString DeviceDiscoveryService::identifyDeviceType(const QVariantMap &deviceInfo) {
    // Linus: "简单实用的设备类型识别"
    QString manufacturer = deviceInfo.value("manufacturer").toString().toLower();
    QString description  = deviceInfo.value("description").toString().toLower();

    // 基于制造商和描述的简单识别
    if (manufacturer.contains("ftdi") || description.contains("ftdi")) {
        return "FTDI设备";
    } else if (manufacturer.contains("prolific") || description.contains("prolific")) {
        return "Prolific设备";
    } else if (manufacturer.contains("ch340") || description.contains("ch340")) {
        return "CH340设备";
    } else if (description.contains("arduino")) {
        return "Arduino设备";
    } else if (description.contains("usb serial")) {
        return "USB串口设备";
    }

    return "未知设备";
}

QStringList DeviceDiscoveryService::getSupportedPortTypes(const QString &deviceType) {
    // 根据设备类型返回支持的端口类型
    if (deviceType.contains("FTDI") || deviceType.contains("Prolific") || deviceType.contains("CH340") || deviceType.contains("Arduino")) {
        return {"Serial"};
    }

    return {"Serial", "USB"};
}

QList<DeviceDiscoveryResult> DeviceDiscoveryService::discoverDevicesFromPorts() {
    qDebug() << "DeviceDiscoveryService: 基于端口进行Linus式设备发现";

    QList<DeviceDiscoveryResult> results;

    // Linus式组合: 使用注入的端口扫描器获取端口列表
    auto portList = m_portScanner->scanAvailablePorts();
    qDebug() << "DeviceDiscoveryService: 端口扫描器找到" << portList.size() << "个端口";

    for (const auto &portInfo : portList) {
        try {
            DeviceDiscoveryResult result;

            // 基础信息设置
            result.deviceId       = QString("discovered_%1").arg(portInfo.portName);
            result.deviceName     = QString("设备@%1").arg(portInfo.portName);
            result.discoveredTime = QDateTime::currentDateTime();

            // 端口类型到设备类型映射
            QString portTypeStr      = QString::number(static_cast<int>(portInfo.portType));
            result.deviceType        = identifyDeviceFromPortType(portInfo.portType);
            result.requiredPortTypes = QStringList() << portTypeStr;

            // 设备属性
            result.properties["portName"] = portInfo.portName;
            result.properties["portType"] = portTypeStr;
            result.properties["status"]   = QString::number(static_cast<int>(portInfo.status));

            // 如果有设备探测器和识别器，进行更详细的识别
            if (m_deviceProber && m_deviceIdentifier) {
                // TODO: 使用探测器和识别器进行更详细的设备识别
                qDebug() << "DeviceDiscoveryService: 可进行详细设备识别 (探测器和识别器已注入)";
            }

            results.append(result);
            emit deviceDiscovered(result);

            qDebug() << "DeviceDiscoveryService: 发现设备" << result.deviceName << "于端口" << portInfo.portName;

        } catch (const QString &error) {
            qWarning() << "DeviceDiscoveryService: 处理端口" << portInfo.portName << "时出错:" << error;
        }
    }

    return results;
}

QString DeviceDiscoveryService::identifyDeviceFromPortType(PortType portType) {
    // 简单的端口类型到设备类型映射
    switch (portType) {
    case PortType::Serial:
        return "串口设备";
    case PortType::Network:
        return "网络设备";
    case PortType::USB:
        return "USB设备";
    default:
        return "未知设备";
    }
}

QString DeviceDiscoveryService::identifySerialDevice(const QVariantMap &portInfo) {
    return identifyDeviceType(portInfo);
}

QString DeviceDiscoveryService::identifyNetworkDevice(const QVariantMap &networkInfo) {
    Q_UNUSED(networkInfo)
    return "网络设备";
}

void DeviceDiscoveryService::onDiscoveryTimeout() {
    if (m_isDiscovering) {
        qWarning() << "DeviceDiscoveryService: 设备发现超时";
        m_isDiscovering = false;
        emit discoveryError("设备发现超时");
    }
}

void DeviceDiscoveryService::setDiscoveryTimeout(int timeoutMs) {
    m_discoveryTimeout = timeoutMs;
    qDebug() << "DeviceDiscoveryService: 设置发现超时为" << timeoutMs << "ms";
}

void DeviceDiscoveryService::setDiscoveryFilters(const QStringList &filters) {
    m_discoveryFilters = filters;
    qDebug() << "DeviceDiscoveryService: 设置发现过滤器:" << filters;
}

bool DeviceDiscoveryService::isDiscovering() const {
    return m_isDiscovering;
}

int DeviceDiscoveryService::getDiscoveredDeviceCount() const {
    return m_discoveredDevices.size();
}

}  // namespace Discovery
}  // namespace DeviceManagement
}  // namespace LA
