#include "ParameterManager.h"
#include <QDir>
#include <QFile>
#include <QFileInfo>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QXmlStreamWriter>
#include <QXmlStreamReader>
#include <QCryptographicHash>
#include <QStandardPaths>
#include <QRegularExpression>
#include <QDebug>

namespace LA {
namespace Support {
namespace Config {

ParameterManager::ParameterManager(QObject* parent)
    : IParameterManager(parent)
    , m_fileWatcher(nullptr)
    , m_backupTimer(nullptr)
    , m_maxHistorySize(1000)
    , m_backupInterval(60)  // 60分钟
    , m_maxBackups(10)
    , m_autoSave(true)
    , m_watchFiles(true)
    , m_encryptStorage(false)
    , m_initialized(false)
{
}

ParameterManager::~ParameterManager()
{
    shutdown();
}

SimpleResult ParameterManager::initialize(const ConfigParameters& config)
{
    QMutexLocker locker(&m_mutex);
    
    if (m_initialized) {
        return SimpleResult(true);
    }

    // 设置配置选项
    m_storageDirectory = config.value("storage_directory", 
        QStandardPaths::writableLocation(QStandardPaths::AppConfigLocation) + "/parameters").toString();
    m_maxHistorySize = config.value("max_history_size", 1000).toInt();
    m_backupInterval = config.value("backup_interval", 60).toInt();
    m_maxBackups = config.value("max_backups", 10).toInt();
    m_autoSave = config.value("auto_save", true).toBool();
    m_watchFiles = config.value("watch_files", true).toBool();
    m_encryptStorage = config.value("encrypt_storage", false).toBool();

    // 初始化存储
    if (!initializeStorage()) {
        qCritical() << "Failed to initialize parameter storage";
        return false;
    }

    // 初始化文件监控
    if (m_watchFiles && !initializeWatcher()) {
        qWarning() << "Failed to initialize file watcher, continuing without file monitoring";
    }

    // 设置定期备份
    if (m_backupInterval > 0) {
        m_backupTimer = new QTimer(this);
        connect(m_backupTimer, &QTimer::timeout, this, &ParameterManager::performPeriodicBackup);
        m_backupTimer->start(m_backupInterval * 60 * 1000); // 转换为毫秒
    }

    // 加载现有参数
    auto loadResult = loadParametersFromStorage();
    if (!loadResult.isSuccess()) {
        qWarning() << "Failed to load parameters from storage:" << loadResult.message();
    }

    m_initialized = true;
    return SimpleResult(true);
}

SimpleResult ParameterManager::shutdown()
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_initialized) {
        return SimpleResult(true);
    }

    // 保存当前状态
    if (m_autoSave) {
        saveParametersToStorage();
    }

    // 清理资源
    cleanupWatcher();
    cleanupStorage();

    if (m_backupTimer) {
        m_backupTimer->stop();
        delete m_backupTimer;
        m_backupTimer = nullptr;
    }

    m_initialized = false;
    return SimpleResult(true);
}

bool ParameterManager::isInitialized() const
{
    QMutexLocker locker(&m_mutex);
    return m_initialized;
}

StatusInfoList ParameterManager::getStatus() const
{
    QMutexLocker locker(&m_mutex);
    
    StatusInfoList status;
    status.append(StatusInfo("initialized", m_initialized ? "true" : "false"));
    status.append(StatusInfo("parameter_count", QString::number(m_parameters.size())));
    status.append(StatusInfo("storage_directory", m_storageDirectory));
    status.append(StatusInfo("auto_save", m_autoSave ? "enabled" : "disabled"));
    status.append(StatusInfo("file_monitoring", m_watchFiles ? "enabled" : "disabled"));
    status.append(StatusInfo("backup_interval", QString::number(m_backupInterval) + " minutes"));
    
    return status;
}

VersionInfo ParameterManager::getVersion() const
{
    VersionInfo version;
    version.major = 1;
    version.minor = 0;
    version.patch = 0;
    version.build = "stable";
    return version;
}

SimpleResult ParameterManager::defineParameter(const ParameterDefinition& definition)
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_initialized) {
        return SimpleResult::error("Parameter manager not initialized");
    }

    if (definition.key.isEmpty()) {
        return SimpleResult::error("Parameter key cannot be empty");
    }

    // 检查是否已存在
    if (m_parameters.find(definition.key) != m_parameters.end()) {
        return SimpleResult::error("Parameter already defined: " + definition.key);
    }

    // 验证定义
    if (definition.type == ParameterType::UNKNOWN) {
        return SimpleResult::error("Invalid parameter type");
    }

    // 创建参数条目
    ParameterEntry entry(definition);
    m_parameters[definition.key] = entry;

    // 自动保存
    if (m_autoSave) {
        saveParameterDefinitions();
    }

    emit parameterDefined(definition);
    return SimpleResult::success(true);
}

SimpleResult ParameterManager::defineParameters(const QList<ParameterDefinition>& definitions)
{
    QMutexLocker locker(&m_mutex);
    
    // 验证所有定义
    for (const auto& definition : definitions) {
        if (definition.key.isEmpty() || definition.type == ParameterType::UNKNOWN) {
            return SimpleResult::error("Invalid parameter definition found");
        }
        
        if (m_parameters.find(definition.key) != m_parameters.end()) {
            return SimpleResult::error("Parameter already defined: " + definition.key);
        }
    }

    // 批量添加
    for (const auto& definition : definitions) {
        ParameterEntry entry(definition);
        m_parameters[definition.key] = entry;
        emit parameterDefined(definition);
    }

    // 自动保存
    if (m_autoSave) {
        saveParameterDefinitions();
    }

    return SimpleResult::success(true);
}

SimpleResult ParameterManager::undefineParameter(const QString& key)
{
    QMutexLocker locker(&m_mutex);
    
    auto it = m_parameters.find(key);
    if (it == m_parameters.end()) {
        return SimpleResult::error("Parameter not found: " + key);
    }

    m_parameters.erase(it);

    // 自动保存
    if (m_autoSave) {
        saveParameterDefinitions();
    }

    emit parameterUndefined(key);
    return SimpleResult::success(true);
}

bool ParameterManager::isParameterDefined(const QString& key) const
{
    QMutexLocker locker(&m_mutex);
    return m_parameters.find(key) != m_parameters.end();
}

Result<ParameterDefinition> ParameterManager::getParameterDefinition(const QString& key) const
{
    QMutexLocker locker(&m_mutex);
    
    auto it = m_parameters.find(key);
    if (it == m_parameters.end()) {
        return Result<ParameterDefinition>::error("Parameter not found: " + key);
    }

    return Result<ParameterDefinition>::success(it->second.definition);
}

QList<ParameterDefinition> ParameterManager::getAllParameterDefinitions() const
{
    QMutexLocker locker(&m_mutex);
    
    QList<ParameterDefinition> definitions;
    for (const auto& pair : m_parameters) {
        definitions.append(pair.second.definition);
    }
    
    return definitions;
}

SimpleResult ParameterManager::setParameter(const QString& key, const QVariant& value, 
                                          const QString& modifier, const QString& reason)
{
    QMutexLocker locker(&m_mutex);
    return doSetParameter(key, value, modifier, reason);
}

SimpleResult ParameterManager::doSetParameter(const QString& key, const QVariant& value,
                                            const QString& modifier, const QString& reason)
{
    if (!m_initialized) {
        return SimpleResult::error("Parameter manager not initialized");
    }

    auto it = m_parameters.find(key);
    if (it == m_parameters.end()) {
        return SimpleResult::error("Parameter not defined: " + key);
    }

    auto& entry = it->second;
    
    // 验证参数值
    if (!validateParameterValue(entry.definition, value)) {
        return SimpleResult::error("Invalid parameter value for: " + key);
    }

    // 检查访问权限
    if (entry.definition.access == ParameterAccess::READ_ONLY) {
        return SimpleResult::error("Parameter is read-only: " + key);
    }

    QVariant oldValue = entry.value.value;
    
    // 更新参数值
    entry.value.value = value;
    entry.value.previousValue = oldValue;
    entry.value.timestamp = QDateTime::currentDateTime();
    entry.value.modifier = modifier;
    entry.value.reason = reason;
    entry.value.isDefault = (value == entry.definition.defaultValue);
    entry.value.isValid = true;

    // 记录变更历史
    recordParameterChange(key, oldValue, value, modifier, reason);

    // 自动保存
    if (m_autoSave) {
        saveParametersToStorage();
    }

    emit parameterChanged(key, oldValue, value);
    return SimpleResult::success(true);
}

QVariant ParameterManager::getParameter(const QString& key, const QVariant& defaultValue) const
{
    QMutexLocker locker(&m_mutex);
    
    auto it = m_parameters.find(key);
    if (it == m_parameters.end()) {
        return defaultValue;
    }

    return it->second.value.value;
}

bool ParameterManager::initializeStorage()
{
    // 创建存储目录
    QDir dir;
    if (!dir.mkpath(m_storageDirectory)) {
        return false;
    }

    m_configFile = getParameterFilePath();
    return true;
}

bool ParameterManager::initializeWatcher()
{
    m_fileWatcher = new QFileSystemWatcher(this);
    connect(m_fileWatcher, &QFileSystemWatcher::fileChanged,
            this, &ParameterManager::onConfigFileChanged);

    // 监控配置文件
    QString configFile = getParameterFilePath();
    if (QFile::exists(configFile)) {
        m_fileWatcher->addPath(configFile);
    }

    return true;
}

void ParameterManager::cleanupStorage()
{
    // 清理存储相关资源
}

void ParameterManager::cleanupWatcher()
{
    if (m_fileWatcher) {
        delete m_fileWatcher;
        m_fileWatcher = nullptr;
    }
}

bool ParameterManager::validateParameterValue(const ParameterDefinition& definition, const QVariant& value) const
{
    // 基本类型检查
    switch (definition.type) {
    case ParameterType::BOOL:
        return value.canConvert<bool>();
    case ParameterType::INT:
        return value.canConvert<int>();
    case ParameterType::DOUBLE:
        return value.canConvert<double>();
    case ParameterType::STRING:
        return value.canConvert<QString>();
    default:
        break;
    }

    // 范围验证
    if (definition.validation.minValue.isValid() && definition.validation.maxValue.isValid()) {
        double val = value.toDouble();
        double min = definition.validation.minValue.toDouble();
        double max = definition.validation.maxValue.toDouble();
        if (val < min || val > max) {
            return false;
        }
    }

    // 允许值列表验证
    if (!definition.validation.allowedValues.isEmpty()) {
        QString strValue = value.toString();
        if (!definition.validation.allowedValues.contains(strValue)) {
            return false;
        }
    }

    // 正则表达式验证
    if (!definition.validation.pattern.isEmpty()) {
        QRegularExpression regex(definition.validation.pattern);
        if (!regex.match(value.toString()).hasMatch()) {
            return false;
        }
    }

    return true;
}

void ParameterManager::recordParameterChange(const QString& key, const QVariant& oldValue, 
                                           const QVariant& newValue, const QString& modifier, 
                                           const QString& reason)
{
    auto it = m_parameters.find(key);
    if (it == m_parameters.end()) {
        return;
    }

    ParameterChangeRecord record;
    record.key = key;
    record.oldValue = oldValue;
    record.newValue = newValue;
    record.modifier = modifier;
    record.reason = reason;
    record.timestamp = QDateTime::currentDateTime();

    auto& history = it->second.history;
    history.append(record);
    
    // 限制历史记录大小
    limitHistory(history, m_maxHistorySize);
}

SimpleResult ParameterManager::loadParametersFromStorage()
{
    QString filePath = getParameterFilePath();
    QFile file(filePath);
    
    if (!file.exists()) {
        return SimpleResult::success(true); // 首次运行，文件不存在是正常的
    }

    if (!file.open(QIODevice::ReadOnly)) {
        return SimpleResult::error("Cannot read parameter file: " + filePath);
    }

    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(file.readAll(), &error);
    file.close();

    if (error.error != QJsonParseError::NoError) {
        return SimpleResult::error("JSON parse error: " + error.errorString());
    }

    // 解析参数数据
    QJsonObject root = doc.object();
    for (auto it = root.begin(); it != root.end(); ++it) {
        QString key = it.key();
        QJsonObject paramObj = it.value().toObject();
        
        // 构造参数定义
        ParameterDefinition definition;
        definition.key = key;
        definition.name = paramObj["name"].toString();
        definition.description = paramObj["description"].toString();
        definition.type = stringToParameterType(paramObj["type"].toString());
        definition.defaultValue = paramObj["defaultValue"].toVariant();
        
        // 构造参数值
        ParameterValue value;
        value.key = key;
        value.value = paramObj["value"].toVariant();
        value.isDefault = paramObj["isDefault"].toBool();
        value.timestamp = QDateTime::fromString(paramObj["timestamp"].toString(), Qt::ISODate);
        
        // 创建参数条目
        ParameterEntry entry(definition);
        entry.value = value;
        
        m_parameters[key] = entry;
    }

    return SimpleResult::success(true);
}

SimpleResult ParameterManager::saveParametersToStorage()
{
    QString filePath = getParameterFilePath();
    QFile file(filePath);
    
    if (!file.open(QIODevice::WriteOnly)) {
        return SimpleResult::error("Cannot write parameter file: " + filePath);
    }

    QJsonObject root;
    for (const auto& pair : m_parameters) {
        const auto& entry = pair.second;
        
        QJsonObject paramObj;
        paramObj["name"] = entry.definition.name;
        paramObj["description"] = entry.definition.description;
        paramObj["type"] = parameterTypeToString(entry.definition.type);
        paramObj["defaultValue"] = QJsonValue::fromVariant(entry.definition.defaultValue);
        paramObj["value"] = QJsonValue::fromVariant(entry.value.value);
        paramObj["isDefault"] = entry.value.isDefault;
        paramObj["timestamp"] = entry.value.timestamp.toString(Qt::ISODate);
        
        root[pair.first] = paramObj;
    }

    QJsonDocument doc(root);
    file.write(doc.toJson());
    file.close();

    return SimpleResult::success(true);
}

QString ParameterManager::getParameterFilePath() const
{
    return m_storageDirectory + "/parameters.json";
}

QString ParameterManager::getDefinitionFilePath() const
{
    return m_storageDirectory + "/definitions.json";
}

QString ParameterManager::getHistoryFilePath() const
{
    return m_storageDirectory + "/history.json";
}

QString ParameterManager::getBackupDirectory() const
{
    return m_storageDirectory + "/backups";
}

void ParameterManager::limitHistory(QList<ParameterChangeRecord>& history, int maxSize) const
{
    while (history.size() > maxSize) {
        history.removeFirst();
    }
}

void ParameterManager::onConfigFileChanged(const QString& filePath)
{
    Q_UNUSED(filePath)
    
    // 重新加载参数（在实际项目中需要更仔细的处理）
    qDebug() << "Parameter config file changed, reloading...";
    loadParametersFromStorage();
}

void ParameterManager::performPeriodicBackup()
{
    QString backupName = QString("auto_backup_%1").arg(QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss"));
    backupParameters(backupName);
}

// 其他接口方法的基础实现...
SimpleResult ParameterManager::setParameters(const QVariantMap& parameters, const QString& modifier, const QString& reason)
{
    QMutexLocker locker(&m_mutex);
    
    for (auto it = parameters.begin(); it != parameters.end(); ++it) {
        auto result = doSetParameter(it.key(), it.value(), modifier, reason);
        if (!result.isSuccess()) {
            return result;
        }
    }
    
    emit parametersChanged(parameters.keys());
    return SimpleResult::success(true);
}

// 其他方法的简化实现...
Result<ParameterValue> ParameterManager::getParameterValue(const QString& key) const
{
    QMutexLocker locker(&m_mutex);
    
    auto it = m_parameters.find(key);
    if (it == m_parameters.end()) {
        return Result<ParameterValue>::error("Parameter not found: " + key);
    }
    
    return Result<ParameterValue>::success(it->second.value);
}

QVariantMap ParameterManager::getParameters(const QStringList& keys) const
{
    QMutexLocker locker(&m_mutex);
    
    QVariantMap result;
    for (const QString& key : keys) {
        auto it = m_parameters.find(key);
        if (it != m_parameters.end()) {
            result[key] = it->second.value.value;
        }
    }
    
    return result;
}

bool ParameterManager::hasParameter(const QString& key) const
{
    QMutexLocker locker(&m_mutex);
    return m_parameters.find(key) != m_parameters.end();
}

SimpleResult ParameterManager::validateParameter(const QString& key, const QVariant& value) const
{
    QMutexLocker locker(&m_mutex);
    
    auto it = m_parameters.find(key);
    if (it == m_parameters.end()) {
        return SimpleResult::error("Parameter not found: " + key);
    }
    
    if (!validateParameterValue(it->second.definition, value)) {
        return SimpleResult::error("Invalid parameter value");
    }
    
    return SimpleResult::success(true);
}

// 占位符实现，实际项目中需要完整实现
Result<QList<ParameterDefinition>> ParameterManager::queryParameters(const ParameterQuery& query) const { return Result<QList<ParameterDefinition>>::error("Not implemented"); }
SimpleResult ParameterManager::resetParameter(const QString& key) { return SimpleResult::error("Not implemented"); }
SimpleResult ParameterManager::resetParameters(const QStringList& keys) { return SimpleResult::error("Not implemented"); }
SimpleResult ParameterManager::removeParameter(const QString& key) { return SimpleResult::error("Not implemented"); }
Result<QList<ParameterChangeRecord>> ParameterManager::getParameterHistory(const QString& key, int limit) const { return Result<QList<ParameterChangeRecord>>::error("Not implemented"); }
ByteArrayResult ParameterManager::exportParameters(const ParameterQuery& query, const QString& format) const { return ByteArrayResult::error("Not implemented"); }
SimpleResult ParameterManager::importParameters(const QByteArray& data, const QString& format, bool overwrite) { return SimpleResult::error("Not implemented"); }
StringResult ParameterManager::backupParameters(const QString& backupName) { return StringResult::error("Not implemented"); }
SimpleResult ParameterManager::restoreParameters(const QString& backupId) { return SimpleResult::error("Not implemented"); }
QStringList ParameterManager::getBackupList() const { return QStringList(); }
SimpleResult ParameterManager::deleteBackup(const QString& backupId) { return SimpleResult::error("Not implemented"); }
SimpleResult ParameterManager::cleanupHistory(const QDateTime& beforeTime) { return SimpleResult::error("Not implemented"); }
SimpleResult ParameterManager::loadParameterDefinitions() { return SimpleResult::error("Not implemented"); }
SimpleResult ParameterManager::saveParameterDefinitions() { return SimpleResult::error("Not implemented"); }

// 工厂实现
std::shared_ptr<IParameterManager> ParameterManagerFactory::createParameterManager(const ConfigParameters& config)
{
    auto manager = std::make_shared<ParameterManager>();
    manager->initialize(config);
    return manager;
}

// 工具函数实现
QString parameterTypeToString(ParameterType type)
{
    switch (type) {
    case ParameterType::INT: return "INT";
    case ParameterType::DOUBLE: return "DOUBLE";
    case ParameterType::STRING: return "STRING";
    case ParameterType::BOOL: return "BOOL";
    case ParameterType::LIST: return "LIST";
    case ParameterType::MAP: return "MAP";
    case ParameterType::BINARY: return "BINARY";
    case ParameterType::DATETIME: return "DATETIME";
    case ParameterType::CUSTOM: return "CUSTOM";
    case ParameterType::UNKNOWN:
    default: return "UNKNOWN";
    }
}

ParameterType stringToParameterType(const QString& typeStr)
{
    QString upper = typeStr.toUpper();
    if (upper == "INT" || upper == "INTEGER") return ParameterType::INT;
    if (upper == "DOUBLE") return ParameterType::DOUBLE;
    if (upper == "STRING") return ParameterType::STRING;
    if (upper == "BOOL" || upper == "BOOLEAN") return ParameterType::BOOL;
    if (upper == "LIST") return ParameterType::LIST;
    if (upper == "MAP") return ParameterType::MAP;
    if (upper == "BINARY") return ParameterType::BINARY;
    if (upper == "DATETIME") return ParameterType::DATETIME;
    if (upper == "CUSTOM") return ParameterType::CUSTOM;
    return ParameterType::UNKNOWN;  // 默认类型
}

}  // namespace Config
}  // namespace Support
}  // namespace LA