#include "FileSync.h"

#include <QFile>
#include <QDir>
#include <QTextStream>
#include <QRegularExpression>
#include <QJsonDocument>
#include <QJsonParseError>
#include <QFileInfo>
#include <QDebug>
#include <QCryptographicHash>

// 静态常量定义
const QStringList FileSync::SUPPORTED_FORMATS = {"json", "h", "hpp", "c", "cpp"};
const QString FileSync::DEFAULT_HEADER_GUARD_PREFIX = "FEATURE_CONFIG";
const QString FileSync::GENERATED_HEADER_COMMENT = 
    "/*\n"
    " * This file is auto-generated by Embedded Feature Manager Plugin\n"
    " * DO NOT EDIT MANUALLY - Changes will be overwritten\n"
    " * Generated on: %1\n"
    " * Source: %2\n"
    " */\n\n";

FileSync::FileSync(QObject *parent)
    : QObject(parent)
    , m_fileWatcher(nullptr)
    , m_syncInProgress(false)
    , m_autoSync(false)
    , m_createBackup(true)
    , m_addTimestamp(true)
    , m_preserveComments(false)
    , m_headerGuardPrefix(DEFAULT_HEADER_GUARD_PREFIX)
    , m_indentSize(4)
    , m_useSpaces(true)
    , m_lineEnding("\n")
{
    // 初始化文件监视器
    m_fileWatcher = new QFileSystemWatcher(this);
    connect(m_fileWatcher, &QFileSystemWatcher::fileChanged,
            this, &FileSync::onFileChanged);
    connect(m_fileWatcher, &QFileSystemWatcher::directoryChanged,
            this, &FileSync::onDirectoryChanged);
    
    // 设置默认同步选项
    m_syncOptions["auto_sync"] = false;
    m_syncOptions["create_backup"] = true;
    m_syncOptions["add_timestamp"] = true;
    m_syncOptions["preserve_comments"] = false;
    
    qDebug() << "FileSync initialized";
}

FileSync::~FileSync()
{
    disableFileWatching();
    qDebug() << "FileSync destroyed";
}

bool FileSync::jsonToHeader(const QJsonObject& configData, const QString& headerPath, 
                           const QString& targetName)
{
    qDebug() << "Converting JSON to header:" << headerPath << "target:" << targetName;
    
    if (!validateJsonConfig(configData)) {
        m_lastError = "Invalid JSON configuration data";
        emit syncError(m_lastError);
        return false;
    }
    
    m_syncInProgress = true;
    
    try {
        // 创建备份
        if (m_createBackup && QFile::exists(headerPath)) {
            createBackup(headerPath);
        }
        
        // 生成头文件内容
        QString headerContent = generateHeaderContent(configData, targetName);
        
        // 写入文件
        if (!writeToFile(headerPath, headerContent)) {
            m_lastError = QString("Failed to write header file: %1").arg(headerPath);
            emit syncError(m_lastError);
            m_syncInProgress = false;
            return false;
        }
        
        // 更新修改时间戳
        QFileInfo fileInfo(headerPath);
        if (fileInfo.exists()) {
            m_headerModified = fileInfo.lastModified();
        }
        
        m_syncInProgress = false;
        emit syncCompleted("json_to_header", true);
        qDebug() << "Successfully converted JSON to header";
        return true;
        
    } catch (const std::exception& e) {
        m_lastError = QString("Exception during JSON to header conversion: %1").arg(e.what());
        emit syncError(m_lastError);
        m_syncInProgress = false;
        return false;
    }
}

bool FileSync::headerToJson(const QString& headerPath, QJsonObject& configData)
{
    qDebug() << "Converting header to JSON:" << headerPath;
    
    if (!QFile::exists(headerPath)) {
        m_lastError = QString("Header file does not exist: %1").arg(headerPath);
        emit syncError(m_lastError);
        return false;
    }
    
    m_syncInProgress = true;
    
    try {
        // 解析头文件
        if (!parseHeaderFile(headerPath, configData)) {
            m_syncInProgress = false;
            return false;
        }
        
        // 更新修改时间戳
        QFileInfo fileInfo(headerPath);
        m_headerModified = fileInfo.lastModified();
        
        m_syncInProgress = false;
        emit syncCompleted("header_to_json", true);
        qDebug() << "Successfully converted header to JSON";
        return true;
        
    } catch (const std::exception& e) {
        m_lastError = QString("Exception during header to JSON conversion: %1").arg(e.what());
        emit syncError(m_lastError);
        m_syncInProgress = false;
        return false;
    }
}

void FileSync::enableFileWatching(const QString& jsonPath, const QString& headerPath)
{
    qDebug() << "Enabling file watching:" << jsonPath << headerPath;
    
    disableFileWatching();
    
    m_jsonPath = jsonPath;
    m_headerPath = headerPath;
    
    // 添加文件到监视器
    if (QFile::exists(jsonPath)) {
        m_fileWatcher->addPath(jsonPath);
        QFileInfo jsonInfo(jsonPath);
        m_jsonModified = jsonInfo.lastModified();
    }
    
    if (QFile::exists(headerPath)) {
        m_fileWatcher->addPath(headerPath);
        QFileInfo headerInfo(headerPath);
        m_headerModified = headerInfo.lastModified();
    }
    
    // 监视目录以便检测新文件
    QString jsonDir = QFileInfo(jsonPath).absolutePath();
    QString headerDir = QFileInfo(headerPath).absolutePath();
    
    if (QDir(jsonDir).exists()) {
        m_fileWatcher->addPath(jsonDir);
    }
    
    if (headerDir != jsonDir && QDir(headerDir).exists()) {
        m_fileWatcher->addPath(headerDir);
    }
}

void FileSync::disableFileWatching()
{
    if (m_fileWatcher) {
        QStringList watchedFiles = m_fileWatcher->files();
        QStringList watchedDirs = m_fileWatcher->directories();
        
        if (!watchedFiles.isEmpty()) {
            m_fileWatcher->removePaths(watchedFiles);
        }
        
        if (!watchedDirs.isEmpty()) {
            m_fileWatcher->removePaths(watchedDirs);
        }
    }
    
    m_jsonPath.clear();
    m_headerPath.clear();
    
    qDebug() << "File watching disabled";
}

QStringList FileSync::checkConflicts(const QString& jsonPath, const QString& headerPath)
{
    QStringList conflicts;
    
    if (!QFile::exists(jsonPath) || !QFile::exists(headerPath)) {
        return conflicts;
    }
    
    QFileInfo jsonInfo(jsonPath);
    QFileInfo headerInfo(headerPath);
    
    QDateTime jsonModified = jsonInfo.lastModified();
    QDateTime headerModified = headerInfo.lastModified();
    
    // 检查是否都比已知的修改时间更新
    bool jsonChanged = (jsonModified > m_jsonModified);
    bool headerChanged = (headerModified > m_headerModified);
    
    if (jsonChanged && headerChanged) {
        conflicts << QString("Both files have been modified externally:");
        conflicts << QString("  JSON: %1").arg(jsonModified.toString());
        conflicts << QString("  Header: %1").arg(headerModified.toString());
        conflicts << "Manual resolution may be required.";
    } else if (jsonChanged) {
        conflicts << QString("JSON file modified externally: %1").arg(jsonModified.toString());
    } else if (headerChanged) {
        conflicts << QString("Header file modified externally: %1").arg(headerModified.toString());
    }
    
    return conflicts;
}

QJsonObject FileSync::mergeConfigs(const QJsonObject& baseConfig, const QJsonObject& newConfig)
{
    QJsonObject result = baseConfig;
    
    // 简单的深度合并逻辑
    for (auto it = newConfig.begin(); it != newConfig.end(); ++it) {
        QString key = it.key();
        QJsonValue newValue = it.value();
        
        if (result.contains(key) && result[key].isObject() && newValue.isObject()) {
            // 递归合并对象
            result[key] = mergeConfigs(result[key].toObject(), newValue.toObject());
        } else {
            // 直接覆盖
            result[key] = newValue;
        }
    }
    
    return result;
}

void FileSync::setSyncOptions(const QJsonObject& options)
{
    m_syncOptions = options;
    
    // 更新内部设置
    m_autoSync = options.value("auto_sync").toBool(false);
    m_createBackup = options.value("create_backup").toBool(true);
    m_addTimestamp = options.value("add_timestamp").toBool(true);
    m_preserveComments = options.value("preserve_comments").toBool(false);
    m_headerGuardPrefix = options.value("header_guard_prefix").toString(DEFAULT_HEADER_GUARD_PREFIX);
    
    m_indentSize = options.value("indent_size").toInt(4);
    m_useSpaces = options.value("use_spaces").toBool(true);
    m_lineEnding = options.value("line_ending").toString("\n");
    
    qDebug() << "Sync options updated";
}

QStringList FileSync::getSupportedFormats() const
{
    return SUPPORTED_FORMATS;
}

void FileSync::performSync(const QString& direction)
{
    if (m_syncInProgress) {
        qWarning() << "Sync already in progress, ignoring request";
        return;
    }
    
    if (m_jsonPath.isEmpty() || m_headerPath.isEmpty()) {
        emit syncError("File paths not configured");
        return;
    }
    
    // 检查冲突
    QStringList conflicts = checkConflicts(m_jsonPath, m_headerPath);
    if (!conflicts.isEmpty()) {
        emit fileConflict(conflicts);
        return;
    }
    
    if (direction == "json_to_header") {
        // 从JSON文件读取配置
        QString jsonContent;
        if (readFromFile(m_jsonPath, jsonContent)) {
            QJsonParseError error;
            QJsonDocument doc = QJsonDocument::fromJson(jsonContent.toUtf8(), &error);
            
            if (error.error == QJsonParseError::NoError && doc.isObject()) {
                jsonToHeader(doc.object(), m_headerPath);
            } else {
                emit syncError(QString("JSON parse error: %1").arg(error.errorString()));
            }
        }
    } else if (direction == "header_to_json") {
        // 从头文件解析并更新JSON
        QJsonObject configData;
        if (headerToJson(m_headerPath, configData)) {
            QJsonDocument doc(configData);
            QString jsonContent = doc.toJson(QJsonDocument::Indented);
            
            if (m_createBackup && QFile::exists(m_jsonPath)) {
                createBackup(m_jsonPath);
            }
            
            writeToFile(m_jsonPath, jsonContent);
        }
    } else {
        emit syncError(QString("Unknown sync direction: %1").arg(direction));
    }
}

void FileSync::onFileChanged(const QString& path)
{
    if (m_syncInProgress) {
        return; // 避免循环同步
    }
    
    qDebug() << "File changed:" << path;
    emit fileChanged(path);
    
    if (!m_autoSync) {
        return;
    }
    
    // 自动同步逻辑
    if (path == m_jsonPath) {
        performSync("json_to_header");
    } else if (path == m_headerPath) {
        performSync("header_to_json");
    }
}

void FileSync::onDirectoryChanged(const QString& path)
{
    qDebug() << "Directory changed:" << path;
    
    // 检查监视的文件是否被重新创建
    if (!m_jsonPath.isEmpty() && !m_fileWatcher->files().contains(m_jsonPath)) {
        if (QFile::exists(m_jsonPath)) {
            m_fileWatcher->addPath(m_jsonPath);
        }
    }
    
    if (!m_headerPath.isEmpty() && !m_fileWatcher->files().contains(m_headerPath)) {
        if (QFile::exists(m_headerPath)) {
            m_fileWatcher->addPath(m_headerPath);
        }
    }
}

QString FileSync::generateHeaderContent(const QJsonObject& configData, const QString& targetName)
{
    QString content;
    QTextStream stream(&content);
    
    // 选择目标
    QString selectedTarget = targetName;
    if (selectedTarget.isEmpty()) {
        QJsonArray targets = configData.value("targets").toArray();
        if (!targets.isEmpty()) {
            selectedTarget = targets.first().toObject().value("target_name").toString();
        }
    }
    
    QString productId = configData.value("product_id").toString("UNKNOWN_PRODUCT");
    QString filename = QFileInfo(m_headerPath).fileName();
    
    // 生成文件头注释
    if (m_addTimestamp) {
        stream << QString(GENERATED_HEADER_COMMENT)
                   .arg(QDateTime::currentDateTime().toString())
                   .arg(productId);
    }
    
    // 生成头文件保护符
    QString headerGuard = generateHeaderGuard(filename);
    stream << QString("#ifndef %1\n").arg(headerGuard);
    stream << QString("#define %1\n\n").arg(headerGuard);
    
    // 产品信息宏
    stream << "/* Product Configuration */\n";
    stream << QString("#define PRODUCT_ID \"%1\"\n").arg(productId);
    stream << QString("#define PRODUCT_VERSION \"%1\"\n").arg(configData.value("version").toString("1.0.0"));
    stream << QString("#define TARGET_NAME \"%1\"\n").arg(selectedTarget);
    stream << "\n";
    
    // 功能宏定义
    stream << "/* Feature Configuration */\n";
    QJsonArray features = configData.value("features").toArray();
    QString featureDefines = generateFeatureDefines(features, selectedTarget);
    stream << featureDefines;
    
    // 结束头文件保护符
    stream << QString("\n#endif // %1\n").arg(headerGuard);
    
    return content;
}

QString FileSync::generateHeaderGuard(const QString& filename)
{
    QString guard = m_headerGuardPrefix + "_" + 
                   filename.toUpper().replace(".", "_").replace("-", "_");
    
    // 移除非字母数字字符
    guard.remove(QRegularExpression("[^A-Z0-9_]"));
    
    return guard + "_H";
}

QString FileSync::generateFeatureDefines(const QJsonArray& features, const QString& targetName)
{
    QStringList defines;
    processFeatureArray(features, targetName, defines);
    
    return defines.join("\n") + "\n";
}

void FileSync::processFeatureArray(const QJsonArray& features, const QString& targetName,
                                  QStringList& defines, const QString& prefix)
{
    for (const auto& featureValue : features) {
        QJsonObject feature = featureValue.toObject();
        processFeatureObject(feature, targetName, defines, prefix);
    }
}

void FileSync::processFeatureObject(const QJsonObject& feature, const QString& targetName,
                                   QStringList& defines, const QString& prefix)
{
    QString featureId = feature.value("id").toString();
    if (featureId.isEmpty()) {
        return;
    }
    
    // 生成功能启用宏
    bool enabled = feature.value("enabled").toBool(true);
    QString enableDefine = generateDefineName(prefix + featureId, "ENABLED");
    defines << generateDefineStatement(enableDefine, enabled ? 1 : 0);
    
    // 生成目标特定值宏
    QJsonObject perTarget = feature.value("per_target").toObject();
    if (perTarget.contains(targetName)) {
        QJsonValue targetValue = perTarget.value(targetName);
        QString valueDefine = generateDefineName(prefix + featureId);
        defines << generateDefineStatement(valueDefine, targetValue);
    }
    
    // 处理其他属性
    QJsonObject::const_iterator it;
    for (it = feature.begin(); it != feature.end(); ++it) {
        QString key = it.key();
        QJsonValue value = it.value();
        
        if (key == "id" || key == "enabled" || key == "per_target" || key == "children") {
            continue;
        }
        
        if (!value.isNull() && !value.isUndefined()) {
            QString propDefine = generateDefineName(prefix + featureId, key.toUpper());
            defines << generateDefineStatement(propDefine, value);
        }
    }
    
    // 递归处理子功能项
    QJsonArray children = feature.value("children").toArray();
    if (!children.isEmpty()) {
        QString childPrefix = prefix + featureId + "_";
        processFeatureArray(children, targetName, defines, childPrefix);
    }
}

QString FileSync::generateDefineStatement(const QString& name, const QJsonValue& value)
{
    QString valueStr = jsonValueToString(value);
    return QString("#define %1 %2").arg(name).arg(valueStr);
}

QString FileSync::jsonValueToString(const QJsonValue& value)
{
    if (value.isBool()) {
        return value.toBool() ? "1" : "0";
    } else if (value.isDouble()) {
        return QString::number(value.toDouble());
    } else if (value.isString()) {
        return QString("\"%1\"").arg(value.toString());
    } else {
        return "0";
    }
}

QString FileSync::generateDefineName(const QString& featureId, const QString& property)
{
    QString name = featureId.toUpper();
    if (!property.isEmpty()) {
        name += "_" + property;
    }
    
    return sanitizeDefineName(name);
}

QString FileSync::sanitizeDefineName(const QString& name)
{
    QString result = name;
    result.replace(QRegularExpression("[^A-Z0-9_]"), "_");
    result.replace(QRegularExpression("_+"), "_");
    
    // 确保不以数字开头
    if (!result.isEmpty() && result[0].isDigit()) {
        result.prepend("FEATURE_");
    }
    
    return result;
}

bool FileSync::parseHeaderFile(const QString& filePath, QJsonObject& configData)
{
    QString content;
    if (!readFromFile(filePath, content)) {
        m_lastError = QString("Failed to read header file: %1").arg(filePath);
        return false;
    }
    
    if (!validateHeaderFormat(content)) {
        m_lastError = "Invalid header file format";
        return false;
    }
    
    // 提取宏定义
    QStringList defines = extractDefines(content);
    
    // 创建基本配置结构
    configData = QJsonObject();
    configData["product_id"] = "Imported_Product";
    configData["version"] = "1.0.0";
    
    // 解析宏定义并构建功能配置
    QJsonArray features;
    QJsonObject currentFeature;
    
    for (const QString& define : defines) {
        QPair<QString, QJsonValue> parsed = parseDefine(define);
        QString macroName = parsed.first;
        QJsonValue macroValue = parsed.second;
        
        if (macroName.isEmpty()) {
            continue;
        }
        
        // 检查是否是产品级宏
        if (macroName == "PRODUCT_ID") {
            configData["product_id"] = macroValue.toString();
            continue;
        } else if (macroName == "PRODUCT_VERSION") {
            configData["version"] = macroValue.toString();
            continue;
        }
        
        // 解析功能宏（这里使用简化的解析逻辑）
        // TODO: 实现更智能的宏名称解析，构建完整的功能树
        QJsonObject feature;
        feature["id"] = macroName.toLower();
        feature["name"] = macroName;
        feature["type"] = macroValue.isBool() ? "bool" : (macroValue.isDouble() ? "number" : "string");
        
        QJsonObject perTarget;
        perTarget["DEFAULT_TARGET"] = macroValue;
        feature["per_target"] = perTarget;
        
        features.append(feature);
    }
    
    configData["features"] = features;
    
    // 添加默认目标配置
    QJsonArray targets;
    QJsonObject defaultTarget;
    defaultTarget["target_name"] = "DEFAULT_TARGET";
    defaultTarget["defines_file"] = QFileInfo(filePath).fileName();
    targets.append(defaultTarget);
    configData["targets"] = targets;
    
    return true;
}

QStringList FileSync::extractDefines(const QString& content)
{
    QStringList defines;
    
    QRegularExpression defineRegex(R"(^\s*#define\s+(\w+)\s+(.+)$)", 
                                   QRegularExpression::MultilineOption);
    
    QRegularExpressionMatchIterator iterator = defineRegex.globalMatch(content);
    while (iterator.hasNext()) {
        QRegularExpressionMatch match = iterator.next();
        defines << match.captured(0).trimmed();
    }
    
    return defines;
}

QPair<QString, QJsonValue> FileSync::parseDefine(const QString& line)
{
    QRegularExpression defineRegex(R"(^\s*#define\s+(\w+)\s+(.+)$)");
    QRegularExpressionMatch match = defineRegex.match(line);
    
    if (!match.hasMatch()) {
        return qMakePair(QString(), QJsonValue());
    }
    
    QString name = match.captured(1);
    QString valueStr = match.captured(2).trimmed();
    
    QJsonValue value = stringToJsonValue(valueStr);
    
    return qMakePair(name, value);
}

QJsonValue FileSync::stringToJsonValue(const QString& str)
{
    QString trimmed = str.trimmed();
    
    // 布尔值
    if (trimmed == "0") {
        return false;
    } else if (trimmed == "1") {
        return true;
    }
    
    // 字符串（带引号）
    if (trimmed.startsWith('"') && trimmed.endsWith('"')) {
        return trimmed.mid(1, trimmed.length() - 2);
    }
    
    // 数字
    bool ok;
    double number = trimmed.toDouble(&ok);
    if (ok) {
        return number;
    }
    
    // 默认作为字符串处理
    return trimmed;
}

bool FileSync::writeToFile(const QString& filePath, const QString& content)
{
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        qWarning() << "Cannot open file for writing:" << filePath;
        return false;
    }
    
    QTextStream stream(&file);
    stream.setCodec("UTF-8");
    stream << content;
    file.close();
    
    return true;
}

bool FileSync::readFromFile(const QString& filePath, QString& content)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        qWarning() << "Cannot open file for reading:" << filePath;
        return false;
    }
    
    QTextStream stream(&file);
    stream.setCodec("UTF-8");
    content = stream.readAll();
    file.close();
    
    return true;
}

bool FileSync::createBackup(const QString& filePath)
{
    if (!QFile::exists(filePath)) {
        return false;
    }
    
    QString backupPath = filePath + ".backup." + 
                        QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss");
    
    bool success = QFile::copy(filePath, backupPath);
    if (success) {
        qDebug() << "Backup created:" << backupPath;
    } else {
        qWarning() << "Failed to create backup:" << backupPath;
    }
    
    return success;
}

bool FileSync::validateJsonConfig(const QJsonObject& config)
{
    // 基本结构验证
    if (!config.contains("product_id") || !config.contains("features")) {
        return false;
    }
    
    if (config.value("product_id").toString().isEmpty()) {
        return false;
    }
    
    if (!config.value("features").isArray()) {
        return false;
    }
    
    return true;
}

bool FileSync::validateHeaderFormat(const QString& content)
{
    // 简单验证：检查是否包含宏定义
    return content.contains(QRegularExpression(R"(^\s*#define\s+\w+)", 
                                               QRegularExpression::MultilineOption));
}