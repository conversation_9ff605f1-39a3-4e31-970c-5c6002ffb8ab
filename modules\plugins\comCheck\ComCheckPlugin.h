#pragma once

#include <LA/Plugins/BasePlugin.h>
#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QTextEdit>
#include <QPushButton>
#include <QLineEdit>
#include <QCheckBox>

// Linus: "Use shared UI components instead of Qt basics" (暂时注释直到依赖解决)
// #include <LA/UI/DeviceCommunication/widgets/DevicePortWidget.h>
#include <LA/Themes/ThemeManager.h>

// 保留基础Qt串口功能用于通信测试
#include <QSerialPort>
#include <QSerialPortInfo>

namespace LA {
namespace Plugins {
namespace ComCheck {

/**
 * @brief ComCheck插件主类
 * 
 * 提供串口通信检查和测试功能，集成到LA插件系统中
 */
class ComCheckPlugin : public BaseFunctionPlugin {
    Q_OBJECT
    Q_PLUGIN_METADATA(IID "com.LA.IPlugin/1.0" FILE "plugin.json")

public:
    explicit ComCheckPlugin(QObject* parent = nullptr);
    virtual ~ComCheckPlugin() = default;

    // IPlugin接口实现
    QString name() const override { return "ComCheck"; }
    QString version() const override { return "1.0.0"; }
    QString description() const override { return "Communication check and testing plugin"; }
    QString author() const override { return "LA Development Team"; }
    QString id() const override { return "com.la.plugins.comcheck"; }
    QStringList dependencies() const override { return QStringList(); }
    QStringList optionalDependencies() const override { return QStringList(); }
    
    bool initialize() override;
    bool start() override { return true; }
    void stop() override {}
    void shutdown() override;
    
    // 状态查询接口
    PluginState getState() const override { return PluginState::Running; }
    bool isInitialized() const override { return true; }
    bool isRunning() const override { return true; }
    bool isEnabled() const override { return true; }
    
    // 其他必需的IPlugin接口方法
    PluginSourceType sourceType() const override { return PluginSourceType::DynamicPlugin; }
    PluginPriority priority() const override { return PluginPriority::Normal; }
    PluginMetadata metadata() const override { return PluginMetadata(); }
    QList<PluginPermission> requiredPermissions() const override { return QList<PluginPermission>(); }
    bool hasPermission(PluginPermission) const override { return true; }
    QVariantMap getConfiguration() const override { return QVariantMap(); }
    bool setConfiguration(const QVariantMap&) override { return true; }
    qint64 getMemoryUsage() const override { return 0; }
    double getCpuUsage() const override { return 0.0; }
    qint64 getLoadTime() const override { return 0; }
    
    // IFunctionPlugin接口实现
    QString getCategory() const override { return "Communication"; }
    QIcon getIcon() const override { return QIcon(); }
    bool supportsFloating() const override { return true; }
    QSize getMinimumSize() const override { return QSize(400, 300); }
    QSize getPreferredSize() const override { return QSize(600, 500); }
    QStringList getSupportedThemes() const override { return {"Default", "Dark"}; }

protected:
    // BaseFunctionPlugin接口实现
    QWidget* doCreateWidget(QWidget* parent = nullptr) override;

public:
    QWidget* createSidebarPanel(QWidget* parent = nullptr);
    
private slots:
    void onSendData();
    void onReceiveData(const QByteArray& data);
    void onDevicePortConnected(const QString& deviceId, const QString& portName);
    void onDevicePortDisconnected(const QString& deviceId, const QString& portName);

private:
    void setupComCheckSpecificUI();
    void applyTheme();
    void appendLog(const QString& message, const QString& prefix = "");
    
    // 主程序UI组件 - Linus: "Reuse, don't reinvent" (暂时注释直到依赖解决)
    QWidget* m_mainWidget = nullptr;
    // LA::UI::DeviceCommunication::DevicePortWidget* m_devicePortWidget = nullptr;
    
    // ComCheck特有UI组件
    QLineEdit* m_sendLineEdit = nullptr;
    QPushButton* m_sendButton = nullptr;
    QCheckBox* m_hexModeCheckBox = nullptr;
    QCheckBox* m_deviceCmdModeCheckBox = nullptr;  // 设备命令模式
    QTextEdit* m_logTextEdit = nullptr;
    
    // Linus: "Keep simple serial communication for testing"
    QSerialPort* m_serialPort = nullptr;
    QString m_currentPortName;
    bool m_isConnected = false;
};

} // namespace ComCheck
} // namespace Plugins
} // namespace LA