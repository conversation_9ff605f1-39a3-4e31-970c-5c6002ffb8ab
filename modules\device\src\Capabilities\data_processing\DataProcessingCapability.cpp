/**
 * @file DataProcessingCapability.cpp
 * @brief 数据处理能力适配器实现 - 设备层与data sharing模块的桥接
 */

#include "DataProcessingCapability.h"
#include <QDebug>
#include <QDateTime>
#include <QJsonDocument>
#include <QJsonObject>

namespace LA::Device::Capability {

DataProcessingCapability::DataProcessingCapability(QObject* parent)
    : IDeviceCapability(parent)
    , m_initialized(false)
    , m_streamProcessingActive(false)
    , m_dataProcessedCount(0)
    , m_dataValidatedCount(0)
    , m_dataSharedCount(0)
    , m_dataReceivedCount(0)
    , m_errorCount(0)
    , m_streamTimer(nullptr)
    , m_cacheCleanupTimer(nullptr)
{
    // 初始化定时器
    m_streamTimer = new QTimer(this);
    connect(m_streamTimer, &QTimer::timeout, this, &DataProcessingCapability::processDataStreamBatch);
    
    m_cacheCleanupTimer = new QTimer(this);
    connect(m_cacheCleanupTimer, &QTimer::timeout, this, &DataProcessingCapability::cleanupExpiredCache);
    m_cacheCleanupTimer->start(300000); // 5分钟清理一次过期缓存
}

DataProcessingCapability::~DataProcessingCapability()
{
    shutdown();
}

bool DataProcessingCapability::initialize(const QVariantMap& config)
{
    if (m_initialized) {
        qWarning() << "DataProcessingCapability already initialized";
        return true;
    }
    
    m_processingConfig = config;
    
    // 验证基础设施服务
    if (!validateInfrastructureServices()) {
        qCritical() << "Failed to validate infrastructure services";
        return false;
    }
    
    // 初始化默认规则
    initializeDefaultProcessingRules();
    initializeDefaultValidationRules();
    
    // 初始化数据共享系统连接
    if (m_dataSharingSystem) {
        // 连接数据共享系统的信号
        // 注意：这里需要根据实际的IDataSharingSystem接口调整
        // connect(m_dataSharingSystem.get(), &IDataSharingSystem::dataReceived, 
        //         this, &DataProcessingCapability::onSharedDataReceived);
    }
    
    m_initialized = true;
    qInfo() << "DataProcessingCapability initialized successfully";
    return true;
}

QVariantMap DataProcessingCapability::executeCapability(const QString& action, const QVariantMap& params)
{
    if (!m_initialized) {
        return createErrorResult("DataProcessingCapability not initialized");
    }
    
    if (action == "process_data") {
        return processDeviceData(params.value("device_data").toMap(), 
                                params.value("processing_rules").toStringList());
    }
    else if (action == "validate_data") {
        return validateDeviceData(params.value("device_data").toMap(), 
                                 params.value("validation_rules").toStringList());
    }
    else if (action == "cache_data") {
        bool success = cacheDeviceData(
            params.value("device_id").toString(),
            params.value("data").toMap(),
            params.value("cache_key", "default").toString()
        );
        return success ? createSuccessResult() : createErrorResult("Failed to cache data");
    }
    else if (action == "get_cached_data") {
        QVariantMap data = getCachedData(
            params.value("device_id").toString(),
            params.value("cache_key", "default").toString()
        );
        return createSuccessResult(data);
    }
    else if (action == "share_data") {
        bool success = shareData(
            params.value("data_type").toString(),
            params.value("data").toMap(),
            params.value("metadata").toMap()
        );
        return success ? createSuccessResult() : createErrorResult("Failed to share data");
    }
    else if (action == "start_stream") {
        bool success = startDataStreamProcessing(params);
        return success ? createSuccessResult() : createErrorResult("Failed to start data stream");
    }
    else if (action == "stop_stream") {
        bool success = stopDataStreamProcessing();
        return success ? createSuccessResult() : createErrorResult("Failed to stop data stream");
    }
    else {
        return createErrorResult("Unknown action: " + action);
    }
}

void DataProcessingCapability::shutdown()
{
    if (!m_initialized) {
        return;
    }
    
    // 停止数据流处理
    stopDataStreamProcessing();
    
    // 停止定时器
    if (m_streamTimer) {
        m_streamTimer->stop();
    }
    if (m_cacheCleanupTimer) {
        m_cacheCleanupTimer->stop();
    }
    
    // 清理缓存
    m_dataCache.clear();
    m_streamDataQueue.clear();
    
    // 断开基础设施服务连接
    m_dataSharingSystem.reset();
    
    m_initialized = false;
    qInfo() << "DataProcessingCapability shutdown completed";
}

void DataProcessingCapability::setInfrastructureServices(const QVariantMap& services)
{
    // 注入数据共享系统服务
    if (services.contains("data_sharing_system")) {
        auto serviceVariant = services["data_sharing_system"];
        // 这里需要根据实际的服务注入方式调整
        // m_dataSharingSystem = serviceVariant.value<std::shared_ptr<IDataSharingSystem>>();
        qInfo() << "Data sharing system service injected";
    }
    
    // 注入算法服务
    if (services.contains("algorithm_service")) {
        m_algorithmService = services["algorithm_service"];
        qInfo() << "Algorithm service injected";
    }
}

QVariantMap DataProcessingCapability::processDeviceData(const QVariantMap& deviceData, const QStringList& processingRules)
{
    if (!m_initialized) {
        return createErrorResult("DataProcessingCapability not initialized");
    }
    
    QVariantMap originalData = deviceData;
    QDateTime startTime = QDateTime::currentDateTime();
    
    try {
        // 选择要应用的处理规则
        QVector<DataProcessingRule> rulesToApply;
        if (processingRules.isEmpty()) {
            rulesToApply = m_processingRules; // 使用所有默认规则
        } else {
            // 根据规则名称过滤
            for (const DataProcessingRule& rule : m_processingRules) {
                if (processingRules.contains(rule.ruleId) && rule.enabled) {
                    rulesToApply.append(rule);
                }
            }
        }
        
        // 应用处理规则
        QVariantMap processedData = applyProcessingRules(originalData, rulesToApply);
        
        // 构建处理结果
        QVariantMap result = createSuccessResult();
        result["original_data"] = originalData;
        result["processed_data"] = processedData;
        result["applied_rules"] = QVariantList(); // 转换规则列表
        result["processing_time_ms"] = startTime.msecsTo(QDateTime::currentDateTime());
        
        // 发出信号
        emit dataProcessed("", originalData, processedData);
        
        // 更新统计
        updateStatistics("process_data", true);
        
        return result;
        
    } catch (const std::exception& e) {
        QString error = QString("Data processing failed: %1").arg(e.what());
        updateStatistics("process_data", false);
        return createErrorResult(error);
    }
}

QVariantMap DataProcessingCapability::validateDeviceData(const QVariantMap& deviceData, const QStringList& validationRules)
{
    if (!m_initialized) {
        return createErrorResult("DataProcessingCapability not initialized");
    }
    
    try {
        // 选择要应用的验证规则
        QVector<DataValidationRule> rulesToApply;
        if (validationRules.isEmpty()) {
            rulesToApply = m_validationRules; // 使用所有默认规则
        } else {
            // 根据规则名称过滤
            for (const DataValidationRule& rule : m_validationRules) {
                if (validationRules.contains(rule.fieldName)) {
                    rulesToApply.append(rule);
                }
            }
        }
        
        // 应用验证规则
        QVariantMap validationResult = applyValidationRules(deviceData, rulesToApply);
        
        bool isValid = validationResult.value("is_valid", false).toBool();
        
        // 发出信号
        if (isValid) {
            emit dataValidated("", deviceData, true);
        } else {
            emit dataValidationFailed("", deviceData, validationResult.value("error").toString());
        }
        
        // 更新统计
        updateStatistics("validate_data", isValid);
        
        return validationResult;
        
    } catch (const std::exception& e) {
        QString error = QString("Data validation failed: %1").arg(e.what());
        updateStatistics("validate_data", false);
        return createErrorResult(error);
    }
}

bool DataProcessingCapability::cacheDeviceData(const QString& deviceId, const QVariantMap& data, const QString& cacheKey)
{
    if (deviceId.isEmpty() || data.isEmpty()) {
        qWarning() << "Invalid cache parameters";
        return false;
    }
    
    // 添加时间戳到缓存数据
    QVariantMap cacheData = data;
    cacheData["cache_timestamp"] = QDateTime::currentDateTime();
    
    m_dataCache[deviceId][cacheKey] = cacheData;
    
    qDebug() << "Data cached for device:" << deviceId << "key:" << cacheKey;
    return true;
}

QVariantMap DataProcessingCapability::getCachedData(const QString& deviceId, const QString& cacheKey)
{
    if (m_dataCache.contains(deviceId) && m_dataCache[deviceId].contains(cacheKey)) {
        return m_dataCache[deviceId][cacheKey];
    }
    
    return QVariantMap(); // 返回空数据表示未找到
}

bool DataProcessingCapability::shareData(const QString& dataType, const QVariantMap& data, const QVariantMap& metadata)
{
    if (!m_dataSharingSystem) {
        qWarning() << "Data sharing system not available";
        return false;
    }
    
    try {
        // 准备要共享的数据
        QVariantMap sharedData = data;
        sharedData["shared_timestamp"] = QDateTime::currentDateTime();
        if (!metadata.isEmpty()) {
            sharedData["metadata"] = metadata;
        }
        
        // 通过数据共享系统共享数据
        // 注意：这里需要根据实际的IDataSharingSystem接口调整
        // bool success = m_dataSharingSystem->shareData(dataType, sharedData);
        
        // 临时实现 - 直接发出信号
        emit dataShared(dataType, sharedData);
        
        // 更新统计
        updateStatistics("share_data", true);
        m_dataSharedCount++;
        
        qDebug() << "Data shared successfully, type:" << dataType;
        return true;
        
    } catch (const std::exception& e) {
        qCritical() << "Failed to share data:" << e.what();
        updateStatistics("share_data", false);
        return false;
    }
}

bool DataProcessingCapability::subscribeToSharedData(const QString& dataType, const QVariantMap& filter)
{
    if (!m_dataSharingSystem) {
        qWarning() << "Data sharing system not available";
        return false;
    }
    
    try {
        // 通过数据共享系统订阅数据
        // 注意：这里需要根据实际的IDataSharingSystem接口调整
        // bool success = m_dataSharingSystem->subscribeToData(dataType, filter);
        
        qInfo() << "Subscribed to shared data type:" << dataType;
        return true;
        
    } catch (const std::exception& e) {
        qCritical() << "Failed to subscribe to shared data:" << e.what();
        return false;
    }
}

bool DataProcessingCapability::startDataStreamProcessing(const QVariantMap& streamConfig)
{
    if (m_streamProcessingActive) {
        qWarning() << "Data stream processing already active";
        return true;
    }
    
    m_currentStreamId = streamConfig.value("stream_id", "default_stream").toString();
    int intervalMs = streamConfig.value("interval_ms", 100).toInt();
    
    m_streamTimer->start(intervalMs);
    m_streamProcessingActive = true;
    
    emit dataStreamStarted(m_currentStreamId);
    qInfo() << "Data stream processing started, ID:" << m_currentStreamId;
    
    return true;
}

bool DataProcessingCapability::stopDataStreamProcessing()
{
    if (!m_streamProcessingActive) {
        return true;
    }
    
    m_streamTimer->stop();
    m_streamProcessingActive = false;
    
    emit dataStreamStopped(m_currentStreamId);
    qInfo() << "Data stream processing stopped, ID:" << m_currentStreamId;
    
    // 清理流数据队列
    m_streamDataQueue.clear();
    m_currentStreamId.clear();
    
    return true;
}

QVariantMap DataProcessingCapability::processDataStream(const QVariantMap& streamData)
{
    if (!m_streamProcessingActive) {
        return createErrorResult("Data stream processing not active");
    }
    
    // 将数据添加到队列
    m_streamDataQueue.enqueue(streamData);
    
    // 如果队列过大，移除旧数据
    while (m_streamDataQueue.size() > 1000) {
        m_streamDataQueue.dequeue();
    }
    
    return createSuccessResult();
}

void DataProcessingCapability::onSharedDataReceived(const QString& dataType, const QVariantMap& data)
{
    m_dataReceivedCount++;
    emit sharedDataReceived(dataType, data);
    qDebug() << "Shared data received, type:" << dataType;
}

void DataProcessingCapability::onDataSharingError(const QString& error)
{
    m_errorCount++;
    qCritical() << "Data sharing error:" << error;
}

void DataProcessingCapability::onStreamDataReceived()
{
    // 处理接收到的流数据
    processDataStreamBatch();
}

void DataProcessingCapability::processDataStreamBatch()
{
    if (!m_streamProcessingActive || m_streamDataQueue.isEmpty()) {
        return;
    }
    
    // 批量处理队列中的数据
    QVariantList batchData;
    int batchSize = qMin(10, m_streamDataQueue.size());
    
    for (int i = 0; i < batchSize; ++i) {
        if (!m_streamDataQueue.isEmpty()) {
            batchData.append(m_streamDataQueue.dequeue());
        }
    }
    
    if (!batchData.isEmpty()) {
        QVariantMap processedBatch;
        processedBatch["batch_data"] = batchData;
        processedBatch["batch_size"] = batchData.size();
        processedBatch["timestamp"] = QDateTime::currentDateTime();
        
        emit dataStreamProcessed(m_currentStreamId, processedBatch);
    }
}

bool DataProcessingCapability::validateInfrastructureServices() const
{
    // 验证数据共享系统
    if (!m_dataSharingSystem) {
        qWarning() << "Data sharing system service not available";
        // 注意：在实际使用中，这可能不是强制要求
        // return false;
    }
    
    // 验证算法服务
    if (m_algorithmService.isNull()) {
        qWarning() << "Algorithm service not available";
        // 注意：在实际使用中，这可能不是强制要求
        // return false;
    }
    
    return true; // 暂时允许部分服务缺失
}

void DataProcessingCapability::initializeDefaultProcessingRules()
{
    // 数据格式化规则
    DataProcessingRule formatRule;
    formatRule.ruleId = "format_numeric";
    formatRule.ruleName = "数值格式化";
    formatRule.dataField = "distance";
    formatRule.operation = "format_decimal";
    formatRule.parameters["decimal_places"] = 2;
    formatRule.enabled = true;
    m_processingRules.append(formatRule);
    
    // 数据平滑规则
    DataProcessingRule smoothRule;
    smoothRule.ruleId = "smooth_data";
    smoothRule.ruleName = "数据平滑";
    smoothRule.dataField = "distance";
    smoothRule.operation = "moving_average";
    smoothRule.parameters["window_size"] = 3;
    smoothRule.enabled = true;
    m_processingRules.append(smoothRule);
    
    // 异常值过滤规则
    DataProcessingRule outlierRule;
    outlierRule.ruleId = "filter_outliers";
    outlierRule.ruleName = "异常值过滤";
    outlierRule.dataField = "distance";
    outlierRule.operation = "outlier_filter";
    outlierRule.parameters["threshold"] = 3.0; // 3倍标准差
    outlierRule.enabled = true;
    m_processingRules.append(outlierRule);
}

void DataProcessingCapability::initializeDefaultValidationRules()
{
    // 距离值范围验证
    DataValidationRule rangeRule;
    rangeRule.fieldName = "distance";
    rangeRule.validationType = "range";
    rangeRule.constraints["min"] = 0.0;
    rangeRule.constraints["max"] = 10000.0; // 10米最大测距
    rangeRule.errorMessage = "Distance value out of valid range (0-10000mm)";
    rangeRule.required = true;
    m_validationRules.append(rangeRule);
    
    // 置信度验证
    DataValidationRule confidenceRule;
    confidenceRule.fieldName = "confidence";
    confidenceRule.validationType = "range";
    confidenceRule.constraints["min"] = 0.0;
    confidenceRule.constraints["max"] = 100.0;
    confidenceRule.errorMessage = "Confidence value must be between 0-100%";
    confidenceRule.required = false;
    m_validationRules.append(confidenceRule);
    
    // 时间戳验证
    DataValidationRule timestampRule;
    timestampRule.fieldName = "timestamp";
    timestampRule.validationType = "required";
    timestampRule.errorMessage = "Timestamp is required";
    timestampRule.required = true;
    m_validationRules.append(timestampRule);
}

QVariantMap DataProcessingCapability::applyProcessingRules(const QVariantMap& data, const QVector<DataProcessingRule>& rules)
{
    QVariantMap processedData = data;
    
    for (const DataProcessingRule& rule : rules) {
        if (!rule.enabled || !processedData.contains(rule.dataField)) {
            continue;
        }
        
        QVariant fieldValue = processedData[rule.dataField];
        
        // 根据操作类型处理数据
        if (rule.operation == "format_decimal") {
            int decimals = rule.parameters.value("decimal_places", 2).toInt();
            double value = fieldValue.toDouble();
            processedData[rule.dataField] = QString::number(value, 'f', decimals);
        }
        else if (rule.operation == "moving_average") {
            // 这里可以集成infrastructure/algorithm的移动平均算法
            // 暂时使用简单实现
            double value = fieldValue.toDouble();
            processedData[rule.dataField] = value; // 保持原值，实际应该调用算法服务
        }
        else if (rule.operation == "outlier_filter") {
            // 这里可以集成infrastructure/algorithm的异常值检测算法
            double value = fieldValue.toDouble();
            double threshold = rule.parameters.value("threshold", 3.0).toDouble();
            // 简单实现：如果值在合理范围内则保持，否则标记为异常
            processedData[rule.dataField] = value;
        }
    }
    
    return processedData;
}

QVariantMap DataProcessingCapability::applyValidationRules(const QVariantMap& data, const QVector<DataValidationRule>& rules)
{
    QVariantMap result = createSuccessResult();
    QStringList errors;
    bool isValid = true;
    
    for (const DataValidationRule& rule : rules) {
        if (rule.validationType == "range") {
            if (!data.contains(rule.fieldName)) {
                if (rule.required) {
                    errors.append(QString("Required field '%1' is missing").arg(rule.fieldName));
                    isValid = false;
                }
                continue;
            }
            
            double value = data[rule.fieldName].toDouble();
            double minVal = rule.constraints.value("min", -std::numeric_limits<double>::max()).toDouble();
            double maxVal = rule.constraints.value("max", std::numeric_limits<double>::max()).toDouble();
            
            if (value < minVal || value > maxVal) {
                errors.append(rule.errorMessage);
                isValid = false;
            }
        }
        else if (rule.validationType == "required") {
            if (!data.contains(rule.fieldName) || data[rule.fieldName].toString().isEmpty()) {
                errors.append(rule.errorMessage);
                isValid = false;
            }
        }
    }
    
    result["is_valid"] = isValid;
    result["validation_errors"] = errors;
    result["validated_data"] = data;
    
    return result;
}

void DataProcessingCapability::cleanupExpiredCache()
{
    QDateTime cutoffTime = QDateTime::currentDateTime().addSecs(-3600); // 1小时过期
    
    for (auto deviceIt = m_dataCache.begin(); deviceIt != m_dataCache.end();) {
        auto& deviceCache = deviceIt.value();
        
        for (auto cacheIt = deviceCache.begin(); cacheIt != deviceCache.end();) {
            QDateTime cacheTime = cacheIt.value().value("cache_timestamp").toDateTime();
            if (cacheTime < cutoffTime) {
                cacheIt = deviceCache.erase(cacheIt);
            } else {
                ++cacheIt;
            }
        }
        
        if (deviceCache.isEmpty()) {
            deviceIt = m_dataCache.erase(deviceIt);
        } else {
            ++deviceIt;
        }
    }
}

QVariantMap DataProcessingCapability::createErrorResult(const QString& error) const
{
    QVariantMap result;
    result["success"] = false;
    result["error"] = error;
    result["timestamp"] = QDateTime::currentDateTime();
    return result;
}

QVariantMap DataProcessingCapability::createSuccessResult(const QVariantMap& data) const
{
    QVariantMap result;
    result["success"] = true;
    result["timestamp"] = QDateTime::currentDateTime();
    if (!data.isEmpty()) {
        result["data"] = data;
    }
    return result;
}

void DataProcessingCapability::updateStatistics(const QString& operation, bool success)
{
    if (operation == "process_data") {
        m_dataProcessedCount++;
    } else if (operation == "validate_data") {
        m_dataValidatedCount++;
    } else if (operation == "share_data") {
        m_dataSharedCount++;
    }
    
    if (!success) {
        m_errorCount++;
    }
}

// === DataProcessingCapabilityFactory 实现 ===

std::unique_ptr<DataProcessingCapability> DataProcessingCapabilityFactory::createCapability(const QString& deviceType, const QVariantMap& config)
{
    auto capability = std::make_unique<DataProcessingCapability>();
    
    if (!capability->initialize(config)) {
        qCritical() << "Failed to initialize DataProcessingCapability for device type:" << deviceType;
        return nullptr;
    }
    
    return capability;
}

std::unique_ptr<DataProcessingCapability> DataProcessingCapabilityFactory::createWithServices(
    const QString& deviceType,
    std::shared_ptr<IDataSharingSystem> dataSharingSystem,
    QVariant algorithmService)
{
    auto capability = std::make_unique<DataProcessingCapability>();
    
    // 注入基础设施服务
    QVariantMap services;
    // services["data_sharing_system"] = QVariant::fromValue(dataSharingSystem);
    services["algorithm_service"] = algorithmService;
    capability->setInfrastructureServices(services);
    
    // 初始化配置（可以根据设备类型定制）
    QVariantMap config;
    config["device_type"] = deviceType;
    config["enable_caching"] = true;
    config["cache_timeout_ms"] = 3600000; // 1小时
    
    if (!capability->initialize(config)) {
        qCritical() << "Failed to initialize DataProcessingCapability with services for device type:" << deviceType;
        return nullptr;
    }
    
    return capability;
}

} // namespace LA::Device::Capability