#include <LA/Communication/Session/ICommunicationSession.h>
#include <LA/Communication/Connection/IConnection.h>
#include <LA/Communication/Protocol/IProtocol.h>
#include <LA/Communication/Command/ICommandHandler.h>
#include <QDebug>
#include <QMutexLocker>
#include <QDateTime>
#include <QTimer>

namespace LA {
namespace Communication {
namespace Session {

/**
 * @brief 基础通信会话实现
 * 
 * 组合连接、协议、命令处理器的Linus式实现
 */
class BasicCommunicationSession : public ICommunicationSession {
    Q_OBJECT

public:
    explicit BasicCommunicationSession(QObject* parent = nullptr);
    virtual ~BasicCommunicationSession();

    // === 会话管理 ===
    SimpleResult openSession(const ConfigParameters& config) override;
    SimpleResult closeSession() override;
    bool isSessionOpen() const override;
    ConnectionStatus getSessionStatus() const override;

    // === 数据通信 ===
    qint64 sendRawData(const QByteArray& data) override;
    QByteArray receiveRawData(qint64 maxBytes = -1) override;
    SimpleResult sendMessage(const QVariantMap& message) override;
    QVariantMap receiveMessage() override;

    // === 命令处理 ===
    CommandResult executeCommand(const QVariantMap& command) override;
    CommandResult sendCommandAndWait(const QVariantMap& command, int timeout = 5000) override;

    // === 组件管理 ===
    SimpleResult setConnection(std::shared_ptr<Connection::IConnection> connection) override;
    SimpleResult setProtocol(std::shared_ptr<Protocol::IProtocol> protocol) override;
    SimpleResult setCommandHandler(std::shared_ptr<Command::ICommandHandler> handler) override;

    // === 状态查询 ===
    DeviceStatistics getStatistics() const override;
    QString errorString() const override;
    void resetStatistics() override;
    ConfigParameters getSessionConfig() const override;

private slots:
    void onConnectionStatusChanged(ConnectionStatus status);
    void onDataReceived();
    void onCommandExecuted(const QVariantMap& command, const CommandResult& result);

private:
    mutable QMutex m_mutex;
    
    // 组件
    std::shared_ptr<Connection::IConnection> m_connection;
    std::shared_ptr<Protocol::IProtocol> m_protocol;
    std::shared_ptr<Command::ICommandHandler> m_commandHandler;
    
    // 状态
    ConnectionStatus m_sessionStatus;
    ConfigParameters m_sessionConfig;
    DeviceStatistics m_statistics;
    QString m_lastError;
    
    // 内部方法
    void updateStatistics(qint64 bytesSent = 0, qint64 bytesReceived = 0);
    bool validateComponents() const;
    void connectComponentSignals();
    void disconnectComponentSignals();
};

BasicCommunicationSession::BasicCommunicationSession(QObject* parent)
    : ICommunicationSession(parent)
    , m_sessionStatus(ConnectionStatus::Disconnected)
{
    resetStatistics();
}

BasicCommunicationSession::~BasicCommunicationSession() {
    if (isSessionOpen()) {
        closeSession();
    }
}

// === 会话管理 ===

SimpleResult BasicCommunicationSession::openSession(const ConfigParameters& config) {
    QMutexLocker locker(&m_mutex);
    
    m_lastError.clear();
    m_sessionConfig = config;
    
    // 验证组件
    if (!validateComponents()) {
        m_lastError = "Required components not set (connection, protocol, or command handler missing)";
        return SimpleResult::failure(m_lastError);
    }
    
    if (m_sessionStatus == ConnectionStatus::Connected) {
        return SimpleResult::success(true);
    }
    
    m_sessionStatus = ConnectionStatus::Connecting;
    emit sessionStatusChanged(m_sessionStatus);
    
    // 打开连接
    ConnectionConfig connConfig;
    connConfig.parameters = config;
    // 从config中提取连接特定参数
    if (config.contains("hostAddress")) {
        connConfig.hostAddress = config["hostAddress"].toString();
    }
    if (config.contains("port")) {
        connConfig.port = config["port"].toInt();
    }
    if (config.contains("portName")) {
        connConfig.portName = config["portName"].toString();
    }
    
    auto result = m_connection->open(connConfig);
    if (!result.isSuccess) {
        m_lastError = QString("Failed to open connection: %1").arg(result.message);
        m_sessionStatus = ConnectionStatus::Error;
        emit sessionStatusChanged(m_sessionStatus);
        return SimpleResult::failure(m_lastError);
    }
    
    // 连接组件信号
    connectComponentSignals();
    
    m_sessionStatus = ConnectionStatus::Connected;
    emit sessionStatusChanged(m_sessionStatus);
    
    qDebug() << "Communication session opened successfully";
    return SimpleResult::success(true);
}

SimpleResult BasicCommunicationSession::closeSession() {
    QMutexLocker locker(&m_mutex);
    
    if (m_sessionStatus == ConnectionStatus::Disconnected) {
        return SimpleResult::success(true);
    }
    
    // 断开组件信号
    disconnectComponentSignals();
    
    // 关闭连接
    if (m_connection) {
        m_connection->close();
    }
    
    m_sessionStatus = ConnectionStatus::Disconnected;
    emit sessionStatusChanged(m_sessionStatus);
    
    qDebug() << "Communication session closed";
    return SimpleResult::success(true);
}

bool BasicCommunicationSession::isSessionOpen() const {
    QMutexLocker locker(&m_mutex);
    return m_sessionStatus == ConnectionStatus::Connected && 
           m_connection && m_connection->isOpen();
}

ConnectionStatus BasicCommunicationSession::getSessionStatus() const {
    QMutexLocker locker(&m_mutex);
    return m_sessionStatus;
}

// === 数据通信 ===

qint64 BasicCommunicationSession::sendRawData(const QByteArray& data) {
    QMutexLocker locker(&m_mutex);
    
    if (!isSessionOpen()) {
        m_lastError = "Session not open";
        return -1;
    }
    
    qint64 bytesSent = m_connection->write(data);
    if (bytesSent > 0) {
        updateStatistics(bytesSent, 0);
    }
    
    return bytesSent;
}

QByteArray BasicCommunicationSession::receiveRawData(qint64 maxBytes) {
    QMutexLocker locker(&m_mutex);
    
    if (!isSessionOpen()) {
        return QByteArray();
    }
    
    QByteArray data = m_connection->read(maxBytes);
    if (!data.isEmpty()) {
        updateStatistics(0, data.size());
        emit dataReceived(data);
    }
    
    return data;
}

SimpleResult BasicCommunicationSession::sendMessage(const QVariantMap& message) {
    QMutexLocker locker(&m_mutex);
    
    if (!isSessionOpen()) {
        m_lastError = "Session not open";
        return SimpleResult::failure(m_lastError);
    }
    
    if (!m_protocol) {
        m_lastError = "Protocol not set";
        return SimpleResult::failure(m_lastError);
    }
    
    // 编码消息
    QByteArray encodedData = m_protocol->encode(message);
    if (encodedData.isEmpty()) {
        m_lastError = QString("Protocol encoding failed: %1").arg(m_protocol->errorString());
        return SimpleResult::failure(m_lastError);
    }
    
    // 发送编码后的数据
    qint64 bytesSent = m_connection->write(encodedData);
    if (bytesSent <= 0) {
        m_lastError = QString("Failed to send data: %1").arg(m_connection->errorString());
        return SimpleResult::failure(m_lastError);
    }
    
    updateStatistics(bytesSent, 0);
    return SimpleResult::success(true);
}

QVariantMap BasicCommunicationSession::receiveMessage() {
    QMutexLocker locker(&m_mutex);
    
    if (!isSessionOpen() || !m_protocol) {
        return QVariantMap();
    }
    
    // 接收原始数据
    QByteArray rawData = m_connection->read();
    if (rawData.isEmpty()) {
        return QVariantMap();
    }
    
    // 检查是否为完整帧
    if (!m_protocol->isCompleteFrame(rawData)) {
        // 需要接收更多数据，这里简化处理
        return QVariantMap();
    }
    
    // 解码消息
    QVariantMap message = m_protocol->decode(rawData);
    if (!message.isEmpty()) {
        updateStatistics(0, rawData.size());
        emit messageReceived(message);
    }
    
    return message;
}

// === 命令处理 ===

CommandResult BasicCommunicationSession::executeCommand(const QVariantMap& command) {
    QMutexLocker locker(&m_mutex);
    
    CommandResult result;
    result.success = false;
    
    if (!m_commandHandler) {
        result.errorMessage = "Command handler not set";
        return result;
    }
    
    // 执行命令
    result = m_commandHandler->executeCommand(command);
    
    // 发出信号
    emit commandExecuted(command, result);
    
    return result;
}

CommandResult BasicCommunicationSession::sendCommandAndWait(const QVariantMap& command, int timeout) {
    QMutexLocker locker(&m_mutex);
    
    CommandResult result;
    result.success = false;
    
    if (!isSessionOpen()) {
        result.errorMessage = "Session not open";
        return result;
    }
    
    // 首先执行命令处理
    result = executeCommand(command);
    if (!result.success) {
        return result;
    }
    
    // 如果命令需要发送到设备
    if (command.contains("sendToDevice") && command["sendToDevice"].toBool()) {
        // 发送命令消息
        auto sendResult = sendMessage(command);
        if (!sendResult.isSuccess) {
            result.success = false;
            result.errorMessage = sendResult.message;
            return result;
        }
        
        // 等待响应（简化实现，实际应该用QTimer或QEventLoop）
        QTimer::singleShot(timeout, [&]() {
            // 尝试接收响应
            QVariantMap response = receiveMessage();
            if (!response.isEmpty()) {
                result.data["response"] = response;
            }
        });
    }
    
    return result;
}

// === 组件管理 ===

SimpleResult BasicCommunicationSession::setConnection(std::shared_ptr<Connection::IConnection> connection) {
    QMutexLocker locker(&m_mutex);
    
    if (isSessionOpen()) {
        m_lastError = "Cannot change connection while session is open";
        return SimpleResult::failure(m_lastError);
    }
    
    m_connection = connection;
    return SimpleResult::success(true);
}

SimpleResult BasicCommunicationSession::setProtocol(std::shared_ptr<Protocol::IProtocol> protocol) {
    QMutexLocker locker(&m_mutex);
    
    if (isSessionOpen()) {
        m_lastError = "Cannot change protocol while session is open";
        return SimpleResult::failure(m_lastError);
    }
    
    m_protocol = protocol;
    return SimpleResult::success(true);
}

SimpleResult BasicCommunicationSession::setCommandHandler(std::shared_ptr<Command::ICommandHandler> handler) {
    QMutexLocker locker(&m_mutex);
    
    m_commandHandler = handler;
    return SimpleResult::success(true);
}

// === 状态查询 ===

DeviceStatistics BasicCommunicationSession::getStatistics() const {
    QMutexLocker locker(&m_mutex);
    return m_statistics;
}

QString BasicCommunicationSession::errorString() const {
    QMutexLocker locker(&m_mutex);
    return m_lastError;
}

void BasicCommunicationSession::resetStatistics() {
    QMutexLocker locker(&m_mutex);
    m_statistics = DeviceStatistics();
    m_statistics.lastActivity = QDateTime::currentMSecsSinceEpoch();
    m_lastError.clear();
}

ConfigParameters BasicCommunicationSession::getSessionConfig() const {
    QMutexLocker locker(&m_mutex);
    return m_sessionConfig;
}

// === 私有槽函数 ===

void BasicCommunicationSession::onConnectionStatusChanged(ConnectionStatus status) {
    QMutexLocker locker(&m_mutex);
    m_sessionStatus = status;
    emit sessionStatusChanged(status);
}

void BasicCommunicationSession::onDataReceived() {
    // 自动接收数据并处理
    QByteArray data = receiveRawData();
    // 数据已经通过receiveRawData发出信号
}

void BasicCommunicationSession::onCommandExecuted(const QVariantMap& command, const CommandResult& result) {
    // 转发命令执行信号
    emit commandExecuted(command, result);
}

// === 私有辅助方法 ===

void BasicCommunicationSession::updateStatistics(qint64 bytesSent, qint64 bytesReceived) {
    qint64 currentTime = QDateTime::currentMSecsSinceEpoch();
    
    if (bytesSent > 0) {
        m_statistics.bytesSent += bytesSent;
        m_statistics.packetsSent++;
        m_statistics.lastActivity = currentTime;
    }
    
    if (bytesReceived > 0) {
        m_statistics.bytesReceived += bytesReceived;
        m_statistics.packetsReceived++;
        m_statistics.lastActivity = currentTime;
    }
}

bool BasicCommunicationSession::validateComponents() const {
    return m_connection != nullptr && 
           m_protocol != nullptr && 
           m_commandHandler != nullptr;
}

void BasicCommunicationSession::connectComponentSignals() {
    if (m_connection) {
        connect(m_connection.get(), &Connection::IConnection::statusChanged,
                this, &BasicCommunicationSession::onConnectionStatusChanged);
        connect(m_connection.get(), &Connection::IConnection::readyRead,
                this, &BasicCommunicationSession::onDataReceived);
    }
    
    if (m_commandHandler) {
        connect(m_commandHandler.get(), &Command::ICommandHandler::commandExecuted,
                this, &BasicCommunicationSession::onCommandExecuted);
    }
}

void BasicCommunicationSession::disconnectComponentSignals() {
    if (m_connection) {
        disconnect(m_connection.get(), nullptr, this, nullptr);
    }
    
    if (m_commandHandler) {
        disconnect(m_commandHandler.get(), nullptr, this, nullptr);
    }
}

} // namespace Session
} // namespace Communication
} // namespace LA

#include "BasicCommunicationSession.moc"