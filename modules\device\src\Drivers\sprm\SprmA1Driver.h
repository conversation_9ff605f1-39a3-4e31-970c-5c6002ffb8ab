/**
 * @file SprmA1Driver.h
 * @brief SPRM-A1设备驱动适配器 - 四层架构第1层
 * 
 * 职责：
 * - 封装SPRM-A1硬件交互细节
 * - 提供标准化的驱动接口
 * - 处理设备特定的通信协议
 * - 硬件差异完全隔离在此层
 */

#pragma once

#include "LA/Device/Core/Device.h"
#include <QObject>
#include <QString>
#include <QVariantMap>
#include <QSerialPort>
#include <QTimer>
#include <QMap>
#include <QByteArray>
#include <memory>

namespace LA::Device::Driver {

// 前向声明和协议定义
namespace SprmA1Protocol {
    constexpr uint8_t CMD_START_MEASURE = 0x01;
    constexpr uint8_t CMD_STOP_MEASURE = 0x02; 
    constexpr uint8_t CMD_GET_DISTANCE = 0x09;
    constexpr uint8_t CMD_CALIBRATE = 0x07;
    constexpr uint8_t CMD_SET_LASER_POWER = 0x08;
    constexpr uint8_t CMD_GET_STATUS = 0x05;
    constexpr uint8_t CMD_RESET = 0x04;
    constexpr uint8_t CMD_GET_VERSION = 0x14;
    constexpr uint8_t CMD_SET_BAUDRATE = 0x06;
    constexpr uint8_t CMD_SELF_TEST = 0x03;
}

// CommandCode结构在类中定义

/**
 * @brief SPRM-A1设备驱动适配器
 * 
 * 设备规格：
 * - 激光波长: 650nm (红光)
 * - 测量范围: 50-2000mm
 * - 精度: ±1mm
 * - 通信接口: RS485
 * - 波特率: 19200
 */
class SprmA1Driver : public QObject, public IDriver {
    Q_OBJECT

public:
    explicit SprmA1Driver(QObject* parent = nullptr);
    virtual ~SprmA1Driver() = default;

    // === IDriver接口实现 ===
    
    bool initialize() override;
    bool connect() override;
    bool disconnect() override;
    QVariantMap sendCommand(const QString& command, const QVariantMap& params) override;
    QString getDriverType() const override { return "SprmA1Driver"; }
    bool isConnected() const override;

    // === SPRM-A1特定配置 ===
    
    /**
     * @brief 设置串口参数
     * @param portName 端口名
     * @param baudRate 波特率 (默认19200)
     */
    void setSerialConfig(const QString& portName, int baudRate = 19200);
    
    /**
     * @brief 设置激光器参数
     * @param power 功率 (默认5.0mW)
     * @param wavelength 波长 (固定650nm)
     */
    void setLaserConfig(double power = 5.0);
    
    /**
     * @brief 获取设备规格
     * @return 设备技术参数
     */
    QVariantMap getDeviceSpecs() const;
    
    /**
     * @brief 获取设备信息
     * @return 设备信息
     */
    QVariantMap getDeviceInfo() const;

Q_SIGNALS:
    void dataReceived(const QByteArray& data);
    void connectionStatusChanged(bool connected);
    void errorOccurred(const QString& error);

private slots:
    void onSerialDataReceived();
    void onSerialError(QSerialPort::SerialPortError error);
    void onCommandTimeout();

private:
    // 串口通信
    std::unique_ptr<QSerialPort> m_serialPort;
    QString m_portName;
    int m_baudRate;
    bool m_connected;
    
    // 设备参数
    double m_laserPower;          // 激光功率 (mW)
    QString m_firmwareVersion;    // 固件版本
    
    // 通信控制
    QTimer* m_commandTimeout;
    int m_timeoutMs;
    QByteArray m_responseBuffer;
    bool m_waitingForResponse;
    
    // SPRM-A1命令映射
    struct CommandCode {
        uint8_t code;
        QString description;
        int expectedResponseLength;
    };
    
    static const QMap<QString, CommandCode> s_commandCodes;
    
    // === 内部方法 ===
    
    /**
     * @brief 构建SPRM-A1协议帧
     * @param command 命令名
     * @param params 命令参数
     * @return 协议帧数据
     */
    QByteArray buildCommandFrame(const QString& command, const QVariantMap& params);
    
    /**
     * @brief 解析SPRM-A1响应帧
     * @param data 响应数据
     * @return 解析结果
     */
    QVariantMap parseResponse(const QByteArray& data);
    
    /**
     * @brief 验证帧校验和
     * @param frame 帧数据
     * @return 是否有效
     */
    bool validateChecksum(const QByteArray& frame);
    
    /**
     * @brief 创建错误结果
     * @param error 错误信息
     * @return 错误结果
     */
    QVariantMap createErrorResult(const QString& error) const;
    
    /**
     * @brief 创建成功结果
     * @param data 结果数据
     * @return 成功结果
     */
    QVariantMap createSuccessResult(const QVariantMap& data = {}) const;
    
    /**
     * @brief 获取状态文本
     * @param statusCode 状态代码
     * @return 状态描述
     */
    QString getStatusText(int statusCode) const;
    
    /**
     * @brief 计算帧校验和
     * @param data 数据
     * @return 校验和
     */
    uint8_t calculateChecksum(const QByteArray& data);

private slots:
    /**
     * @brief 接收到串口数据
     */
    void onDataReceived();
    
    /**
     * @brief 串口错误处理
     * @param error 错误类型
     */
    void onSerialPortError(QSerialPort::SerialPortError error);
    
    /**
     * @brief 发送原始数据
     * @param data 数据
     * @return 是否成功
     */
    bool sendRawData(const QByteArray& data);
    
    /**
     * @brief 等待响应数据
     * @param timeoutMs 超时时间
     * @return 响应数据
     */
    QByteArray waitForResponse(int timeoutMs = 3000);
    
    /**
     * @brief 设备自检
     * @return 自检结果
     */
    QVariantMap performSelfTest();
    
};

// Remove duplicate SprmA1Protocol struct definition - use namespace only

} // namespace LA::Device::Driver