#include "InterpolationFactory.h"
#include "../interpolation/BilinearInterpolation.h"
#include "../interpolation/NoInterpolation.h"

namespace ImageProcessing {

InterpolationFactory &InterpolationFactory::getInstance() {
    static InterpolationFactory instance;
    return instance;
}

InterpolationFactory::InterpolationFactory() {
    initializeDefaultAlgorithms();
    logDebug("InterpolationFactory initialized");
}

std::unique_ptr<IInterpolation> InterpolationFactory::createInterpolation(InterpolationType type) {
    auto it = creators_.find(type);
    if (it == creators_.end()) {
        throw UnsupportedOperationException(QString("Interpolation type not supported: %1").arg(static_cast<int>(type)));
    }

    auto interpolation = it.value()();
    logDebug(QString("Created interpolation: %1").arg(interpolation->getAlgorithmName()));
    return interpolation;
}

QVector<InterpolationType> InterpolationFactory::getSupportedTypes() const {
    QVector<InterpolationType> types;
    for (auto it = creators_.begin(); it != creators_.end(); ++it) {
        types.append(it.key());
    }
    return types;
}

QString InterpolationFactory::getTypeDescription(InterpolationType type) const {
    switch (type) {
    case InterpolationType::None:
        return "No interpolation - use original data without any processing";
    case InterpolationType::Bilinear:
        return "Bilinear interpolation - smooth scaling with good quality/performance balance";
    case InterpolationType::Bicubic:
        return "Bicubic interpolation - high quality scaling with smooth curves";
    case InterpolationType::Nonlinear:
        return "Nonlinear interpolation - specialized algorithm for specific use cases";
    case InterpolationType::NearestNeighbor:
        return "Nearest neighbor interpolation - fast but pixelated scaling";
    default:
        return "Unknown interpolation type";
    }
}

void InterpolationFactory::registerInterpolation(InterpolationType type, std::function<std::unique_ptr<IInterpolation>()> creator) {
    creators_[type] = creator;
    logDebug(QString("Registered interpolation type: %1").arg(static_cast<int>(type)));
}

bool InterpolationFactory::isSupported(InterpolationType type) const {
    return creators_.contains(type);
}

QString InterpolationFactory::getVersion() const {
    return "1.0.0";
}

void InterpolationFactory::initializeDefaultAlgorithms() {
    // 注册无插值算法
    // registerInterpolation(InterpolationType::None, []() { return std::make_unique<NoInterpolation>(); });

    // 注册双线性插值
    registerInterpolation(InterpolationType::Bilinear, []() { return std::make_unique<BilinearInterpolation>(); });

    // TODO: 注册其他插值算法
    // registerInterpolation(InterpolationType::Bicubic, []() {
    //     return std::make_unique<BicubicInterpolation>();
    // });

    logDebug("Default interpolation algorithms initialized");
}

void InterpolationFactory::logDebug(const QString &message) const {
    qDebug() << "[InterpolationFactory]" << message;
}

}  // namespace ImageProcessing
