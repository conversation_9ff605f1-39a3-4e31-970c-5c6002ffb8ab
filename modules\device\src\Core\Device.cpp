#include "LA/Device/Core/Device.h"
#include <QDebug>

namespace LA::Device {

Device::Device(const QString& deviceType, const QString& deviceId, QObject* parent)
    : QObject(parent), m_deviceType(deviceType), m_deviceId(deviceId), m_state(DeviceState::Uninitialized) {
    qDebug() << "Device created:" << deviceType << deviceId;
    m_createTime = QDateTime::currentDateTime();
}

bool Device::initialize() { 
    setState(DeviceState::Initializing);
    // TODO: Initialize components
    setState(DeviceState::Ready);
    return true; 
}

bool Device::connect() { 
    if (m_driver) {
        bool result = m_driver->connect();
        if (result) {
            setState(DeviceState::Active);
        }
        return result;
    }
    return false; 
}

bool Device::disconnect() { 
    if (m_driver) {
        bool result = m_driver->disconnect();
        setState(DeviceState::Disconnected);
        return result;
    }
    return true; 
}

bool Device::start() { 
    setState(DeviceState::Active);
    return true; 
}

bool Device::stop() { 
    setState(DeviceState::Ready);
    return true; 
}

void Device::setState(DeviceState newState) { 
    if (m_state != newState) {
        m_state = newState; 
        emit stateChanged(newState);
    }
}

QVariantMap Device::executeCommand(const QString& command, const QVariantMap& params) { 
    QVariantMap result;
    result["success"] = false;
    result["command"] = command;
    
    try {
        // Find capable component
        auto* capability = findCapabilityForCommand(command);
        if (capability && m_driver) {
            result = capability->executeCapability(command, params, m_driver.get());
            emit commandExecuted(command, result);
        } else {
            result["error"] = "No capability found for command: " + command;
            emit commandFailed(command, result["error"].toString());
        }
    } catch (const std::exception& e) {
        result["error"] = QString("Exception: %1").arg(e.what());
        emit commandFailed(command, result["error"].toString());
    }
    
    return result;
}

QVariantMap Device::handleScriptEvent(const QString& event, const QVariantMap& params) { 
    QVariantMap result;
    result["success"] = true;
    result["event"] = event;
    result["handled"] = false;
    // TODO: Implement script event handling
    emit scriptEventTriggered(event, params);
    return result;
}

void Device::setDriver(std::unique_ptr<Driver::IDriver> driver) {
    m_driver = std::move(driver);
}

void Device::addCapability(std::unique_ptr<Capability::ICapability> capability) {
    m_capabilities.push_back(std::move(capability));
    buildCommandRoutes(); // Rebuild command routing
}

void Device::setStrategy(const QString& strategyType, std::unique_ptr<Strategy::IStrategy> strategy) {
    m_strategies[strategyType] = std::move(strategy);
}

bool Device::loadScript(const QString& scriptFile) { 
    Q_UNUSED(scriptFile);
    // TODO: Load script file
    return false; 
}

void Device::setScriptVariable(const QString& name, const QVariant& value) {
    m_properties[name] = value;
}

QVariant Device::getScriptVariable(const QString& name) {
    return m_properties.value(name);
}

bool Device::reloadScript() { 
    emit scriptReloaded();
    return false; 
}

QVariant Device::callScriptFunction(const QString& functionName, const QVariantList& args) {
    Q_UNUSED(functionName);
    Q_UNUSED(args);
    return QVariant();
}

std::unique_ptr<Device> Device::createFromConfig(const QVariantMap& config) { 
    QString deviceType = config.value("type").toString();
    QString deviceId = config.value("id").toString();
    return std::make_unique<Device>(deviceType, deviceId);
}

QVariantMap Device::getAllProperties() const { 
    return m_properties; 
}

QStringList Device::getSupportedCommands() const { 
    QStringList commands;
    for (const auto& capability : m_capabilities) {
        commands.append(capability->getProvidedCommands());
    }
    return commands;
}

bool Device::hasCommand(const QString& command) const { 
    return getSupportedCommands().contains(command);
}

QVariant Device::getSpec(const QString& key) const { 
    return m_config.value(key); 
}

void Device::buildCommandRoutes() {
    m_commandRoutes.clear();
    for (const auto& capability : m_capabilities) {
        QStringList commands = capability->getProvidedCommands();
        for (const QString& command : commands) {
            m_commandRoutes[command] = capability.get();
        }
    }
}

Capability::ICapability* Device::findCapabilityForCommand(const QString& command) const { 
    auto it = m_commandRoutes.find(command);
    return (it != m_commandRoutes.end()) ? it->second : nullptr;
}

QVariantMap Device::executeCommandThroughLayers(const QString& command, const QVariantMap& params) { 
    return executeCommand(command, params);
}

QVariantMap Device::applyScriptSelectedStrategy(const QString& command, const QVariantMap& data) { 
    Q_UNUSED(command);
    return data;
}

bool Device::validateComponents() const { 
    return m_driver != nullptr;
}

void Device::onScriptError(const QString& error) {
    m_lastError = error;
    emit scriptError(error);
    emit deviceError(error);
}

void Device::onScriptTimeout() {
    emit scriptError("Script execution timeout");
}

} // namespace LA::Device
