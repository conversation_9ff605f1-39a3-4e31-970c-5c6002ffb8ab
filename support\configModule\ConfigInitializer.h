#ifndef CONFIG_INITIALIZER_H
#define CONFIG_INITIALIZER_H

#include "ConfigTypes.h"
#include <QObject>

namespace Config {

/**
 * @brief 配置文件初始化工具类
 * 
 * 负责在应用程序启动时检查和初始化配置文件
 * 提供配置文件验证和状态检查功能
 */
class ConfigInitializer : public QObject
{
    Q_OBJECT

public:
    explicit ConfigInitializer(QObject* parent = nullptr);
    
    // 初始化配置文件系统
    ConfigResult initializeConfigs();
    
    // 检查配置文件完整性
    ConfigResult checkConfigIntegrity();
    
    // 验证配置文件状态
    ConfigResult validateConfigFiles();

Q_SIGNALS:
    void initializationProgress(const QString& message, int percentage);
    void initializationError(const QString& error);
    void initializationComplete();

private:
    // 检查单个配置文件
    bool checkConfigFile(const QString& filePath, const QString& configType);
    
    // 创建配置目录
    bool createConfigDirectory();
    
    // 获取配置目录路径
    QString getConfigDirectory() const;
    
    // 日志记录
    void logInfo(const QString& message);
    void logError(const QString& message);
    void logWarning(const QString& message);
};

} // namespace Config

#endif // CONFIG_INITIALIZER_H
