@echo off
rem 测试目录结构验证脚本
rem 检查项目是否符合 test_guideline.md 规范

setlocal

echo ========================================
echo 测试结构验证器 (Test Structure Validator)
echo 基于 test_guideline.md 规范
echo ========================================
echo.

set PASS_COUNT=0
set FAIL_COUNT=0

echo 验证测试目录结构...
echo.

rem 检查主要测试目录
if exist "tests\" (
    echo ✅ tests/ - 主测试目录存在
    set /a PASS_COUNT+=1
) else (
    echo ❌ tests/ - 主测试目录缺失
    set /a FAIL_COUNT+=1
)

if exist "tests\unit\" (
    echo ✅ tests/unit/ - 单元测试目录存在
    set /a PASS_COUNT+=1
) else (
    echo ❌ tests/unit/ - 单元测试目录缺失
    set /a FAIL_COUNT+=1
)

if exist "tests\integration\" (
    echo ✅ tests/integration/ - 集成测试目录存在
    set /a PASS_COUNT+=1
) else (
    echo ❌ tests/integration/ - 集成测试目录缺失
    set /a FAIL_COUNT+=1
)

if exist "tests\algorithm\" (
    echo ✅ tests/algorithm/ - 算法测试目录存在
    set /a PASS_COUNT+=1
) else (
    echo ❌ tests/algorithm/ - 算法测试目录缺失
    set /a FAIL_COUNT+=1
)

if exist "tests\ui\" (
    echo ✅ tests/ui/ - UI测试目录存在
    set /a PASS_COUNT+=1
) else (
    echo ❌ tests/ui/ - UI测试目录缺失
    set /a FAIL_COUNT+=1
)

echo.
echo 检查设备测试迁移状态...

if exist "tests\unit\devices\sprm\" (
    echo ✅ tests/unit/devices/sprm/ - 设备单元测试目录符合规范
    set /a PASS_COUNT+=1
) else (
    echo ❌ tests/unit/devices/sprm/ - 设备单元测试目录缺失
    set /a FAIL_COUNT+=1
)

if exist "tests\integration\devices\sprm\" (
    echo ✅ tests/integration/devices/sprm/ - 设备集成测试目录符合规范
    set /a PASS_COUNT+=1
) else (
    echo ❌ tests/integration/devices/sprm/ - 设备集成测试目录缺失
    set /a FAIL_COUNT+=1
)

rem 检查违规的模块内测试目录
if exist "modules\device\tests\" (
    echo ❌ modules/device/tests/ - 违反规范！应迁移到 tests/unit/devices/sprm/
    set /a FAIL_COUNT+=1
) else (
    echo ✅ modules/device/tests/ - 已正确移除违规目录
    set /a PASS_COUNT+=1
)

echo.
echo 检查构建输出目录...

if exist "build\test\" (
    echo ✅ build/test/ - 测试构建输出目录存在
    set /a PASS_COUNT+=1
) else (
    echo ❌ build/test/ - 测试构建输出目录缺失（将自动创建）
    mkdir "build\test" 2>nul
    set /a PASS_COUNT+=1
)

echo.
echo ========================================
echo 验证结果统计
echo ========================================
echo ✅ 通过：%PASS_COUNT% 项
echo ❌ 失败：%FAIL_COUNT% 项

if %FAIL_COUNT% == 0 (
    echo.
    echo 🎉 恭喜！测试目录结构完全符合 test_guideline.md 规范
    exit /b 0
) else (
    echo.
    echo ⚠️  发现 %FAIL_COUNT% 个不符合规范的问题，请参考上述输出进行修正
    exit /b 1
)