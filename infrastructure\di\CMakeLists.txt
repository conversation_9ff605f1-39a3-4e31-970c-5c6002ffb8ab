# Dependency Injection Module
cmake_minimum_required(VERSION 3.16)

# 1. Project definition
project(LA_infrastructure_di VERSION 1.0.0 LANGUAGES CXX)

# C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Qt配置
set(CMAKE_AUTOMOC ON)

# 源文件
set(SOURCE_FILES
    ServiceContainer.cpp
)

set(HEADER_FILES
    ServiceContainer.h
)

# 创建库
add_library(LA_infrastructure_di_lib SHARED ${SOURCE_FILES} ${HEADER_FILES})

# 设置包含目录
target_include_directories(LA_infrastructure_di_lib
    PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}
    PRIVATE
        ${PROJECT_SOURCE_DIR}
)

# 链接Support层和Qt库
target_link_libraries(LA_infrastructure_di_lib
    PUBLIC
        LA::Support     # 依赖Support层
    PRIVATE
        Qt5::Core
)

# 设置库属性
set_target_properties(LA_infrastructure_di_lib PROPERTIES
    VERSION ${PROJECT_VERSION}
    SOVERSION 1
    OUTPUT_NAME LA_infrastructure_di
)

# 安装配置
install(TARGETS LA_infrastructure_di_lib
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
)

install(FILES ${HEADER_FILES}
    DESTINATION include/LA/Infrastructure/DI
)