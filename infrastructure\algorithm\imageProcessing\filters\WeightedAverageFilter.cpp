#include "WeightedAverageFilter.h"
#include <QtMath>

namespace ImageProcessing {

WeightedAverageFilter::WeightedAverageFilter() {
    // 设置默认参数和3x3均匀权重
    params_.strength   = 1.0f;
    params_.enabled    = true;
    params_.kernelSize = 3;
    params_.normalize  = true;

    // 默认均匀权重
    params_.weights.resize(3);
    for (auto &row : params_.weights) {
        row.resize(3);
        row.fill(1.0f / 9.0f);
    }

    logDebug("WeightedAverageFilter initialized with uniform weights");
}

bool WeightedAverageFilter::apply(ImageDataU32 &data) {
    try {
        validateInput(data);

        if (!params_.enabled) {
            logDebug("Filter disabled, skipping processing");
            return true;
        }

        logDebug(QString("Applying weighted average filter to %1x%2 image").arg(data.width()).arg(data.height()));

        // 创建临时图像存储结果
        ImageDataU32 temp(data.width(), data.height());

        // 对每个像素应用加权均值
        for (uint32_t y = 0; y < data.height(); ++y) {
            for (uint32_t x = 0; x < data.width(); ++x) {
                float avgResult = applyWeightedAverageAtPixel(data, x, y);

                // 应用滤波强度
                float originalValue = static_cast<float>(data.matrix()[y][x]);
                float filteredValue = originalValue + params_.strength * (avgResult - originalValue);

                temp.matrix()[y][x] = safeFloatToUint32(filteredValue);
            }
        }

        // 复制结果回原图像
        data = std::move(temp);

        logDebug("Weighted average filter applied successfully");
        return true;

    } catch (const ProcessingException &e) {
        qWarning() << "WeightedAverageFilter::apply failed:" << e.qMessage();
        return false;
    } catch (const std::exception &e) {
        qWarning() << "WeightedAverageFilter::apply failed:" << e.what();
        return false;
    }
}

bool WeightedAverageFilter::apply(const ImageDataU32 &src, ImageDataU32 &dst) {
    try {
        validateInput(src);

        // 确保目标图像尺寸正确
        if (dst.width() != src.width() || dst.height() != src.height()) {
            dst.resize(src.width(), src.height());
        }

        // 复制源数据到目标
        dst = src;

        // 应用滤波
        return apply(dst);

    } catch (const ProcessingException &e) {
        qWarning() << "WeightedAverageFilter::apply (src->dst) failed:" << e.qMessage();
        return false;
    } catch (const std::exception &e) {
        qWarning() << "WeightedAverageFilter::apply (src->dst) failed:" << e.what();
        return false;
    }
}

void WeightedAverageFilter::setParameters(const FilterParams &params) {
    const WeightedAverageParams *avgParams = dynamic_cast<const WeightedAverageParams *>(&params);
    if (!avgParams) {
        throw InvalidParameterException("params", "must be WeightedAverageParams type");
    }

    validateWeightedAverageParams(*avgParams);
    params_ = *avgParams;

    if (params_.normalize) {
        normalizeWeights();
    }

    logDebug("Parameters updated: " + params_.toString());
}

std::unique_ptr<FilterParams> WeightedAverageFilter::getParameters() const {
    return std::make_unique<WeightedAverageParams>(params_);
}

QString WeightedAverageFilter::getAlgorithmName() const {
    return "WeightedAverageFilter";
}

QString WeightedAverageFilter::getDescription() const {
    return "Weighted average filter with customizable weight matrices for smoothing and noise reduction";
}

bool WeightedAverageFilter::isSupported(uint32_t width, uint32_t height) const {
    try {
        ValidationUtils::validatePositive(width, "width");
        ValidationUtils::validatePositive(height, "height");

        // 检查图像是否足够大以应用权重矩阵
        uint32_t minSize = static_cast<uint32_t>(params_.kernelSize);
        return width >= minSize && height >= minSize;
    } catch (const ProcessingException &) {
        return false;
    }
}

uint32_t WeightedAverageFilter::estimateProcessingTime(uint32_t width, uint32_t height) const {
    // 加权均值操作复杂度与核大小相关
    uint64_t totalPixels = static_cast<uint64_t>(width) * height;
    uint64_t kernelOps   = static_cast<uint64_t>(params_.kernelSize) * params_.kernelSize;
    // 假设每个加权均值操作需要0.0005毫秒
    return static_cast<uint32_t>((totalPixels * kernelOps) / 2000);
}

void WeightedAverageFilter::reset() {
    params_.reset();
    logDebug("WeightedAverageFilter reset to default state");
}

QString WeightedAverageFilter::getVersion() const {
    return "1.0.0";
}

bool WeightedAverageFilter::isThreadSafe() const {
    return true;  // 无状态操作，线程安全
}

bool WeightedAverageFilter::supportsInPlace() const {
    return false;  // 需要临时缓冲区
}

void WeightedAverageFilter::setPredefinedWeights(const QString &type) {
    params_.weights    = createPredefinedWeights(type);
    params_.kernelSize = params_.weights.size();

    qDebug() << "WeightedAverageFilter: 权重矩阵大小" << params_.kernelSize << "x" << params_.kernelSize;

    if (params_.normalize) {
        normalizeWeights();
    }

    logDebug(QString("WeightedAverageFilter: 成功设置预定义权重: %1").arg(type));
}

QStringList WeightedAverageFilter::getSupportedWeightTypes() {
    return {"uniform", "gaussian", "center_weighted", "edge_enhance", "smooth"};
}

float WeightedAverageFilter::applyWeightedAverageAtPixel(const ImageDataU32 &src, uint32_t x, uint32_t y) const {
    float result     = 0.0f;
    int   halfKernel = params_.kernelSize / 2;

    for (int ky = 0; ky < params_.kernelSize; ++ky) {
        for (int kx = 0; kx < params_.kernelSize; ++kx) {
            int srcX = static_cast<int>(x) + kx - halfKernel;
            int srcY = static_cast<int>(y) + ky - halfKernel;

            uint32_t pixelValue = getSafePixelValue(src, srcX, srcY);
            result += static_cast<float>(pixelValue) * params_.weights[ky][kx];
        }
    }

    return result;
}

uint32_t WeightedAverageFilter::getSafePixelValue(const ImageDataU32 &src, int x, int y) const {
    // 边界处理：边缘复制（避免零填充导致的边缘偏小问题）
    // 将坐标限制在有效范围内，使用最近的边缘像素值
    int clampedX = qBound(0, x, static_cast<int>(src.width() - 1));
    int clampedY = qBound(0, y, static_cast<int>(src.height() - 1));

    return src.matrix()[clampedY][clampedX];
}

void WeightedAverageFilter::normalizeWeights() {
    float sum = 0.0f;

    // 计算权重总和
    for (const auto &row : params_.weights) {
        for (float weight : row) {
            sum += weight;
        }
    }

    // 如果总和为0，不进行归一化
    if (qFuzzyIsNull(sum)) {
        logDebug("Weight sum is zero, skipping normalization");
        return;
    }

    // 归一化
    for (auto &row : params_.weights) {
        for (float &weight : row) {
            weight /= sum;
        }
    }

    logDebug(QString("Weights normalized with sum: %1").arg(sum));
}

QVector<QVector<float>> WeightedAverageFilter::createPredefinedWeights(const QString &type) const {
    QVector<QVector<float>> weights;

    if (type == "uniform") {
        // 3x3均匀权重
        weights = {{1.0f, 1.0f, 1.0f}, {1.0f, 1.0f, 1.0f}, {1.0f, 1.0f, 1.0f}};
    } else if (type == "gaussian") {
        // 3x3高斯权重
        weights = {{1.0f, 2.0f, 1.0f}, {2.0f, 4.0f, 2.0f}, {1.0f, 2.0f, 1.0f}};
    } else if (type == "center_weighted") {
        // 中心加权
        weights = {{1.0f, 2.0f, 1.0f}, {2.0f, 8.0f, 2.0f}, {1.0f, 2.0f, 1.0f}};
    } else if (type == "edge_enhance") {
        // 边缘增强权重
        weights = {{0.0f, -1.0f, 0.0f}, {-1.0f, 5.0f, -1.0f}, {0.0f, -1.0f, 0.0f}};
    } else if (type == "smooth") {
        // 平滑权重
        weights = {{1.0f, 4.0f, 1.0f}, {4.0f, 12.0f, 4.0f}, {1.0f, 4.0f, 1.0f}};
    } else {
        throw InvalidParameterException("weight type", QString("unsupported type: %1").arg(type));
    }

    return weights;
}

void WeightedAverageFilter::validateWeightedAverageParams(const WeightedAverageParams &params) const {
    params.validate();
}

void WeightedAverageFilter::logDebug(const QString &message) const {
    qDebug() << "[WeightedAverageFilter]" << message;
}

}  // namespace ImageProcessing
