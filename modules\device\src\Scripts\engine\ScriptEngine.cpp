/**
 * @file ScriptEngine.cpp
 * @brief 脚本引擎实现 - 四层架构第4层
 */

#include "ScriptEngine.h"
#include <QDebug>
#include <QTimer>
#include <QElapsedTimer>
#include <QStandardPaths>
#include <QDir>

#ifdef ENABLE_LUA_SUPPORT
extern "C" {
#include "lua.h"
#include "lualib.h"
#include "lauxlib.h"
}
#endif

namespace LA::Device::Script {

ScriptEngine::ScriptEngine(QObject* parent)
    : QObject(parent)
    , m_luaState(nullptr)
    , m_sandboxEnabled(true)
    , m_memoryLimitKB(1024)
    , m_timeLimitMs(5000)
{
#ifdef ENABLE_LUA_SUPPORT
    initializeLua();
#else
    qWarning() << "[ScriptEngine] Lua scripting disabled - compile with ENABLE_LUA_SCRIPT to enable";
#endif
}

ScriptEngine::~ScriptEngine() {
#ifdef ENABLE_LUA_SUPPORT
    if (m_luaState) {
        lua_close(m_luaState);
    }
#endif
}

bool ScriptEngine::initializeLua() {
#ifdef ENABLE_LUA_SUPPORT
    m_luaState = luaL_newstate();
    if (!m_luaState) {
        emit scriptError("Failed to create Lua state");
        return false;
    }

    // 基础库
    luaL_openlibs(m_luaState);
    
    // 注册设备API
    registerDeviceAPI();
    
    // 配置沙箱
    if (m_sandboxEnabled) {
        setupSandbox();
    }
    
    return true;
#else
    qWarning() << "[ScriptEngine] initializeLua called but Lua is not enabled";
    return false;
#endif
}

bool ScriptEngine::loadScript(const QString& scriptFile) {
    if (!m_luaState) {
        emit scriptError("Lua state not initialized");
        return false;
    }

    QFileInfo fileInfo(scriptFile);
    if (!fileInfo.exists()) {
        emit scriptError(QString("Script file not found: %1").arg(scriptFile));
        return false;
    }

    // 加载脚本文件
    int result = luaL_loadfile(m_luaState, scriptFile.toUtf8().constData());
    if (result != LUA_OK) {
        QString error = QString("Failed to load script: %1").arg(lua_tostring(m_luaState, -1));
        lua_pop(m_luaState, 1);
        emit scriptError(error);
        return false;
    }

    // 执行脚本
    result = lua_pcall(m_luaState, 0, 0, 0);
    if (result != LUA_OK) {
        QString error = QString("Failed to execute script: %1").arg(lua_tostring(m_luaState, -1));
        lua_pop(m_luaState, 1);
        emit scriptError(error);
        return false;
    }

    m_loadedScript = scriptFile;
    emit scriptLoaded(scriptFile);
    return true;
}

QVariantMap ScriptEngine::executeDeviceBehavior(const QString& deviceType, const QString& event, const QVariantMap& params) {
    if (!m_luaState) {
        return createErrorResult("Lua state not initialized");
    }

    QElapsedTimer timer;
    timer.start();

    // 获取处理函数
    lua_getglobal(m_luaState, "handle_device_event");
    if (!lua_isfunction(m_luaState, -1)) {
        lua_pop(m_luaState, 1);
        return createErrorResult("Function 'handle_device_event' not found in script");
    }

    // 推入参数
    lua_pushstring(m_luaState, deviceType.toUtf8().constData());
    lua_pushstring(m_luaState, event.toUtf8().constData());
    pushVariantMap(params);

    // 执行函数
    int result = lua_pcall(m_luaState, 3, 1, 0);
    
    // 检查执行时间
    if (timer.elapsed() > m_timeLimitMs) {
        lua_pop(m_luaState, 1);
        return createErrorResult("Script execution timeout");
    }

    if (result != LUA_OK) {
        QString error = QString("Script execution error: %1").arg(lua_tostring(m_luaState, -1));
        lua_pop(m_luaState, 1);
        return createErrorResult(error);
    }

    // 获取返回值
    QVariantMap resultMap = popVariantMap();
    
    return resultMap;
}

QVariantMap ScriptEngine::executeRuleEngine(const QVariantMap& deviceState, const QVariantMap& context) {
    if (!m_luaState) {
        return createErrorResult("Lua state not initialized");
    }

    // 获取规则处理函数
    lua_getglobal(m_luaState, "evaluate_rules");
    if (!lua_isfunction(m_luaState, -1)) {
        lua_pop(m_luaState, 1);
        return createErrorResult("Function 'evaluate_rules' not found in script");
    }

    // 推入参数
    pushVariantMap(deviceState);
    pushVariantMap(context);

    // 执行规则评估
    int result = lua_pcall(m_luaState, 2, 1, 0);
    if (result != LUA_OK) {
        QString error = QString("Rule evaluation error: %1").arg(lua_tostring(m_luaState, -1));
        lua_pop(m_luaState, 1);
        return createErrorResult(error);
    }

    return popVariantMap();
}

void ScriptEngine::setSandboxMode(bool enabled) {
    m_sandboxEnabled = enabled;
    if (m_luaState) {
        setupSandbox();
    }
}

void ScriptEngine::setResourceLimits(int memoryLimitKB, int timeLimitMs) {
    m_memoryLimitKB = memoryLimitKB;
    m_timeLimitMs = timeLimitMs;
}

QString ScriptEngine::getScriptVersion() const {
    if (!m_luaState) {
        return "Unknown";
    }

    lua_getglobal(m_luaState, "SCRIPT_VERSION");
    if (lua_isstring(m_luaState, -1)) {
        QString version = lua_tostring(m_luaState, -1);
        lua_pop(m_luaState, 1);
        return version;
    }
    lua_pop(m_luaState, 1);
    return "1.0.0";
}

QStringList ScriptEngine::getAvailableFunctions() const {
    QStringList functions;
    if (!m_luaState) {
        return functions;
    }

    lua_pushglobaltable(m_luaState);
    lua_pushnil(m_luaState);

    while (lua_next(m_luaState, -2) != 0) {
        if (lua_isfunction(m_luaState, -1)) {
            const char* name = lua_tostring(m_luaState, -2);
            if (name && QString(name).startsWith("handle_")) {
                functions << QString(name);
            }
        }
        lua_pop(m_luaState, 1);
    }
    
    lua_pop(m_luaState, 1);
    return functions;
}

void ScriptEngine::registerDeviceAPI() {
    // 注册设备API函数
    lua_pushcfunction(m_luaState, l_log_info);
    lua_setglobal(m_luaState, "log_info");

    lua_pushcfunction(m_luaState, l_log_warning);  
    lua_setglobal(m_luaState, "log_warning");

    lua_pushcfunction(m_luaState, l_log_error);
    lua_setglobal(m_luaState, "log_error");

    lua_pushcfunction(m_luaState, l_get_device_config);
    lua_setglobal(m_luaState, "get_device_config");

    lua_pushcfunction(m_luaState, l_set_device_param);
    lua_setglobal(m_luaState, "set_device_param");

    lua_pushcfunction(m_luaState, l_send_command);
    lua_setglobal(m_luaState, "send_command");
}

void ScriptEngine::setupSandbox() {
    if (!m_sandboxEnabled) {
        return;
    }

    // 禁用危险函数
    const char* restrictedFunctions[] = {
        "io", "os", "package", "require", "dofile", "loadfile", "load"
    };

    for (const char* func : restrictedFunctions) {
        lua_pushnil(m_luaState);
        lua_setglobal(m_luaState, func);
    }

    // 设置内存限制
    lua_gc(m_luaState, LUA_GCSETMEMLIMIT, m_memoryLimitKB);
}

void ScriptEngine::pushVariantMap(const QVariantMap& map) {
    lua_createtable(m_luaState, 0, map.size());
    
    for (auto it = map.constBegin(); it != map.constEnd(); ++it) {
        lua_pushstring(m_luaState, it.key().toUtf8().constData());
        pushVariant(it.value());
        lua_settable(m_luaState, -3);
    }
}

void ScriptEngine::pushVariant(const QVariant& value) {
    switch (value.type()) {
        case QVariant::String:
            lua_pushstring(m_luaState, value.toString().toUtf8().constData());
            break;
        case QVariant::Int:
        case QVariant::LongLong:
            lua_pushinteger(m_luaState, value.toLongLong());
            break;
        case QVariant::Double:
            lua_pushnumber(m_luaState, value.toDouble());
            break;
        case QVariant::Bool:
            lua_pushboolean(m_luaState, value.toBool());
            break;
        case QVariant::Map:
            pushVariantMap(value.toMap());
            break;
        default:
            lua_pushnil(m_luaState);
            break;
    }
}

QVariantMap ScriptEngine::popVariantMap() {
    QVariantMap result;
    
    if (!lua_istable(m_luaState, -1)) {
        lua_pop(m_luaState, 1);
        return result;
    }

    lua_pushnil(m_luaState);
    while (lua_next(m_luaState, -2) != 0) {
        QString key = lua_tostring(m_luaState, -2);
        QVariant value = popVariant();
        result[key] = value;
    }
    
    lua_pop(m_luaState, 1);
    return result;
}

QVariant ScriptEngine::popVariant() {
    QVariant result;
    
    int type = lua_type(m_luaState, -1);
    switch (type) {
        case LUA_TSTRING:
            result = QString(lua_tostring(m_luaState, -1));
            break;
        case LUA_TNUMBER:
            if (lua_isinteger(m_luaState, -1)) {
                result = lua_tointeger(m_luaState, -1);
            } else {
                result = lua_tonumber(m_luaState, -1);
            }
            break;
        case LUA_TBOOLEAN:
            result = (bool)lua_toboolean(m_luaState, -1);
            break;
        case LUA_TTABLE:
            result = popVariantMap();
            return result; // 不弹出栈，在popVariantMap中已处理
        default:
            result = QVariant();
            break;
    }
    
    lua_pop(m_luaState, 1);
    return result;
}

QVariantMap ScriptEngine::createErrorResult(const QString& error) {
    QVariantMap result;
    result["success"] = false;
    result["error"] = error;
    result["timestamp"] = QDateTime::currentDateTime().toString();
    return result;
}

// Lua C API 函数实现
int ScriptEngine::l_log_info(lua_State* L) {
    const char* message = luaL_checkstring(L, 1);
    qDebug() << "[Script Info]" << message;
    return 0;
}

int ScriptEngine::l_log_warning(lua_State* L) {
    const char* message = luaL_checkstring(L, 1);
    qWarning() << "[Script Warning]" << message;
    return 0;
}

int ScriptEngine::l_log_error(lua_State* L) {
    const char* message = luaL_checkstring(L, 1);
    qCritical() << "[Script Error]" << message;
    return 0;
}

int ScriptEngine::l_get_device_config(lua_State* L) {
    const char* key = luaL_checkstring(L, 1);
    // 这里应该从设备配置中获取值
    lua_pushstring(L, "default_value");
    return 1;
}

int ScriptEngine::l_set_device_param(lua_State* L) {
    const char* param = luaL_checkstring(L, 1);
    const char* value = luaL_checkstring(L, 2);
    qDebug() << "[Script] Set device param:" << param << "=" << value;
    lua_pushboolean(L, true);
    return 1;
}

int ScriptEngine::l_send_command(lua_State* L) {
    const char* command = luaL_checkstring(L, 1);
    qDebug() << "[Script] Send command:" << command;
    lua_pushstring(L, "command_executed");
    return 1;
}

} // namespace LA::Device::Script