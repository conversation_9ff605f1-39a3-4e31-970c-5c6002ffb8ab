# 设备开发指南

**文档状态**: 🚀 最新  
**更新日期**: 2025-08-22  
**适用版本**: v2.0+  
**关联文档**: [[deviceSystem]] | [[commandSystem]] | [[deviceManagement]] | [[data_flow_architecture]]

## 新设备开发流程

### 准备工作
1. 确定设备类型和功能需求
2. 分析硬件通信协议
3. 设计设备能力组件
4. 规划配置参数

### 开发步骤

#### 1. 创建指令提供器
```cpp
// 文件：modules/device/src/Devices/[Category]/[DeviceName]/[DeviceName]CommandProvider.h
class MotorCommandProvider : public ICommandProvider {
public:
    CommandMapping getCommandMapping() const override {
        return {
            {"start_motor", {0x10, CommandType::Control}},
            {"stop_motor", {0x11, CommandType::Control}},
            {"set_speed", {0x12, CommandType::Write, "speed"}},
            {"get_speed", {0x20, CommandType::Read}},
            {"get_status", {0x21, CommandType::Read}}
        };
    }
    
    QString getDeviceType() const override { return "MotorController"; }
};
```

#### 2. 实现设备类
```cpp
// 文件：modules/device/src/Devices/[Category]/[DeviceName]/Modern[DeviceName].h
class ModernMotorController : public ModernBaseDevice {
public:
    ModernMotorController(const QString& deviceId, const QString& model = "standard");
    
    // 业务方法
    bool startMotor() { return executeCommand("start_motor"); }
    bool stopMotor() { return executeCommand("stop_motor"); }
    bool setSpeed(int speed) { 
        QVariantMap params;
        params["speed"] = speed;
        return executeCommand("set_speed", params); 
    }
    
    int getCurrentSpeed() {
        if (executeCommand("get_speed")) {
            return getLastResponse()["speed"].toInt();
        }
        return -1;
    }
    
protected:
    std::unique_ptr<ICommandProvider> createCommandProvider() override {
        return std::make_unique<MotorCommandProvider>(m_model);
    }
    
    void configureCapabilities() override {
        // 添加设备特有能力
        addCapability(std::make_shared<MotorControlCapability>());
        addCapability(std::make_shared<SpeedMonitoringCapability>());
    }
};
```

#### 3. 注册设备类型
```cpp
// 文件：modules/device/src/DeviceRegistration.cpp
void registerAllDeviceTypes() {
    auto& registry = DeviceRegistry::instance();
    
    // 注册新设备类型
    registry.registerDeviceType("MotorController",
        std::make_shared<ModernDeviceFactory<ModernMotorController>>());
        
    // 设置设备分类
    registry.setDeviceCategory("MotorController", DeviceCategory::Actuator);
}
```

#### 4. 添加配置支持
```json
// 文件：config/devices/motor_controller.json
{
    "device_type": "MotorController",
    "display_name": "电机控制器",
    "category": "actuator",
    "models": {
        "standard": {
            "name": "标准电机控制器",
            "max_speed": 3000,
            "acceleration": 1000
        },
        "high_speed": {
            "name": "高速电机控制器", 
            "max_speed": 6000,
            "acceleration": 2000
        }
    },
    "default_settings": {
        "auto_start": false,
        "speed_limit": 1500
    }
}
```

## 开发模板

### CMakeLists.txt模板
```cmake
# modules/device/src/Devices/[Category]/[DeviceName]/CMakeLists.txt
set(DEVICE_SOURCES
    Modern${DEVICE_NAME}.cpp
    ${DEVICE_NAME}CommandProvider.cpp
    ${DEVICE_NAME}Capabilities.cpp
)

set(DEVICE_HEADERS
    Modern${DEVICE_NAME}.h
    ${DEVICE_NAME}CommandProvider.h
    ${DEVICE_NAME}Capabilities.h
)

add_library(${DEVICE_NAME}Device ${DEVICE_SOURCES} ${DEVICE_HEADERS})

target_link_libraries(${DEVICE_NAME}Device
    LA::Device::Core
    LA::Communication
    Qt5::Core
)

target_include_directories(${DEVICE_NAME}Device PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
)
```

### 单元测试模板
```cpp
// tests/device/Test[DeviceName].cpp
class TestMotorController : public QObject {
    Q_OBJECT
    
private slots:
    void initTestCase();
    void testCommandProvider();
    void testBasicOperations();
    void testSpeedControl();
    void testErrorHandling();
    void cleanupTestCase();
    
private:
    std::unique_ptr<ModernMotorController> m_device;
};

void TestMotorController::testBasicOperations() {
    QVERIFY(m_device->initialize());
    QVERIFY(m_device->startMotor());
    QVERIFY(m_device->setSpeed(1500));
    QCOMPARE(m_device->getCurrentSpeed(), 1500);
    QVERIFY(m_device->stopMotor());
}
```

## 最佳实践

### 1. 命令命名约定
```cpp
// 统一的动词前缀
"start_*"       // 启动类命令
"stop_*"        // 停止类命令  
"set_*"         // 设置类命令
"get_*"         // 获取类命令
"query_*"       // 查询类命令
"calibrate_*"   // 校准类命令
"reset_*"       // 重置类命令
```

### 2. 错误处理
```cpp
class RobustMotorController : public ModernMotorController {
protected:
    bool executeCommand(const QString& commandId, 
                       const QVariantMap& params = {}) override {
        try {
            return ModernMotorController::executeCommand(commandId, params);
        } catch (const DeviceException& e) {
            qWarning() << "Motor command failed:" << commandId << e.what();
            emit errorOccurred(e.errorCode(), e.what());
            return false;
        }
    }
};
```

### 3. 状态管理
```cpp
class StatefulMotorController : public ModernMotorController {
    Q_OBJECT
    
public:
    enum State { Stopped, Starting, Running, Stopping, Error };
    
    State getCurrentState() const { return m_state; }
    
public slots:
    void updateState() {
        auto status = getDeviceStatus();
        State newState = parseStateFromStatus(status);
        if (newState != m_state) {
            m_state = newState;
            emit stateChanged(m_state);
        }
    }
    
signals:
    void stateChanged(State newState);
    
private:
    State m_state = Stopped;
};
```

### 4. 配置驱动设计
```cpp
// 支持多种型号的配置驱动方法
class ConfigurableMotorController : public ModernMotorController {
public:
    ConfigurableMotorController(const QString& deviceId, const QString& model) 
        : ModernMotorController(deviceId, model) {
        loadConfiguration(model);
    }
    
private:
    void loadConfiguration(const QString& model) {
        auto config = DeviceConfigManager::instance().getDeviceConfig("MotorController", model);
        m_maxSpeed = config["max_speed"].toInt();
        m_acceleration = config["acceleration"].toInt();
        m_autoStart = config["default_settings"]["auto_start"].toBool();
    }
    
    int m_maxSpeed;
    int m_acceleration;  
    bool m_autoStart;
};
```

## 调试和测试

### 设备模拟器
```cpp
class MotorSimulator : public IDeviceSimulator {
public:
    QByteArray simulateResponse(const QByteArray& command) override {
        // 根据命令返回模拟响应
        auto commandId = parseCommandId(command);
        if (commandId == 0x20) { // get_speed
            return buildSpeedResponse(m_currentSpeed);
        }
        return buildAckResponse();
    }
    
private:
    int m_currentSpeed = 0;
};
```

### 集成测试
```cpp
TEST(DeviceIntegrationTest, MotorControllerEndToEnd) {
    // 创建真实设备或模拟器
    auto device = DeviceRegistry::instance().createDevice("MotorController", "test_motor");
    
    // 测试完整操作流程
    ASSERT_TRUE(device->initialize());
    ASSERT_TRUE(device->startMotor());
    QTest::qWait(100);  // 等待启动
    ASSERT_EQ(device->getCurrentState(), MotorController::Running);
    
    ASSERT_TRUE(device->setSpeed(2000));
    QTest::qWait(500);  // 等待加速
    ASSERT_EQ(device->getCurrentSpeed(), 2000);
    
    ASSERT_TRUE(device->stopMotor());
    device->shutdown();
}
```

## 部署清单

开发完成后，确保以下文件就位：

### 源码文件
- [ ] `Modern[DeviceName].h/.cpp` - 设备实现
- [ ] `[DeviceName]CommandProvider.h/.cpp` - 指令提供器  
- [ ] `[DeviceName]Capabilities.h/.cpp` - 设备能力组件
- [ ] `CMakeLists.txt` - 构建配置

### 配置文件
- [ ] `config/devices/[device_name].json` - 设备配置
- [ ] `config/ui/[device_name]_panel.json` - UI面板配置

### 测试文件
- [ ] `tests/device/Test[DeviceName].cpp` - 单元测试
- [ ] `tests/integration/[DeviceName]Integration.cpp` - 集成测试

### 文档文件
- [ ] 设备使用说明文档
- [ ] API接口文档  

---

**开发原则**: 遵循四层架构，保持单一职责，配置驱动优于硬编码，测试驱动开发。