#include "LA/DeviceManagement/Registration/RegistrationManager.h"
#include <QDebug>
#include <QMutexLocker>
#include <QThread>

namespace LA {
namespace DeviceManagement {
namespace Registration {

RegistrationManager::RegistrationManager(QObject* parent)
    : QObject(parent)
    , m_cleanupTimer(new QTimer(this))
{
    // 设置清理定时器
    m_cleanupTimer->setInterval(CLEANUP_INTERVAL_MS);
    connect(m_cleanupTimer, &QTimer::timeout, this, &RegistrationManager::onCleanupTimer);
    m_cleanupTimer->start();

    qDebug() << "RegistrationManager: Linus式注册管理器已初始化";
}

RegistrationManager::~RegistrationManager() {
    QMutexLocker locker(&m_mutex);
    qDebug() << "RegistrationManager: 析构，清理" << m_deviceToPort.size() << "个注册关系";
}

void RegistrationManager::setDeviceRegistry(std::shared_ptr<IDeviceRegistry> deviceRegistry) {
    m_deviceRegistry = deviceRegistry;
    qDebug() << "RegistrationManager: 设备注册表已注入";
}

void RegistrationManager::setPortManager(std::shared_ptr<IPortManager> portManager) {
    m_portManager = portManager;
    qDebug() << "RegistrationManager: 端口管理器已注入";
}

RegistrationResult RegistrationManager::registerDevicePort(
    const DeviceInfo& deviceInfo, 
    const PortInfo& portInfo) 
{
    QMutexLocker locker(&m_mutex);
    
    RegistrationResult result;
    result.deviceId = deviceInfo.deviceId;
    result.portId = portInfo.portName;

    // Linus: "验证参数，失败要快速明确"
    QString validationError = validateRegistrationParams(deviceInfo, portInfo);
    if (!validationError.isEmpty()) {
        result.errorMessage = validationError;
        emit registrationError(result.deviceId, validationError);
        return result;
    }

    // 检查设备是否已注册
    if (isDeviceRegistered(deviceInfo.deviceId)) {
        result.errorMessage = QString("设备 %1 已经注册").arg(deviceInfo.deviceId);
        emit registrationError(result.deviceId, result.errorMessage);
        return result;
    }

    // 执行注册
    if (performRegistration(deviceInfo.deviceId, portInfo.portName)) {
        result.success = true;
        m_registrationCount++;
        
        qDebug() << "RegistrationManager: 成功注册设备" << deviceInfo.deviceId 
                 << "到端口" << portInfo.portName;
        
        emit deviceRegistered(deviceInfo.deviceId, portInfo.portName);
    } else {
        result.errorMessage = "注册操作失败";
        emit registrationError(result.deviceId, result.errorMessage);
    }

    return result;
}

bool RegistrationManager::unregisterDevice(const QString& deviceId) {
    QMutexLocker locker(&m_mutex);

    if (!isDeviceRegistered(deviceId)) {
        qWarning() << "RegistrationManager: 设备" << deviceId << "未注册，无法注销";
        return false;
    }

    QString portId = performUnregistration(deviceId);
    if (!portId.isEmpty()) {
        m_unregistrationCount++;
        
        qDebug() << "RegistrationManager: 成功注销设备" << deviceId << "从端口" << portId;
        emit deviceUnregistered(deviceId, portId);
        return true;
    }

    return false;
}

int RegistrationManager::unregisterPort(const QString& portId) {
    QMutexLocker locker(&m_mutex);

    QStringList devicesOnPort = getPortDevices(portId);
    int unregisteredCount = 0;

    for (const QString& deviceId : devicesOnPort) {
        if (performUnregistration(deviceId) == portId) {
            emit deviceUnregistered(deviceId, portId);
            unregisteredCount++;
        }
    }

    if (unregisteredCount > 0) {
        qDebug() << "RegistrationManager: 从端口" << portId << "注销了" << unregisteredCount << "个设备";
    }

    return unregisteredCount;
}

bool RegistrationManager::synchronizeLifecycle(
    const QString& deviceId, 
    const QString& portId,
    ConnectionStatus targetState) 
{
    QMutexLocker locker(&m_mutex);

    // 验证设备端口映射关系
    if (getDevicePort(deviceId) != portId) {
        qWarning() << "RegistrationManager: 设备" << deviceId << "与端口" << portId << "未建立映射关系";
        return false;
    }

    // Linus: "状态同步要么全成功，要么全失败"
    bool deviceResult = true;
    bool portResult = true;

    // 这里应该调用具体的设备和端口管理器来同步状态
    // 目前先更新本地状态跟踪
    m_deviceStates[deviceId] = targetState;

    bool success = deviceResult && portResult;
    if (success) {
        qDebug() << "RegistrationManager: 成功同步设备" << deviceId << "和端口" << portId 
                 << "到状态" << static_cast<int>(targetState);
        emit lifecycleStateChanged(deviceId, targetState);
    } else {
        qWarning() << "RegistrationManager: 同步设备" << deviceId << "和端口" << portId << "状态失败";
    }

    return success;
}

bool RegistrationManager::openDevice(const QString& deviceId) {
    QString portId = getDevicePort(deviceId);
    if (portId.isEmpty()) {
        qWarning() << "RegistrationManager: 设备" << deviceId << "未注册，无法开启";
        return false;
    }

    return synchronizeLifecycle(deviceId, portId, ConnectionStatus::Connected);
}

bool RegistrationManager::closeDevice(const QString& deviceId) {
    QString portId = getDevicePort(deviceId);
    if (portId.isEmpty()) {
        qWarning() << "RegistrationManager: 设备" << deviceId << "未注册，无法关闭";
        return false;
    }

    return synchronizeLifecycle(deviceId, portId, ConnectionStatus::Disconnected);
}

QString RegistrationManager::getDevicePort(const QString& deviceId) const {
    QMutexLocker locker(&m_mutex);
    return m_deviceToPort.value(deviceId, QString());
}

QStringList RegistrationManager::getPortDevices(const QString& portId) const {
    QMutexLocker locker(&m_mutex);
    return m_portToDevices.value(portId, QStringList());
}

bool RegistrationManager::isDeviceRegistered(const QString& deviceId) const {
    QMutexLocker locker(&m_mutex);
    return m_deviceToPort.contains(deviceId);
}

bool RegistrationManager::isPortOccupied(const QString& portId) const {
    QMutexLocker locker(&m_mutex);
    return m_portToDevices.contains(portId) && !m_portToDevices[portId].isEmpty();
}

QMap<QString, QString> RegistrationManager::getAllMappings() const {
    QMutexLocker locker(&m_mutex);
    return m_deviceToPort;
}

int RegistrationManager::getRegisteredDeviceCount() const {
    QMutexLocker locker(&m_mutex);
    return m_deviceToPort.size();
}

int RegistrationManager::getOccupiedPortCount() const {
    QMutexLocker locker(&m_mutex);
    return m_portToDevices.size();
}

int RegistrationManager::batchRegister(const QList<QPair<DeviceInfo, PortInfo>>& devicePortPairs) {
    int successCount = 0;
    
    for (const auto& pair : devicePortPairs) {
        RegistrationResult result = registerDevicePort(pair.first, pair.second);
        if (result.success) {
            successCount++;
        }
    }

    qDebug() << "RegistrationManager: 批量注册完成，成功" << successCount << "个，总数" << devicePortPairs.size();
    return successCount;
}

int RegistrationManager::clearAllRegistrations() {
    QMutexLocker locker(&m_mutex);

    int clearedCount = m_deviceToPort.size();
    
    // 发送注销事件
    for (auto it = m_deviceToPort.constBegin(); it != m_deviceToPort.constEnd(); ++it) {
        emit deviceUnregistered(it.key(), it.value());
    }

    // 清空所有映射
    m_deviceToPort.clear();
    m_portToDevices.clear();
    m_deviceStates.clear();

    qDebug() << "RegistrationManager: 清除了" << clearedCount << "个注册关系";
    return clearedCount;
}

bool RegistrationManager::validateConsistency() const {
    QMutexLocker locker(&m_mutex);

    // 验证双向映射一致性
    for (auto it = m_deviceToPort.constBegin(); it != m_deviceToPort.constEnd(); ++it) {
        const QString& deviceId = it.key();
        const QString& portId = it.value();
        
        if (!m_portToDevices.contains(portId) || !m_portToDevices[portId].contains(deviceId)) {
            qWarning() << "RegistrationManager: 发现不一致映射 - 设备" << deviceId << "到端口" << portId;
            return false;
        }
    }

    for (auto it = m_portToDevices.constBegin(); it != m_portToDevices.constEnd(); ++it) {
        const QString& portId = it.key();
        const QStringList& deviceList = it.value();
        
        for (const QString& deviceId : deviceList) {
            if (!m_deviceToPort.contains(deviceId) || m_deviceToPort[deviceId] != portId) {
                qWarning() << "RegistrationManager: 发现不一致映射 - 端口" << portId << "到设备" << deviceId;
                return false;
            }
        }
    }

    return true;
}

void RegistrationManager::onCleanupTimer() {
    QMutexLocker locker(&m_mutex);
    cleanupInvalidMappings();
}

QString RegistrationManager::validateRegistrationParams(
    const DeviceInfo& deviceInfo, 
    const PortInfo& portInfo) const 
{
    if (deviceInfo.deviceId.isEmpty()) {
        return "设备ID不能为空";
    }
    
    if (portInfo.portName.isEmpty()) {
        return "端口名称不能为空";
    }

    if (deviceInfo.deviceType.isEmpty()) {
        return "设备类型不能为空";
    }

    return QString();  // 验证通过
}

bool RegistrationManager::performRegistration(const QString& deviceId, const QString& portId) {
    try {
        updateMappings(deviceId, portId, true);
        m_deviceStates[deviceId] = ConnectionStatus::Disconnected;  // 初始状态
        return true;
    } catch (...) {
        qWarning() << "RegistrationManager: 执行注册操作时发生异常";
        return false;
    }
}

QString RegistrationManager::performUnregistration(const QString& deviceId) {
    QString portId = m_deviceToPort.value(deviceId);
    if (!portId.isEmpty()) {
        updateMappings(deviceId, portId, false);
        m_deviceStates.remove(deviceId);
    }
    return portId;
}

void RegistrationManager::updateMappings(const QString& deviceId, const QString& portId, bool isRegistering) {
    if (isRegistering) {
        // 注册：添加映射关系
        m_deviceToPort[deviceId] = portId;
        
        if (!m_portToDevices.contains(portId)) {
            m_portToDevices[portId] = QStringList();
        }
        if (!m_portToDevices[portId].contains(deviceId)) {
            m_portToDevices[portId].append(deviceId);
        }
    } else {
        // 注销：移除映射关系
        m_deviceToPort.remove(deviceId);
        
        if (m_portToDevices.contains(portId)) {
            m_portToDevices[portId].removeAll(deviceId);
            if (m_portToDevices[portId].isEmpty()) {
                m_portToDevices.remove(portId);
            }
        }
    }
}

void RegistrationManager::cleanupInvalidMappings() {
    // 简单的清理逻辑 - 可以扩展为更复杂的验证
    if (!validateConsistency()) {
        qWarning() << "RegistrationManager: 检测到映射不一致，需要手动修复";
    }
}

} // namespace Registration
} // namespace DeviceManagement
} // namespace LA

#include "RegistrationManager.moc"