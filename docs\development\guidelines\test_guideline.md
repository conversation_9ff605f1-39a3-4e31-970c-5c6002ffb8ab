# test_guideline

## 1. 内容与范围

- 内容：
	- 统一测试原则
	- 测试类型与要点
	- 目录结构
- 范围：整个项目

## 2. 测试原则

## 3. 目录与产物放置约定

把测试相关文件与产物按下面规则放置，便于 CI、subagent 与人工审查。
tests编译目录在build/test/
```
project/
├─ src/...
├─ .claude/agents/        # subagent 定义（必须版本化）
│  └─ ui-tester.md
├─ tests/
│  ├─ unit/                    # 单元测试（co-located 或集中）
│  ├─ integration/             # 模块间交互测试
│  ├─ algorithm/               # 算法精度、边界条件测试
│  ├─ data/                    # 数据清洗、ETL 流程测试
│  └─ ui/                      # Squish / QTest / Spix 脚本（录制/源码）
├─ build/                #
│  ├─ debug/                    #
│  ├─ release/                    # 
│  └─ test/                      # Squish / QTest / Spix 脚本（录制/源码）
├─ tools/                # 
│  ├─ run_unit_tests.sh
│  ├─ run_integration.sh
│  └─ run_ui_and_agent.sh
├─ artifacts/            # 运行时产物（gitignore）
│  └─ ui_test/<run-id>/
│     ├─ control_states.json
│     ├─ screenshot.png
│     └─ agent_input.json
└─ docs.md  
|    |-development
		|-guidelines
			|-test_guideline # 本份 guideline
```
		

## 4. 测试类型与要点（每种应在 guideline 中单独列出）

1. **Unit Tests（单元）**
    
    - 目的：验证函数/类行为、边界条件、异常路径。
        
    - 放置：`tests/unit/` 或 `src/moduleX/tests/`（co-located）。
        
    - 构建/产物：快速、Debug 构建输出至 `build/tests/bin`。
        
    - 运行频率：本地每次提交、CI 每次 PR。
        
    - subagent：**unit-tester** —— 负责解析测试输出（JUnit/pytest）并生成 summary。
        
2. **Integration Tests（集成）**
    
    - 目的：模块间接口和协作。
        
    - 放置：`tests/integration/`。
        
    - 环境：用 docker-compose 或 mock 服务隔离依赖。
        
    - 运行频率：PR 或 nightly，按变更区域触发。
        
    - subagent：**integration-tester** —— 分析日志与交互 trace。
        
3. **Algorithm Tests（算法/数值）**
    
    - 目的：精度、数值稳定性、边界/极端输入。
        
    - 放置：`tests/algorithm/`，带 baseline 数据（`tests/algorithm/baselines/`）。
        
    - 典型断言：相对/绝对误差阈值、收敛次数、时间复杂度指标。
        
    - 运行频率：PR（影响算法代码）+ nightly long-run (回归/随机化输入)。
        
    - subagent：**algorithm-tester** —— 比较输出与 baseline、计算统计指标、检测漂移。
        
4. **Data Tests（数据处理/ETL）**
    
    - 目的：数据完整性、schema 兼容性、异常值处理、隐私合规（脱敏）。
        
    - 放置：`tests/data/`，包含合成/匿名化样本。**不放真实 PII 数据**。
        
    - 策略：使用小样本单元、再用更大数据集做 nightly。
        
    - subagent：**data-tester** —— 校验 schema、记录聚合统计与变更。
        
5. **UI Tests（视觉/交互）**
    
    - 目的：界面元素可见性、文本、布局、视觉回归。
        
    - 放置：`tests/ui/`（Squish 脚本、reference screenshots）。
        
    - 策略：控件状态优先（fast），截图 diff（慢）作为 nightly/regression。
        
    - subagent：**qt-ui-tester**（详见前文）。
        
6. **E2E / System / Performance**
    
    - 目的：真实用户路径与性能 SLA。
        
    - 放置：`tests/e2e/`、`tests/perf/`（可能需独立基础设施）。
        
    - 运行频率：nightly 或 release gating。
        
    - subagents：**e2e-tester**、**perf-tester**（聚合 metrics）。
        

---

## 5. Subagent 规划原则（跨类型通用）

- 每类测试**一个或多个专用 subagent**（职责单一）。
    
- subagent 的 frontmatter 明确 `tools`、禁止 Network、禁止写入源码。
    
- subagent 输出统一 schema（见下一节）。
    
- 由**orchestrator agent**（主 agent）调度子 agent：例如 `test-orchestrator` 接受指令（哪些测试、并行度、git_ref），并收集各 subagent 报告合成最终报告。
    
- 对自动修复持保守态度：subagent 仅提出建议 patch，人工审阅并在 CI 中通过审批后合并。
    

---

## 6. 统一的 Test Report JSON Schema（所有 subagent 输出遵循）

`{   "run_id": "uuid",   "git_ref": "commit-hash",   "agent": "algorithm-tester",   "agent_version": "v1.0.0",   "start_time": "2025-08-20T12:00:00Z",   "end_time": "2025-08-20T12:05:00Z",   "test_type": "algorithm|unit|integration|data|ui|e2e|perf",   "status": "pass|warn|fail",   "metrics": {     "passed": 10,     "failed": 1,     "duration_sec": 300,     "error_rate": 0.09   },   "issues": [     {       "id": "ISSUE-001",       "severity": "critical|major|minor",       "component": "moduleX",       "test": "test_edge_case_1",       "message": "Absolute error 0.15 exceeds threshold 0.001",       "evidence": "path/to/output.csv or snippet"     }   ],   "artifacts": {     "control_states": "artifacts/.../control_states.json",     "screenshot": "artifacts/.../shot.png",     "log": "artifacts/.../run.log"   },   "next_actions": [     "Run algorithm debug harness with input X",     "Check numeric type promotion in moduleY"   ] }`

> 说明：所有 subagent 必须写 `artifacts` 引用，CI 会收集这些路径做归档。

---

## 7. CI 策略（并行 + 分层）

- **快速层（on PR）**：run unit tests + smoke integration + algorithm fast checks + UI control-state checks（无截图 diff）。目标：快速反馈 (< 10-20min)。
    
- **重度层（nightly / release）**：full integration + screenshot diff + perf + long-running randomized algorithm runs。目标：回归防线。
    
- **并行化**：把不同 test_type 映射为不同 job，独立 runner（例如：`unit-job`, `algorithm-job`, `ui-job`）。Orchestrator 收集 job artifacts 并合成最终报告。
    
- **资源**：算法/性能测试用 GPU/large CPU runner；UI 测试用桌面 runner（需要 GUI / X server / Windows runner）或使用 headless Xvfb。
    

示例 GitHub Actions jobs（简化）：

`jobs:   unit:     runs-on: ubuntu-latest     steps: [...]   algorithm:     runs-on: ubuntu-latest     steps: [...]   ui:     runs-on: windows-latest   # 或 self-hosted GUI runner     steps: [...]   orchestrator:     needs: [unit, algorithm, ui]     runs-on: ubuntu-latest     steps:       - name: aggregate reports         run: tools/aggregate_reports.sh artifacts/test_runs/*`

---

# 7. 测试数据与隐私（严肃规则）

- **不要在 repo 中存放真实 PII**。测试数据必须是合成或脱敏。
    
- 对于使用真实/敏感数据的测试，仅在受控内部环境执行，并用短期凭证注入（由 CI secret 管理，不由 subagent 访问）。
    
- 数据版本控制：把 data fixtures 放在 `tests/data/fixtures/vN/` 并记录生成脚本 `tools/generate_fixtures.py`，方便再现。
    
- 数据大小策略：unit 用小样本，nightly 用更完整样本，避免 CI 成本爆炸。
    

---

# 8. 失败处理与优先级（如何处理 subagent 报告）

- 报告中 `severity: critical` 自动 fail gate（阻止合并）；`major` 触发 review 阶段；`minor` 记录为 tech debt。
    
- 不可忽视 flaky：对 flaky test 做标注 `flaky=true` 并安排稳定化计划（不在 PR gate 中自动 fail）。
    
- 每个失败都应包含 `reproduction steps` 和 `minimal test input`（由 subagent 或 test harness 提供），便于 dev 本地复现。