@echo off
REM 列出所有可用的设备测试配置

echo ===============================================
echo        Available Device Test Configurations
echo ===============================================

set CONFIG_DIR=%~dp0..\tests\config\devices

echo.
echo Device configurations found:
echo.

for %%f in ("%CONFIG_DIR%\*_test_config.json") do (
    set CONFIG_FILE=%%~nf
    set DEVICE_TYPE=!CONFIG_FILE:_test_config=!
    
    echo [DEVICE] !DEVICE_TYPE!
    
    REM 提取设备名称和描述
    for /f "tokens=2 delims=:" %%i in ('findstr "name" "%%f" ^| head -1') do (
        set DEVICE_NAME=%%i
        set DEVICE_NAME=!DEVICE_NAME: "=!
        set DEVICE_NAME=!DEVICE_NAME:"=!
        set DEVICE_NAME=!DEVICE_NAME:,=!
        echo   Name: !DEVICE_NAME!
    )
    
    for /f "tokens=2 delims=:" %%i in ('findstr "category" "%%f"') do (
        set DEVICE_CATEGORY=%%i
        set DEVICE_CATEGORY=!DEVICE_CATEGORY: "=!
        set DEVICE_CATEGORY=!DEVICE_CATEGORY:"=!
        set DEVICE_CATEGORY=!DEVICE_CATEGORY:,=!
        echo   Category: !DEVICE_CATEGORY!
    )
    
    echo   Config: %%~nxf
    echo.
)

echo ===============================================
echo Usage Examples:
echo   tools/run_device_command_tests.bat sprm
echo   tools/run_device_command_tests.bat motor  
echo   tools/run_device_command_tests.bat lens
echo   tools/run_all_device_tests.bat
echo ===============================================