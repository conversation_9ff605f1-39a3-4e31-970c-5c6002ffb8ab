#pragma once

#include "IDeviceProber.h"
#include <QSerialPort>
#include <QTcpSocket>
#include <QTimer>
#include <QMap>
#include <QMutex>

namespace LA {
namespace DeviceManagement {
namespace Matching {

/**
 * @brief 标准设备探测器 - IDeviceProber的具体实现
 * 
 * Linus: "只做一件事：发送探测指令并接收响应"
 * 支持串口和网络设备的探测
 */
class StandardDeviceProber : public IDeviceProber {
public:
    StandardDeviceProber() = default;
    virtual ~StandardDeviceProber() = default;

    // ====== IDeviceProber接口实现 ======
    ProbeResult probeDevice(const QString& portName, const DeviceProbeConfig& config) override;
    bool sendProbeCommand(const QString& portName, const QByteArray& command, int timeoutMs = 1000) override;
    QByteArray receiveProbeResponse(const QString& portName, int timeoutMs = 1000) override;
    void cancelProbe(const QString& portName) override;

private:
    // ====== 串口探测 ======
    ProbeResult probeSerialDevice(const QString& portName, const DeviceProbeConfig& config);
    bool openSerialPort(const QString& portName, QSerialPort& port);
    void configureSerialPort(QSerialPort& port);
    
    // ====== 网络探测 ======
    ProbeResult probeNetworkDevice(const QString& address, int port, const DeviceProbeConfig& config);
    bool connectToNetworkDevice(const QString& address, int port, QTcpSocket& socket, int timeoutMs);
    
    // ====== 通用探测指令 ======
    QList<QByteArray> getStandardProbeCommands(const QStringList& deviceTypes);
    QByteArray createUniversalProbeCommand();
    
    // ====== 响应验证 ======
    bool isValidResponse(const QByteArray& response);
    int calculateResponseTime(qint64 startTime);
    
    // ====== 状态管理 ======
    QMap<QString, bool> m_cancelFlags;  // 取消标志
    QMutex m_mutex;                     // 线程安全
};

} // namespace Matching
} // namespace DeviceManagement
} // namespace LA