#pragma once

#include "IRegistrationManager.h"
#include <QObject>
#include <QMutex>
#include <QTimer>
#include <memory>

// 前向声明
class IDeviceRegistry;
class IPortManager;

namespace LA {
namespace DeviceManagement {
namespace Registration {

/**
 * @brief 注册管理器具体实现 - Linus式组合架构
 * 
 * Linus: "管理设备-端口关系，通过依赖注入避免职责混淆"
 * 
 * 设计原则:
 * - 单一职责：只管注册关系和生命周期，不做设备/端口的具体操作
 * - 依赖注入：通过DeviceRegistry和PortManager操作具体对象
 * - 线程安全：支持多线程环境下的注册操作
 * - 状态一致性：确保设备和端口状态同步
 */
class RegistrationManager : public QObject, public IRegistrationManager {
    Q_OBJECT

public:
    explicit RegistrationManager(QObject* parent = nullptr);
    virtual ~RegistrationManager();

    // ====== 依赖注入 - Linus式组合 ======
    void setDeviceRegistry(std::shared_ptr<IDeviceRegistry> deviceRegistry);
    void setPortManager(std::shared_ptr<IPortManager> portManager);

    // ====== IRegistrationManager接口实现 ======
    RegistrationResult registerDevicePort(
        const DeviceInfo& deviceInfo, 
        const PortInfo& portInfo
    ) override;

    bool unregisterDevice(const QString& deviceId) override;
    int unregisterPort(const QString& portId) override;

    bool synchronizeLifecycle(
        const QString& deviceId, 
        const QString& portId,
        ConnectionStatus targetState
    ) override;

    bool openDevice(const QString& deviceId) override;
    bool closeDevice(const QString& deviceId) override;

    // ====== 查询接口实现 ======
    QString getDevicePort(const QString& deviceId) const override;
    QStringList getPortDevices(const QString& portId) const override;
    bool isDeviceRegistered(const QString& deviceId) const override;
    bool isPortOccupied(const QString& portId) const override;
    QMap<QString, QString> getAllMappings() const override;

    int getRegisteredDeviceCount() const override;
    int getOccupiedPortCount() const override;

    // ====== 扩展功能 ======
    
    /**
     * @brief 批量注册设备端口对
     * @param devicePortPairs 设备端口对列表
     * @return 成功注册的数量
     */
    int batchRegister(const QList<QPair<DeviceInfo, PortInfo>>& devicePortPairs);

    /**
     * @brief 清除所有注册
     * @return 清除的注册数量
     */
    int clearAllRegistrations();

    /**
     * @brief 验证注册数据一致性
     * @return 是否一致
     */
    bool validateConsistency() const;

signals:
    // ====== 注册事件信号 ======
    void deviceRegistered(const QString& deviceId, const QString& portId);
    void deviceUnregistered(const QString& deviceId, const QString& portId);
    void lifecycleStateChanged(const QString& deviceId, ConnectionStatus newState);
    void registrationError(const QString& deviceId, const QString& error);

private slots:
    void onCleanupTimer();

private:
    // ====== Linus式实现 - 职责分离 ======
    
    /**
     * @brief 验证注册参数
     * @param deviceInfo 设备信息
     * @param portInfo 端口信息
     * @return 验证结果
     */
    QString validateRegistrationParams(const DeviceInfo& deviceInfo, const PortInfo& portInfo) const;

    /**
     * @brief 执行实际注册操作
     * @param deviceId 设备ID
     * @param portId 端口ID
     * @return 是否成功
     */
    bool performRegistration(const QString& deviceId, const QString& portId);

    /**
     * @brief 执行实际注销操作
     * @param deviceId 设备ID
     * @return 被注销的端口ID
     */
    QString performUnregistration(const QString& deviceId);

    /**
     * @brief 更新映射关系
     * @param deviceId 设备ID
     * @param portId 端口ID
     * @param isRegistering 是注册还是注销
     */
    void updateMappings(const QString& deviceId, const QString& portId, bool isRegistering);

    /**
     * @brief 清理无效的映射关系
     */
    void cleanupInvalidMappings();

    // ====== 依赖注入的组件 ======
    std::shared_ptr<IDeviceRegistry> m_deviceRegistry;
    std::shared_ptr<IPortManager> m_portManager;

    // ====== 核心数据结构 - 基于"好品味"原则 ======
    QMap<QString, QString> m_deviceToPort;    // 设备ID → 端口ID
    QMap<QString, QStringList> m_portToDevices;  // 端口ID → 设备ID列表
    QMap<QString, ConnectionStatus> m_deviceStates;  // 设备状态跟踪

    // ====== 线程安全 ======
    mutable QMutex m_mutex;

    // ====== 清理和维护 ======
    QTimer* m_cleanupTimer;
    static constexpr int CLEANUP_INTERVAL_MS = 30000;  // 30秒清理一次

    // ====== 统计信息 ======
    mutable int m_registrationCount = 0;
    mutable int m_unregistrationCount = 0;
};

} // namespace Registration
} // namespace DeviceManagement
} // namespace LA