@echo off
echo Testing LA Application...

set BUILD_DIR=build\Debug
set BIN_DIR=%BUILD_DIR%\bin
set APP_NAME=CSPC_LA_function.exe

echo Checking application file...
if exist "%BIN_DIR%\%APP_NAME%" (
    echo [OK] Application exists: %APP_NAME%
) else (
    echo [ERROR] Application not found: %BIN_DIR%\%APP_NAME%
    exit /b 1
)

echo.
echo Checking Qt libraries...
if exist "%BIN_DIR%\Qt5Core.dll" echo [OK] Qt5Core.dll
if exist "%BIN_DIR%\Qt5Widgets.dll" echo [OK] Qt5Widgets.dll
if exist "%BIN_DIR%\Qt5Gui.dll" echo [OK] Qt5Gui.dll

echo.
echo Checking custom libraries...
if exist "%BIN_DIR%\libLA_editview.dll" echo [OK] libLA_editview.dll
if exist "%BIN_DIR%\libLA_rightsidebar.dll" echo [OK] libLA_rightsidebar.dll
if exist "%BIN_DIR%\libLA_sidebar.dll" echo [OK] libLA_sidebar.dll

echo.
echo Checking platform plugins...
if exist "%BIN_DIR%\platforms\qwindows.dll" echo [OK] qwindows.dll

echo.
echo Starting application test...
cd /d "%BIN_DIR%"
echo Running: %APP_NAME%
start "" "%APP_NAME%"

echo.
echo Test completed! Check if the application window opened successfully.
echo Application location: %BIN_DIR%\%APP_NAME%
