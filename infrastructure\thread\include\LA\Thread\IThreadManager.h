#ifndef LA_ITHREADMANAGER_H
#define LA_ITHREADMANAGER_H

#include "ThreadTypes.h"
#include <QObject>
#include <QStringList>

namespace LA {
namespace Thread {

class IThreadPool;
class ICommunicationThread;

/**
 * @brief 线程管理器接口
 * 
 * 统一管理应用程序中的所有线程，提供线程创建、启动、停止、监控等功能。
 * 采用单例模式，确保全局唯一的线程管理实例。
 */
class IThreadManager : public QObject
{
    Q_OBJECT

public:
    virtual ~IThreadManager() = default;

    // 单例获取
    static IThreadManager* instance();

    // 基础线程管理
    virtual QThread* createThread(const QString& name, QObject* worker = nullptr) = 0;
    virtual bool startThread(const QString& name) = 0;
    virtual bool stopThread(const QString& name, int timeoutMs = 5000) = 0;
    virtual bool isThreadRunning(const QString& name) const = 0;
    virtual ThreadState getThreadState(const QString& name) const = 0;
    
    // 线程池管理
    virtual IThreadPool* getThreadPool() const = 0;
    virtual void setMaxThreadCount(int count) = 0;
    virtual int maxThreadCount() const = 0;
    virtual int activeThreadCount() const = 0;
    
    // 通信线程管理
    virtual ICommunicationThread* createCommunicationThread(const QString& deviceId, 
                                                           const ThreadConfig& config = ThreadConfig()) = 0;
    virtual ICommunicationThread* getCommunicationThread(const QString& deviceId) const = 0;
    virtual bool removeCommunicationThread(const QString& deviceId) = 0;
    
    // 线程监控
    virtual QStringList getActiveThreads() const = 0;
    virtual QStringList getCommunicationThreads() const = 0;
    virtual ThreadStatistics getThreadStatistics(const QString& name) const = 0;
    virtual QList<ThreadStatistics> getAllThreadStatistics() const = 0;
    
    // 全局控制
    virtual void stopAllThreads(int timeoutMs = 10000) = 0;
    virtual void pauseAllThreads() = 0;
    virtual void resumeAllThreads() = 0;
    
    // 配置管理
    virtual void setGlobalThreadConfig(const ThreadConfig& config) = 0;
    virtual ThreadConfig getGlobalThreadConfig() const = 0;

signals:
    // 线程状态信号
    void threadStarted(const QString& name);
    void threadFinished(const QString& name);
    void threadError(const QString& name, const QString& error);
    void threadStateChanged(const QString& name, ThreadState oldState, ThreadState newState);
    
    // 通信线程信号
    void communicationThreadCreated(const QString& deviceId);
    void communicationThreadRemoved(const QString& deviceId);
    
    // 统计信号
    void threadStatisticsUpdated(const QString& name, const ThreadStatistics& stats);
    void globalStatisticsUpdated(int totalThreads, int activeThreads, double avgCpuUsage);

protected:
    explicit IThreadManager(QObject* parent = nullptr) : QObject(parent) {}

private:
    Q_DISABLE_COPY(IThreadManager)
};

} // namespace Thread
} // namespace LA

#endif // LA_ITHREADMANAGER_H