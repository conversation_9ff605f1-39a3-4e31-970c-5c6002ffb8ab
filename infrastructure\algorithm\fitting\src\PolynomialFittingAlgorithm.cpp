#include "PolynomialFittingAlgorithm.h"
#include <QDebug>

// 临时的多项式拟合算法实现
namespace LA {
namespace Algorithm {
namespace Fitting {

PolynomialFittingAlgorithm::PolynomialFittingAlgorithm() {
    qDebug() << "PolynomialFittingAlgorithm created";
}

void PolynomialFittingAlgorithm::fit(const QVector<double>& x, const QVector<double>& y) {
    qDebug() << "PolynomialFittingAlgorithm::fit called with" << x.size() << "points";
    // TODO: 实现多项式拟合算法
}

} // namespace Fitting
} // namespace Algorithm
} // namespace LA