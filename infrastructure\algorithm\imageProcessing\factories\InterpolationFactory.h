#ifndef IMAGEPROCESSING_INTERPOLATIONFACTORY_H
#define IMAGEPROCESSING_INTERPOLATIONFACTORY_H

#include "../interfaces/IInterpolation.h"
#include "../interpolation/BilinearInterpolation.h"
#include <QDebug>

namespace ImageProcessing {

/**
 * @brief 插值算法工厂实现
 * 
 * 实现插值算法的创建和管理，遵循工厂模式
 */
class InterpolationFactory : public IInterpolationFactory {
public:
    /**
     * @brief 获取工厂单例实例
     * @return 工厂实例引用
     */
    static InterpolationFactory& getInstance();

    /**
     * @brief 析构函数
     */
    ~InterpolationFactory() override = default;

    // IInterpolationFactory接口实现
    std::unique_ptr<IInterpolation> createInterpolation(InterpolationType type) override;
    QVector<InterpolationType> getSupportedTypes() const override;
    QString getTypeDescription(InterpolationType type) const override;

    /**
     * @brief 注册自定义插值算法
     * @param type 插值类型
     * @param creator 创建函数
     */
    void registerInterpolation(InterpolationType type, 
                              std::function<std::unique_ptr<IInterpolation>()> creator);

    /**
     * @brief 检查是否支持指定类型
     * @param type 插值类型
     * @return true if supported, false otherwise
     */
    bool isSupported(InterpolationType type) const;

    /**
     * @brief 获取工厂版本
     * @return 版本字符串
     */
    QString getVersion() const;

private:
    /**
     * @brief 私有构造函数（单例模式）
     */
    InterpolationFactory();

    /**
     * @brief 初始化默认算法
     */
    void initializeDefaultAlgorithms();

    /**
     * @brief 记录调试信息
     * @param message 调试信息
     */
    void logDebug(const QString& message) const;

    // 算法创建函数映射
    QMap<InterpolationType, std::function<std::unique_ptr<IInterpolation>()>> creators_;
};

} // namespace ImageProcessing

#endif // IMAGEPROCESSING_INTERPOLATIONFACTORY_H
