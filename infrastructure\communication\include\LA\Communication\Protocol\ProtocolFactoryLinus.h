#pragma once

/**
 * @file ProtocolFactoryLinus.h
 * @brief Linus式协议工厂头文件 - 严格遵循IProtocolFactory接口
 * 
 * 遵循Linus设计原则：
 * - 实现IProtocolFactory接口
 * - 最小化功能，专注协议实例创建
 * - 基于标准设计模式的稳定实现
 * - Layer 2实现：依赖Layer 1，为Layer 3提供协议抽象
 */

#include "IProtocol.h"
#include <memory>

namespace LA {
namespace Communication {
namespace Protocol {

// 前向声明协议类
class TextProtocol;
class BinaryProtocol;

/**
 * @brief Linus式协议工厂实现
 * 
 * 严格遵循IProtocolFactory接口，只负责协议实例的创建
 */
class ProtocolFactoryLinus : public IProtocolFactory {
public:
    virtual ~ProtocolFactoryLinus() = default;
    
    /**
     * @brief 创建协议实例
     * @param type 协议类型
     * @return 协议实例的智能指针
     */
    std::shared_ptr<IProtocol> createProtocol(ProtocolType type) override;
    
    /**
     * @brief 检查是否支持指定协议类型
     * @param type 协议类型
     * @return 是否支持
     */
    bool supportsProtocol(ProtocolType type) const override;
};

/**
 * @brief 全局协议工厂实例获取函数
 * 
 * 提供单例模式的协议工厂访问
 * @return 协议工厂实例的智能指针
 */
std::shared_ptr<IProtocolFactory> getProtocolFactory();

} // namespace Protocol
} // namespace Communication
} // namespace LA