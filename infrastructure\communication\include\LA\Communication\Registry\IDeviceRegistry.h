#pragma once

/**
 * @file IDeviceRegistry.h
 * @brief 设备注册表接口定义
 * 
 * 设备注册表作为设备信息的**单一来源**，管理所有设备的属性、协议、指令等信息。
 * 遵循**单一职责原则**，专注于设备信息的注册、查询和管理。
 */

#include "../DataStructures/DeviceAttributes.h"
#include "../DataStructures/DeviceMappingTables.h"
#include <QString>
#include <QStringList>
#include <QSharedPointer>
#include <QObject>

namespace LA {
namespace Communication {
namespace Registry {

using namespace DataStructures;

/**
 * @brief 设备注册结果
 */
struct DeviceRegistrationResult {
    bool success;                   // 注册是否成功
    QString deviceId;              // 设备ID
    QString message;               // 结果消息
    
    DeviceRegistrationResult() : success(false) {}
    DeviceRegistrationResult(bool ok, const QString& id = "", const QString& msg = "")
        : success(ok), deviceId(id), message(msg) {}
};

/**
 * @brief 设备查询条件
 */
struct DeviceQuery {
    QString deviceId;               // 按设备ID查询
    DeviceType deviceType;          // 按设备类型查询
    ProtocolType protocolType;      // 按协议类型查询
    PortType portType;              // 按端口类型查询
    QString manufacturer;           // 按制造商查询
    QString model;                  // 按型号查询
    bool onlyConnected = false;     // 只查询已连接设备
    
    DeviceQuery() : deviceType(DeviceType::Unknown), 
                   protocolType(ProtocolType::Raw), 
                   portType(PortType::Unknown) {}
};

/**
 * @brief 设备注册表接口
 * 
 * 负责管理系统中所有设备的注册、查询和生命周期管理
 */
class IDeviceRegistry : public QObject
{
    Q_OBJECT

public:
    explicit IDeviceRegistry(QObject* parent = nullptr) : QObject(parent) {}
    virtual ~IDeviceRegistry() = default;

    // === 设备注册管理 ===
    
    /**
     * @brief 注册设备
     * @param deviceAttributes 设备属性信息
     * @return 注册结果
     */
    virtual DeviceRegistrationResult registerDevice(const DeviceAttributes& deviceAttributes) = 0;
    
    /**
     * @brief 注销设备
     * @param deviceId 设备ID
     * @return 是否成功
     */
    virtual bool unregisterDevice(const QString& deviceId) = 0;
    
    /**
     * @brief 更新设备属性
     * @param deviceId 设备ID
     * @param deviceAttributes 新的设备属性
     * @return 是否成功
     */
    virtual bool updateDevice(const QString& deviceId, const DeviceAttributes& deviceAttributes) = 0;
    
    // === 设备查询接口 ===
    
    /**
     * @brief 根据设备ID获取设备属性
     * @param deviceId 设备ID
     * @return 设备属性，如果不存在返回空智能指针
     */
    virtual QSharedPointer<DeviceAttributes> getDevice(const QString& deviceId) const = 0;
    
    /**
     * @brief 检查设备是否存在
     * @param deviceId 设备ID
     * @return 是否存在
     */
    virtual bool hasDevice(const QString& deviceId) const = 0;
    
    /**
     * @brief 查询设备列表
     * @param query 查询条件
     * @return 符合条件的设备ID列表
     */
    virtual QStringList queryDevices(const DeviceQuery& query) const = 0;
    
    /**
     * @brief 获取所有已注册设备ID
     * @return 设备ID列表
     */
    virtual QStringList getAllDeviceIds() const = 0;
    
    /**
     * @brief 获取指定类型的设备列表
     * @param deviceType 设备类型
     * @return 设备ID列表
     */
    virtual QStringList getDevicesByType(DeviceType deviceType) const = 0;
    
    // === 设备能力查询 ===
    
    /**
     * @brief 获取设备支持的指令列表
     * @param deviceId 设备ID
     * @return 指令定义列表
     */
    virtual QList<CommandDefinition> getDeviceCommands(const QString& deviceId) const = 0;
    
    /**
     * @brief 获取设备支持的协议列表
     * @param deviceId 设备ID  
     * @return 协议类型列表
     */
    virtual QList<ProtocolType> getDeviceProtocols(const QString& deviceId) const = 0;
    
    /**
     * @brief 获取设备支持的端口列表
     * @param deviceId 设备ID
     * @return 端口类型列表
     */
    virtual QList<PortType> getDevicePorts(const QString& deviceId) const = 0;
    
    /**
     * @brief 检查设备是否支持指定指令
     * @param deviceId 设备ID
     * @param commandId 指令ID
     * @return 是否支持
     */
    virtual bool supportsCommand(const QString& deviceId, const QString& commandId) const = 0;
    
    /**
     * @brief 检查设备是否支持指定协议
     * @param deviceId 设备ID
     * @param protocolType 协议类型
     * @return 是否支持
     */
    virtual bool supportsProtocol(const QString& deviceId, ProtocolType protocolType) const = 0;
    
    /**
     * @brief 检查设备是否支持指定端口
     * @param deviceId 设备ID
     * @param portType 端口类型
     * @return 是否支持
     */
    virtual bool supportsPort(const QString& deviceId, PortType portType) const = 0;
    
    // === 配置验证 ===
    
    /**
     * @brief 验证设备配置
     * @param deviceId 设备ID
     * @param protocolType 协议类型
     * @param portType 端口类型
     * @return 是否有效
     */
    virtual bool validateDeviceConfiguration(const QString& deviceId, 
                                           ProtocolType protocolType, 
                                           PortType portType) const = 0;
    
    /**
     * @brief 获取设备推荐配置
     * @param deviceId 设备ID
     * @return 推荐配置
     */
    virtual RecommendedConfig getRecommendedConfiguration(const QString& deviceId) const = 0;
    
    // === 设备状态管理 ===
    
    /**
     * @brief 更新设备状态
     * @param deviceId 设备ID
     * @param newState 新状态
     * @return 是否成功
     */
    virtual bool updateDeviceState(const QString& deviceId, DeviceState newState) = 0;
    
    /**
     * @brief 获取设备当前状态
     * @param deviceId 设备ID
     * @return 设备状态
     */
    virtual DeviceState getDeviceState(const QString& deviceId) const = 0;
    
    /**
     * @brief 获取在线设备列表
     * @return 在线设备ID列表
     */
    virtual QStringList getOnlineDevices() const = 0;
    
    /**
     * @brief 获取离线设备列表
     * @return 离线设备ID列表
     */
    virtual QStringList getOfflineDevices() const = 0;
    
    // === 依赖注入管理接口 ===
    
    /**
     * @brief 获取设备的通讯组件依赖配置
     * @param deviceId 设备ID
     * @return 依赖配置
     */
    virtual DeviceCommunicationDependency getDeviceDependency(const QString& deviceId) const = 0;
    
    /**
     * @brief 注入通讯组件到设备
     * @param deviceId 设备ID
     * @param connectionInstance 连接组件实例
     * @param protocolInstance 协议组件实例
     * @param commandInstance 指令组件实例
     * @return 是否成功
     */
    virtual bool injectCommunicationComponents(
        const QString& deviceId,
        QSharedPointer<QObject> connectionInstance = QSharedPointer<QObject>(),
        QSharedPointer<QObject> protocolInstance = QSharedPointer<QObject>(),
        QSharedPointer<QObject> commandInstance = QSharedPointer<QObject>()) = 0;
    
    /**
     * @brief 检查设备是否需要指定的服务组件
     * @param deviceId 设备ID
     * @param serviceId 服务ID
     * @return 是否需要
     */
    virtual bool deviceRequiresService(const QString& deviceId, const QString& serviceId) const = 0;
    
    /**
     * @brief 获取设备需要的服务列表
     * @param deviceId 设备ID
     * @return 服务ID列表
     */
    virtual QStringList getRequiredServices(const QString& deviceId) const = 0;
    
    /**
     * @brief 获取所有需要连接组件的设备ID列表
     * @return 设备ID列表
     */
    virtual QStringList getDevicesNeedingConnection() const = 0;
    
    /**
     * @brief 获取所有需要协议组件的设备ID列表
     * @return 设备ID列表
     */
    virtual QStringList getDevicesNeedingProtocol() const = 0;
    
    /**
     * @brief 获取所有需要指令系统的设备ID列表
     * @return 设备ID列表
     */
    virtual QStringList getDevicesNeedingCommands() const = 0;
    
    /**
     * @brief 获取所有独立设备（无通讯依赖）的设备ID列表
     * @return 设备ID列表
     */
    virtual QStringList getStandaloneDevices() const = 0;
    
    /**
     * @brief 获取设备的父设备ID（对于间接通讯设备）
     * @param deviceId 设备ID
     * @return 父设备ID，如果不存在返回空字符串
     */
    virtual QString getParentDeviceId(const QString& deviceId) const = 0;
    
    /**
     * @brief 获取设备的子设备ID列表（对于包含间接设备的父设备）
     * @param deviceId 设备ID
     * @return 子设备ID列表
     */
    virtual QStringList getChildDeviceIds(const QString& deviceId) const = 0;
    
    /**
     * @brief 检查两个设备是否有父子关系
     * @param parentId 父设备ID
     * @param childId 子设备ID
     * @return 是否有父子关系
     */
    virtual bool hasParentChildRelation(const QString& parentId, const QString& childId) const = 0;

    // === 统计信息 ===
    
    /**
     * @brief 获取注册设备总数
     * @return 设备数量
     */
    virtual int getDeviceCount() const = 0;
    
    /**
     * @brief 获取指定类型设备数量
     * @param deviceType 设备类型
     * @return 设备数量
     */
    virtual int getDeviceCountByType(DeviceType deviceType) const = 0;
    
    /**
     * @brief 获取注册表统计信息
     * @return 统计信息映射
     */
    virtual QVariantMap getRegistryStatistics() const = 0;

signals:
    /**
     * @brief 设备注册信号
     * @param deviceId 设备ID
     * @param deviceType 设备类型
     */
    void deviceRegistered(const QString& deviceId, DeviceType deviceType);
    
    /**
     * @brief 设备注销信号
     * @param deviceId 设备ID
     */
    void deviceUnregistered(const QString& deviceId);
    
    /**
     * @brief 设备状态改变信号
     * @param deviceId 设备ID
     * @param oldState 旧状态
     * @param newState 新状态
     */
    void deviceStateChanged(const QString& deviceId, DeviceState oldState, DeviceState newState);
    
    /**
     * @brief 设备属性更新信号
     * @param deviceId 设备ID
     */
    void deviceUpdated(const QString& deviceId);
    
    /**
     * @brief 设备连接状态改变信号
     * @param deviceId 设备ID
     * @param isOnline 是否在线
     */
    void deviceConnectionChanged(const QString& deviceId, bool isOnline);
};

/**
 * @brief 设备注册表工厂接口
 */
class IDeviceRegistryFactory
{
public:
    virtual ~IDeviceRegistryFactory() = default;
    
    /**
     * @brief 创建设备注册表实例
     * @return 设备注册表智能指针
     */
    virtual QSharedPointer<IDeviceRegistry> createRegistry() = 0;
};

} // namespace Registry
} // namespace Communication
} // namespace LA