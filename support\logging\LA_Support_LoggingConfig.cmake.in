# LA_Support_LoggingConfig.cmake.in
# LA Support Logging Module CMake配置文件模板

@PACKAGE_INIT@

# 查找依赖
include(CMakeFindDependencyMacro)
find_dependency(Qt5 REQUIRED COMPONENTS Core Network)

# 包含目标定义
include("${CMAKE_CURRENT_LIST_DIR}/LA_Support_LoggingTargets.cmake")

# 设置变量
set(LA_Support_Logging_FOUND TRUE)
set(LA_Support_Logging_VERSION "@PROJECT_VERSION@")
set(LA_Support_Logging_VERSION_MAJOR "@PROJECT_VERSION_MAJOR@")
set(LA_Support_Logging_VERSION_MINOR "@PROJECT_VERSION_MINOR@")
set(LA_Support_Logging_VERSION_PATCH "@PROJECT_VERSION_PATCH@")

# 提供组件信息
set(LA_Support_Logging_COMPONENTS
    Logger
    Formatter
    Output
)

# 检查组件
if(LA_Support_Logging_FIND_COMPONENTS)
    foreach(component ${LA_Support_Logging_FIND_COMPONENTS})
        if(NOT ${component} IN_LIST LA_Support_Logging_COMPONENTS)
            set(LA_Support_Logging_FOUND FALSE)
            set(LA_Support_Logging_NOT_FOUND_MESSAGE "Component ${component} not found")
        endif()
    endforeach()
endif()

check_required_components(LA_Support_Logging)