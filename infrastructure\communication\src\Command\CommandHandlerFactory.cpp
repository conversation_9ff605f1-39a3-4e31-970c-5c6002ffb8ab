#include <LA/Communication/Command/ICommandHandler.h>
#include <QDebug>
#include <memory>

namespace LA {
namespace Communication {
namespace Command {

// 前向声明命令处理器类
class BasicCommandHandler;

/**
 * @brief Linus式命令处理器工厂实现
 * 
 * 严格遵循ICommandHandlerFactory接口，只负责命令处理器实例的创建
 */
class CommandHandlerFactoryLinus : public ICommandHandlerFactory {
public:
    virtual ~CommandHandlerFactoryLinus() = default;
    
    std::shared_ptr<ICommandHandler> createCommandHandler(const QString& type) override;
    bool supportsHandlerType(const QString& type) const override;
    QStringList getSupportedHandlerTypes() const override;

private:
    static const QStringList SUPPORTED_TYPES;
};

// 支持的命令处理器类型定义
const QStringList CommandHandlerFactoryLinus::SUPPORTED_TYPES = {
    "basic",        // 基础命令处理器
    "device",       // 设备命令处理器（待实现）
    "protocol",     // 协议命令处理器（待实现）
    "custom"        // 自定义命令处理器（待实现）
};

std::shared_ptr<ICommandHandler> CommandHandlerFactoryLinus::createCommandHandler(const QString& type) {
    QString lowerType = type.toLower();
    
    if (lowerType == "basic") {
        return std::make_shared<BasicCommandHandler>();
    } else if (lowerType == "device") {
        // TODO: 实现设备命令处理器
        qWarning() << "Device command handler not implemented yet";
        return nullptr;
    } else if (lowerType == "protocol") {
        // TODO: 实现协议命令处理器
        qWarning() << "Protocol command handler not implemented yet";
        return nullptr;
    } else if (lowerType == "custom") {
        // TODO: 支持自定义命令处理器
        qWarning() << "Custom command handler not supported yet";
        return nullptr;
    } else {
        qWarning() << "Unsupported command handler type:" << type;
        return nullptr;
    }
}

bool CommandHandlerFactoryLinus::supportsHandlerType(const QString& type) const {
    if (type.toLower() == "basic") {
        return true;
    }
    
    // 其他类型暂时不支持
    return false;
}

QStringList CommandHandlerFactoryLinus::getSupportedHandlerTypes() const {
    // 只返回当前实际支持的类型
    return QStringList() << "basic";
}

/**
 * @brief 全局命令处理器工厂实例获取函数
 * 
 * 提供单例模式的命令处理器工厂访问
 */
std::shared_ptr<ICommandHandlerFactory> getCommandHandlerFactory() {
    static std::shared_ptr<ICommandHandlerFactory> factory = 
        std::make_shared<CommandHandlerFactoryLinus>();
    return factory;
}

} // namespace Command
} // namespace Communication
} // namespace LA