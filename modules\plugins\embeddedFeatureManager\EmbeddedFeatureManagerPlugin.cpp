#include "EmbeddedFeatureManagerPlugin.h"
#include "FeatureConfigWidget.h"
#include "PermissionManager.h"
#include "FileSync.h"

#include <QDir>
#include <QStandardPaths>
#include <QMessageBox>
#include <QJsonParseError>
#include <QDebug>

EmbeddedFeatureManagerPlugin::EmbeddedFeatureManagerPlugin(QObject *parent)
    : LA::Plugins::BaseFunctionPlugin(parent)
    , m_configWidget(nullptr)
    , m_permissionManager(nullptr)
    , m_fileSync(nullptr)
{
    // 设置插件基本信息
    setPluginInfo(
        "com.la.plugins.EmbeddedFeatureManager",
        "Embedded Feature Manager",
        "1.0.0",
        "Embedded project feature configuration management plugin",
        "LA Development Team"
    );
    
    // 设置功能插件特定信息
    setCategory("Development Tools");
    setWindowSizes(QSize(800, 600), QSize(1200, 800));
    setSupportedThemes({"Light", "Dark", "Default"});
    setSupportsFloating(true);
    
    // 设置权限需求
    setRequiredPermissions({
        LA::Plugins::PluginPermission::FileSystemAccess,
        LA::Plugins::PluginPermission::NetworkAccess
    });
    
    qDebug() << "EmbeddedFeatureManagerPlugin constructed";
}

EmbeddedFeatureManagerPlugin::~EmbeddedFeatureManagerPlugin()
{
    qDebug() << "EmbeddedFeatureManagerPlugin destroyed";
}

bool EmbeddedFeatureManagerPlugin::doInitialize()
{
    qDebug() << "Initializing EmbeddedFeatureManagerPlugin...";
    
    try {
        // 初始化权限管理器
        m_permissionManager = new PermissionManager(this);
        
        // 初始化文件同步器
        m_fileSync = new FileSync(this);
        
        // 设置连接
        setupConnections();
        
        // 加载默认配置
        m_configData = getDefaultConfig();
        
        qDebug() << "EmbeddedFeatureManagerPlugin initialized successfully";
        return true;
        
    } catch (const std::exception& e) {
        qCritical() << "Failed to initialize EmbeddedFeatureManagerPlugin:" << e.what();
        return false;
    }
}

bool EmbeddedFeatureManagerPlugin::doStart()
{
    qDebug() << "EmbeddedFeatureManagerPlugin started";
    return true;
}

void EmbeddedFeatureManagerPlugin::doStop()
{
    qDebug() << "EmbeddedFeatureManagerPlugin stopped";
}

void EmbeddedFeatureManagerPlugin::doShutdown()
{
    // 清理UI组件
    if (m_configWidget) {
        m_configWidget->deleteLater();
        m_configWidget = nullptr;
    }
    
    qDebug() << "EmbeddedFeatureManagerPlugin shutdown";
}

QWidget* EmbeddedFeatureManagerPlugin::doCreateWidget(QWidget* parent)
{
    if (!m_configWidget) {
        m_configWidget = new FeatureConfigWidget(parent);
        
        // 设置初始配置数据
        m_configWidget->setConfigData(m_configData);
        
        // 设置权限管理器
        m_configWidget->setPermissionManager(m_permissionManager);
        
        // 连接信号
        connect(m_configWidget, &FeatureConfigWidget::configChanged,
                this, &EmbeddedFeatureManagerPlugin::onFeatureChanged);
        connect(m_configWidget, &FeatureConfigWidget::syncRequested,
                this, &EmbeddedFeatureManagerPlugin::onSyncRequired);
    }
    
    return m_configWidget;
}

void EmbeddedFeatureManagerPlugin::loadProject(const QString& configPath)
{
    qDebug() << "Loading project from:" << configPath;
    
    if (loadConfigFromFile(configPath)) {
        m_currentConfigPath = configPath;
        
        if (m_configWidget) {
            m_configWidget->setConfigData(m_configData);
        }
        
        notifyConfigChanged();
        qDebug() << "Project loaded successfully";
    } else {
        qWarning() << "Failed to load project from:" << configPath;
    }
}

void EmbeddedFeatureManagerPlugin::saveProject(const QString& configPath)
{
    QString savePath = configPath.isEmpty() ? m_currentConfigPath : configPath;
    
    if (savePath.isEmpty()) {
        qWarning() << "No save path specified";
        return;
    }
    
    qDebug() << "Saving project to:" << savePath;
    
    // 从UI获取当前配置
    if (m_configWidget) {
        m_configData = m_configWidget->getConfigData();
    }
    
    if (saveConfigToFile(savePath)) {
        m_currentConfigPath = savePath;
        qDebug() << "Project saved successfully";
    } else {
        qWarning() << "Failed to save project to:" << savePath;
    }
}

void EmbeddedFeatureManagerPlugin::syncToHeader(const QString& headerPath)
{
    qDebug() << "Syncing to header file:" << headerPath;
    
    if (!m_fileSync) {
        qWarning() << "FileSync not initialized";
        return;
    }
    
    // 从UI获取最新配置
    if (m_configWidget) {
        m_configData = m_configWidget->getConfigData();
    }
    
    if (m_fileSync->jsonToHeader(m_configData, headerPath)) {
        qDebug() << "Successfully synced to header file";
    } else {
        qWarning() << "Failed to sync to header file";
    }
}

void EmbeddedFeatureManagerPlugin::importFromHeader(const QString& headerPath)
{
    qDebug() << "Importing from header file:" << headerPath;
    
    if (!m_fileSync) {
        qWarning() << "FileSync not initialized";
        return;
    }
    
    QJsonObject importedConfig;
    if (m_fileSync->headerToJson(headerPath, importedConfig)) {
        // 合并导入的配置
        // TODO: 实现智能合并逻辑
        m_configData = importedConfig;
        
        if (m_configWidget) {
            m_configWidget->setConfigData(m_configData);
        }
        
        notifyConfigChanged();
        qDebug() << "Successfully imported from header file";
    } else {
        qWarning() << "Failed to import from header file";
    }
}

void EmbeddedFeatureManagerPlugin::onFeatureChanged()
{
    qDebug() << "Feature configuration changed";
    
    // 更新内部配置数据
    if (m_configWidget) {
        m_configData = m_configWidget->getConfigData();
    }
    
    notifyConfigChanged();
}

void EmbeddedFeatureManagerPlugin::onPermissionChanged()
{
    qDebug() << "Permission settings changed";
    
    // 更新UI权限状态
    if (m_configWidget && m_permissionManager) {
        m_configWidget->updatePermissionState();
    }
}

void EmbeddedFeatureManagerPlugin::onSyncRequired()
{
    qDebug() << "Synchronization requested";
    
    // 执行自动同步逻辑
    if (!m_currentConfigPath.isEmpty()) {
        saveProject();
        
        // 如果有配置的头文件路径，自动同步
        // TODO: 从配置中获取头文件路径
        QString headerPath = m_configData.value("output").toObject()
                            .value("header_file").toString();
        if (!headerPath.isEmpty()) {
            syncToHeader(headerPath);
        }
    }
}

void EmbeddedFeatureManagerPlugin::setupConnections()
{
    if (m_permissionManager) {
        connect(m_permissionManager, &PermissionManager::permissionChanged,
                this, &EmbeddedFeatureManagerPlugin::onPermissionChanged);
    }
    
    if (m_fileSync) {
        connect(m_fileSync, &FileSync::syncCompleted,
                this, [this]() { qDebug() << "File sync completed"; });
        connect(m_fileSync, &FileSync::syncError,
                this, [this](const QString& error) { 
                    qWarning() << "File sync error:" << error; 
                });
    }
}

bool EmbeddedFeatureManagerPlugin::validateConfig(const QJsonObject& config)
{
    // 基本结构验证
    if (!config.contains("product_id") || !config.contains("features")) {
        return false;
    }
    
    // 检查必要字段
    if (config.value("product_id").toString().isEmpty()) {
        return false;
    }
    
    if (!config.value("features").isArray()) {
        return false;
    }
    
    return true;
}

void EmbeddedFeatureManagerPlugin::notifyConfigChanged()
{
    // 通知其他组件配置已更改
    emit configurationChanged(m_configData);
    
    // 触发UI更新
    if (m_configWidget) {
        m_configWidget->refreshUI();
    }
}

QJsonObject EmbeddedFeatureManagerPlugin::getDefaultConfig() const
{
    QJsonObject defaultConfig;
    
    defaultConfig["product_id"] = "New_Product";
    defaultConfig["version"] = "1.0.0";
    
    // 默认target配置
    QJsonArray targets;
    QJsonObject defaultTarget;
    defaultTarget["target_name"] = "DEFAULT_TARGET";
    defaultTarget["defines_file"] = "feature_defines.h";
    defaultTarget["macros_file"] = "feature_macros.json";
    targets.append(defaultTarget);
    defaultConfig["targets"] = targets;
    
    // 默认功能配置
    QJsonArray features;
    defaultConfig["features"] = features;
    
    // 输出配置
    QJsonObject output;
    output["header_file"] = "feature_defines.h";
    output["json_file"] = "feature_config.json";
    defaultConfig["output"] = output;
    
    // 权限配置
    QJsonObject permissions;
    permissions["current_user"] = "developer";
    permissions["role"] = "developer";
    defaultConfig["permissions"] = permissions;
    
    return defaultConfig;
}

bool EmbeddedFeatureManagerPlugin::loadConfigFromFile(const QString& filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        qWarning() << "Cannot open file for reading:" << filePath;
        return false;
    }
    
    QByteArray data = file.readAll();
    file.close();
    
    QJsonParseError parseError;
    QJsonDocument doc = QJsonDocument::fromJson(data, &parseError);
    
    if (parseError.error != QJsonParseError::NoError) {
        qWarning() << "JSON parse error:" << parseError.errorString();
        return false;
    }
    
    if (!doc.isObject()) {
        qWarning() << "Invalid JSON format, expected object";
        return false;
    }
    
    QJsonObject config = doc.object();
    if (!validateConfig(config)) {
        qWarning() << "Invalid configuration format";
        return false;
    }
    
    m_configData = config;
    return true;
}

bool EmbeddedFeatureManagerPlugin::saveConfigToFile(const QString& filePath)
{
    if (!validateConfig(m_configData)) {
        qWarning() << "Invalid configuration data, cannot save";
        return false;
    }
    
    QJsonDocument doc(m_configData);
    QByteArray data = doc.toJson(QJsonDocument::Indented);
    
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly)) {
        qWarning() << "Cannot open file for writing:" << filePath;
        return false;
    }
    
    qint64 bytesWritten = file.write(data);
    file.close();
    
    if (bytesWritten != data.size()) {
        qWarning() << "Failed to write complete data to file";
        return false;
    }
    
    return true;
}

// 静态插件注册
namespace {
    struct PluginRegistrar {
        PluginRegistrar() {
            LA::Plugins::PluginMetadata metadata;
            metadata.id = "com.la.plugins.EmbeddedFeatureManager";
            metadata.name = "Embedded Feature Manager";
            metadata.version = "1.0.0";
            metadata.description = "Embedded project feature configuration management plugin";
            metadata.author = "LA Development Team";
            metadata.category = "Development Tools";
            metadata.priority = LA::Plugins::PluginPriority::Normal;
            metadata.sourceType = LA::Plugins::PluginSourceType::Static;
            
            LA::Plugins::StaticPluginRegistrar::registerPlugin(
                metadata.id,
                []() -> std::shared_ptr<LA::Plugins::IPlugin> {
                    return std::make_shared<EmbeddedFeatureManagerPlugin>();
                },
                metadata
            );
        }
    };
    static PluginRegistrar registrar;
}