#ifndef EMBEDDED_FEATURE_MANAGER_PLUGIN_H
#define EMBEDDED_FEATURE_MANAGER_PLUGIN_H

#include <QObject>
#include <QWidget>
#include <QJsonObject>
#include <QJsonDocument>
#include <QString>
#include <QStringList>

#include <LA/Plugins/BasePlugin.h>

class FeatureConfigWidget;
class PermissionManager;
class FileSync;

/**
 * @brief 嵌入式功能管理插件
 * 
 * 实现嵌入式项目功能配置管理，支持：
 * - 功能项可视化编辑
 * - 多角色权限控制(RBAC)
 * - JSON配置与C头文件双向同步
 * - Gitee版本控制集成
 * 
 * 基于LA插件系统架构，遵循嵌入式项目功能管理系统设计规范
 */
class EmbeddedFeatureManagerPlugin : public LA::Plugins::BaseFunctionPlugin
{
    Q_OBJECT

public:
    explicit EmbeddedFeatureManagerPlugin(QObject *parent = nullptr);
    virtual ~EmbeddedFeatureManagerPlugin();

protected:
    // 重写BaseFunctionPlugin的虚函数
    bool doInitialize() override;
    bool doStart() override;
    void doStop() override;
    void doShutdown() override;
    
    // IFunctionPlugin接口实现
    QWidget* doCreateWidget(QWidget* parent = nullptr) override;

public slots:
    /**
     * @brief 加载项目配置
     * @param configPath JSON配置文件路径
     */
    void loadProject(const QString& configPath);
    
    /**
     * @brief 保存项目配置
     * @param configPath 保存路径，如果为空则使用当前路径
     */
    void saveProject(const QString& configPath = QString());
    
    /**
     * @brief 同步到头文件
     * @param headerPath C头文件路径
     */
    void syncToHeader(const QString& headerPath);
    
    /**
     * @brief 从头文件导入
     * @param headerPath C头文件路径
     */
    void importFromHeader(const QString& headerPath);

private slots:
    void onFeatureChanged();
    void onPermissionChanged();
    void onSyncRequired();

private:
    QString m_currentConfigPath;
    QJsonObject m_configData;
    
    // 核心组件
    FeatureConfigWidget* m_configWidget;      // 配置界面
    PermissionManager* m_permissionManager;   // 权限管理器
    FileSync* m_fileSync;                     // 文件同步器
    
    // 内部方法
    void setupConnections();
    bool validateConfig(const QJsonObject& config);
    void notifyConfigChanged();
    
    // 数据访问方法
    QJsonObject getDefaultConfig() const;
    bool loadConfigFromFile(const QString& filePath);
    bool saveConfigToFile(const QString& filePath);
};

#endif // EMBEDDED_FEATURE_MANAGER_PLUGIN_H