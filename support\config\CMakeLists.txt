# Configuration Management Modules
cmake_minimum_required(VERSION 3.16)

# 设置项目信息
project(LA_Support_Config VERSION 1.0.0 LANGUAGES CXX)

# 设置标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 查找依赖
find_package(Qt5 REQUIRED COMPONENTS Core Xml)

# 全局编译定义
add_compile_definitions(
    QT_NO_KEYWORDS
    QT_USE_QSTRINGBUILDER
)

# 添加子模块
add_subdirectory(parameter)
add_subdirectory(file)
add_subdirectory(validation)

# 创建总配置模块库（可选）
option(BUILD_UNIFIED_CONFIG_LIB "Build unified configuration library" ON)
if(BUILD_UNIFIED_CONFIG_LIB)
    # 创建统一的配置库
    add_library(LA_Support_Config INTERFACE)
    
    # 链接所有子模块
    target_link_libraries(LA_Support_Config INTERFACE
        LA_Support_Config_Parameter
        LA_Support_Config_File
        LA_Support_Config_Validation
    )
    
    # 设置包含目录
    target_include_directories(LA_Support_Config INTERFACE
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>
        $<INSTALL_INTERFACE:include>
    )
    
    # 设置导出名称
    set_target_properties(LA_Support_Config PROPERTIES
        EXPORT_NAME Config
    )
    
    message(STATUS "Unified configuration library will be built")
endif()

# 构建集成测试
option(BUILD_CONFIG_INTEGRATION_TEST "Build configuration integration test" OFF)
if(BUILD_CONFIG_INTEGRATION_TEST)
    # 检查所有必需的模块是否可用
    if(TARGET LA_Support_Config_Parameter AND 
       TARGET LA_Support_Config_File AND 
       TARGET LA_Support_Config_Validation)
        
        add_executable(config_integration_test test_integration.cpp)
        
        target_link_libraries(config_integration_test
            PRIVATE
                Qt5::Core
                LA_Support_Config_Parameter
                LA_Support_Config_File
                LA_Support_Config_Validation
        )
        
        # 设置包含目录
        target_include_directories(config_integration_test PRIVATE
            ${CMAKE_CURRENT_SOURCE_DIR}
            ${CMAKE_CURRENT_SOURCE_DIR}/parameter
            ${CMAKE_CURRENT_SOURCE_DIR}/file
            ${CMAKE_CURRENT_SOURCE_DIR}/validation
        )
        
        # 设置编译定义
        target_compile_definitions(config_integration_test PRIVATE
            QT_NO_KEYWORDS
            QT_USE_QSTRINGBUILDER
            CONFIG_INTEGRATION_TEST
        )
        
        # 添加测试
        if(BUILD_TESTING)
            enable_testing()
            add_test(NAME ConfigIntegrationTest COMMAND config_integration_test)
            
            # 设置测试环境
            set_tests_properties(ConfigIntegrationTest PROPERTIES
                WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
                TIMEOUT 60
            )
        endif()
        
        message(STATUS "Configuration integration test will be built")
    else()
        message(WARNING "Cannot build integration test: some modules are missing")
    endif()
endif()

# 文档生成
option(BUILD_CONFIG_DOCUMENTATION "Build configuration modules documentation" OFF)
if(BUILD_CONFIG_DOCUMENTATION)
    find_package(Doxygen)
    if(Doxygen_FOUND)
        # 配置Doxygen
        set(DOXYGEN_PROJECT_NAME "LA Configuration Management Modules")
        set(DOXYGEN_PROJECT_VERSION ${PROJECT_VERSION})
        set(DOXYGEN_PROJECT_DESCRIPTION "Modular configuration management system")
        set(DOXYGEN_OUTPUT_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/docs)
        set(DOXYGEN_INPUT_DIRECTORIES 
            "${CMAKE_CURRENT_SOURCE_DIR}/parameter "
            "${CMAKE_CURRENT_SOURCE_DIR}/file "
            "${CMAKE_CURRENT_SOURCE_DIR}/validation"
        )
        set(DOXYGEN_RECURSIVE YES)
        set(DOXYGEN_GENERATE_HTML YES)
        set(DOXYGEN_GENERATE_LATEX NO)
        set(DOXYGEN_EXTRACT_ALL YES)
        set(DOXYGEN_EXTRACT_PRIVATE YES)
        set(DOXYGEN_EXTRACT_STATIC YES)
        set(DOXYGEN_CALL_GRAPH YES)
        set(DOXYGEN_CALLER_GRAPH YES)
        
        # 创建Doxygen目标
        doxygen_add_docs(config_docs
            ${CMAKE_CURRENT_SOURCE_DIR}/parameter
            ${CMAKE_CURRENT_SOURCE_DIR}/file
            ${CMAKE_CURRENT_SOURCE_DIR}/validation
            WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
            COMMENT "Generating configuration modules documentation"
        )
        
        message(STATUS "Configuration documentation will be generated")
    else()
        message(WARNING "Doxygen not found, documentation will not be generated")
    endif()
endif()

# 安装规则（如果构建了统一库）
if(BUILD_UNIFIED_CONFIG_LIB)
    # 导出目标
    install(TARGETS LA_Support_Config
        EXPORT LA_Support_ConfigTargets
        INCLUDES DESTINATION include
    )
    
    # 安装导出文件
    install(EXPORT LA_Support_ConfigTargets
        FILE LA_Support_ConfigTargets.cmake
        NAMESPACE LA::Support::
        DESTINATION lib/cmake/LA_Support_Config
    )
    
    # 创建配置文件
    include(CMakePackageConfigHelpers)
    
    configure_package_config_file(
        "${CMAKE_CURRENT_SOURCE_DIR}/Config.cmake.in"
        "${CMAKE_CURRENT_BINARY_DIR}/LA_Support_ConfigConfig.cmake"
        INSTALL_DESTINATION lib/cmake/LA_Support_Config
    )
    
    write_basic_package_version_file(
        "${CMAKE_CURRENT_BINARY_DIR}/LA_Support_ConfigConfigVersion.cmake"
        VERSION ${PROJECT_VERSION}
        COMPATIBILITY SameMajorVersion
    )
    
    install(FILES
        "${CMAKE_CURRENT_BINARY_DIR}/LA_Support_ConfigConfig.cmake"
        "${CMAKE_CURRENT_BINARY_DIR}/LA_Support_ConfigConfigVersion.cmake"
        DESTINATION lib/cmake/LA_Support_Config
    )
endif()

# 打印构建配置摘要
message(STATUS "")
message(STATUS "=== Configuration Modules Build Summary ===")
message(STATUS "Project: ${PROJECT_NAME} v${PROJECT_VERSION}")
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "Install prefix: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "")
message(STATUS "Modules:")
message(STATUS "  * Parameter Management: ENABLED")
message(STATUS "  * File Handling: ENABLED")
message(STATUS "  * Validation: ENABLED")
message(STATUS "")
message(STATUS "Options:")
message(STATUS "  * Unified library: ${BUILD_UNIFIED_CONFIG_LIB}")
message(STATUS "  * Integration test: ${BUILD_CONFIG_INTEGRATION_TEST}")
message(STATUS "  * Documentation: ${BUILD_CONFIG_DOCUMENTATION}")
message(STATUS "  * Testing: ${BUILD_TESTING}")
message(STATUS "")
message(STATUS "Dependencies:")
message(STATUS "  * Qt5 Core: ${Qt5Core_VERSION}")
message(STATUS "  * Qt5 Xml: ${Qt5Xml_VERSION}")
if(Doxygen_FOUND)
    message(STATUS "  * Doxygen: ${DOXYGEN_VERSION}")
endif()
message(STATUS "")

# 构建后的提示信息
if(BUILD_CONFIG_INTEGRATION_TEST)
    message(STATUS "After building, run the integration test with:")
    message(STATUS "  ${CMAKE_CURRENT_BINARY_DIR}/config_integration_test")
    message(STATUS "")
endif()

if(BUILD_CONFIG_DOCUMENTATION AND Doxygen_FOUND)
    message(STATUS "After building, generate documentation with:")
    message(STATUS "  make config_docs")
    message(STATUS "  Documentation will be in: ${CMAKE_CURRENT_BINARY_DIR}/docs/html/")
    message(STATUS "")
endif()

message(STATUS "=== End Configuration Modules Build Summary ===")
message(STATUS "")