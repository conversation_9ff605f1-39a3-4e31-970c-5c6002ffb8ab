#include "ResourceManager.h"
#include "AsyncLoadWorker.h"
#include <QDir>
#include <QFile>
#include <QFileInfo>
#include <QImageReader>
#include <QPixmapCache>
#include <QStandardPaths>
#include <QCryptographicHash>
#include <QMimeDatabase>
#include <QNetworkRequest>
#include <QNetworkReply>
#include <QMutexLocker>
#include <QDebug>
#include <QJsonDocument>
#include <QJsonObject>
#include <QCoreApplication>
#include <QElapsedTimer>
#include <QTextCodec>
#include <algorithm>

namespace LA {
namespace Support {
namespace Resource {

//=====================================================================
// ResourceManager Implementation
//=====================================================================

ResourceManager::ResourceManager(QObject* parent)
    : IResourceManager(parent)
    , m_asyncWorker(nullptr)
    , m_fileWatcher(new QFileSystemWatcher(this))
    , m_networkManager(new QNetworkAccessManager(this))
    , m_maintenanceTimer(new QTimer(this))
    , m_initialized(false)
    , m_fileWatchingEnabled(true)
    , m_totalResources(0)
    , m_loadedResources(0)
    , m_cacheHits(0)
    , m_cacheMisses(0)
    , m_totalLoadTime(0)
    , m_creationTime(QDateTime::currentDateTime())
{
    // 连接文件系统监控信号
    connect(m_fileWatcher, &QFileSystemWatcher::fileChanged,
            this, &ResourceManager::handleFileChanged);
    connect(m_fileWatcher, &QFileSystemWatcher::directoryChanged,
            this, &ResourceManager::handleDirectoryChanged);
    
    // 连接维护定时器
    connect(m_maintenanceTimer, &QTimer::timeout,
            this, &ResourceManager::performPeriodicMaintenance);
    
    // 设置默认缓存配置
    m_cacheConfig.enabled = true;
    m_cacheConfig.maxSize = 100 * 1024 * 1024;  // 100MB
    m_cacheConfig.maxItems = 1000;
    m_cacheConfig.defaultExpiry = 3600;         // 1 hour
    m_cacheConfig.persistentCache = false;
    m_cacheConfig.cacheDirectory = QStandardPaths::writableLocation(QStandardPaths::CacheLocation) + "/resources";
    
    m_cache.setMaxCost(static_cast<int>(m_cacheConfig.maxSize));
}

ResourceManager::~ResourceManager() {
    shutdown();
}

SimpleResult ResourceManager::initialize(const ConfigParameters& config) {
    QMutexLocker locker(&m_mutex);
    
    if (m_initialized) {
        return SimpleResult::success(true);
    }
    
    try {
        // 应用配置
        if (config.contains("cacheEnabled")) {
            m_cacheConfig.enabled = config.value("cacheEnabled").toBool();
        }
        if (config.contains("maxCacheSize")) {
            m_cacheConfig.maxSize = config.value("maxCacheSize").toLongLong();
            m_cache.setMaxCost(static_cast<int>(m_cacheConfig.maxSize));
        }
        if (config.contains("cacheDirectory")) {
            m_cacheConfig.cacheDirectory = config.value("cacheDirectory").toString();
        }
        if (config.contains("fileWatching")) {
            m_fileWatchingEnabled = config.value("fileWatching").toBool();
        }
        
        // 创建缓存目录
        if (m_cacheConfig.persistentCache) {
            QDir cacheDir(m_cacheConfig.cacheDirectory);
            if (!cacheDir.exists()) {
                cacheDir.mkpath(".");
            }
        }
        
        // 初始化内置提供者
        initializeBuiltinProviders();
        
        // 启动异步工作器
        m_asyncWorker = new AsyncLoadWorker(this);
        
        // 启动维护定时器（每5分钟执行一次）
        m_maintenanceTimer->start(5 * 60 * 1000);
        
        // 加载持久化缓存
        if (m_cacheConfig.persistentCache) {
            loadPersistentCache();
        }
        
        m_initialized = true;
        return SimpleResult::success(true);
        
    } catch (const std::exception& e) {
        return SimpleResult::failure(QString("Resource manager initialization failed: %1").arg(e.what()));
    }
}

SimpleResult ResourceManager::shutdown() {
    QMutexLocker locker(&m_mutex);
    
    if (!m_initialized) {
        return SimpleResult::success(true);
    }
    
    try {
        // 停止维护定时器
        m_maintenanceTimer->stop();
        
        // 停止异步工作器
        if (m_asyncWorker) {
            m_asyncWorker->deleteLater();
            m_asyncWorker = nullptr;
        }
        
        // 保存持久化缓存
        if (m_cacheConfig.persistentCache) {
            savePersistentCache();
        }
        
        // 清理资源
        clearCache();
        m_resources.clear();
        m_providers.clear();
        m_dependencies.clear();
        
        m_initialized = false;
        return SimpleResult::success(true);
        
    } catch (const std::exception& e) {
        return SimpleResult::failure(QString("Resource manager shutdown failed: %1").arg(e.what()));
    }
}

bool ResourceManager::isInitialized() const {
    return m_initialized;
}

StatusInfoList ResourceManager::getStatus() const {
    StatusInfoList status;
    
    status.append({
        "initialized", m_initialized ? "Yes" : "No",
        "Initialized", "是否已初始化"
    });
    
    status.append({
        "total_resources", QString::number(m_totalResources),
        "Total Resources", "总资源数"
    });
    
    status.append({
        "loaded_resources", QString::number(m_loadedResources),
        "Loaded Resources", "已加载资源数"
    });
    
    status.append({
        "cache_hits", QString::number(m_cacheHits),
        "Cache Hits", "缓存命中数"
    });
    
    return status;
}

bool ResourceManager::registerResource(const ResourceInfo& info) {
    QMutexLocker locker(&m_mutex);
    
    if (!m_initialized || info.id.isEmpty()) {
        return false;
    }
    
    // 检查是否已存在
    if (m_resources.find(info.id) != m_resources.end()) {
        return false;
    }
    
    // 注册资源
    ResourceInfo resourceInfo = info;
    resourceInfo.created = QDateTime::currentDateTime();
    m_resources[info.id] = resourceInfo;
    m_totalResources++;
    
    // 设置文件监控
    if (m_fileWatchingEnabled && resourceInfo.source == ResourceSource::Local) {
        setupFileWatching(resourceInfo);
    }
    
    emit resourceRegistered(info.id, resourceInfo);
    return true;
}

bool ResourceManager::registerResources(const QList<ResourceInfo>& resources) {
    bool success = true;
    for (const auto& info : resources) {
        if (!registerResource(info)) {
            success = false;
        }
    }
    return success;
}

void ResourceManager::unregisterResource(const QString& id) {
    QMutexLocker locker(&m_mutex);
    
    auto it = m_resources.find(id);
    if (it != m_resources.end()) {
        // 移除文件监控
        if (m_fileWatchingEnabled && it->second.source == ResourceSource::Local) {
            removeFileWatching(it->second.path);
        }
        
        // 从缓存中移除
        removeFromCache(id);
        
        // 移除依赖关系
        m_dependencies.erase(id);
        
        // 从注册表中移除
        m_resources.erase(it);
        m_totalResources--;
        
        emit resourceUnregistered(id);
    }
}

bool ResourceManager::hasResource(const QString& id) const {
    QMutexLocker locker(&m_mutex);
    return m_resources.find(id) != m_resources.end();
}

Result<ResourceInfo> ResourceManager::getResourceInfo(const QString& id) const {
    QMutexLocker locker(&m_mutex);
    
    auto it = m_resources.find(id);
    if (it != m_resources.end()) {
        return Result<ResourceInfo>::success(it->second);
    }
    
    return Result<ResourceInfo>::failure("Resource not found: " + id);
}

QList<ResourceInfo> ResourceManager::getAllResourceInfo() const {
    QMutexLocker locker(&m_mutex);
    
    QList<ResourceInfo> result;
    result.reserve(static_cast<int>(m_resources.size()));
    
    for (const auto& pair : m_resources) {
        result.append(pair.second);
    }
    
    return result;
}

QList<ResourceInfo> ResourceManager::getResourcesByType(ResourceType type) const {
    QMutexLocker locker(&m_mutex);
    
    QList<ResourceInfo> result;
    for (const auto& pair : m_resources) {
        if (pair.second.type == type) {
            result.append(pair.second);
        }
    }
    
    return result;
}

QList<ResourceInfo> ResourceManager::getResourcesByCategory(const QString& category) const {
    QMutexLocker locker(&m_mutex);
    
    QList<ResourceInfo> result;
    for (const auto& pair : m_resources) {
        if (pair.second.category == category) {
            result.append(pair.second);
        }
    }
    
    return result;
}

QList<ResourceInfo> ResourceManager::searchResources(const QString& query, const ConfigParameters& filters) const {
    QMutexLocker locker(&m_mutex);
    
    QList<ResourceInfo> result;
    
    for (const auto& pair : m_resources) {
        const ResourceInfo& info = pair.second;
        bool matches = false;
        
        // 文本搜索
        if (query.isEmpty() || 
            info.name.contains(query, Qt::CaseInsensitive) ||
            info.displayName.contains(query, Qt::CaseInsensitive) ||
            info.description.contains(query, Qt::CaseInsensitive) ||
            info.tags.join(" ").contains(query, Qt::CaseInsensitive)) {
            matches = true;
        }
        
        // 应用过滤器
        if (matches && !filters.isEmpty()) {
            for (auto it = filters.begin(); it != filters.end(); ++it) {
                const QString& key = it.key();
                const QVariant& value = it.value();
                
                if (key == "type" && resourceTypeToString(info.type) != value.toString()) {
                    matches = false;
                    break;
                }
                if (key == "category" && info.category != value.toString()) {
                    matches = false;
                    break;
                }
                if (key == "source" && resourceSourceToString(info.source) != value.toString()) {
                    matches = false;
                    break;
                }
            }
        }
        
        if (matches) {
            result.append(info);
        }
    }
    
    return result;
}

Result<ResourceData> ResourceManager::loadResource(const QString& id, const ResourceLoadOptions& options) {
    QMutexLocker locker(&m_mutex);
    
    if (!m_initialized) {
        return Result<ResourceData>::failure("Resource manager not initialized");
    }
    
    auto it = m_resources.find(id);
    if (it == m_resources.end()) {
        return Result<ResourceData>::failure("Resource not found: " + id);
    }
    
    // 检查缓存
    if (options.useCache && !options.forceReload && isInCache(id)) {
        ResourceData cachedData = getFromCache(id);
        if (cachedData.isLoaded) {
            m_cacheHits++;
            return Result<ResourceData>::success(cachedData);
        }
    }
    
    m_cacheMisses++;
    
    // 加载资源
    QElapsedTimer timer;
    timer.start();
    
    ResourceData data = loadResourceInternal(id, options);
    
    m_totalLoadTime += timer.elapsed();
    
    if (data.isLoaded) {
        // 处理数据
        data.processedData = processResourceData(data);
        
        // 添加到缓存
        if (options.useCache && m_cacheConfig.enabled) {
            addToCache(id, data);
        }
        
        // 更新状态
        updateResourceStatus(id, ResourceStatus::Loaded);
        m_loadedResources++;
        
        emit resourceLoaded(id, data);
        return Result<ResourceData>::success(data);
    } else {
        updateResourceStatus(id, ResourceStatus::Error);
        emit resourceLoadFailed(id, data.errorMessage);
        return Result<ResourceData>::failure(data.errorMessage);
    }
}

SimpleResult ResourceManager::loadResourceAsync(const QString& id, const ResourceLoadOptions& options) {
    if (!m_initialized || !m_asyncWorker) {
        return SimpleResult::failure("Resource manager not initialized or async worker not available");
    }
    
    if (!hasResource(id)) {
        return SimpleResult::failure("Resource not found: " + id);
    }
    
    // 加入异步队列
    AsyncLoadWorker::LoadTask task;
    task.id = id;
    task.options = options;
    task.timestamp = QDateTime::currentDateTime();
    
    m_asyncWorker->addTask(task);
    
    // 更新状态
    updateResourceStatus(id, ResourceStatus::Loading);
    
    return SimpleResult::success(true);
}

SimpleResult ResourceManager::preloadResource(const QString& id) {
    ResourceLoadOptions options;
    options.useCache = true;
    options.forceReload = false;
    options.async = false;
    
    auto result = loadResource(id, options);
    return result.isSuccess() ? SimpleResult::success(true) : SimpleResult::failure(result.message());
}

void ResourceManager::unloadResource(const QString& id) {
    QMutexLocker locker(&m_mutex);
    
    removeFromCache(id);
    updateResourceStatus(id, ResourceStatus::Available);
    
    if (m_loadedResources > 0) {
        m_loadedResources--;
    }
}

Result<QPixmap> ResourceManager::getImage(const QString& id, const QSize& size) {
    auto result = loadResource(id);
    if (!result.isSuccess()) {
        return Result<QPixmap>::failure(result.message());
    }
    
    const ResourceData& data = result.value();
    if (data.info.type != ResourceType::Image) {
        return Result<QPixmap>::failure("Resource is not an image: " + id);
    }
    
    QPixmap pixmap = processImage(data.data, size);
    if (pixmap.isNull()) {
        return Result<QPixmap>::failure("Failed to process image: " + id);
    }
    
    return Result<QPixmap>::success(pixmap);
}

Result<QIcon> ResourceManager::getIcon(const QString& id, const QSize& size) {
    auto result = loadResource(id);
    if (!result.isSuccess()) {
        return Result<QIcon>::failure(result.message());
    }
    
    const ResourceData& data = result.value();
    if (data.info.type != ResourceType::Icon && data.info.type != ResourceType::Image) {
        return Result<QIcon>::failure("Resource is not an icon: " + id);
    }
    
    QIcon icon = processIcon(data.data, size);
    if (icon.isNull()) {
        return Result<QIcon>::failure("Failed to process icon: " + id);
    }
    
    return Result<QIcon>::success(icon);
}

StringResult ResourceManager::getText(const QString& id, const QString& encoding) {
    auto result = loadResource(id);
    if (!result.isSuccess()) {
        return StringResult::failure(result.message());
    }
    
    const ResourceData& data = result.value();
    if (data.info.type != ResourceType::Text && data.info.type != ResourceType::Config) {
        return StringResult::failure("Resource is not text: " + id);
    }
    
    // 转换编码
    QTextCodec* codec = QTextCodec::codecForName(encoding.toUtf8());
    if (!codec) {
        codec = QTextCodec::codecForName("UTF-8");
    }
    
    QString text = codec->toUnicode(data.data);
    return StringResult::success(text);
}

ByteArrayResult ResourceManager::getBinary(const QString& id) {
    auto result = loadResource(id);
    if (!result.isSuccess()) {
        return ByteArrayResult::failure(result.message());
    }
    
    const ResourceData& data = result.value();
    return ByteArrayResult::success(data.data);
}

SimpleResult ResourceManager::saveResource(const QString& id, const QByteArray& data, const ResourceInfo& info) {
    QMutexLocker locker(&m_mutex);
    
    if (!m_initialized) {
        return SimpleResult::failure("Resource manager not initialized");
    }
    
    // 检查资源是否存在
    auto it = m_resources.find(id);
    if (it == m_resources.end()) {
        return SimpleResult::failure("Resource not found: " + id);
    }
    
    ResourceInfo& resourceInfo = it->second;
    
    try {
        // 保存到文件
        if (resourceInfo.source == ResourceSource::Local) {
            QString fullPath = resolveResourcePath(resourceInfo.path);
            QFile file(fullPath);
            
            if (!file.open(QIODevice::WriteOnly)) {
                return SimpleResult::failure("Failed to open file for writing: " + fullPath);
            }
            
            if (file.write(data) != data.size()) {
                return SimpleResult::failure("Failed to write data to file: " + fullPath);
            }
            
            file.close();
            
            // 更新资源信息
            resourceInfo.size = data.size();
            resourceInfo.modified = QDateTime::currentDateTime();
            resourceInfo.checksum = calculateChecksum(data);
            
            // 如果提供了新的信息，则更新
            if (!info.displayName.isEmpty()) resourceInfo.displayName = info.displayName;
            if (!info.description.isEmpty()) resourceInfo.description = info.description;
            if (!info.category.isEmpty()) resourceInfo.category = info.category;
            if (!info.tags.isEmpty()) resourceInfo.tags = info.tags;
            
            // 更新缓存
            ResourceData resourceData(resourceInfo, data);
            addToCache(id, resourceData);
            
            emit resourceUpdated(id, resourceInfo);
            return SimpleResult::success(true);
        } else {
            return SimpleResult::failure("Cannot save non-local resource: " + id);
        }
        
    } catch (const std::exception& e) {
        return SimpleResult::failure(QString("Failed to save resource: %1").arg(e.what()));
    }
}

SimpleResult ResourceManager::deleteResource(const QString& id) {
    QMutexLocker locker(&m_mutex);
    
    if (!m_initialized) {
        return SimpleResult::failure("Resource manager not initialized");
    }
    
    auto it = m_resources.find(id);
    if (it == m_resources.end()) {
        return SimpleResult::failure("Resource not found: " + id);
    }
    
    const ResourceInfo& info = it->second;
    
    try {
        // 删除文件
        if (info.source == ResourceSource::Local) {
            QString fullPath = resolveResourcePath(info.path);
            QFile file(fullPath);
            
            if (file.exists() && !file.remove()) {
                return SimpleResult::failure("Failed to delete file: " + fullPath);
            }
        }
        
        // 注销资源
        unregisterResource(id);
        
        return SimpleResult::success(true);
        
    } catch (const std::exception& e) {
        return SimpleResult::failure(QString("Failed to delete resource: %1").arg(e.what()));
    }
}

SimpleResult ResourceManager::copyResource(const QString& sourceId, const QString& targetId) {
    // 加载源资源
    auto result = loadResource(sourceId);
    if (!result.isSuccess()) {
        return SimpleResult::failure("Failed to load source resource: " + result.message());
    }
    
    const ResourceData& sourceData = result.value();
    
    // 创建目标资源信息
    ResourceInfo targetInfo = sourceData.info;
    targetInfo.id = targetId;
    targetInfo.created = QDateTime::currentDateTime();
    targetInfo.modified = targetInfo.created;
    
    // 注册目标资源
    if (!registerResource(targetInfo)) {
        return SimpleResult::failure("Failed to register target resource: " + targetId);
    }
    
    // 保存数据
    return saveResource(targetId, sourceData.data, targetInfo);
}

SimpleResult ResourceManager::moveResource(const QString& sourceId, const QString& targetId) {
    // 先复制
    auto copyResult = copyResource(sourceId, targetId);
    if (!copyResult.isSuccess()) {
        return copyResult;
    }
    
    // 再删除源
    return deleteResource(sourceId);
}

void ResourceManager::setCacheConfig(const ResourceCacheConfig& config) {
    QMutexLocker locker(&m_mutex);
    m_cacheConfig = config;
    m_cache.setMaxCost(static_cast<int>(config.maxSize));
}

ResourceCacheConfig ResourceManager::getCacheConfig() const {
    QMutexLocker locker(&m_mutex);
    return m_cacheConfig;
}

void ResourceManager::clearCache() {
    QMutexLocker locker(&m_mutex);
    
    int itemsRemoved = m_cache.size();
    qint64 bytesFreed = m_cache.totalCost();
    
    m_cache.clear();
    
    emit cacheCleared(itemsRemoved, bytesFreed);
}

void ResourceManager::clearExpiredCache() {
    QMutexLocker locker(&m_mutex);
    
    // 清理过期缓存项的实现
    // 这里简化处理，清理所有缓存
    clearCache();
}

StatusInfoList ResourceManager::getCacheStatistics() const {
    QMutexLocker locker(&m_mutex);
    
    StatusInfoList stats;
    
    stats.append({
        "cache_enabled", m_cacheConfig.enabled ? "Yes" : "No",
        "Cache Enabled", "缓存是否启用"
    });
    
    stats.append({
        "cache_size", QString::number(m_cache.size()),
        "Cache Items", "缓存项数"
    });
    
    stats.append({
        "cache_cost", QString::number(m_cache.totalCost()),
        "Cache Cost", "缓存占用"
    });
    
    stats.append({
        "cache_hits", QString::number(m_cacheHits),
        "Cache Hits", "缓存命中数"
    });
    
    stats.append({
        "cache_misses", QString::number(m_cacheMisses),
        "Cache Misses", "缓存失误数"
    });
    
    return stats;
}

SimpleResult ResourceManager::refreshResource(const QString& id) {
    ResourceLoadOptions options;
    options.forceReload = true;
    options.useCache = false;
    
    auto result = loadResource(id, options);
    return result.isSuccess() ? SimpleResult::success(true) : SimpleResult::failure(result.message());
}

SimpleResult ResourceManager::validateResource(const QString& id) {
    auto result = loadResource(id);
    if (!result.isSuccess()) {
        return SimpleResult::failure(result.message());
    }
    
    const ResourceData& data = result.value();
    
    if (validateResourceData(data)) {
        return SimpleResult::success(true);
    } else {
        return SimpleResult::failure("Resource validation failed: " + id);
    }
}

QStringList ResourceManager::getResourceDependencies(const QString& id) const {
    QMutexLocker locker(&m_mutex);
    
    auto it = m_dependencies.find(id);
    if (it != m_dependencies.end()) {
        return it->second;
    }
    
    return QStringList();
}

SimpleResult ResourceManager::setResourceDependencies(const QString& id, const QStringList& dependencies) {
    QMutexLocker locker(&m_mutex);
    
    if (!hasResource(id)) {
        return SimpleResult::failure("Resource not found: " + id);
    }
    
    m_dependencies[id] = dependencies;
    return SimpleResult::success(true);
}

bool ResourceManager::registerProvider(const QString& name, std::shared_ptr<IResourceProvider> provider) {
    QMutexLocker locker(&m_mutex);
    
    if (!provider || name.isEmpty()) {
        return false;
    }
    
    m_providers[name] = provider;
    return true;
}

void ResourceManager::unregisterProvider(const QString& name) {
    QMutexLocker locker(&m_mutex);
    m_providers.erase(name);
}

std::shared_ptr<IResourceProvider> ResourceManager::getProvider(const QString& name) const {
    QMutexLocker locker(&m_mutex);
    
    auto it = m_providers.find(name);
    if (it != m_providers.end()) {
        return it->second;
    }
    
    return nullptr;
}

void ResourceManager::setFileSystemWatching(bool enabled) {
    QMutexLocker locker(&m_mutex);
    m_fileWatchingEnabled = enabled;
}

QVariantMap ResourceManager::getStatistics() const {
    QMutexLocker locker(&m_mutex);
    
    QVariantMap stats;
    stats["total_resources"] = static_cast<qulonglong>(m_totalResources);
    stats["loaded_resources"] = static_cast<qulonglong>(m_loadedResources);
    stats["cache_hits"] = static_cast<qulonglong>(m_cacheHits);
    stats["cache_misses"] = static_cast<qulonglong>(m_cacheMisses);
    stats["total_load_time"] = static_cast<qulonglong>(m_totalLoadTime);
    stats["creation_time"] = m_creationTime;
    stats["providers_count"] = static_cast<int>(m_providers.size());
    
    return stats;
}

void ResourceManager::handleFileChanged(const QString& path) {
    QMutexLocker locker(&m_mutex);
    
    // 查找受影响的资源
    for (auto& pair : m_resources) {
        ResourceInfo& info = pair.second;
        if (resolveResourcePath(info.path) == path) {
            // 从缓存中移除
            removeFromCache(pair.first);
            
            // 更新状态
            updateResourceStatus(pair.first, ResourceStatus::Available);
            
            // 如果文件不存在，标记为NotFound
            if (!QFile::exists(path)) {
                updateResourceStatus(pair.first, ResourceStatus::NotFound);
            }
            
            break;
        }
    }
}

void ResourceManager::handleDirectoryChanged(const QString& path) {
    Q_UNUSED(path)
    // 处理目录变化
    // 可以扫描新增的文件并自动注册
}

void ResourceManager::processAsyncQueue() {
    // 由AsyncLoadWorker调用
}

void ResourceManager::performPeriodicMaintenance() {
    // 清理过期缓存
    clearExpiredCache();
    
    // 验证文件资源状态
    QMutexLocker locker(&m_mutex);
    for (auto& pair : m_resources) {
        ResourceInfo& info = pair.second;
        if (info.source == ResourceSource::Local) {
            QString fullPath = resolveResourcePath(info.path);
            if (!QFile::exists(fullPath)) {
                updateResourceStatus(pair.first, ResourceStatus::NotFound);
            }
        }
    }
}

void ResourceManager::handleNetworkReply() {
    QNetworkReply* reply = qobject_cast<QNetworkReply*>(sender());
    if (!reply) return;
    
    reply->deleteLater();
    
    // 处理网络资源加载结果
    // 实现略...
}

//=====================================================================
// Private Methods
//=====================================================================

ResourceData ResourceManager::loadResourceInternal(const QString& id, const ResourceLoadOptions& options) {
    auto it = m_resources.find(id);
    if (it == m_resources.end()) {
        ResourceData data;
        data.errorMessage = "Resource not found: " + id;
        return data;
    }
    
    const ResourceInfo& info = it->second;
    
    switch (info.source) {
        case ResourceSource::Local:
            return loadFromFile(info, options);
        case ResourceSource::Embedded:
            return loadFromEmbedded(info, options);
        case ResourceSource::Network:
            return loadFromNetwork(info, options);
        default:
            return loadFromProvider(info, options);
    }
}

ResourceData ResourceManager::loadFromFile(const ResourceInfo& info, const ResourceLoadOptions& options) {
    Q_UNUSED(options)
    
    QString fullPath = resolveResourcePath(info.path);
    QFile file(fullPath);
    
    ResourceData data;
    data.info = info;
    
    if (!file.exists()) {
        data.errorMessage = "File not found: " + fullPath;
        return data;
    }
    
    if (!file.open(QIODevice::ReadOnly)) {
        data.errorMessage = "Failed to open file: " + fullPath;
        return data;
    }
    
    data.data = file.readAll();
    data.isLoaded = true;
    
    return data;
}

ResourceData ResourceManager::loadFromEmbedded(const ResourceInfo& info, const ResourceLoadOptions& options) {
    Q_UNUSED(options)
    
    QFile file(info.path);  // 嵌入资源路径通常以 ": " 开头
    
    ResourceData data;
    data.info = info;
    
    if (!file.open(QIODevice::ReadOnly)) {
        data.errorMessage = "Failed to open embedded resource: " + info.path;
        return data;
    }
    
    data.data = file.readAll();
    data.isLoaded = true;
    
    return data;
}

ResourceData ResourceManager::loadFromNetwork(const ResourceInfo& info, const ResourceLoadOptions& options) {
    ResourceData data;
    data.info = info;
    
    QNetworkRequest request(info.url);
    QNetworkReply* reply = m_networkManager->get(request);
    
    // 等待响应（简化处理，实际应使用异步）
    QEventLoop loop;
    connect(reply, &QNetworkReply::finished, &loop, &QEventLoop::quit);
    
    QTimer timer;
    timer.setSingleShot(true);
    connect(&timer, &QTimer::timeout, &loop, &QEventLoop::quit);
    timer.start(options.timeout);
    
    loop.exec();
    
    if (reply->error() == QNetworkReply::NoError) {
        data.data = reply->readAll();
        data.isLoaded = true;
    } else {
        data.errorMessage = reply->errorString();
    }
    
    reply->deleteLater();
    return data;
}

ResourceData ResourceManager::loadFromProvider(const ResourceInfo& info, const ResourceLoadOptions& options) {
    ResourceData data;
    data.info = info;
    
    // 尝试通过提供者加载
    for (const auto& pair : m_providers) {
        const auto& provider = pair.second;
        if (provider->canProvide(info.path)) {
            auto result = provider->loadResource(info.path, options);
            if (result.isSuccess()) {
                return result.value();
            }
        }
    }
    
    data.errorMessage = "No suitable provider found for resource: " + info.id;
    return data;
}

QVariant ResourceManager::processResourceData(const ResourceData& data) {
    switch (data.info.type) {
        case ResourceType::Image:
            return QVariant::fromValue(processImage(data.data));
        case ResourceType::Icon:
            return QVariant::fromValue(processIcon(data.data));
        case ResourceType::Text:
        case ResourceType::Config:
            return QString::fromUtf8(data.data);
        default:
            return QVariant::fromValue(data.data);
    }
}

QPixmap ResourceManager::processImage(const QByteArray& data, const QSize& targetSize) {
    QPixmap pixmap;
    pixmap.loadFromData(data);
    
    if (!pixmap.isNull() && targetSize.isValid()) {
        pixmap = pixmap.scaled(targetSize, Qt::KeepAspectRatio, Qt::SmoothTransformation);
    }
    
    return pixmap;
}

QIcon ResourceManager::processIcon(const QByteArray& data, const QSize& targetSize) {
    QPixmap pixmap = processImage(data, targetSize);
    return QIcon(pixmap);
}

bool ResourceManager::validateResourceData(const ResourceData& data) {
    if (!data.isLoaded || data.data.isEmpty()) {
        return false;
    }
    
    // 验证校验和
    if (!data.info.checksum.isEmpty()) {
        QString actualChecksum = calculateChecksum(data.data);
        if (actualChecksum != data.info.checksum) {
            return false;
        }
    }
    
    // 验证大小
    if (data.info.size > 0 && data.data.size() != data.info.size) {
        return false;
    }
    
    return true;
}

QString ResourceManager::calculateChecksum(const QByteArray& data) {
    return QString(QCryptographicHash::hash(data, QCryptographicHash::Md5).toHex());
}

QString ResourceManager::detectMimeType(const QByteArray& data, const QString& filename) {
    QMimeDatabase db;
    
    if (!filename.isEmpty()) {
        QMimeType mimeType = db.mimeTypeForFile(filename);
        if (mimeType.isValid()) {
            return mimeType.name();
        }
    }
    
    QMimeType mimeType = db.mimeTypeForData(data);
    return mimeType.name();
}

void ResourceManager::updateResourceStatus(const QString& id, ResourceStatus status) {
    auto it = m_resources.find(id);
    if (it != m_resources.end()) {
        ResourceStatus oldStatus = it->second.status;
        it->second.status = status;
        
        emit resourceStatusChanged(id, oldStatus, status);
    }
}

void ResourceManager::addToCache(const QString& id, const ResourceData& data) {
    if (!m_cacheConfig.enabled) return;
    
    int cost = data.data.size();
    ResourceData* cacheData = new ResourceData(data);
    m_cache.insert(id, cacheData, cost);
}

ResourceData ResourceManager::getFromCache(const QString& id) {
    ResourceData* cacheData = m_cache.object(id);
    if (cacheData) {
        return *cacheData;
    }
    
    return ResourceData();
}

bool ResourceManager::isInCache(const QString& id) const {
    return m_cache.contains(id);
}

void ResourceManager::removeFromCache(const QString& id) {
    m_cache.remove(id);
}

void ResourceManager::setupFileWatching(const ResourceInfo& info) {
    QString fullPath = resolveResourcePath(info.path);
    if (!m_fileWatcher->files().contains(fullPath)) {
        m_fileWatcher->addPath(fullPath);
    }
}

void ResourceManager::removeFileWatching(const QString& path) {
    QString fullPath = resolveResourcePath(path);
    m_fileWatcher->removePath(fullPath);
}

void ResourceManager::initializeBuiltinProviders() {
    // 注册内置提供者
    registerProvider("file", std::make_shared<FileResourceProvider>());
    registerProvider("embedded", std::make_shared<EmbeddedResourceProvider>());
    registerProvider("network", std::make_shared<NetworkResourceProvider>(m_networkManager));
}

void ResourceManager::loadPersistentCache() {
    // 加载持久化缓存的实现
    // 简化处理，实际应从文件系统加载
}

void ResourceManager::savePersistentCache() {
    // 保存持久化缓存的实现
    // 简化处理，实际应保存到文件系统
}

QString ResourceManager::resolveResourcePath(const QString& path) const {
    if (QFileInfo(path).isAbsolute()) {
        return path;
    }
    
    // 相对路径处理
    QString appDir = QCoreApplication::applicationDirPath();
    return QDir(appDir).absoluteFilePath(path);
}

ResourceInfo ResourceManager::createResourceInfoFromFile(const QString& filePath) {
    ResourceInfo info;
    QFileInfo fileInfo(filePath);
    
    info.id = fileInfo.baseName();
    info.name = fileInfo.fileName();
    info.displayName = fileInfo.baseName();
    info.path = filePath;
    info.size = fileInfo.size();
    info.created = fileInfo.birthTime();
    info.modified = fileInfo.lastModified();
    info.source = ResourceSource::Local;
    info.status = ResourceStatus::Available;
    
    // 检测资源类型
    QString suffix = fileInfo.suffix().toLower();
    if (QStringList{"png", "jpg", "jpeg", "bmp", "gif", "svg"}.contains(suffix)) {
        info.type = ResourceType::Image;
    } else if (QStringList{"ico", "icns"}.contains(suffix)) {
        info.type = ResourceType::Icon;
    } else if (QStringList{"txt", "md", "readme"}.contains(suffix)) {
        info.type = ResourceType::Text;
    } else if (QStringList{"json", "xml", "ini", "conf"}.contains(suffix)) {
        info.type = ResourceType::Config;
    } else {
        info.type = ResourceType::Unknown;
    }
    
    return info;
}

//=====================================================================
// AsyncLoadWorker Implementation
//=====================================================================
// Provider Implementations (简化版本)
//=====================================================================

QString FileResourceProvider::getName() const {
    return "file";
}

QList<ResourceType> FileResourceProvider::getSupportedTypes() const {
    return {ResourceType::Image, ResourceType::Icon, ResourceType::Text, 
            ResourceType::Config, ResourceType::Binary, ResourceType::Document};
}

bool FileResourceProvider::canProvide(const QString& path) const {
    return QFile::exists(path);
}

Result<ResourceData> FileResourceProvider::loadResource(const QString& path, const ResourceLoadOptions& options) {
    Q_UNUSED(options)
    
    QFile file(path);
    if (!file.open(QIODevice::ReadOnly)) {
        return Result<ResourceData>::failure("Failed to open file: " + path);
    }
    
    ResourceData data;
    data.data = file.readAll();
    data.isLoaded = true;
    
    return Result<ResourceData>::success(data);
}

Result<ResourceInfo> FileResourceProvider::getResourceInfo(const QString& path) const {
    QFileInfo fileInfo(path);
    if (!fileInfo.exists()) {
        return Result<ResourceInfo>::failure("File not found: " + path);
    }
    
    ResourceInfo info;
    info.name = fileInfo.fileName();
    info.path = path;
    info.size = fileInfo.size();
    info.type = detectResourceType(path);
    info.source = ResourceSource::Local;
    info.status = ResourceStatus::Available;
    
    return Result<ResourceInfo>::success(info);
}

ResourceType FileResourceProvider::detectResourceType(const QString& path) const {
    QString suffix = QFileInfo(path).suffix().toLower();
    
    if (QStringList{"png", "jpg", "jpeg", "bmp", "gif", "svg"}.contains(suffix)) {
        return ResourceType::Image;
    } else if (QStringList{"ico", "icns"}.contains(suffix)) {
        return ResourceType::Icon;
    } else if (QStringList{"txt", "md"}.contains(suffix)) {
        return ResourceType::Text;
    }
    
    return ResourceType::Unknown;
}

QString FileResourceProvider::detectMimeType(const QString& path) const {
    QMimeDatabase db;
    return db.mimeTypeForFile(path).name();
}

//=====================================================================
// Utility Functions
//=====================================================================

QString resourceTypeToString(ResourceType type) {
    switch (type) {
        case ResourceType::Unknown:     return "Unknown";
        case ResourceType::Image:       return "Image";
        case ResourceType::Icon:        return "Icon";
        case ResourceType::Font:        return "Font";
        case ResourceType::Style:       return "Style";
        case ResourceType::Config:      return "Config";
        case ResourceType::Data:        return "Data";
        case ResourceType::Document:    return "Document";
        case ResourceType::Audio:       return "Audio";
        case ResourceType::Video:       return "Video";
        case ResourceType::Binary:      return "Binary";
        case ResourceType::Text:        return "Text";
        case ResourceType::Template:    return "Template";
        case ResourceType::Custom:      return "Custom";
        default:                        return "Unknown";
    }
}

ResourceType stringToResourceType(const QString& typeStr) {
    QString upper = typeStr.toUpper();
    if (upper == "IMAGE")       return ResourceType::Image;
    if (upper == "ICON")        return ResourceType::Icon;
    if (upper == "FONT")        return ResourceType::Font;
    if (upper == "STYLE")       return ResourceType::Style;
    if (upper == "CONFIG")      return ResourceType::Config;
    if (upper == "DATA")        return ResourceType::Data;
    if (upper == "DOCUMENT")    return ResourceType::Document;
    if (upper == "AUDIO")       return ResourceType::Audio;
    if (upper == "VIDEO")       return ResourceType::Video;
    if (upper == "BINARY")      return ResourceType::Binary;
    if (upper == "TEXT")        return ResourceType::Text;
    if (upper == "TEMPLATE")    return ResourceType::Template;
    if (upper == "CUSTOM")      return ResourceType::Custom;
    return ResourceType::Unknown;
}

QString resourceStatusToString(ResourceStatus status) {
    switch (status) {
        case ResourceStatus::Unknown:   return "Unknown";
        case ResourceStatus::Available: return "Available";
        case ResourceStatus::Loading:   return "Loading";
        case ResourceStatus::Loaded:    return "Loaded";
        case ResourceStatus::Error:     return "Error";
        case ResourceStatus::NotFound:  return "NotFound";
        case ResourceStatus::Expired:   return "Expired";
        default:                        return "Unknown";
    }
}

ResourceStatus stringToResourceStatus(const QString& statusStr) {
    QString upper = statusStr.toUpper();
    if (upper == "AVAILABLE")   return ResourceStatus::Available;
    if (upper == "LOADING")     return ResourceStatus::Loading;
    if (upper == "LOADED")      return ResourceStatus::Loaded;
    if (upper == "ERROR")       return ResourceStatus::Error;
    if (upper == "NOTFOUND")    return ResourceStatus::NotFound;
    if (upper == "EXPIRED")     return ResourceStatus::Expired;
    return ResourceStatus::Unknown;
}

QString resourceSourceToString(ResourceSource source) {
    switch (source) {
        case ResourceSource::Local:     return "Local";
        case ResourceSource::Embedded:  return "Embedded";
        case ResourceSource::Network:   return "Network";
        case ResourceSource::Database:  return "Database";
        case ResourceSource::Memory:    return "Memory";
        case ResourceSource::Cache:     return "Cache";
        case ResourceSource::Custom:    return "Custom";
        default:                        return "Local";
    }
}

ResourceSource stringToResourceSource(const QString& sourceStr) {
    QString upper = sourceStr.toUpper();
    if (upper == "LOCAL")       return ResourceSource::Local;
    if (upper == "EMBEDDED")    return ResourceSource::Embedded;
    if (upper == "NETWORK")     return ResourceSource::Network;
    if (upper == "DATABASE")    return ResourceSource::Database;
    if (upper == "MEMORY")      return ResourceSource::Memory;
    if (upper == "CACHE")       return ResourceSource::Cache;
    if (upper == "CUSTOM")      return ResourceSource::Custom;
    return ResourceSource::Local;
}

}  // namespace Resource
}  // namespace Support
}  // namespace LA