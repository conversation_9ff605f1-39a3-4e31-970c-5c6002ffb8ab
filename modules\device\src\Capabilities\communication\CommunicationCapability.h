/**
 * @file CommunicationCapability.h
 * @brief 通信能力适配器 - 设备层与infrastructure/communication的桥接
 * 
 * Linus式设计原则：
 * - "Do One Thing And Do It Well" - 只负责设备通信能力
 * - 适配器模式 - 将基础设施通信模块适配为设备能力
 * - 依赖注入 - 通过构造器注入通信服务
 */

#pragma once

#include "../command/ICommandProvider.h"
#include "LA/Foundation/Core/CommonTypes.h"
// TODO: Re-enable infrastructure dependencies when they are fixed
// #include "../../../../infrastructure/communication/include/LA/Communication/Session/ICommunicationSession.h"
// #include "../../../../infrastructure/communication/include/LA/Communication/Protocol/IProtocol.h"
#include <QObject>
#include <QVariantMap>
#include <QTimer>
#include <memory>

namespace LA::Device::Capability {

/**
 * @brief 设备通信能力接口
 */
class IDeviceCapability : public QObject {
    Q_OBJECT
    
public:
    explicit IDeviceCapability(QObject* parent = nullptr) : QObject(parent) {}
    virtual ~IDeviceCapability() = default;
    
    virtual QString getCapabilityId() const = 0;
    virtual QStringList getDependencies() const = 0;
    virtual bool initialize(const QVariantMap& config) = 0;
    virtual QVariantMap executeCapability(const QString& action, const QVariantMap& params) = 0;
    virtual void shutdown() = 0;
    virtual void setInfrastructureServices(const QVariantMap& services) = 0;
    
Q_SIGNALS:
    void capabilityReady();
    void capabilityError(const QString& error);
    void capabilityDataReady(const QVariantMap& data);
};

/**
 * @brief 通信能力适配器 - 设备与基础设施通信模块的桥接
 * 
 * 职责：
 * - 为设备提供统一的通信接口
 * - 适配infrastructure/communication模块的服务
 * - 管理设备特定的通信配置和状态
 * - 提供命令发送、响应接收、状态监控功能
 */
class CommunicationCapability : public IDeviceCapability {
    Q_OBJECT

public:
    explicit CommunicationCapability(QObject* parent = nullptr);
    virtual ~CommunicationCapability();

    // === IDeviceCapability接口实现 ===
    QString getCapabilityId() const override { return "device_communication"; }
    QStringList getDependencies() const override { 
        return {"communication_session", "protocol_handler", "command_system"}; 
    }
    
    bool initialize(const QVariantMap& config) override;
    QVariantMap executeCapability(const QString& action, const QVariantMap& params) override;
    void shutdown() override;
    void setInfrastructureServices(const QVariantMap& services) override;

    // === 设备通信专用接口 ===
    
    /**
     * @brief 建立设备连接
     * @param connectionConfig 连接配置
     * @return 连接结果
     */
    QVariantMap establishConnection(const QVariantMap& connectionConfig);
    
    /**
     * @brief 关闭设备连接
     * @return 关闭结果
     */
    QVariantMap closeConnection();
    
    /**
     * @brief 发送设备命令
     * @param command 命令ID
     * @param params 命令参数
     * @return 执行结果
     */
    QVariantMap sendDeviceCommand(const QString& command, const QVariantMap& params = {});
    
    /**
     * @brief 发送原始数据
     * @param data 原始数据
     * @return 发送结果
     */
    QVariantMap sendRawData(const QByteArray& data);
    
    /**
     * @brief 接收设备响应
     * @param timeout 超时时间(ms)
     * @return 响应数据
     */
    QVariantMap receiveResponse(int timeout = 5000);
    
    /**
     * @brief 执行命令并等待响应
     * @param command 命令ID
     * @param params 命令参数  
     * @param timeout 超时时间(ms)
     * @return 完整执行结果
     */
    QVariantMap executeCommandAndWait(const QString& command, 
                                    const QVariantMap& params = {}, 
                                    int timeout = 5000);

    // === 状态查询 ===
    
    /**
     * @brief 获取连接状态
     * @return 连接状态信息
     */
    QVariantMap getConnectionStatus() const;
    
    /**
     * @brief 获取通信统计
     * @return 通信统计信息
     */
    QVariantMap getCommunicationStatistics() const;
    
    /**
     * @brief 获取设备信息
     * @return 设备信息
     */
    QVariantMap getDeviceInfo() const;

    // === 配置管理 ===
    
    /**
     * @brief 设置命令提供者
     * @param provider 命令提供者
     */
    void setCommandProvider(std::shared_ptr<Command::ICommandProvider> provider);
    
    /**
     * @brief 更新通信配置
     * @param config 新配置
     * @return 是否成功
     */
    bool updateCommunicationConfig(const QVariantMap& config);

Q_SIGNALS:
    // === 通信事件信号 ===
    void connectionEstablished();
    void connectionClosed();
    void connectionError(const QString& error);
    
    // === 数据信号 ===
    void commandSent(const QString& command, const QVariantMap& params);
    void responseReceived(const QVariantMap& response);
    void rawDataReceived(const QByteArray& data);
    
    // === 状态信号 ===
    void communicationStatusChanged(const QString& status);
    void deviceInfoUpdated(const QVariantMap& deviceInfo);

private slots:
    // === 基础设施模块事件处理 ===
    void onSessionStatusChanged(LA::Foundation::Core::ConnectionStatus status);
    void onSessionDataReceived(const QByteArray& data);
    void onSessionMessageReceived(const QVariantMap& message);
    void onSessionError(const QString& error);
    void onCommandExecuted(const QVariantMap& command, const LA::Device::Command::CommandResult& result);

private:
    // === 基础设施服务注入 ===
    // TODO: Re-enable when infrastructure is fixed
    // std::shared_ptr<Communication::Session::ICommunicationSession> m_session;  // 通信会话服务
    // std::shared_ptr<Communication::Protocol::IProtocol> m_protocol;            // 协议处理服务
    std::shared_ptr<Command::ICommandProvider> m_commandProvider;              // 命令提供者
    
    // === 设备通信状态 ===
    bool m_initialized;
    bool m_connected;
    QVariantMap m_communicationConfig;
    QVariantMap m_deviceInfo;
    
    // === 统计信息 ===
    quint64 m_commandsSent;
    quint64 m_responsesReceived;
    quint64 m_bytesTransmitted;
    quint64 m_bytesReceived;
    quint64 m_errorCount;
    QDateTime m_lastActivity;
    
    // === 缓冲和状态管理 ===
    QByteArray m_responseBuffer;
    QTimer* m_statusTimer;
    QString m_lastError;

private:
    // === 内部工具方法 ===
    
    /**
     * @brief 验证基础设施服务可用性
     * @return 是否所有必需服务都可用
     */
    bool validateInfrastructureServices() const;
    
    /**
     * @brief 初始化通信统计
     */
    void initializeCommunicationStatistics();
    
    /**
     * @brief 更新通信统计
     * @param operation 操作类型
     * @param bytes 字节数
     * @param success 是否成功
     */
    void updateStatistics(const QString& operation, qint64 bytes, bool success);
    
    /**
     * @brief 构建错误结果
     * @param error 错误描述
     * @return 错误结果映射
     */
    QVariantMap createErrorResult(const QString& error) const;
    
    /**
     * @brief 构建成功结果
     * @param data 结果数据
     * @return 成功结果映射
     */
    QVariantMap createSuccessResult(const QVariantMap& data = {}) const;
    
    /**
     * @brief 启动状态监控定时器
     */
    void startStatusMonitoring();
    
    /**
     * @brief 停止状态监控定时器
     */
    void stopStatusMonitoring();

private slots:
    /**
     * @brief 定期状态检查
     */
    void performStatusCheck();
};

/**
 * @brief 通信能力工厂
 */
class CommunicationCapabilityFactory {
public:
    /**
     * @brief 创建通信能力实例
     * @param deviceType 设备类型
     * @param config 配置参数
     * @return 通信能力实例
     */
    static std::unique_ptr<CommunicationCapability> createCapability(const QString& deviceType, 
                                                                    const QVariantMap& config = {});
    
    /**
     * @brief 创建带基础设施服务的通信能力
     * @param deviceType 设备类型
     * @param session 通信会话服务
     * @param protocol 协议服务
     * @param commandProvider 命令提供者
     * @return 通信能力实例
     */
    static std::unique_ptr<CommunicationCapability> createWithServices(
        const QString& deviceType,
        // TODO: Re-enable when infrastructure is fixed
        // std::shared_ptr<Communication::Session::ICommunicationSession> session,
        // std::shared_ptr<Communication::Protocol::IProtocol> protocol,
        std::shared_ptr<Command::ICommandProvider> commandProvider);
};

} // namespace LA::Device::Capability