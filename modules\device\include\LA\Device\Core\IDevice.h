/**
 * @file IDevice.h
 * @brief 纯净的四层架构设备接口 - Linus "好品味" 设计
 * 
 * "消除特殊情况，统一处理模式" - 单一设备接口替代多层继承
 * "做一件事并做好" - 专注设备核心功能
 */

#pragma once

#include <QObject>
#include <QVariantMap>
#include <QString>

namespace LA::Device::Core {

/**
 * @brief 统一设备接口 
 * 
 * Linus原则实现：
 * - "好品味" - 无特殊情况，所有设备统一接口
 * - "简洁执念" - 5个核心方法，职责单一
 * - "实用主义" - 解决实际设备操作需求
 */
class IDevice : public QObject {
    Q_OBJECT

public:
    explicit IDevice(QObject* parent = nullptr) : QObject(parent) {}
    virtual ~IDevice();

    // === 核心设备操作 - "Do One Thing And Do It Well" ===
    
    /**
     * @brief 初始化设备 - 配置四层架构组件
     * @param config 设备配置(JSON格式)
     * @return 初始化成功返回true
     */
    virtual bool initialize(const QVariantMap& config) = 0;
    
    /**
     * @brief 连接设备 - 建立通信连接
     * @return 连接成功返回true
     */
    virtual bool connect() = 0;
    
    /**
     * @brief 断开设备连接
     * @return 断开成功返回true
     */
    virtual bool disconnect() = 0;
    
    /**
     * @brief 统一命令接口 - 替代Legacy任务系统
     * @param command 命令名称 ("ranging", "calibrate", "get_status")
     * @param params 命令参数
     * @return 执行结果 {"success": bool, "data": variant, "error": string}
     */
    virtual QVariantMap sendCommand(const QString& command, const QVariantMap& params = {}) = 0;
    
    /**
     * @brief 检查连接状态
     * @return 已连接返回true
     */
    virtual bool isConnected() const = 0;

    // === 设备信息获取 ===
    
    /**
     * @brief 获取设备类型
     * @return 设备类型字符串 ("SPRM", "Motor", "Sensor")
     */
    virtual QString getDeviceType() const = 0;
    
    /**
     * @brief 获取设备型号
     * @return 设备型号字符串 ("SPRM-A1", "Motor-X2")
     */
    virtual QString getDeviceModel() const = 0;
    
    /**
     * @brief 获取设备序列号
     * @return 设备序列号
     */
    virtual QString getSerialNumber() const = 0;
    
    /**
     * @brief 获取设备版本信息
     * @return 版本信息 {"hardware": "1.2", "firmware": "2.1.3", "software": "3.0.0"}
     */
    virtual QVariantMap getVersionInfo() const = 0;
    
    // === 四层架构支持 - 层级能力管理 ===
    
    /**
     * @brief 获取设备支持的能力列表
     * @return 能力列表 ["ranging", "calibration", "data_processing"]
     */
    virtual QStringList getSupportedCapabilities() const = 0;
    
    /**
     * @brief 检查设备是否支持指定能力
     * @param capability 能力名称
     * @return 支持返回true
     */
    virtual bool hasCapability(const QString& capability) const = 0;
    
    /**
     * @brief 获取能力配置信息
     * @param capability 能力名称
     * @return 能力配置参数
     */
    virtual QVariantMap getCapabilityConfig(const QString& capability) const = 0;
    
    // === 配置管理 - 运行时配置 ===
    
    /**
     * @brief 获取当前设备配置
     * @return 完整配置信息
     */
    virtual QVariantMap getCurrentConfig() const = 0;
    
    /**
     * @brief 更新设备配置
     * @param config 新的配置参数
     * @return 更新成功返回true
     */
    virtual bool updateConfig(const QVariantMap& config) = 0;
    
    /**
     * @brief 重置设备到默认配置
     * @return 重置成功返回true
     */
    virtual bool resetToDefaultConfig() = 0;
    
    /**
     * @brief 验证配置参数有效性
     * @param config 待验证的配置
     * @return 验证结果 {"valid": bool, "errors": [string]}
     */
    virtual QVariantMap validateConfig(const QVariantMap& config) const = 0;
    
    // === 设备状态管理 ===
    
    /**
     * @brief 获取详细设备状态
     * @return 状态信息 {"connection": string, "health": string, "operation": string}
     */
    virtual QVariantMap getDeviceStatus() const = 0;
    
    /**
     * @brief 执行设备自检
     * @return 自检结果 {"success": bool, "tests": [{"name": string, "result": bool, "message": string}]}
     */
    virtual QVariantMap performSelfTest() = 0;
    
    /**
     * @brief 获取设备统计信息
     * @return 统计信息 {"uptime": int, "commands_executed": int, "errors": int}
     */
    virtual QVariantMap getStatistics() const = 0;
    
    // === 错误处理和恢复 ===
    
    /**
     * @brief 获取最近的错误列表
     * @param count 返回错误数量限制
     * @return 错误列表
     */
    virtual QList<QVariantMap> getRecentErrors(int count = 10) const = 0;
    
    /**
     * @brief 清除错误状态
     * @return 清除成功返回true
     */
    virtual bool clearErrors() = 0;
    
    /**
     * @brief 尝试设备恢复
     * @return 恢复成功返回true
     */
    virtual bool attemptRecovery() = 0;

Q_SIGNALS:
    /**
     * @brief 设备状态变化信号
     * @param status 新状态 ("connected", "disconnected", "error", "busy")
     */
    void deviceStatusChanged(const QString& status);
    
    /**
     * @brief 设备错误信号
     * @param error 错误描述
     */
    void deviceError(const QString& error);
    
    /**
     * @brief 设备数据就绪信号
     * @param data 设备数据
     */
    void deviceDataReady(const QVariantMap& data);
    
    /**
     * @brief 配置变化信号
     * @param configKey 变化的配置项
     * @param newValue 新值
     */
    void configurationChanged(const QString& configKey, const QVariant& newValue);
    
    /**
     * @brief 能力状态变化信号
     * @param capability 能力名称
     * @param enabled 是否启用
     */
    void capabilityStatusChanged(const QString& capability, bool enabled);
    
    /**
     * @brief 自检完成信号
     * @param results 自检结果
     */
    void selfTestCompleted(const QVariantMap& results);
    
    /**
     * @brief 统计信息更新信号
     * @param statistics 统计数据
     */
    void statisticsUpdated(const QVariantMap& statistics);
};

} // namespace LA::Device::Core