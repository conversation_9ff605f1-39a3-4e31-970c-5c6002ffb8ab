#include <LA/Communication/Protocol/IProtocol.h>
#include <QDebug>
#include <QMutexLocker>
#include <QDateTime>
#include <QRegularExpression>

namespace LA {
namespace Communication {
namespace Protocol {

/**
 * @brief 简单文本协议实现
 * 
 * 格式：START|command|param1=value1;param2=value2|checksum|END
 * 例如：START|READ|id=123;type=sensor|A5B3|END
 */
class TextProtocol : public IProtocol {
    Q_OBJECT

public:
    explicit TextProtocol(QObject* parent = nullptr);
    virtual ~TextProtocol() = default;

    // === 协议信息 ===
    ProtocolType type() const override;
    QString name() const override;

    // === 核心编解码 ===
    QByteArray encode(const QVariantMap& data) override;
    QVariantMap decode(const QByteArray& frame) override;
    bool validateFrame(const QByteArray& frame) override;
    bool isCompleteFrame(const QByteArray& data) override;

    // === 状态查询 ===
    QString errorString() const override;
    DeviceStatistics getStatistics() const override;
    void resetStatistics() override;

private:
    mutable QMutex m_mutex;
    DeviceStatistics m_statistics;
    QString m_lastError;
    
    QString calculateChecksum(const QString& content) const;
    bool verifyChecksum(const QString& content, const QString& checksum) const;
    void updateStatistics(bool success);
};

TextProtocol::TextProtocol(QObject* parent)
    : IProtocol(parent)
{
    resetStatistics();
}

// === 协议信息 ===

ProtocolType TextProtocol::type() const {
    return ProtocolType::Text;
}

QString TextProtocol::name() const {
    return "Simple Text Protocol";
}

// === 核心编解码 ===

QByteArray TextProtocol::encode(const QVariantMap& data) {
    QMutexLocker locker(&m_mutex);
    
    m_lastError.clear();
    
    // 检查必需的命令字段
    if (!data.contains("command")) {
        m_lastError = "Missing 'command' field";
        updateStatistics(false);
        return QByteArray();
    }
    
    QString command = data["command"].toString();
    if (command.isEmpty()) {
        m_lastError = "Empty command";
        updateStatistics(false);
        return QByteArray();
    }
    
    // 构建参数字符串
    QString params;
    for (auto it = data.begin(); it != data.end(); ++it) {
        if (it.key() == "command") continue; // 跳过命令字段
        
        if (!params.isEmpty()) {
            params += ";";
        }
        params += QString("%1=%2").arg(it.key()).arg(it.value().toString());
    }
    
    // 构建内容（不包括START和END）
    QString content = QString("%1|%2").arg(command).arg(params);
    
    // 计算校验和
    QString checksum = calculateChecksum(content);
    
    // 构建完整帧
    QString frame = QString("START|%1|%2|END").arg(content).arg(checksum);
    
    updateStatistics(true);
    return frame.toUtf8();
}

QVariantMap TextProtocol::decode(const QByteArray& frame) {
    QMutexLocker locker(&m_mutex);
    
    m_lastError.clear();
    QVariantMap result;
    
    QString frameStr = QString::fromUtf8(frame);
    
    // 验证帧格式
    if (!validateFrame(frame)) {
        updateStatistics(false);
        return result;
    }
    
    // 解析帧：START|command|params|checksum|END
    QStringList parts = frameStr.split("|");
    if (parts.size() != 5) {
        m_lastError = "Invalid frame format";
        updateStatistics(false);
        return result;
    }
    
    QString command = parts[1];
    QString params = parts[2];
    QString checksum = parts[3];
    
    // 验证校验和
    QString content = QString("%1|%2").arg(command).arg(params);
    if (!verifyChecksum(content, checksum)) {
        m_lastError = "Checksum verification failed";
        updateStatistics(false);
        return result;
    }
    
    // 解析命令
    result["command"] = command;
    
    // 解析参数
    if (!params.isEmpty()) {
        QStringList paramList = params.split(";");
        for (const QString& param : paramList) {
            QStringList keyValue = param.split("=");
            if (keyValue.size() == 2) {
                result[keyValue[0]] = keyValue[1];
            }
        }
    }
    
    updateStatistics(true);
    emit frameDecoded(result);
    
    return result;
}

bool TextProtocol::validateFrame(const QByteArray& frame) {
    QString frameStr = QString::fromUtf8(frame);
    
    // 检查基本格式
    if (!frameStr.startsWith("START|") || !frameStr.endsWith("|END")) {
        m_lastError = "Frame doesn't start with START| or end with |END";
        return false;
    }
    
    // 检查分段数量
    QStringList parts = frameStr.split("|");
    if (parts.size() != 5) {
        m_lastError = QString("Expected 5 parts, got %1").arg(parts.size());
        return false;
    }
    
    // 检查命令不为空
    if (parts[1].isEmpty()) {
        m_lastError = "Empty command";
        return false;
    }
    
    // 检查校验和格式
    if (parts[3].isEmpty()) {
        m_lastError = "Empty checksum";
        return false;
    }
    
    return true;
}

bool TextProtocol::isCompleteFrame(const QByteArray& data) {
    QString dataStr = QString::fromUtf8(data);
    return dataStr.startsWith("START|") && dataStr.endsWith("|END");
}

// === 状态查询 ===

QString TextProtocol::errorString() const {
    QMutexLocker locker(&m_mutex);
    return m_lastError;
}

DeviceStatistics TextProtocol::getStatistics() const {
    QMutexLocker locker(&m_mutex);
    return m_statistics;
}

void TextProtocol::resetStatistics() {
    QMutexLocker locker(&m_mutex);
    m_statistics = DeviceStatistics();
    m_statistics.lastActivity = QDateTime::currentMSecsSinceEpoch();
    m_lastError.clear();
}

// === 私有辅助方法 ===

QString TextProtocol::calculateChecksum(const QString& content) const {
    // 简单的校验和算法：所有字符的ASCII值异或
    quint16 checksum = 0;
    for (const QChar& ch : content) {
        checksum ^= ch.unicode();
    }
    return QString::number(checksum, 16).toUpper().rightJustified(4, '0');
}

bool TextProtocol::verifyChecksum(const QString& content, const QString& checksum) const {
    return calculateChecksum(content) == checksum;
}

void TextProtocol::updateStatistics(bool success) {
    qint64 currentTime = QDateTime::currentMSecsSinceEpoch();
    m_statistics.lastActivity = currentTime;
    
    if (success) {
        m_statistics.packetsReceived++;
    } else {
        m_statistics.errorsCount++;
        emit errorOccurred(m_lastError);
    }
}

} // namespace Protocol
} // namespace Communication
} // namespace LA

#include "TextProtocol.moc"