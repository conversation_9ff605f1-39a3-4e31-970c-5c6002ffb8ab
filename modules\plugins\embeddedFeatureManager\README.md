# 嵌入式功能管理插件

基于LA软件架构的嵌入式项目功能配置管理插件，支持多角色协作的功能项可视化编辑和配置同步。

## 功能特性

### 🎯 核心功能
- **功能项可视化编辑** - 树状结构展示，支持拖拽操作
- **多目标平台支持** - 不同target的配置参数管理
- **双向文件同步** - JSON配置 ↔ C头文件自动同步
- **实时配置验证** - 参数合法性检查和依赖关系验证

### 🔒 权限管理
- **基于角色的访问控制(RBAC)** - 支持5种用户角色
- **细粒度权限控制** - 功能级、操作级、资源级权限
- **权限审计日志** - 完整的操作记录和追踪

### 👥 支持角色
| 角色 | 权限范围 | 主要职责 |
|------|----------|----------|
| 业务人员 | 查看功能配置 | 需求反馈 |
| 产品经理 | 功能规划、配置编辑 | 功能定义、需求管理 |
| 项目经理 | 全局管理、审批 | 进度控制、变更审批 |
| 开发工程师 | 技术配置、文件同步 | 代码实现、技术集成 |
| 测试人员 | 测试配置、验证 | 功能验证、测试执行 |

## 系统架构

```
嵌入式功能管理插件架构
├── EmbeddedFeatureManagerPlugin    # 插件主类
├── FeatureConfigWidget            # 配置界面
├── FileSync                       # 文件同步引擎
├── PermissionManager              # 权限管理器
└── 配置数据模型                    # JSON数据结构
```

### 数据流架构
```
QT界面 ↔ JSON配置 ↔ Git仓库 ↔ VSCode插件 ↔ C头文件 → 固件编译
   ↑                                    ↓
   └──────────── 反馈同步 ←─────────────┘
```

## 安装和集成

### 依赖要求
- Qt 5.15+ (Core, Widgets)
- CMake 3.16+
- C++17编译器
- LA框架插件系统

### 构建步骤
```bash
# 在LA项目根目录执行
mkdir build && cd build
cmake .. -DCMAKE_BUILD_TYPE=Release
cmake --build . --target EmbeddedFeatureManagerPlugin
```

### 插件注册
插件使用静态注册方式，会在LA应用启动时自动加载：
```cpp
REGISTER_STATIC_PLUGIN(EmbeddedFeatureManagerPlugin)
```

## 使用指南

### 基本操作流程

1. **项目初始化**
   - 创建或加载项目配置文件(JSON)
   - 设置目标平台和输出路径
   - 配置用户角色和权限

2. **功能配置管理**
   - 使用树形界面添加/编辑功能项
   - 设置功能参数和目标特定值
   - 拖拽调整功能项顺序和层次

3. **配置同步**
   - 保存配置到JSON文件
   - 同步生成C头文件
   - 提交到版本控制系统

4. **协作流程**
   - 多用户权限控制
   - 配置变更审批
   - 冲突检测和解决

### 配置文件格式

#### JSON配置结构
```json
{
  "product_id": "Lidar-X100",
  "version": "1.0.0",
  "targets": [
    {
      "target_name": "LIDAR_X100_STD",
      "defines_file": "feature_defines.h"
    }
  ],
  "features": [
    {
      "id": "max_range",
      "name": "Maximum Range",
      "type": "number",
      "enabled": true,
      "per_target": {
        "LIDAR_X100_STD": 40.0,
        "LIDAR_X100_PRO": 120.0
      }
    }
  ]
}
```

#### 生成的C头文件
```c
#ifndef FEATURE_CONFIG_H
#define FEATURE_CONFIG_H

/* Product Configuration */
#define PRODUCT_ID "Lidar-X100"
#define PRODUCT_VERSION "1.0.0"
#define TARGET_NAME "LIDAR_X100_STD"

/* Feature Configuration */
#define MAX_RANGE_ENABLED 1
#define MAX_RANGE 40.0

#endif // FEATURE_CONFIG_H
```

## API参考

### 插件主类
```cpp
class EmbeddedFeatureManagerPlugin : public IFunctionPlugin
{
public:
    // 创建配置界面
    QWidget* createWidget(QWidget* parent = nullptr) override;
    
    // 加载/保存项目
    void loadProject(const QString& configPath);
    void saveProject(const QString& configPath = QString());
    
    // 文件同步
    void syncToHeader(const QString& headerPath);
    void importFromHeader(const QString& headerPath);
};
```

### 权限管理
```cpp
class PermissionManager
{
public:
    // 设置当前用户
    void setCurrentUser(const QString& userId, UserRole role);
    
    // 权限检查
    bool hasPermission(Permission permission, const QString& resourceId = QString());
    
    // 权限配置
    void setRolePermissions(UserRole role, const Permissions& permissions);
};
```

### 文件同步
```cpp
class FileSync
{
public:
    // 双向同步
    bool jsonToHeader(const QJsonObject& configData, const QString& headerPath);
    bool headerToJson(const QString& headerPath, QJsonObject& configData);
    
    // 文件监视
    void enableFileWatching(const QString& jsonPath, const QString& headerPath);
    
    // 冲突处理
    QStringList checkConflicts(const QString& jsonPath, const QString& headerPath);
};
```

## 配置选项

### 同步选项
```json
{
  "sync_options": {
    "auto_sync": false,
    "create_backup": true,
    "add_timestamp": true,
    "preserve_comments": false,
    "header_guard_prefix": "FEATURE_CONFIG"
  }
}
```

### 权限配置
```json
{
  "permission_config": {
    "strict_mode": false,
    "audit_enabled": true,
    "max_audit_log_size": 1000
  }
}
```

## 扩展开发

### 自定义功能项类型
```cpp
// 继承基础功能项类
class CustomFeatureType : public FeatureItemBase
{
    // 实现自定义验证逻辑
    bool validate(const QJsonValue& value) override;
    
    // 实现自定义UI编辑器
    QWidget* createEditor(QWidget* parent) override;
};
```

### 自定义权限规则
```cpp
// 扩展权限检查逻辑
class CustomPermissionRule : public PermissionRuleBase
{
    bool checkPermission(const QString& userId, Permission permission, 
                        const QString& resourceId) override;
};
```

## 故障排除

### 常见问题

1. **插件加载失败**
   - 检查Qt版本兼容性
   - 验证依赖库是否完整
   - 查看插件注册宏是否正确

2. **文件同步错误**
   - 检查文件权限和路径
   - 验证JSON格式正确性
   - 查看同步选项配置

3. **权限控制问题**
   - 验证用户角色设置
   - 检查权限配置文件
   - 查看审计日志定位问题

### 调试模式
```cpp
// 启用详细日志输出
QLoggingCategory::setFilterRules("*.debug=true");

// 查看权限检查详情
permissionManager->setDebugMode(true);

// 监控文件同步状态
fileSync->setVerboseMode(true);
```

## 版本历史

- **v1.0.0** (2025-08-12)
  - 初始版本发布
  - 基础功能配置管理
  - RBAC权限系统
  - JSON/头文件同步
  - 多用户协作支持

## 贡献指南

1. Fork项目仓库
2. 创建功能分支: `git checkout -b feature/new-feature`
3. 提交变更: `git commit -am 'Add new feature'`
4. 推送分支: `git push origin feature/new-feature`
5. 创建Pull Request

## 许可证

本项目基于 MIT 许可证开源 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 技术支持

- 🐛 问题反馈: [GitHub Issues](https://github.com/your-org/LA/issues)
- 📚 文档: [项目Wiki](https://github.com/your-org/LA/wiki)
- 💬 讨论: [GitHub Discussions](https://github.com/your-org/LA/discussions)

---

*嵌入式功能管理插件 - 让功能配置管理更简单高效*