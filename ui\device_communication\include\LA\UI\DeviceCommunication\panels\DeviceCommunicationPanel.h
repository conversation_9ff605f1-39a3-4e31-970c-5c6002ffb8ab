#pragma once

/**
 * @file DeviceCommunicationPanel.h
 * @brief 设备通信统一管理面板 - 右侧边栏集成
 *
 * Linus式设计：
 * - 替换原来分散的设备管理面板和端口管理面板
 * - 提供统一的设备-端口管理界面
 * - 集成到右侧边栏框架中
 */

#include <LA/RightSidebar/RightSidebarPanel.h>
#include <memory>

QT_BEGIN_NAMESPACE
class QVBoxLayout;
QT_END_NAMESPACE

// 前向声明
namespace LA {
namespace DeviceManagement {
class IDeviceRegistry;
class DeviceManagementOrchestrator;  // Linus: "分层架构协调器"
namespace Discovery {
class IDeviceDiscoveryService;
}
}  // namespace DeviceManagement
namespace Communication {
namespace PortManagement {
class IPortManager;
}
namespace DeviceMatching {
class IDeviceMatchingService;
}
}  // namespace Communication
namespace UI {
namespace DeviceCommunication {
class DevicePortWidget;
}
}  // namespace UI
}  // namespace LA

namespace LA {
namespace UI {
namespace DeviceCommunication {

/**
 * @brief 设备通信统一管理面板
 *
 * 集成了设备发现、端口管理、设备-端口连接的统一管理界面
 * 作为右侧边栏面板提供给用户使用
 */
class DeviceCommunicationPanel : public LA::RightSidebar::RightSidebarPanel {
    Q_OBJECT

  public:
    explicit DeviceCommunicationPanel(QWidget *parent = nullptr);
    virtual ~DeviceCommunicationPanel() = default;

    // 设置后端服务
    void setDeviceRegistry(std::shared_ptr<LA::DeviceManagement::IDeviceRegistry> registry);
    void setDeviceDiscoveryService(std::shared_ptr<LA::DeviceManagement::Discovery::IDeviceDiscoveryService> service);
    void setPortManager(std::shared_ptr<LA::Communication::PortManagement::IPortManager> manager);
    void setDeviceMatchingService(std::shared_ptr<LA::Communication::DeviceMatching::IDeviceMatchingService> service);

    // Linus: "分层架构协调器集成"
    void setDeviceManagementOrchestrator(LA::DeviceManagement::DeviceManagementOrchestrator *orchestrator);

    // RightSidebarPanel 接口实现
    void updateContent() override;

    // 功能接口
    void        refreshDevicesAndPorts();
    int         getConnectedDeviceCount() const;
    QStringList getConnectedDevices() const;

  signals:
    /**
     * @brief 设备连接状态改变信号
     * @param deviceId 设备ID
     * @param portName 端口名称
     * @param connected 是否已连接
     */
    void deviceConnectionChanged(const QString &deviceId, const QString &portName, bool connected);

    /**
     * @brief 连接错误信号
     * @param deviceId 设备ID
     * @param portName 端口名称
     * @param error 错误信息
     */
    void connectionError(const QString &deviceId, const QString &portName, const QString &error);

  public slots:
    /**
     * @brief 处理设备连接事件
     */
    void onDeviceConnected(const QString &deviceId, const QString &portName);

    /**
     * @brief 处理设备断开事件
     */
    void onDeviceDisconnected(const QString &deviceId, const QString &portName);

    /**
     * @brief 处理连接失败事件
     */
    void onConnectionFailed(const QString &deviceId, const QString &portName, const QString &error);

  protected:
    /**
     * @brief 初始化UI内容
     */
    void setupContentUI() override;

    /**
     * @brief 连接信号槽
     */
    void connectSignals();

    /**
     * @brief 更新面板状态
     */
    void updatePanelStatus();

  private:
    // UI组件
    QVBoxLayout *     m_layout;
    DevicePortWidget *m_devicePortWidget;

    // 后端服务 - 暂时注释等待实现
    // std::shared_ptr<LA::DeviceManagement::IDeviceRegistry> m_deviceRegistry;
    // std::shared_ptr<LA::DeviceManagement::Discovery::IDeviceDiscoveryService> m_discoveryService;
    std::shared_ptr<LA::Communication::PortManagement::IPortManager> m_portManager;
};

}  // namespace DeviceCommunication
}  // namespace UI
}  // namespace LA