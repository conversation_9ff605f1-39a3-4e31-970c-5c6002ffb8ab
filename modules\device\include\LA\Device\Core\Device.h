/**
 * @file Device.h
 * @brief 四层组合架构统一设备类
 * 
 * 架构层次：
 * 第4层: Script/Rule Engine - 业务逻辑脚本化
 * 第3层: Strategy - 算法策略选择
 * 第2层: Capabilities - 功能组件组合  
 * 第1层: Driver/Adapter - 硬件驱动适配
 */

#pragma once

#include <QObject>
#include <QString>
#include <QVariantMap>
#include <QVariantList>
#include <QStringList>
#include <QDateTime>
#include <memory>
#include <vector>
#include <map>

namespace LA::Device {

// 前向声明四层架构接口
namespace Script { class ScriptEngine; }
namespace Strategy { class IStrategy; }
namespace Capability { class ICapability; }
namespace Driver { class IDriver; }

/**
 * @brief 四层架构设备状态
 */
enum class DeviceState {
    Uninitialized,      // 未初始化
    Initializing,       // 初始化中 
    Ready,              // 就绪
    Active,             // 活动中
    Script_Executing,   // 脚本执行中
    Error,              // 错误状态
    Disconnected        // 断开连接
};

/**
 * @brief 四层组合架构统一设备类
 * 
 * 设计哲学：
 * - "组合优于继承" - 通过四层组件组合实现功能
 * - "脚本化业务逻辑" - 设备行为可由非工程师调整
 * - "分层清晰" - 每层职责单一，接口明确
 * - "配置驱动" - 组件组合由配置文件决定
 */
class Device : public QObject {
    Q_OBJECT
    Q_ENUM(DeviceState)

public:
    explicit Device(const QString& deviceType, const QString& deviceId, QObject* parent = nullptr);
    virtual ~Device() = default;

    // === 基础设备接口 ===
    
    QString deviceType() const { return m_deviceType; }
    QString deviceId() const { return m_deviceId; }
    DeviceState state() const { return m_state; }
    QString category() const { return m_config.value("category").toString(); }
    
    // === 四层架构命令执行 ===
    
    /**
     * @brief 执行设备命令 - 四层协同处理
     * 
     * 执行流程：
     * 1. Script层：执行行为脚本，决定策略和参数
     * 2. Strategy层：根据脚本选择的策略处理数据
     * 3. Capability层：执行具体功能逻辑
     * 4. Driver层：与硬件实际交互
     * 
     * @param command 命令名
     * @param params 命令参数
     * @return 执行结果
     */
    QVariantMap executeCommand(const QString& command, const QVariantMap& params = {});
    
    /**
     * @brief 脚本化事件处理
     * @param event 事件名 (如 "onMeasurementStart", "onCalibration")
     * @param params 事件参数
     * @return 处理结果
     */
    QVariantMap handleScriptEvent(const QString& event, const QVariantMap& params = {});

    // === 四层组件管理 ===
    
    /**
     * @brief 设置硬件驱动 (第1层)
     * @param driver 驱动实现
     */
    void setDriver(std::unique_ptr<Driver::IDriver> driver);
    
    /**
     * @brief 添加功能能力 (第2层)
     * @param capability 能力组件
     */
    void addCapability(std::unique_ptr<Capability::ICapability> capability);
    
    /**
     * @brief 设置算法策略 (第3层)
     * @param strategyType 策略类型
     * @param strategy 策略实现
     */
    void setStrategy(const QString& strategyType, std::unique_ptr<Strategy::IStrategy> strategy);
    
    /**
     * @brief 加载行为脚本 (第4层)
     * @param scriptFile 脚本文件路径
     * @return 是否加载成功
     */
    bool loadScript(const QString& scriptFile);

    // === 脚本化控制接口 ===
    
    /**
     * @brief 设置脚本变量
     * @param name 变量名
     * @param value 变量值
     */
    void setScriptVariable(const QString& name, const QVariant& value);
    
    /**
     * @brief 获取脚本变量
     * @param name 变量名
     * @return 变量值
     */
    QVariant getScriptVariable(const QString& name);
    
    /**
     * @brief 重新加载脚本 (热更新)
     * @return 是否成功
     */
    bool reloadScript();
    
    /**
     * @brief 调用脚本函数
     * @param functionName 函数名
     * @param args 参数
     * @return 返回值
     */
    QVariant callScriptFunction(const QString& functionName, const QVariantList& args = {});

    // === 设备配置和属性 ===
    
    /**
     * @brief 从配置创建设备 (工厂方法)
     * @param config 设备配置
     * @return 设备实例
     */
    static std::unique_ptr<Device> createFromConfig(const QVariantMap& config);
    
    /**
     * @brief 获取设备所有属性
     * @return 完整属性映射
     */
    QVariantMap getAllProperties() const;
    
    /**
     * @brief 获取支持的命令列表
     * @return 命令列表
     */
    QStringList getSupportedCommands() const;
    
    /**
     * @brief 检查是否支持命令
     * @param command 命令名
     * @return 是否支持
     */
    bool hasCommand(const QString& command) const;
    
    /**
     * @brief 获取设备规格参数
     * @param key 参数键名
     * @return 参数值
     */
    QVariant getSpec(const QString& key) const;

    // === 生命周期管理 ===
    
    bool initialize();
    bool connect();
    bool disconnect();
    bool start();
    bool stop();

Q_SIGNALS:
    // 设备状态信号
    void stateChanged(DeviceState newState);
    void deviceError(const QString& error);
    
    // 命令执行信号
    void commandExecuted(const QString& command, const QVariantMap& result);
    void commandFailed(const QString& command, const QString& error);
    
    // 脚本执行信号
    void scriptEventTriggered(const QString& event, const QVariantMap& data);
    void scriptError(const QString& error);
    void scriptReloaded();

private slots:
    void onScriptError(const QString& error);
    void onScriptTimeout();

private:
    // 设备基本属性
    QString m_deviceType;
    QString m_deviceId;
    QVariantMap m_config;
    QVariantMap m_properties;
    DeviceState m_state;
    QDateTime m_createTime;
    QString m_lastError;
    
    // 四层架构组件
    std::unique_ptr<Driver::IDriver> m_driver;                                  // 第1层: 驱动
    std::vector<std::unique_ptr<Capability::ICapability>> m_capabilities;       // 第2层: 能力
    std::map<QString, std::unique_ptr<Strategy::IStrategy>> m_strategies;       // 第3层: 策略
    // std::unique_ptr<Script::ScriptEngine> m_scriptEngine;                       // 第4层: 脚本 - 临时注释，修复不完整类型问题
    
    // 命令路由缓存
    std::map<QString, Capability::ICapability*> m_commandRoutes;
    
    // === 内部方法 ===
    
    void setState(DeviceState newState);
    void buildCommandRoutes();
    Capability::ICapability* findCapabilityForCommand(const QString& command) const;
    
    /**
     * @brief 四层协同执行命令
     */
    QVariantMap executeCommandThroughLayers(const QString& command, const QVariantMap& params);
    
    /**
     * @brief 应用脚本选择的策略
     */
    QVariantMap applyScriptSelectedStrategy(const QString& command, const QVariantMap& data);
    
    /**
     * @brief 验证组件完整性
     */
    bool validateComponents() const;
};

// ===== 四层架构接口定义 =====

namespace Driver {
/**
 * @brief 第1层: 驱动适配器接口
 * 职责: 与具体硬件设备交互
 */
class IDriver {
public:
    virtual ~IDriver() = default;
    
    virtual bool initialize() = 0;
    virtual bool connect() = 0;
    virtual bool disconnect() = 0;
    virtual QVariantMap sendCommand(const QString& command, const QVariantMap& params) = 0;
    virtual QString getDriverType() const = 0;
    virtual bool isConnected() const = 0;
};
}

namespace Capability {
/**
 * @brief 第2层: 功能能力接口  
 * 职责: 提供设备功能模块
 */
class ICapability {
public:
    virtual ~ICapability() = default;
    
    virtual QString getCapabilityName() const = 0;
    virtual QStringList getProvidedCommands() const = 0;
    virtual QVariantMap executeCapability(const QString& command, const QVariantMap& params, Driver::IDriver* driver) = 0;
    virtual bool isAvailable() const = 0;
};
}

namespace Strategy {
/**
 * @brief 第3层: 算法策略接口
 * 职责: 提供可选择的算法策略
 */
class IStrategy {
public:
    virtual ~IStrategy() = default;
    
    virtual QString getStrategyType() const = 0;
    virtual QString getStrategyName() const = 0;
    virtual QVariantMap executeStrategy(const QVariantMap& input) = 0;
    virtual QVariantMap getStrategyConfig() const = 0;
    virtual bool setStrategyConfig(const QVariantMap& config) = 0;
};
}

} // namespace LA::Device

Q_DECLARE_METATYPE(LA::Device::DeviceState)