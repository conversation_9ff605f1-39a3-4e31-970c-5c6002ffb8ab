#pragma once

#include "IDeviceIdentifier.h"
#include <QRegExp>
#include <QJsonDocument>
#include <QJsonObject>

namespace LA {
namespace DeviceManagement {
namespace Matching {

/**
 * @brief 通用设备识别器 - IDeviceIdentifier的具体实现
 * 
 * Linus: "只做一件事：解析响应数据并识别设备类型"
 * 支持多种设备类型的自动识别
 */
class UniversalDeviceIdentifier : public IDeviceIdentifier {
public:
    UniversalDeviceIdentifier();
    virtual ~UniversalDeviceIdentifier() = default;

    // ====== IDeviceIdentifier接口实现 ======
    QString identifyDeviceType(const QByteArray& response) override;
    DeviceInfo extractDeviceInfo(const QByteArray& response) override;
    QStringList getDeviceCapabilities(const QString& deviceType) override;
    bool isValidDeviceResponse(const QByteArray& response) override;
    void addIdentificationRule(const IdentificationRule& rule) override;
    QStringList getSupportedDeviceTypes() override;

private:
    // ====== 初始化默认规则 ======
    void initializeDefaultRules();
    void initializeSprmRules();
    void initializeMotorRules();
    void initializeSensorRules();
    void initializeCapabilities();
    
    // ====== 识别处理 ======
    QString matchByPattern(const QByteArray& response);
    QString matchByKeywords(const QByteArray& response);
    QString matchByStructure(const QByteArray& response);
    
    // ====== 信息提取 ======
    DeviceInfo extractSprmInfo(const QByteArray& response);
    DeviceInfo extractMotorInfo(const QByteArray& response);
    DeviceInfo extractSensorInfo(const QByteArray& response);
    DeviceInfo extractGenericInfo(const QString& deviceType, const QByteArray& response);
    
    // ====== 工具方法 ======
    QString extractValue(const QByteArray& data, const QString& key);
    QString extractValueByRegex(const QByteArray& data, const QString& pattern);
    bool containsKeywords(const QByteArray& data, const QStringList& keywords);
    QVariantMap parseProperties(const QByteArray& response, const QVariantMap& rules);
    
    // ====== 数据成员 ======
    QList<IdentificationRule> m_rules;              // 识别规则
    QMap<QString, QStringList> m_deviceCapabilities; // 设备能力映射
    QStringList m_supportedTypes;                   // 支持的设备类型
};

} // namespace Matching
} // namespace DeviceManagement
} // namespace LA