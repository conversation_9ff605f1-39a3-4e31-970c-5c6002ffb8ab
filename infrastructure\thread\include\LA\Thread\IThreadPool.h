#ifndef LA_ITHREADPOOL_H
#define LA_ITHREADPOOL_H

#include "ThreadTypes.h"
#include <QObject>
#include <QRunnable>
#include <future>

namespace LA {
namespace Thread {

/**
 * @brief 线程池接口
 * 
 * 提供高效的线程池管理，支持任务队列、优先级调度、负载均衡等功能。
 * 适用于工业软件中的大量并发任务处理。
 */
class IThreadPool : public QObject
{
    Q_OBJECT

public:
    virtual ~IThreadPool() = default;

    // 线程池控制
    virtual void setMaxThreadCount(int count) = 0;
    virtual int maxThreadCount() const = 0;
    virtual int activeThreadCount() const = 0;
    virtual int queuedTaskCount() const = 0;
    
    // 任务提交 - 函数式接口
    template<typename Func>
    auto submit(Func&& func) -> std::future<decltype(func())> {
        using ReturnType = decltype(func());
        
        auto task = std::make_shared<std::packaged_task<ReturnType()>>(
            std::forward<Func>(func)
        );
        
        auto future = task->get_future();
        
        submitTask([task](){ (*task)(); }, TaskType::Normal, 0, "Lambda");
        
        return future;
    }
    
    // 任务提交 - 基础接口
    virtual void submitTask(TaskFunction task, 
                          TaskType type = TaskType::Normal, 
                          int priority = 0,
                          const QString& name = "") = 0;
                          
    virtual void submitTask(QRunnable* runnable, int priority = 0) = 0;
    
    // 批量任务处理
    virtual void submitTasks(const QList<Task>& tasks) = 0;
    
    // 特殊任务类型
    virtual void submitCriticalTask(TaskFunction task, const QString& name = "") = 0;
    virtual void submitBackgroundTask(TaskFunction task, const QString& name = "") = 0;
    
    // 线程池状态控制
    virtual void clear() = 0;
    virtual bool waitForDone(int msecs = -1) = 0;
    virtual void pause() = 0;
    virtual void resume() = 0;
    virtual bool isPaused() const = 0;
    
    // 任务管理
    virtual bool cancelTask(const QString& taskName) = 0;
    virtual void cancelAllTasks() = 0;
    virtual void setTaskTimeout(int timeoutMs) = 0;
    
    // 统计信息
    virtual qint64 completedTaskCount() const = 0;
    virtual qint64 failedTaskCount() const = 0;
    virtual qint64 totalTaskCount() const = 0;
    virtual double averageTaskExecutionTime() const = 0;
    virtual double threadPoolEfficiency() const = 0;
    
    // 性能监控
    virtual void enablePerformanceMonitoring(bool enable) = 0;
    virtual bool isPerformanceMonitoringEnabled() const = 0;

signals:
    // 任务状态信号
    void taskStarted(const QString& taskName, const QString& threadName);
    void taskCompleted(const QString& taskName, qint64 executionTime);
    void taskFailed(const QString& taskName, const QString& error);
    void taskCancelled(const QString& taskName);
    
    // 线程池状态信号
    void threadPoolStateChanged(bool active);
    void threadCountChanged(int active, int total);
    void queueSizeChanged(int queueSize);
    
    // 性能信号
    void performanceReport(double efficiency, double avgExecutionTime, qint64 totalTasks);
    void overloadWarning(int queueSize, int maxQueueSize);

protected:
    explicit IThreadPool(QObject* parent = nullptr) : QObject(parent) {}

private:
    Q_DISABLE_COPY(IThreadPool)
};

} // namespace Thread
} // namespace LA

#endif // LA_ITHREADPOOL_H