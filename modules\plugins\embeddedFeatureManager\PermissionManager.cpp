#include "PermissionManager.h"

#include <QJsonDocument>
#include <QJsonValue>
#include <QDateTime>
#include <QDebug>
#include <QMetaEnum>

// 静态常量定义
const QHash<PermissionManager::UserRole, QString> PermissionManager::ROLE_NAMES = {
    { UNKNOWN_ROLE, "Unknown" },
    { BUSINESS_USER, "Business User" },
    { PRODUCT_MANAGER, "Product Manager" },
    { PROJECT_MANAGER, "Project Manager" },
    { DEVELOPER, "Developer" },
    { TESTER, "Tester" },
    { SYSTEM_ADMIN, "System Admin" }
};

const QHash<PermissionManager::Permission, QString> PermissionManager::PERMISSION_NAMES = {
    { VIEW_FEATURES, "View Features" },
    { EDIT_FEATURES, "Edit Features" },
    { DELETE_FEATURES, "Delete Features" },
    { VIEW_CONFIG, "View Config" },
    { EDIT_CONFIG, "Edit Config" },
    { VIEW_SENSITIVE_CONFIG, "View Sensitive Config" },
    { SYNC_FILES, "Sync Files" },
    { IMPORT_CONFIG, "Import Config" },
    { EXPORT_CONFIG, "Export Config" },
    { CREATE_PROJECT, "Create Project" },
    { DELETE_PROJECT, "Delete Project" },
    { MANAGE_TARGETS, "Manage Targets" },
    { APPROVE_CHANGES, "Approve Changes" },
    { DEPLOY_CONFIG, "Deploy Config" },
    { MANAGE_USERS, "Manage Users" },
    { MANAGE_PERMISSIONS, "Manage Permissions" },
    { VIEW_AUDIT_LOG, "View Audit Log" },
    { FULL_ACCESS, "Full Access" }
};

PermissionManager::PermissionManager(QObject *parent)
    : QObject(parent)
    , m_currentUserId("anonymous")
    , m_currentRole(DEVELOPER)  // 默认开发者角色
    , m_strictMode(false)
    , m_auditEnabled(true)
    , m_maxAuditLogSize(1000)
{
    initializeDefaultRolePermissions();
    refreshPermissions();
    
    qDebug() << "PermissionManager initialized with default developer permissions";
}

PermissionManager::~PermissionManager()
{
    qDebug() << "PermissionManager destroyed";
}

void PermissionManager::setCurrentUser(const QString& userId, UserRole role, 
                                      const Permissions& customPermissions)
{
    QString oldUserId = m_currentUserId;
    UserRole oldRole = m_currentRole;
    Permissions oldPermissions = m_currentPermissions;
    
    m_currentUserId = userId;
    m_currentRole = role;
    
    // 如果提供了自定义权限，使用它们，否则使用角色默认权限
    if (customPermissions != Permissions()) {
        setUserPermissions(userId, customPermissions);
    }
    
    refreshPermissions();
    
    // 发出变更信号
    if (oldRole != role) {
        emit userRoleChanged(userId, oldRole, role);
    }
    
    if (oldPermissions != m_currentPermissions) {
        emit permissionChanged(userId, oldPermissions, m_currentPermissions);
    }
    
    qDebug() << QString("User changed: %1 -> %2, Role: %3")
                .arg(oldUserId).arg(userId).arg(roleToString(role));
}

QString PermissionManager::getCurrentUserRoleString() const
{
    return ROLE_NAMES.value(m_currentRole, "Unknown");
}

bool PermissionManager::hasPermission(Permission permission, const QString& resourceId,
                                     ResourceType resourceType) const
{
    // 系统管理员拥有所有权限
    if (m_currentRole == SYSTEM_ADMIN) {
        auditPermission(permission, resourceId, "Admin access", true);
        return true;
    }
    
    bool granted = false;
    QString reason;
    
    // 检查用户是否有该权限
    if (m_currentPermissions.testFlag(permission)) {
        // 检查资源级权限
        if (!resourceId.isEmpty() && m_resourcePermissions.contains(m_currentUserId)) {
            const auto& userResourcePerms = m_resourcePermissions.value(m_currentUserId);
            if (userResourcePerms.contains(resourceId)) {
                granted = userResourcePerms.value(resourceId).testFlag(permission);
                if (!granted) {
                    reason = QString("Resource level permission denied for: %1").arg(resourceId);
                }
            } else {
                granted = true; // 没有特定资源限制，使用用户级权限
            }
        } else {
            granted = true; // 有用户级权限且没有资源级限制
        }
    } else {
        reason = QString("User lacks permission: %1").arg(PERMISSION_NAMES.value(permission, "Unknown"));
    }
    
    // 记录权限审计
    if (m_auditEnabled) {
        QString action = QString("Permission check: %1").arg(PERMISSION_NAMES.value(permission, "Unknown"));
        if (!resourceId.isEmpty()) {
            action += QString(" on resource: %1").arg(resourceId);
        }
        auditPermission(permission, resourceId, action, granted);
    }
    
    // 如果权限被拒绝且启用了严格模式，发出信号
    if (!granted && m_strictMode) {
        emit const_cast<PermissionManager*>(this)->permissionDenied(permission, reason);
    }
    
    return granted;
}

bool PermissionManager::hasPermissions(const Permissions& permissions, bool requireAll) const
{
    if (permissions == Permissions()) {
        return true; // 没有权限要求
    }
    
    if (requireAll) {
        // 需要所有权限
        return (m_currentPermissions & permissions) == permissions;
    } else {
        // 只需要任一权限
        return (m_currentPermissions & permissions) != Permissions();
    }
}

PermissionManager::Permissions PermissionManager::getCurrentPermissions() const
{
    return m_currentPermissions;
}

PermissionManager::Permissions PermissionManager::getRolePermissions(UserRole role) const
{
    return m_rolePermissions.value(role, Permissions());
}

void PermissionManager::setRolePermissions(UserRole role, const Permissions& permissions)
{
    Permissions oldPermissions = m_rolePermissions.value(role, Permissions());
    m_rolePermissions[role] = permissions;
    
    // 如果当前用户是该角色，更新权限
    if (m_currentRole == role) {
        refreshPermissions();
        emit permissionChanged(m_currentUserId, oldPermissions, m_currentPermissions);
    }
    
    qDebug() << QString("Role permissions updated for %1").arg(roleToString(role));
}

void PermissionManager::setUserPermissions(const QString& userId, const Permissions& permissions)
{
    m_userPermissions[userId] = permissions;
    
    // 如果是当前用户，刷新权限
    if (m_currentUserId == userId) {
        Permissions oldPermissions = m_currentPermissions;
        refreshPermissions();
        emit permissionChanged(userId, oldPermissions, m_currentPermissions);
    }
    
    qDebug() << QString("User permissions updated for %1").arg(userId);
}

void PermissionManager::setResourcePermissions(const QString& userId, const QString& resourceId,
                                              ResourceType resourceType, const Permissions& permissions)
{
    if (!m_resourcePermissions.contains(userId)) {
        m_resourcePermissions[userId] = QHash<QString, Permissions>();
    }
    
    m_resourcePermissions[userId][resourceId] = permissions;
    
    qDebug() << QString("Resource permissions updated for user %1, resource %2").arg(userId).arg(resourceId);
}

void PermissionManager::loadPermissionConfig(const QJsonObject& configData)
{
    qDebug() << "Loading permission configuration...";
    
    // 加载角色权限
    QJsonObject rolePerms = configData.value("role_permissions").toObject();
    for (auto it = rolePerms.begin(); it != rolePerms.end(); ++it) {
        UserRole role = roleFromString(it.key());
        if (role != UNKNOWN_ROLE) {
            Permissions perms = permissionsFromString(it.value().toString());
            m_rolePermissions[role] = perms;
        }
    }
    
    // 加载用户权限
    QJsonObject userPerms = configData.value("user_permissions").toObject();
    for (auto it = userPerms.begin(); it != userPerms.end(); ++it) {
        QString userId = it.key();
        Permissions perms = permissionsFromString(it.value().toString());
        m_userPermissions[userId] = perms;
    }
    
    // 加载资源权限
    QJsonObject resourcePerms = configData.value("resource_permissions").toObject();
    for (auto userIt = resourcePerms.begin(); userIt != resourcePerms.end(); ++userIt) {
        QString userId = userIt.key();
        QJsonObject userResourcePerms = userIt.value().toObject();
        
        QHash<QString, Permissions> resourceMap;
        for (auto resIt = userResourcePerms.begin(); resIt != userResourcePerms.end(); ++resIt) {
            QString resourceId = resIt.key();
            Permissions perms = permissionsFromString(resIt.value().toString());
            resourceMap[resourceId] = perms;
        }
        
        m_resourcePermissions[userId] = resourceMap;
    }
    
    // 加载配置选项
    QJsonObject options = configData.value("options").toObject();
    m_strictMode = options.value("strict_mode").toBool(false);
    m_auditEnabled = options.value("audit_enabled").toBool(true);
    m_maxAuditLogSize = options.value("max_audit_log_size").toInt(1000);
    
    // 刷新当前用户权限
    refreshPermissions();
    
    qDebug() << "Permission configuration loaded successfully";
}

QJsonObject PermissionManager::savePermissionConfig() const
{
    QJsonObject config;
    
    // 保存角色权限
    QJsonObject rolePerms;
    for (auto it = m_rolePermissions.begin(); it != m_rolePermissions.end(); ++it) {
        rolePerms[roleToString(it.key())] = permissionsToString(it.value());
    }
    config["role_permissions"] = rolePerms;
    
    // 保存用户权限
    QJsonObject userPerms;
    for (auto it = m_userPermissions.begin(); it != m_userPermissions.end(); ++it) {
        userPerms[it.key()] = permissionsToString(it.value());
    }
    config["user_permissions"] = userPerms;
    
    // 保存资源权限
    QJsonObject resourcePerms;
    for (auto userIt = m_resourcePermissions.begin(); userIt != m_resourcePermissions.end(); ++userIt) {
        QString userId = userIt.key();
        QJsonObject userResourcePerms;
        
        const auto& resourceMap = userIt.value();
        for (auto resIt = resourceMap.begin(); resIt != resourceMap.end(); ++resIt) {
            userResourcePerms[resIt.key()] = permissionsToString(resIt.value());
        }
        
        resourcePerms[userId] = userResourcePerms;
    }
    config["resource_permissions"] = resourcePerms;
    
    // 保存配置选项
    QJsonObject options;
    options["strict_mode"] = m_strictMode;
    options["audit_enabled"] = m_auditEnabled;
    options["max_audit_log_size"] = m_maxAuditLogSize;
    config["options"] = options;
    
    // 保存审计日志
    config["audit_log"] = m_auditLog;
    
    return config;
}

void PermissionManager::resetToDefaultPermissions()
{
    m_rolePermissions.clear();
    m_userPermissions.clear();
    m_resourcePermissions.clear();
    m_auditLog = QJsonArray();
    
    initializeDefaultRolePermissions();
    refreshPermissions();
    
    qDebug() << "Permissions reset to default values";
    emit permissionChanged(m_currentUserId, Permissions(), m_currentPermissions);
}

void PermissionManager::auditPermission(Permission permission, const QString& resourceId,
                                       const QString& action, bool granted) const
{
    if (!m_auditEnabled) {
        return;
    }
    
    QJsonObject auditEntry;
    auditEntry["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    auditEntry["user_id"] = m_currentUserId;
    auditEntry["role"] = roleToString(m_currentRole);
    auditEntry["permission"] = static_cast<int>(permission);
    auditEntry["permission_name"] = PERMISSION_NAMES.value(permission, "Unknown");
    auditEntry["resource_id"] = resourceId;
    auditEntry["action"] = action;
    auditEntry["granted"] = granted;
    
    m_auditLog.append(auditEntry);
    
    // 限制审计日志大小
    while (m_auditLog.size() > m_maxAuditLogSize) {
        m_auditLog.removeFirst();
    }
}

QJsonArray PermissionManager::getAuditLog(int limit) const
{
    if (limit <= 0 || limit >= m_auditLog.size()) {
        return m_auditLog;
    }
    
    QJsonArray result;
    int startIndex = qMax(0, m_auditLog.size() - limit);
    
    for (int i = startIndex; i < m_auditLog.size(); ++i) {
        result.append(m_auditLog.at(i));
    }
    
    return result;
}

void PermissionManager::refreshPermissions()
{
    m_currentPermissions = calculateEffectivePermissions(m_currentUserId, m_currentRole);
    qDebug() << QString("Permissions refreshed for user %1, role %2")
                .arg(m_currentUserId).arg(roleToString(m_currentRole));
}

void PermissionManager::initializeDefaultRolePermissions()
{
    // 业务人员权限
    Permissions businessUserPerms = VIEW_FEATURES | VIEW_CONFIG;
    m_rolePermissions[BUSINESS_USER] = businessUserPerms;
    
    // 产品经理权限
    Permissions productManagerPerms = VIEW_FEATURES | EDIT_FEATURES | VIEW_CONFIG | EDIT_CONFIG |
                                     EXPORT_CONFIG | CREATE_PROJECT | APPROVE_CHANGES;
    m_rolePermissions[PRODUCT_MANAGER] = productManagerPerms;
    
    // 项目经理权限
    Permissions projectManagerPerms = VIEW_FEATURES | EDIT_FEATURES | DELETE_FEATURES |
                                     VIEW_CONFIG | EDIT_CONFIG | VIEW_SENSITIVE_CONFIG |
                                     EXPORT_CONFIG | CREATE_PROJECT | DELETE_PROJECT |
                                     MANAGE_TARGETS | APPROVE_CHANGES | DEPLOY_CONFIG | VIEW_AUDIT_LOG;
    m_rolePermissions[PROJECT_MANAGER] = projectManagerPerms;
    
    // 开发工程师权限
    Permissions developerPerms = VIEW_FEATURES | EDIT_FEATURES | VIEW_CONFIG | EDIT_CONFIG |
                                VIEW_SENSITIVE_CONFIG | SYNC_FILES | IMPORT_CONFIG | EXPORT_CONFIG |
                                MANAGE_TARGETS;
    m_rolePermissions[DEVELOPER] = developerPerms;
    
    // 测试人员权限
    Permissions testerPerms = VIEW_FEATURES | EDIT_FEATURES | VIEW_CONFIG | IMPORT_CONFIG | EXPORT_CONFIG;
    m_rolePermissions[TESTER] = testerPerms;
    
    // 系统管理员权限（完全访问）
    m_rolePermissions[SYSTEM_ADMIN] = FULL_ACCESS;
    
    qDebug() << "Default role permissions initialized";
}

PermissionManager::Permissions PermissionManager::calculateEffectivePermissions(const QString& userId, UserRole role) const
{
    // 从角色权限开始
    Permissions effectivePerms = m_rolePermissions.value(role, Permissions());
    
    // 添加用户特定权限
    if (m_userPermissions.contains(userId)) {
        effectivePerms |= m_userPermissions.value(userId);
    }
    
    return effectivePerms;
}

QString PermissionManager::permissionsToString(const Permissions& permissions) const
{
    if (permissions == FULL_ACCESS) {
        return "FULL_ACCESS";
    }
    
    QStringList permList;
    
    // 检查每个权限标志
    QMetaEnum metaEnum = QMetaEnum::fromType<Permission>();
    for (int i = 0; i < metaEnum.keyCount(); ++i) {
        Permission perm = static_cast<Permission>(metaEnum.value(i));
        if (permissions.testFlag(perm) && perm != FULL_ACCESS) {
            permList << metaEnum.key(i);
        }
    }
    
    return permList.join("|");
}

PermissionManager::Permissions PermissionManager::permissionsFromString(const QString& permissionStr) const
{
    if (permissionStr == "FULL_ACCESS") {
        return FULL_ACCESS;
    }
    
    Permissions result;
    QStringList permList = permissionStr.split("|", Qt::SkipEmptyParts);
    
    QMetaEnum metaEnum = QMetaEnum::fromType<Permission>();
    for (const QString& permName : permList) {
        bool ok;
        int value = metaEnum.keyToValue(permName.trimmed().toUtf8().constData(), &ok);
        if (ok) {
            result |= static_cast<Permission>(value);
        }
    }
    
    return result;
}

QString PermissionManager::roleToString(UserRole role) const
{
    QMetaEnum metaEnum = QMetaEnum::fromType<UserRole>();
    return metaEnum.valueToKey(role);
}

PermissionManager::UserRole PermissionManager::roleFromString(const QString& roleStr) const
{
    QMetaEnum metaEnum = QMetaEnum::fromType<UserRole>();
    bool ok;
    int value = metaEnum.keyToValue(roleStr.toUtf8().constData(), &ok);
    return ok ? static_cast<UserRole>(value) : UNKNOWN_ROLE;
}