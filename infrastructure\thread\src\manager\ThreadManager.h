#ifndef LA_THREADMANAGER_H
#define LA_THREADMANAGER_H

#include "../../include/LA/Thread/IThreadManager.h"
#include <QMap>
#include <QMutex>
#include <QTimer>
#include <QDateTime>

namespace LA {
namespace Thread {

class ThreadPool;
class CommunicationThread;

/**
 * @brief 线程管理器实现类
 * 
 * 实现统一的线程管理功能，包括普通线程、线程池和通信线程的管理。
 * 采用单例模式，提供全局唯一的线程管理实例。
 */
class ThreadManager : public IThreadManager
{
    Q_OBJECT

public:
    ~ThreadManager() override;

    // 单例获取
    static IThreadManager* instance();

    // 基础线程管理
    QThread* createThread(const QString& name, QObject* worker = nullptr) override;
    bool startThread(const QString& name) override;
    bool stopThread(const QString& name, int timeoutMs = 5000) override;
    bool isThreadRunning(const QString& name) const override;
    ThreadState getThreadState(const QString& name) const override;
    
    // 线程池管理
    IThreadPool* getThreadPool() const override;
    void setMaxThreadCount(int count) override;
    int maxThreadCount() const override;
    int activeThreadCount() const override;
    
    // 通信线程管理
    ICommunicationThread* createCommunicationThread(const QString& deviceId, 
                                                   const ThreadConfig& config = ThreadConfig()) override;
    ICommunicationThread* getCommunicationThread(const QString& deviceId) const override;
    bool removeCommunicationThread(const QString& deviceId) override;
    
    // 线程监控
    QStringList getActiveThreads() const override;
    QStringList getCommunicationThreads() const override;
    ThreadStatistics getThreadStatistics(const QString& name) const override;
    QList<ThreadStatistics> getAllThreadStatistics() const override;
    
    // 全局控制
    void stopAllThreads(int timeoutMs = 10000) override;
    void pauseAllThreads() override;
    void resumeAllThreads() override;
    
    // 配置管理
    void setGlobalThreadConfig(const ThreadConfig& config) override;
    ThreadConfig getGlobalThreadConfig() const override;

private slots:
    void updateStatistics();
    void onThreadPoolTaskCompleted();
    void onCommunicationThreadConnected(const QString& deviceId);
    void onCommunicationThreadDisconnected(const QString& deviceId);
    void onCommunicationThreadError(const QString& deviceId, const QString& error);

private:
    explicit ThreadManager(QObject* parent = nullptr);
    
    void updateThreadState(const QString& name, ThreadState newState);

private:
    // 单例相关
    static ThreadManager* s_instance;
    static QMutex s_instanceMutex;
    
    // 线程管理
    mutable QMutex m_mutex;
    QMap<QString, QThread*> m_threads;
    QMap<QString, ThreadState> m_threadStates;
    QMap<QString, ThreadStatistics> m_threadStatistics;
    QMap<QString, qint64> m_threadStartTimes;
    
    // 通信线程管理
    QMap<QString, ICommunicationThread*> m_communicationThreads;
    
    // 线程池
    ThreadPool* m_threadPool;
    
    // 配置
    ThreadConfig m_globalConfig;
    
    // 监控
    QTimer* m_statisticsTimer;
    
    Q_DISABLE_COPY(ThreadManager)
};

} // namespace Thread
} // namespace LA

#endif // LA_THREADMANAGER_H