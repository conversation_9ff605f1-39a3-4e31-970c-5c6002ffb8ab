#ifndef PERMISSION_MANAGER_H
#define PERMISSION_MANAGER_H

#include <QObject>
#include <QString>
#include <QStringList>
#include <QJsonObject>
#include <QJsonArray>
#include <QHash>
#include <QSet>

/**
 * @brief 基于角色的访问控制(RBAC)权限管理器
 * 
 * 实现嵌入式功能管理系统的多角色权限控制：
 * - 角色管理：产品经理、业务人员、项目经理、开发工程师、测试人员
 * - 权限管理：功能级、操作级、数据级权限控制
 * - 动态权限：支持运行时权限变更
 * - 权限继承：角色间的权限继承关系
 * 
 * 权限矩阵参考嵌入式功能管理QT软件架构设计
 */
class PermissionManager : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 用户角色枚举
     */
    enum UserRole {
        UNKNOWN_ROLE = 0,
        BUSINESS_USER = 1,          // 业务人员
        PRODUCT_MANAGER = 2,        // 产品经理
        PROJECT_MANAGER = 3,        // 项目经理
        DEVELOPER = 4,              // 开发工程师
        TESTER = 5,                 // 测试人员
        SYSTEM_ADMIN = 6            // 系统管理员
    };
    Q_ENUM(UserRole)

    /**
     * @brief 权限类型枚举
     */
    enum Permission {
        // 基础权限
        VIEW_FEATURES = 1,              // 查看功能项
        EDIT_FEATURES = 2,              // 编辑功能项
        DELETE_FEATURES = 4,            // 删除功能项
        
        // 配置权限
        VIEW_CONFIG = 8,                // 查看配置
        EDIT_CONFIG = 16,               // 编辑配置
        VIEW_SENSITIVE_CONFIG = 32,     // 查看敏感配置
        
        // 文件操作权限
        SYNC_FILES = 64,                // 同步文件
        IMPORT_CONFIG = 128,            // 导入配置
        EXPORT_CONFIG = 256,            // 导出配置
        
        // 项目管理权限
        CREATE_PROJECT = 512,           // 创建项目
        DELETE_PROJECT = 1024,          // 删除项目
        MANAGE_TARGETS = 2048,          // 管理目标平台
        
        // 审批权限
        APPROVE_CHANGES = 4096,         // 审批变更
        DEPLOY_CONFIG = 8192,           // 部署配置
        
        // 系统权限
        MANAGE_USERS = 16384,           // 用户管理
        MANAGE_PERMISSIONS = 32768,     // 权限管理
        VIEW_AUDIT_LOG = 65536,         // 查看审计日志
        
        // 组合权限
        FULL_ACCESS = 0x7FFFFFFF        // 完全访问权限
    };
    Q_DECLARE_FLAGS(Permissions, Permission)
    Q_FLAG(Permissions)

    /**
     * @brief 资源类型枚举
     */
    enum ResourceType {
        FEATURE_RESOURCE,
        CONFIG_RESOURCE,
        PROJECT_RESOURCE,
        FILE_RESOURCE,
        USER_RESOURCE
    };
    Q_ENUM(ResourceType)

public:
    explicit PermissionManager(QObject *parent = nullptr);
    virtual ~PermissionManager();

    /**
     * @brief 设置当前用户
     * @param userId 用户ID
     * @param role 用户角色
     * @param customPermissions 自定义权限(可选)
     */
    void setCurrentUser(const QString& userId, UserRole role, 
                       const Permissions& customPermissions = Permissions());
    
    /**
     * @brief 获取当前用户ID
     */
    QString getCurrentUserId() const { return m_currentUserId; }
    
    /**
     * @brief 获取当前用户角色
     */
    UserRole getCurrentUserRole() const { return m_currentRole; }
    
    /**
     * @brief 获取当前用户角色名称
     */
    QString getCurrentUserRoleString() const;
    
    /**
     * @brief 检查权限
     * @param permission 权限类型
     * @param resourceId 资源ID(可选)
     * @param resourceType 资源类型(可选)
     * @return 是否有权限
     */
    bool hasPermission(Permission permission, const QString& resourceId = QString(),
                      ResourceType resourceType = FEATURE_RESOURCE) const;
    
    /**
     * @brief 检查多个权限
     * @param permissions 权限集合
     * @param requireAll 是否需要全部权限(true)或任一权限(false)
     * @return 是否满足权限要求
     */
    bool hasPermissions(const Permissions& permissions, bool requireAll = true) const;
    
    /**
     * @brief 获取当前用户的所有权限
     */
    Permissions getCurrentPermissions() const;
    
    /**
     * @brief 获取角色的默认权限
     * @param role 角色
     */
    Permissions getRolePermissions(UserRole role) const;
    
    /**
     * @brief 角色权限配置
     * @param role 角色
     * @param permissions 权限集合
     */
    void setRolePermissions(UserRole role, const Permissions& permissions);
    
    /**
     * @brief 用户特定权限配置
     * @param userId 用户ID
     * @param permissions 权限集合
     */
    void setUserPermissions(const QString& userId, const Permissions& permissions);
    
    /**
     * @brief 资源级权限配置
     * @param userId 用户ID
     * @param resourceId 资源ID
     * @param resourceType 资源类型
     * @param permissions 权限集合
     */
    void setResourcePermissions(const QString& userId, const QString& resourceId,
                               ResourceType resourceType, const Permissions& permissions);
    
    /**
     * @brief 加载权限配置
     * @param configData JSON配置数据
     */
    void loadPermissionConfig(const QJsonObject& configData);
    
    /**
     * @brief 保存权限配置
     * @return JSON配置数据
     */
    QJsonObject savePermissionConfig() const;
    
    /**
     * @brief 重置为默认权限配置
     */
    void resetToDefaultPermissions();
    
    /**
     * @brief 权限审计记录
     * @param permission 权限
     * @param resourceId 资源ID
     * @param action 操作描述
     * @param granted 是否授权
     */
    void auditPermission(Permission permission, const QString& resourceId,
                        const QString& action, bool granted);
    
    /**
     * @brief 获取权限审计日志
     * @param limit 记录数量限制
     * @return 审计日志数组
     */
    QJsonArray getAuditLog(int limit = 100) const;

public slots:
    /**
     * @brief 刷新权限设置
     */
    void refreshPermissions();

signals:
    /**
     * @brief 权限发生变更
     * @param userId 用户ID
     * @param oldPermissions 旧权限
     * @param newPermissions 新权限
     */
    void permissionChanged(const QString& userId, const Permissions& oldPermissions,
                          const Permissions& newPermissions);
    
    /**
     * @brief 用户角色发生变更
     * @param userId 用户ID
     * @param oldRole 旧角色
     * @param newRole 新角色
     */
    void userRoleChanged(const QString& userId, UserRole oldRole, UserRole newRole);
    
    /**
     * @brief 权限检查被拒绝
     * @param permission 权限
     * @param reason 拒绝原因
     */
    void permissionDenied(Permission permission, const QString& reason);

private:
    /**
     * @brief 初始化默认角色权限
     */
    void initializeDefaultRolePermissions();
    
    /**
     * @brief 计算有效权限
     * @param userId 用户ID
     * @param role 角色
     * @return 有效权限集合
     */
    Permissions calculateEffectivePermissions(const QString& userId, UserRole role) const;
    
    /**
     * @brief 权限字符串转换
     */
    QString permissionsToString(const Permissions& permissions) const;
    Permissions permissionsFromString(const QString& permissionStr) const;
    
    /**
     * @brief 角色字符串转换
     */
    QString roleToString(UserRole role) const;
    UserRole roleFromString(const QString& roleStr) const;

private:
    // 当前用户信息
    QString m_currentUserId;
    UserRole m_currentRole;
    Permissions m_currentPermissions;
    
    // 角色权限映射
    QHash<UserRole, Permissions> m_rolePermissions;
    
    // 用户特定权限
    QHash<QString, Permissions> m_userPermissions;
    
    // 资源级权限 (用户ID -> 资源ID -> 权限)
    QHash<QString, QHash<QString, Permissions>> m_resourcePermissions;
    
    // 权限审计日志
    mutable QJsonArray m_auditLog;
    
    // 配置选项
    bool m_strictMode;              // 严格模式，默认拒绝未定义权限
    bool m_auditEnabled;            // 启用权限审计
    int m_maxAuditLogSize;         // 最大审计日志条目数
    
    static const QHash<UserRole, QString> ROLE_NAMES;
    static const QHash<Permission, QString> PERMISSION_NAMES;
};

Q_DECLARE_OPERATORS_FOR_FLAGS(PermissionManager::Permissions)

#endif // PERMISSION_MANAGER_H