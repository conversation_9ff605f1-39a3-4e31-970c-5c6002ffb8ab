/**
 * @file LaserRangingCapability.h  
 * @brief 激光测距能力组件 - 四层架构第2层
 * 
 * 职责：
 * - 提供激光测距功能
 * - 封装测距业务逻辑
 * - 与驱动层交互执行测距
 * - 支持多种测距模式和参数
 */

#pragma once

#include "LA/Device/Core/Device.h"
#include <QObject>
#include <QString>
#include <QVariantMap>
#include <QStringList>
#include <QTimer>
#include <QDateTime>
#include <vector>
#include <memory>

namespace LA::Device::Capability {

/**
 * @brief 激光测距能力组件
 * 
 * 支持功能：
 * - 单次测距
 * - 连续测距  
 * - 多点平均测距
 * - 测距数据缓存
 * - 测距精度控制
 */
class LaserRangingCapability : public QObject, public ICapability {
    Q_OBJECT

public:
    explicit LaserRangingCapability(QObject* parent = nullptr);
    virtual ~LaserRangingCapability() = default;

    // === ICapability接口实现 ===
    
    QString getCapabilityName() const override { return "LaserRanging"; }
    QStringList getProvidedCommands() const override;
    QVariantMap executeCapability(const QString& command, const QVariantMap& params, Driver::IDriver* driver) override;
    bool isAvailable() const override;

    // === 测距能力配置 ===
    
    /**
     * @brief 设置测距参数
     * @param config 配置参数
     */
    void setRangingConfig(const QVariantMap& config);
    
    /**
     * @brief 获取测距能力信息
     * @return 能力信息
     */
    QVariantMap getCapabilityInfo() const;
    
    /**
     * @brief 设置测距模式
     * @param mode 模式名称 ("single", "continuous", "average")
     */
    void setRangingMode(const QString& mode);
    
    /**
     * @brief 获取历史测距数据
     * @param count 获取数量 (-1表示全部)
     * @return 历史数据
     */
    QVariantList getHistoryData(int count = -1) const;

Q_SIGNALS:
    void measurementStarted();
    void measurementCompleted(const QVariantMap& result);
    void measurementFailed(const QString& error);
    void continuousMeasurementData(const QVariantMap& data);

private slots:
    void onContinuousMeasurementTimer();

private:
    // 测距配置
    struct RangingConfig {
        QString mode = "single";            // 测距模式
        int averageSamples = 3;             // 平均采样数
        double accuracyThreshold = 1.0;     // 精度阈值(mm)
        int timeoutMs = 5000;               // 超时时间
        int continuousIntervalMs = 100;     // 连续测距间隔
        double minRange = 50.0;             // 最小测距(mm) 
        double maxRange = 2000.0;           // 最大测距(mm)
        bool enableFiltering = true;        // 启用数据滤波
        bool enableValidation = true;       // 启用数据验证
    } m_config;
    
    // 测距状态
    bool m_measuring = false;
    bool m_continuousMeasuring = false;
    QTimer* m_continuousTimer = nullptr;
    
    // 数据缓存
    struct MeasurementData {
        double distance;                    // 距离值(mm)
        double accuracy;                    // 精度(mm)
        QDateTime timestamp;                // 时间戳
        QString mode;                       // 测距模式
        int sampleCount;                    // 采样数量
        QVariantMap rawData;                // 原始数据
    };
    
    std::vector<MeasurementData> m_historyData;
    static const int MAX_HISTORY_SIZE = 1000;
    
    // === 内部方法 ===
    
    /**
     * @brief 执行单次测距
     */
    QVariantMap executeSingleMeasurement(const QVariantMap& params, Driver::IDriver* driver);
    
    /**
     * @brief 开始连续测距
     */
    QVariantMap startContinuousMeasurement(const QVariantMap& params, Driver::IDriver* driver);
    
    /**
     * @brief 停止连续测距
     */
    QVariantMap stopContinuousMeasurement();
    
    /**
     * @brief 执行平均测距
     */
    QVariantMap executeAverageMeasurement(const QVariantMap& params, Driver::IDriver* driver);
    
    /**
     * @brief 获取当前测距值
     */
    QVariantMap getCurrentDistance(Driver::IDriver* driver);
    
    /**
     * @brief 执行多点测距
     */
    QVariantMap executeMultiPointMeasurement(const QVariantMap& params, Driver::IDriver* driver);
    
    /**
     * @brief 测距数据验证
     * @param distance 距离值
     * @param rawData 原始数据
     * @return 是否有效
     */
    bool validateMeasurement(double distance, const QVariantMap& rawData);
    
    /**
     * @brief 测距数据滤波
     * @param distances 距离数据数组
     * @return 滤波后的距离
     */
    double filterDistances(const std::vector<double>& distances);
    
    /**
     * @brief 计算测距精度
     * @param distances 距离数据数组
     * @return 精度值(mm)
     */
    double calculateAccuracy(const std::vector<double>& distances);
    
    /**
     * @brief 添加历史数据
     * @param data 测距数据
     */
    void addHistoryData(const MeasurementData& data);
    
    /**
     * @brief 清理历史数据
     */
    void cleanupHistoryData();
    
    /**
     * @brief 格式化测距结果
     * @param distance 距离值
     * @param accuracy 精度
     * @param mode 测距模式
     * @param sampleCount 采样数
     * @param rawData 原始数据
     * @return 格式化结果
     */
    QVariantMap formatMeasurementResult(double distance, double accuracy, 
                                      const QString& mode, int sampleCount,
                                      const QVariantMap& rawData = {});
    
    /**
     * @brief 处理驱动错误
     * @param error 错误信息
     * @return 错误结果
     */
    QVariantMap handleDriverError(const QString& error);
    
    Driver::IDriver* m_currentDriver = nullptr;  // 当前使用的驱动
};

/**
 * @brief 测距数据分析器
 */
class RangingDataAnalyzer {
public:
    struct AnalysisResult {
        double mean;                    // 平均值
        double stdDev;                  // 标准差
        double min;                     // 最小值
        double max;                     // 最大值
        double median;                  // 中位数
        int outlierCount;               // 异常值数量
        std::vector<double> outliers;   // 异常值列表
    };
    
    /**
     * @brief 分析测距数据
     * @param distances 距离数据
     * @return 分析结果
     */
    static AnalysisResult analyzeDistances(const std::vector<double>& distances);
    
    /**
     * @brief 检测异常值
     * @param distances 距离数据
     * @param threshold 异常值阈值 (标准差倍数)
     * @return 异常值索引
     */
    static std::vector<int> detectOutliers(const std::vector<double>& distances, double threshold = 2.0);
    
    /**
     * @brief 计算稳定性
     * @param distances 距离数据
     * @return 稳定性百分比 (0-100)
     */
    static double calculateStability(const std::vector<double>& distances);
};

/**
 * @brief 测距模式定义
 */
enum class RangingMode {
    Single,         // 单次测距
    Continuous,     // 连续测距
    Average,        // 平均测距
    MultiPoint,     // 多点测距
    Tracking        // 跟踪测距
};

/**
 * @brief 测距精度等级
 */
enum class AccuracyLevel {
    Low,            // 低精度 (±2mm)
    Normal,         // 普通精度 (±1mm)
    High,           // 高精度 (±0.5mm)
    Ultra           // 超高精度 (±0.1mm)
};

} // namespace LA::Device::Capability