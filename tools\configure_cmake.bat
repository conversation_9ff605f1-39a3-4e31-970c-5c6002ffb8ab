@echo off
rem 强制使用VSCode配置的编译器和生成器
rem 避免系统默认编译工具干扰

echo 正在配置CMake使用MinGW编译器...

set CMAKE_GENERATOR=Ninja
set CMAKE_C_COMPILER=D:/Programs/Qt/Qt5.14.2/Tools/mingw730_64/bin/gcc.exe
set CMAKE_CXX_COMPILER=D:/Programs/Qt/Qt5.14.2/Tools/mingw730_64/bin/g++.exe
set CMAKE_PREFIX_PATH=D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64

if exist build rmdir /s /q build
mkdir build
cd build

cmake -G "%CMAKE_GENERATOR%" ^
      -DCMAKE_C_COMPILER="%CMAKE_C_COMPILER%" ^
      -DCMAKE_CXX_COMPILER="%CMAKE_CXX_COMPILER%" ^
      -DCMAKE_PREFIX_PATH="%CMAKE_PREFIX_PATH%" ^
      -DCMAKE_BUILD_TYPE=Release ^
      ..

echo.
echo ===================================
echo CMake配置完成，使用的编译器：
echo - 生成器: %CMAKE_GENERATOR%
echo - C编译器: %CMAKE_C_COMPILER%  
echo - C++编译器: %CMAKE_CXX_COMPILER%
echo - Qt路径: %CMAKE_PREFIX_PATH%
echo ===================================
echo.
echo 现在可以运行: ninja