#include "ThreadMonitor.h"
#include <QThread>
#include <QDebug>
#include <QDateTime>
#include <QCoreApplication>

namespace LA {
namespace Thread {

ThreadMonitor::ThreadMonitor(QObject* parent)
    : QObject(parent)
    , m_isMonitoring(false)
    , m_monitorLevel(MonitorLevel::Basic)
    , m_updateInterval(1000)
    , m_cpuThreshold(80.0)
    , m_memoryThreshold(1024 * 1024 * 1024) // 1GB
    , m_alertsEnabled(true)
{
    // 创建更新定时器
    m_updateTimer = new QTimer(this);
    connect(m_updateTimer, &QTimer::timeout, this, &ThreadMonitor::updateMonitoringData);
    
    qDebug() << "ThreadMonitor initialized";
}

ThreadMonitor::~ThreadMonitor()
{
    stopMonitoring();
    qDebug() << "ThreadMonitor destroyed";
}

void ThreadMonitor::startMonitoring()
{
    if (m_isMonitoring) {
        return;
    }
    
    m_isMonitoring = true;
    m_updateTimer->start(m_updateInterval);
    
    emit monitoringStarted();
    qDebug() << "Thread monitoring started with interval:" << m_updateInterval << "ms";
}

void ThreadMonitor::stopMonitoring()
{
    if (!m_isMonitoring) {
        return;
    }
    
    m_isMonitoring = false;
    m_updateTimer->stop();
    
    emit monitoringStopped();
    qDebug() << "Thread monitoring stopped";
}

bool ThreadMonitor::isMonitoring() const
{
    return m_isMonitoring;
}

void ThreadMonitor::setMonitorLevel(MonitorLevel level)
{
    m_monitorLevel = level;
    
    // 根据监控级别调整更新频率
    switch (level) {
    case MonitorLevel::Basic:
        setUpdateInterval(2000);
        break;
    case MonitorLevel::Detailed:
        setUpdateInterval(1000);
        break;
    case MonitorLevel::Diagnostic:
        setUpdateInterval(500);
        break;
    }
    
    qDebug() << "Monitor level set to:" << static_cast<int>(level);
}

ThreadMonitor::MonitorLevel ThreadMonitor::getMonitorLevel() const
{
    return m_monitorLevel;
}

void ThreadMonitor::setUpdateInterval(int intervalMs)
{
    m_updateInterval = intervalMs;
    
    if (m_isMonitoring) {
        m_updateTimer->setInterval(intervalMs);
    }
    
    qDebug() << "Monitor update interval set to:" << intervalMs << "ms";
}

int ThreadMonitor::getUpdateInterval() const
{
    return m_updateInterval;
}

void ThreadMonitor::setPerformanceThreshold(double cpuThreshold, qint64 memoryThreshold)
{
    m_cpuThreshold = cpuThreshold;
    m_memoryThreshold = memoryThreshold;
    
    qDebug() << "Performance thresholds set - CPU:" << cpuThreshold 
             << "% Memory:" << memoryThreshold << "bytes";
}

void ThreadMonitor::enableAlerts(bool enable)
{
    m_alertsEnabled = enable;
    qDebug() << "Alerts" << (enable ? "enabled" : "disabled");
}

bool ThreadMonitor::areAlertsEnabled() const
{
    return m_alertsEnabled;
}

void ThreadMonitor::registerThread(const QString& name, QThread* thread)
{
    if (!thread) {
        qWarning() << "Cannot register null thread:" << name;
        return;
    }
    
    m_registeredThreads[name] = thread;
    m_monitoringData[name] = MonitoringData();
    
    qDebug() << "Thread registered for monitoring:" << name;
}

void ThreadMonitor::unregisterThread(const QString& name)
{
    m_registeredThreads.remove(name);
    m_monitoringData.remove(name);
    
    qDebug() << "Thread unregistered from monitoring:" << name;
}

void ThreadMonitor::registerThreadStatistics(const QString& name, const ThreadStatistics& stats)
{
    if (!m_monitoringData.contains(name)) {
        m_monitoringData[name] = MonitoringData();
    }
    
    MonitoringData& data = m_monitoringData[name];
    data.previousStats = data.currentStats;
    data.currentStats = stats;
    data.lastUpdateTime = QDateTime::currentMSecsSinceEpoch();
    
    // 更新历史数据
    data.cpuHistory.append(stats.cpuUsage);
    data.memoryHistory.append(stats.memoryUsage);
    
    // 限制历史数据大小
    if (data.cpuHistory.size() > MAX_HISTORY_POINTS) {
        data.cpuHistory.removeFirst();
    }
    if (data.memoryHistory.size() > MAX_HISTORY_POINTS) {
        data.memoryHistory.removeFirst();
    }
    
    // 分析性能趋势
    if (m_monitorLevel >= MonitorLevel::Detailed) {
        analyzePerformanceTrends(name, data);
    }
    
    // 检测异常
    if (m_monitorLevel >= MonitorLevel::Diagnostic) {
        detectAnomalies(name, data);
    }
    
    emit threadStatisticsUpdated(name, stats);
}

QList<ThreadStatistics> ThreadMonitor::getAllThreadStatistics() const
{
    QList<ThreadStatistics> allStats;
    
    for (auto it = m_monitoringData.begin(); it != m_monitoringData.end(); ++it) {
        allStats.append(it.value().currentStats);
    }
    
    return allStats;
}

ThreadStatistics ThreadMonitor::getThreadStatistics(const QString& name) const
{
    if (m_monitoringData.contains(name)) {
        return m_monitoringData[name].currentStats;
    }
    
    return ThreadStatistics();
}

QMap<QString, double> ThreadMonitor::getSystemMetrics() const
{
    return m_systemMetrics;
}

QStringList ThreadMonitor::getActiveThreads() const
{
    QStringList activeThreads;
    
    for (auto it = m_monitoringData.begin(); it != m_monitoringData.end(); ++it) {
        if (it.value().currentStats.state == ThreadState::Running) {
            activeThreads << it.key();
        }
    }
    
    return activeThreads;
}

QStringList ThreadMonitor::getProblematicThreads() const
{
    QStringList problematicThreads;
    
    for (auto it = m_monitoringData.begin(); it != m_monitoringData.end(); ++it) {
        if (it.value().isProblematic) {
            problematicThreads << it.key();
        }
    }
    
    return problematicThreads;
}

double ThreadMonitor::getAverageSystemCpuUsage() const
{
    if (m_monitoringData.isEmpty()) {
        return 0.0;
    }
    
    double totalCpu = 0.0;
    int activeThreads = 0;
    
    for (auto it = m_monitoringData.begin(); it != m_monitoringData.end(); ++it) {
        if (it.value().currentStats.state == ThreadState::Running) {
            totalCpu += it.value().currentStats.cpuUsage;
            activeThreads++;
        }
    }
    
    return activeThreads > 0 ? totalCpu / activeThreads : 0.0;
}

qint64 ThreadMonitor::getTotalSystemMemoryUsage() const
{
    qint64 totalMemory = 0;
    
    for (auto it = m_monitoringData.begin(); it != m_monitoringData.end(); ++it) {
        totalMemory += it.value().currentStats.memoryUsage;
    }
    
    return totalMemory;
}

int ThreadMonitor::getTotalThreadCount() const
{
    return m_monitoringData.size();
}

int ThreadMonitor::getActiveThreadCount() const
{
    return getActiveThreads().size();
}

double ThreadMonitor::getSystemEfficiency() const
{
    int totalThreads = getTotalThreadCount();
    int activeThreads = getActiveThreadCount();
    
    if (totalThreads == 0) {
        return 0.0;
    }
    
    return (static_cast<double>(activeThreads) / totalThreads) * 100.0;
}

void ThreadMonitor::updateMonitoringData()
{
    collectThreadStatistics();
    collectSystemMetrics();
    checkPerformanceThresholds();
    
    if (m_monitorLevel >= MonitorLevel::Detailed) {
        analyzeThreadBehavior();
    }
    
    emit monitoringDataUpdated();
}

void ThreadMonitor::checkPerformanceThresholds()
{
    if (!m_alertsEnabled) {
        return;
    }
    
    // 检查系统级阈值
    double avgCpu = getAverageSystemCpuUsage();
    qint64 totalMemory = getTotalSystemMemoryUsage();
    
    if (avgCpu > m_cpuThreshold || totalMemory > m_memoryThreshold) {
        emit systemPerformanceWarning(avgCpu, totalMemory);
    }
    
    // 检查线程级阈值
    for (auto it = m_monitoringData.begin(); it != m_monitoringData.end(); ++it) {
        const QString& threadName = it.key();
        const MonitoringData& data = it.value();
        
        if (data.currentStats.cpuUsage > m_cpuThreshold || 
            data.currentStats.memoryUsage > m_memoryThreshold) {
            emit threadPerformanceWarning(threadName, data.currentStats);
        }
    }
}

void ThreadMonitor::analyzeThreadBehavior()
{
    qint64 currentTime = QDateTime::currentMSecsSinceEpoch();
    
    for (auto it = m_monitoringData.begin(); it != m_monitoringData.end(); ++it) {
        const QString& threadName = it.key();
        MonitoringData& data = it.value();
        
        // 检查线程无响应
        qint64 timeSinceUpdate = currentTime - data.lastUpdateTime;
        if (timeSinceUpdate > 30000) { // 30秒无更新
            data.unresponsiveCount++;
            if (data.unresponsiveCount > 3) {
                emit threadUnresponsive(threadName, timeSinceUpdate);
                data.isProblematic = true;
            }
        } else {
            data.unresponsiveCount = 0;
            data.isProblematic = false;
        }
        
        // 检查线程崩溃
        if (m_registeredThreads.contains(threadName)) {
            QThread* thread = m_registeredThreads[threadName];
            if (thread && !thread->isRunning() && 
                data.currentStats.state == ThreadState::Running) {
                emit threadCrashed(threadName, "Thread terminated unexpectedly");
                data.isProblematic = true;
            }
        }
    }
}

void ThreadMonitor::collectThreadStatistics()
{
    // 这里应该从实际的线程管理器获取统计信息
    // 由于这是一个通用的监控类，具体的统计收集会在ThreadManager中调用registerThreadStatistics
}

void ThreadMonitor::collectSystemMetrics()
{
    m_systemMetrics.clear();
    
    // 计算系统级指标
    m_systemMetrics["total_threads"] = getTotalThreadCount();
    m_systemMetrics["active_threads"] = getActiveThreadCount();
    m_systemMetrics["avg_cpu_usage"] = getAverageSystemCpuUsage();
    m_systemMetrics["total_memory_usage"] = static_cast<double>(getTotalSystemMemoryUsage());
    m_systemMetrics["system_efficiency"] = getSystemEfficiency();
    
    emit systemMetricsUpdated(m_systemMetrics);
}

void ThreadMonitor::analyzePerformanceTrends(const QString& threadName, MonitoringData& data)
{
    // 分析CPU使用率趋势
    if (data.cpuHistory.size() >= 5) {
        double avgRecent = 0.0;
        double avgOlder = 0.0;
        int recentCount = qMin(3, data.cpuHistory.size());
        int olderCount = qMin(3, data.cpuHistory.size() - recentCount);
        
        // 计算最近3个点的平均值
        for (int i = data.cpuHistory.size() - recentCount; i < data.cpuHistory.size(); ++i) {
            avgRecent += data.cpuHistory[i];
        }
        avgRecent /= recentCount;
        
        // 计算之前3个点的平均值
        for (int i = data.cpuHistory.size() - recentCount - olderCount; 
             i < data.cpuHistory.size() - recentCount; ++i) {
            avgOlder += data.cpuHistory[i];
        }
        avgOlder /= olderCount;
        
        // 检查是否有显著增长趋势
        if (avgRecent > avgOlder * 1.5) { // 增长50%以上
            generateAlert(threadName, "Performance Degradation", 
                         QString("CPU usage increased from %1% to %2%")
                         .arg(avgOlder, 0, 'f', 1).arg(avgRecent, 0, 'f', 1));
        }
    }
}

void ThreadMonitor::detectAnomalies(const QString& threadName, const MonitoringData& data)
{
    // 检测CPU使用率异常
    if (data.currentStats.cpuUsage > 95.0) {
        generateAlert(threadName, "High CPU Usage", 
                     QString("CPU usage: %1%").arg(data.currentStats.cpuUsage, 0, 'f', 1));
    }
    
    // 检测内存使用异常
    if (data.currentStats.memoryUsage > 500 * 1024 * 1024) { // 500MB
        generateAlert(threadName, "High Memory Usage", 
                     QString("Memory usage: %1 MB")
                     .arg(data.currentStats.memoryUsage / (1024.0 * 1024.0), 0, 'f', 1));
    }
    
    // 检测错误率异常
    if (data.currentStats.errorCount > data.previousStats.errorCount + 10) {
        generateAlert(threadName, "High Error Rate", 
                     QString("Error count increased by %1")
                     .arg(data.currentStats.errorCount - data.previousStats.errorCount));
    }
}

void ThreadMonitor::generateAlert(const QString& threadName, const QString& alertType, const QString& message)
{
    if (!m_alertsEnabled) {
        return;
    }
    
    qWarning() << "Thread Alert -" << alertType << "for" << threadName << ":" << message;
    
    // 这里可以扩展为发送到日志系统、通知系统等
}

} // namespace Thread
} // namespace LA