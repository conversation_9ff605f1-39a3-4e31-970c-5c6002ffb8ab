# LA Support Logging Module

LA项目支持层日志系统模块，提供全面的日志记录、格式化和输出功能。

## 功能特性

### 核心功能
- **多级别日志记录** - 支持TRACE、DEBUG、INFO、WARNING、ERROR、FATAL六个级别
- **多类型日志分类** - 支持初始化、通信、处理状态、数据、错误等多种日志类型
- **异步日志处理** - 支持异步日志记录，提高性能
- **线程安全** - 所有操作都是线程安全的
- **灵活的过滤系统** - 支持级别、类型、组件、正则表达式等多种过滤方式

### 格式化器
- **SimpleTextFormatter** - 灵活的文本格式化器，支持自定义模式和颜色输出
- **JsonFormatter** - JSON格式输出，支持结构化日志
- **XmlFormatter** - XML格式输出，便于系统集成

### 输出处理器
- **ConsoleHandler** - 控制台输出，支持stdout/stderr分流
- **FileHandler** - 文件输出，支持追加/覆盖模式和自动刷新
- **RotatingFileHandler** - 大小轮转文件输出，防止单个文件过大
- **TimedRotatingFileHandler** - 时间轮转文件输出，支持按小时/天/周/月轮转
- **NetworkHandler** - 网络输出，支持UDP/TCP协议

## 架构设计

### 类层次结构
```
ILogger (接口)
├── Logger (实现类)
└── LoggerManager (管理器)

ILogFormatter (接口)
├── SimpleTextFormatter
├── JsonFormatter
└── XmlFormatter

ILogHandler (接口)
├── ConsoleHandler
├── FileHandler
├── RotatingFileHandler
├── TimedRotatingFileHandler
└── NetworkHandler

ILogFilter (接口)
├── LevelFilter
├── TypeFilter
├── ComponentFilter
└── RegexFilter
```

### 核心组件

#### Logger类
主要的日志记录器实现，提供：
- 多级别日志记录接口
- 处理器和过滤器管理
- 异步处理支持
- 统计信息收集
- 父子关系管理

#### LoggerManager类
日志记录器生命周期管理：
- 单例模式实现
- 记录器创建和销毁
- 全局配置管理
- 批量操作支持

#### AsyncLogWorker类
异步日志处理工作器：
- 独立线程处理
- 队列缓冲机制
- 批量处理优化
- 优雅关闭支持

## 使用示例

### 基本使用
```cpp
#include <LA/Support/Logging/Logger.h>

using namespace LA::Support::Logging;

// 获取日志记录器
auto logger = LoggerManager::getInstance().getLogger("MyApp");

// 记录不同级别的日志
logger->info(LogType::DEFAULT, "Application started");
logger->warning(LogType::PROCESS_STATUS, "Configuration file not found, using defaults");
logger->error(LogType::ERROR_LOG, "Failed to connect to database");

// 带上下文信息的日志
LogContext context;
context.component = "DatabaseManager";
context.module = "Connection";
logger->error(LogType::ERROR_LOG, "Connection timeout", context);
```

### 自定义配置
```cpp
// 创建自定义配置
LoggerConfig config;
config.minLevel = LogLevel::DEBUG;
config.enableAsync = true;
config.enableFile = true;
config.logDirectory = "logs";
config.maxFileSize = 50 * 1024 * 1024;  // 50MB
config.maxBackupCount = 10;

// 创建配置化的日志记录器
auto logger = LoggerManager::getInstance().createLogger("CustomApp", config);
```

### 添加自定义处理器
```cpp
// 创建格式化器
auto formatter = std::make_shared<SimpleTextFormatter>(
    "[%{time}] [%{level}] [%{component}] %{message}"
);

// 创建轮转文件处理器
auto fileHandler = std::make_shared<RotatingFileHandler>(
    "app.log", 10 * 1024 * 1024, 5, formatter  // 10MB, 5个备份
);

// 添加到日志记录器
logger->addHandler(fileHandler);
```

### 添加过滤器
```cpp
// 只记录ERROR和FATAL级别
auto levelFilter = std::make_shared<LevelFilter>(LogLevel::ERROR, LogLevel::FATAL);
logger->addFilter(levelFilter);

// 只记录特定组件
auto componentFilter = std::make_shared<ComponentFilter>(
    QStringList{"DatabaseManager", "NetworkClient"}
);
logger->addFilter(componentFilter);

// 正则表达式过滤
auto regexFilter = std::make_shared<RegexFilter>(".*error.*", true);
logger->addFilter(regexFilter);
```

### JSON格式日志
```cpp
// 创建JSON格式化器
auto jsonFormatter = std::make_shared<JsonFormatter>();
jsonFormatter->setPrettyPrint(true);

// 创建文件处理器
auto jsonHandler = std::make_shared<FileHandler>("app.json", jsonFormatter);
logger->addHandler(jsonHandler);
```

### 网络日志
```cpp
// 创建网络处理器（UDP）
auto networkHandler = std::make_shared<NetworkHandler>(
    "192.168.1.100", 514, NetworkHandler::Protocol::UDP
);
logger->addHandler(networkHandler);
```

## 配置选项

### LoggerConfig结构
```cpp
struct LoggerConfig {
    LogLevel    minLevel;           // 最小日志级别
    bool        enableConsole;      // 启用控制台输出
    bool        enableFile;         // 启用文件输出
    QString     logDirectory;       // 日志目录
    QString     logFilePattern;     // 日志文件名模式
    qint64      maxFileSize;        // 最大文件大小
    int         maxBackupCount;     // 最大备份文件数
    bool        enableRotation;     // 启用日志轮转
    bool        enableAsync;        // 启用异步日志
    int         bufferSize;         // 缓冲区大小
    int         flushInterval;      // 刷新间隔(ms)
    QStringList enabledTypes;       // 启用的日志类型
    bool        enableTimestamp;    // 启用时间戳
    bool        enableThreadId;     // 启用线程ID
    bool        enableSourceInfo;   // 启用源码信息
};
```

### 格式化模式
SimpleTextFormatter支持的占位符：
- `%{time}` - 时间戳
- `%{level}` - 日志级别
- `%{type}` - 日志类型
- `%{message}` - 日志消息
- `%{file}` - 源文件名
- `%{function}` - 函数名
- `%{line}` - 行号
- `%{component}` - 组件名
- `%{module}` - 模块名
- `%{thread}` - 线程ID
- `%{process}` - 进程ID

## 性能特性

### 异步处理
- 独立工作线程处理日志输出
- 无锁队列缓冲
- 批量处理优化
- 可配置缓冲区大小

### 内存管理
- 智能指针管理对象生命周期
- 弱引用避免循环依赖
- RAII资源管理
- 自动清理机制

### 线程安全
- 所有公共接口都是线程安全的
- 读写锁优化读取性能
- 原子操作统计计数
- 无死锁设计

## 编译要求

- Qt 5.14+
- C++17
- CMake 3.16+

## 依赖关系

### 必需依赖
- Qt5::Core - 核心功能
- Qt5::Network - 网络输出支持
- LA_Foundation_Core - 基础类型定义

### 可选依赖
- 无

## 集成指南

### CMake集成
```cmake
find_package(LA_Support_Logging REQUIRED)
target_link_libraries(your_target LA::Support::LA_Support_Logging)
```

### pkg-config集成
```bash
pkg-config --cflags --libs LA_Support_Logging
```

## 测试

模块包含完整的单元测试和集成测试：
```bash
cd build
ctest -R LA_Support_Logging
```

## 许可证

本模块遵循项目整体许可证。

## 更新日志

### v1.0.0 (2025-01-23)
- 初始版本发布
- 完整的日志记录功能
- 多种格式化器和输出处理器
- 异步处理支持
- 完整的测试覆盖

---

有关更多信息，请参考项目文档或联系开发团队。