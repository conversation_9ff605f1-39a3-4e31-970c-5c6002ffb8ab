#pragma once

#include <QObject>
#include <QDateTime>
#include <QString>
#include "IResourceManager.h"

namespace LA {
namespace Support {
namespace Resource {

/**
 * @brief 异步资源加载工作器
 */
class AsyncLoadWorker : public QObject {
    Q_OBJECT

public:
    struct LoadTask {
        QString id;
        ResourceLoadOptions options;
        QDateTime timestamp;
    };

    explicit AsyncLoadWorker(QObject* parent = nullptr);
    ~AsyncLoadWorker() override;

    void addTask(const LoadTask& task);
    void processQueue();

signals:
    void taskCompleted(const QString& id, const ResourceData& data);
    void taskFailed(const QString& id, const QString& error);

private slots:
    void handleTaskCompletion();

private:
    QList<LoadTask> m_taskQueue;
    bool m_processing;
};

}  // namespace Resource
}  // namespace Support
}  // namespace LA