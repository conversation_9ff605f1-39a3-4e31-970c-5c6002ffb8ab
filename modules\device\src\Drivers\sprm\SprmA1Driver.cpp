/**
 * @file SprmA1Driver_Simple.cpp  
 * @brief SPRM A1驱动程序简化版本 - 解决编译问题
 */

#include "SprmA1Driver.h"
#include <QDebug>
#include <QDateTime>

namespace LA::Device::Driver {

// SPRM-A1 command mapping definitions
const QMap<QString, SprmA1Driver::CommandCode> SprmA1Driver::s_commandCodes = {
    {"START_MEASURE", {SprmA1Protocol::CMD_START_MEASURE, "Start Measure", 4}},
    {"STOP_MEASURE", {SprmA1Protocol::CMD_STOP_MEASURE, "Stop Measure", 4}},
    {"GET_DISTANCE", {SprmA1Protocol::CMD_GET_DISTANCE, "Get Distance", 6}},
    {"CALIBRATE", {SprmA1Protocol::CMD_CALIBRATE, "Calibrate Sensor", 4}},
    {"SET_LASER_POWER", {SprmA1Protocol::CMD_SET_LASER_POWER, "Set Laser Power", 4}},
    {"GET_STATUS", {SprmA1Protocol::CMD_GET_STATUS, "Get Status", 5}},
    {"RESET", {SprmA1Protocol::CMD_RESET, "Reset Device", 4}},
    {"GET_VERSION", {SprmA1Protocol::CMD_GET_VERSION, "Get Version", 8}},
    {"SET_BAUDRATE", {SprmA1Protocol::CMD_SET_BAUDRATE, "Set Baudrate", 4}},
    {"SELF_TEST", {SprmA1Protocol::CMD_SELF_TEST, "Self Test", 4}}
};

SprmA1Driver::SprmA1Driver(QObject* parent)
    : QObject(parent)
    , m_serialPort(std::make_unique<QSerialPort>())
    , m_baudRate(19200)
    , m_connected(false)
    , m_laserPower(5.0)
    , m_timeoutMs(3000)
    , m_waitingForResponse(false)
{
    // Initialize timeout timer
    m_commandTimeout = new QTimer(this);
    m_commandTimeout->setSingleShot(true);
    QObject::connect(m_commandTimeout, &QTimer::timeout, this, &SprmA1Driver::onCommandTimeout);
    
    // Connect serial port signals
    QObject::connect(m_serialPort.get(), &QSerialPort::readyRead, this, &SprmA1Driver::onDataReceived);
    QObject::connect(m_serialPort.get(), QOverload<QSerialPort::SerialPortError>::of(&QSerialPort::error),
            this, &SprmA1Driver::onSerialPortError);
            
    qDebug() << "[SprmA1Driver] Driver initialized";
}

bool SprmA1Driver::initialize() 
{
    qDebug() << "[SprmA1Driver] Initializing driver";
    return true;
}

bool SprmA1Driver::connect() 
{
    if (m_connected) {
        qDebug() << "[SprmA1Driver] Already connected";
        return true;
    }
    
    if (m_portName.isEmpty()) {
        qWarning() << "[SprmA1Driver] Port name not set";
        return false;
    }
    
    // Configure serial port parameters
    m_serialPort->setPortName(m_portName);
    m_serialPort->setBaudRate(m_baudRate);
    m_serialPort->setDataBits(QSerialPort::Data8);
    m_serialPort->setParity(QSerialPort::NoParity);
    m_serialPort->setStopBits(QSerialPort::OneStop);
    m_serialPort->setFlowControl(QSerialPort::NoFlowControl);
    
    // Try to open serial port
    if (!m_serialPort->open(QIODevice::ReadWrite)) {
        QString error = QString("Failed to open port %1: %2").arg(m_portName, m_serialPort->errorString());
        qCritical() << "[SprmA1Driver]" << error;
        return false;
    }
    
    m_connected = true;
    qInfo() << "[SprmA1Driver] Connected to" << m_portName << "at" << m_baudRate << "baud";
    
    // 清空缓冲区
    m_serialPort->clear();
    m_responseBuffer.clear();
    
    return true;
}

bool SprmA1Driver::disconnect() 
{
    if (!m_connected) {
        return true;
    }
    
    // Stop response waiting timer
    m_commandTimeout->stop();
    m_waitingForResponse = false;
    
    // Close serial port
    if (m_serialPort->isOpen()) {
        m_serialPort->close();
    }
    
    m_connected = false;
    qInfo() << "[SprmA1Driver] Disconnected from" << m_portName;
    
    return true;
}

QVariantMap SprmA1Driver::sendCommand(const QString& command, const QVariantMap& params) 
{
    if (!m_connected) {
        return createErrorResult("Device not connected");
    }
    
    if (!s_commandCodes.contains(command)) {
        return createErrorResult("Unknown command: " + command);
    }
    
    if (m_waitingForResponse) {
        return createErrorResult("Previous command still waiting for response");
    }
    
    // Build command frame
    QByteArray frame = buildCommandFrame(command, params);
    if (frame.isEmpty()) {
        return createErrorResult("Failed to build command frame");
    }
    
    // Send command
    qint64 written = m_serialPort->write(frame);
    if (written != frame.size()) {
        return createErrorResult("Failed to write complete command");
    }
    
    // Wait for response
    m_waitingForResponse = true;
    m_responseBuffer.clear();
    m_commandTimeout->start(m_timeoutMs);
    
    // Simplified processing: return success directly (should wait for response in reality)
    QVariantMap result = createSuccessResult();
    result["command"] = command;
    result["sent_bytes"] = static_cast<int>(written);
    result["timestamp"] = QDateTime::currentDateTime();
    
    return result;
}

bool SprmA1Driver::isConnected() const 
{
    return m_connected && m_serialPort->isOpen();
}

void SprmA1Driver::setSerialConfig(const QString& portName, int baudRate) 
{
    m_portName = portName;
    m_baudRate = baudRate;
    qDebug() << "[SprmA1Driver] Serial config set:" << portName << "@" << baudRate;
}

QByteArray SprmA1Driver::buildCommandFrame(const QString& command, const QVariantMap& params) 
{
    Q_UNUSED(params)
    
    if (!s_commandCodes.contains(command)) {
        return QByteArray();
    }
    
    const CommandCode& cmdCode = s_commandCodes[command];
    
    // Simplified frame format: [STX][CMD][CHECKSUM][ETX]
    QByteArray frame;
    frame.append(static_cast<char>(0xAA)); // STX
    frame.append(static_cast<char>(cmdCode.code));
    
    // Add parameters (based on specific command)
    if (command == "SET_LASER_POWER" && params.contains("power")) {
        double power = params["power"].toDouble();
        uint8_t powerByte = static_cast<uint8_t>(power * 10); // Power*10 storage
        frame.append(static_cast<char>(powerByte));
    }
    
    // Calculate and add checksum
    uint8_t checksum = calculateChecksum(frame);
    frame.append(static_cast<char>(checksum));
    frame.append(static_cast<char>(0x55)); // ETX
    
    return frame;
}

QVariantMap SprmA1Driver::parseResponse(const QByteArray& data) 
{
    QVariantMap result = createSuccessResult();
    
    if (data.size() < 4) {
        return createErrorResult("Response too short");
    }
    
    // Simplified parsing: extract basic information
    result["response_length"] = data.size();
    result["raw_data"] = data.toHex();
    
    // Parse specific data based on response type
    if (data.size() >= 6) {
        uint8_t cmd = static_cast<uint8_t>(data[1]);
        result["command_code"] = cmd;
        
        if (cmd == SprmA1Protocol::CMD_GET_DISTANCE) {
            // Distance data parsing (assumed in bytes 2-3)
            uint16_t distance = (static_cast<uint16_t>(data[2]) << 8) | static_cast<uint8_t>(data[3]);
            result["distance"] = distance;
            result["unit"] = "mm";
        }
    }
    
    return result;
}

bool SprmA1Driver::validateChecksum(const QByteArray& frame) 
{
    if (frame.size() < 4) {
        return false;
    }
    
    // Calculate checksum excluding last two bytes
    QByteArray dataToCheck = frame.left(frame.size() - 2);
    uint8_t calculatedChecksum = calculateChecksum(dataToCheck);
    uint8_t receivedChecksum = static_cast<uint8_t>(frame[frame.size() - 2]);
    
    return calculatedChecksum == receivedChecksum;
}

uint8_t SprmA1Driver::calculateChecksum(const QByteArray& data) 
{
    uint8_t checksum = 0;
    for (char byte : data) {
        checksum ^= static_cast<uint8_t>(byte);
    }
    return checksum;
}

QVariantMap SprmA1Driver::createErrorResult(const QString& error) const 
{
    QVariantMap result;
    result["success"] = false;
    result["error"] = error;
    result["timestamp"] = QDateTime::currentDateTime();
    return result;
}

QVariantMap SprmA1Driver::createSuccessResult(const QVariantMap& data) const 
{
    QVariantMap result;
    result["success"] = true;
    result["timestamp"] = QDateTime::currentDateTime();
    
    // Merge additional data
    for (auto it = data.begin(); it != data.end(); ++it) {
        result[it.key()] = it.value();
    }
    
    return result;
}

QString SprmA1Driver::getStatusText(int statusCode) const 
{
    static const QMap<int, QString> statusTexts = {
        {0, "Normal"},
        {1, "Initializing"},
        {2, "Measuring"},
        {3, "Calibrating"},
        {4, "Error"},
        {5, "Timeout"}
    };
    
    return statusTexts.value(statusCode, QString("Unknown Status (%1)").arg(statusCode));
}

void SprmA1Driver::onDataReceived() 
{
    if (!m_waitingForResponse) {
        return;
    }
    
    QByteArray data = m_serialPort->readAll();
    m_responseBuffer.append(data);
    
    // Simplified response handling: assume any received data is complete response
    m_commandTimeout->stop();
    m_waitingForResponse = false;
    
    QVariantMap response = parseResponse(m_responseBuffer);
    qDebug() << "[SprmA1Driver] Response received:" << response;
    
    // Signal can be emitted here to notify response reception complete
}

void SprmA1Driver::onCommandTimeout() 
{
    if (m_waitingForResponse) {
        m_waitingForResponse = false;
        qWarning() << "[SprmA1Driver] Command timeout";
        
        // Timeout signal can be emitted
    }
}

void SprmA1Driver::onSerialPortError(QSerialPort::SerialPortError error) 
{
    if (error != QSerialPort::NoError) {
        QString errorString = m_serialPort->errorString();
        qCritical() << "[SprmA1Driver] Serial port error:" << errorString;
        
        // If it's a serious error, disconnect
        if (error == QSerialPort::DeviceNotFoundError || 
            error == QSerialPort::ResourceError) {
            disconnect();
        }
    }
}

void SprmA1Driver::setLaserConfig(double power) 
{
    m_laserPower = power;
    qDebug() << "[SprmA1Driver] Laser power set to" << power << "mW";
}

QVariantMap SprmA1Driver::getDeviceSpecs() const 
{
    QVariantMap specs;
    specs["model"] = "SPRM-A1";
    specs["laser_wavelength"] = 650;  // nm
    specs["measuring_range_min"] = 50;  // mm
    specs["measuring_range_max"] = 2000; // mm
    specs["accuracy"] = 1.0;  // mm
    specs["laser_power"] = m_laserPower;  // mW
    specs["communication"] = "RS485";
    specs["baudrate"] = m_baudRate;
    specs["firmware_version"] = m_firmwareVersion.isEmpty() ? "Unknown" : m_firmwareVersion;
    return specs;
}

void SprmA1Driver::onSerialDataReceived() 
{
    onDataReceived(); // Delegate to existing implementation
}

void SprmA1Driver::onSerialError(QSerialPort::SerialPortError error) 
{
    onSerialPortError(error); // Delegate to existing implementation  
}

bool SprmA1Driver::sendRawData(const QByteArray& data) 
{
    if (!m_connected || !m_serialPort->isOpen()) {
        return false;
    }
    
    qint64 written = m_serialPort->write(data);
    return written == data.size();
}

QByteArray SprmA1Driver::waitForResponse(int timeoutMs) 
{
    if (!m_connected || !m_serialPort->isOpen()) {
        return QByteArray();
    }
    
    // Wait for response with timeout
    if (m_serialPort->waitForReadyRead(timeoutMs)) {
        return m_serialPort->readAll();
    }
    
    return QByteArray();
}

QVariantMap SprmA1Driver::performSelfTest() 
{
    QVariantMap testResult = createSuccessResult();
    
    testResult["serial_port"] = m_serialPort->isOpen();
    testResult["baudrate"] = m_baudRate;
    testResult["laser_power"] = m_laserPower;
    testResult["connection"] = m_connected;
    
    // Perform basic communication test
    if (m_connected) {
        QVariantMap commandResult = sendCommand("GET_STATUS", QVariantMap());
        testResult["communication_test"] = commandResult["success"].toBool();
    } else {
        testResult["communication_test"] = false;
    }
    
    return testResult;
}

QVariantMap SprmA1Driver::getDeviceInfo() const 
{
    QVariantMap info;
    info["driver_type"] = "SprmA1Driver";
    info["device_model"] = "SPRM-A1";
    info["port_name"] = m_portName;
    info["baudrate"] = m_baudRate;
    info["connected"] = m_connected;
    info["laser_power"] = m_laserPower;
    info["firmware_version"] = m_firmwareVersion.isEmpty() ? "Unknown" : m_firmwareVersion;
    return info;
}

} // namespace LA::Device::Driver