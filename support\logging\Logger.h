#pragma once

#include "logger/ILogger.h"
#include "formatter/ILogFormatter.h"
#include "output/ILogOutput.h"
#include <QObject>
#include <QMutex>
#include <QThread>
#include <QQueue>
#include <QTimer>
#include <QWaitCondition>
#include <QDateTime>
#include <QRegularExpression>
#include <memory>
#include <vector>
#include <map>

namespace LA {
namespace Support {
namespace Logging {

/**
 * @brief 日志记录器实现类
 * 
 * 提供完整的日志记录功能，包括：
 * - 多级别日志记录
 * - 多种输出处理器支持
 * - 异步日志记录
 * - 日志过滤和格式化
 * - 线程安全操作
 */
class Logger : public ILogger {
    // 注意：简化版Logger，移除Qt信号槽功能以避免MOC编译问题

public:
    explicit Logger(const QString& name);
    ~Logger() override;

    // IManager 接口实现
    bool initialize(const ConfigParameters& config = {}) override;
    void shutdown() override;
    bool isInitialized() const override;
    StatusInfoList getStatus() const override;
    VersionInfo getVersion() const override;

    // ILogger 接口实现
    void setConfig(const LoggerConfig& config) override;
    LoggerConfig getConfig() const override;

    void addHandler(std::shared_ptr<ILogHandler> handler) override;
    void removeHandler(std::shared_ptr<ILogHandler> handler) override;
    void addFilter(std::shared_ptr<ILogFilter> filter) override;
    void removeFilter(std::shared_ptr<ILogFilter> filter) override;

    void log(LogLevel level, LogType type, const QString& message, 
            const LogContext& context = LogContext()) override;
    void trace(LogType type, const QString& message, 
              const LogContext& context = LogContext()) override;
    void debug(LogType type, const QString& message, 
              const LogContext& context = LogContext()) override;
    void info(LogType type, const QString& message, 
             const LogContext& context = LogContext()) override;
    void warning(LogType type, const QString& message, 
                const LogContext& context = LogContext()) override;
    void error(LogType type, const QString& message, 
              const LogContext& context = LogContext()) override;
    void fatal(LogType type, const QString& message, 
              const LogContext& context = LogContext()) override;

    void flush() override;
    bool isLevelEnabled(LogLevel level) const override;
    bool isTypeEnabled(LogType type) const override;

    /**
     * @brief 获取日志记录器名称
     */
    QString getName() const;

    /**
     * @brief 设置父日志记录器
     */
    void setParent(std::shared_ptr<Logger> parent);

    /**
     * @brief 获取父日志记录器
     */
    std::shared_ptr<Logger> getParent() const;

    /**
     * @brief 添加子日志记录器
     */
    void addChild(std::shared_ptr<Logger> child);

    /**
     * @brief 移除子日志记录器
     */
    void removeChild(std::shared_ptr<Logger> child);

    /**
     * @brief 获取统计信息
     */
    QVariantMap getStatistics() const;

private:
    void processAsyncQueue();
    void performPeriodicFlush();

private:
    // 异步工作器已移除，简化为同步处理

    // 内部方法
    void processLogEntry(const LogEntry& entry);
    bool shouldProcess(const LogEntry& entry) const;
    void updateStatistics(const LogEntry& entry);
    LogContext enrichContext(const LogContext& context) const;
    QString generateLogId() const;

    // 配置初始化
    void initializeFromConfig(const LoggerConfig& config);
    void setupDefaultHandlers();
    void setupAsyncProcessing();

private:
    QString m_name;                                              // 日志记录器名称
    LoggerConfig m_config;                                       // 日志配置
    std::vector<std::shared_ptr<ILogHandler>> m_handlers;        // 日志处理器列表
    std::vector<std::shared_ptr<ILogFilter>> m_filters;         // 日志过滤器列表
    
    std::weak_ptr<Logger> m_parent;                             // 父日志记录器
    std::vector<std::weak_ptr<Logger>> m_children;              // 子日志记录器列表
    
    // AsyncLogWorker* m_asyncWorker; // 已移除
    QTimer* m_flushTimer;                                       // 定期刷新计时器
    
    mutable QMutex m_mutex;                                     // 线程安全锁
    bool m_initialized;                                         // 初始化状态
    
    // 统计信息
    mutable qint64 m_totalLogCount;                             // 总日志数
    mutable qint64 m_errorLogCount;                             // 错误日志数
    mutable qint64 m_warningLogCount;                           // 警告日志数
    mutable qint64 m_lastFlushTime;                             // 上次刷新时间
    mutable QDateTime m_creationTime;                           // 创建时间
};

// LoggerManager 声明移至单独的头文件 LoggerManager.h

/**
 * @brief 日志工厂实现
 */
class LoggerFactory : public ILoggerFactory {
public:
    std::shared_ptr<ILogger> createLogger(const QString& name, 
                                        const LoggerConfig& config = LoggerConfig()) override;
    std::shared_ptr<ILogger> getLogger(const QString& name) override;
    void destroyLogger(const QString& name) override;
};

/**
 * @brief 简单日志过滤器实现
 */
class LevelFilter : public ILogFilter {
public:
    LevelFilter(LogLevel minLevel, LogLevel maxLevel = LogLevel::FATAL);
    bool filter(const LogEntry& entry) const override;

    void setMinLevel(LogLevel level);
    void setMaxLevel(LogLevel level);
    LogLevel getMinLevel() const;
    LogLevel getMaxLevel() const;

private:
    LogLevel m_minLevel;
    LogLevel m_maxLevel;
};

/**
 * @brief 类型过滤器实现
 */
class TypeFilter : public ILogFilter {
public:
    TypeFilter(const QList<LogType>& allowedTypes);
    bool filter(const LogEntry& entry) const override;

    void setAllowedTypes(const QList<LogType>& types);
    QList<LogType> getAllowedTypes() const;

    void addAllowedType(LogType type);
    void removeAllowedType(LogType type);

private:
    QList<LogType> m_allowedTypes;
};

/**
 * @brief 组件过滤器实现
 */
class ComponentFilter : public ILogFilter {
public:
    ComponentFilter(const QStringList& allowedComponents);
    bool filter(const LogEntry& entry) const override;

    void setAllowedComponents(const QStringList& components);
    QStringList getAllowedComponents() const;

    void addAllowedComponent(const QString& component);
    void removeAllowedComponent(const QString& component);

private:
    QStringList m_allowedComponents;
};

/**
 * @brief 正则表达式过滤器实现
 */
class RegexFilter : public ILogFilter {
public:
    RegexFilter(const QString& pattern, bool include = true);
    bool filter(const LogEntry& entry) const override;

    void setPattern(const QString& pattern);
    QString getPattern() const;

    void setIncludeMode(bool include);
    bool isIncludeMode() const;

private:
    QString m_pattern;
    bool m_includeMode;
    QRegularExpression m_regex;
};

}  // namespace Logging
}  // namespace Support
}  // namespace LA