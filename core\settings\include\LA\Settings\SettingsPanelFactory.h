#pragma once

#include "LA/SideBar/interfaces/ISidebarPanel.h"
#include <QString>
#include <QStringList>
#include <memory>

namespace LA {
namespace Settings {

/**
 * @brief 设置面板工厂类
 * 
 * 负责创建设置系统相关的侧边栏面板
 */
class SettingsPanelFactory {
public:
    /**
     * @brief 创建设置侧边栏面板
     * @param parent 父组件
     * @return 面板实例
     */
    static std::unique_ptr<LA::SideBar::ISidebarPanel> createSettingsPanel(QWidget *parent = nullptr);
    
    /**
     * @brief 获取支持的面板列表
     * @return 面板ID列表
     */
    static QStringList getSupportedPanels();
    
    /**
     * @brief 检查是否支持指定面板
     * @param panelId 面板ID
     * @return 是否支持
     */
    static bool supportsPanel(const QString &panelId);
    
    /**
     * @brief 获取面板显示名称
     * @param panelId 面板ID
     * @return 显示名称
     */
    static QString getPanelDisplayName(const QString &panelId);
    
    /**
     * @brief 获取面板图标路径
     * @param panelId 面板ID
     * @return 图标路径
     */
    static QString getPanelIconPath(const QString &panelId);
};

} // namespace Settings
} // namespace LA
