# 1.0
cmake_minimum_required(VERSION 3.16)

# 1.1 Project definition
project(LA VERSION 1.0.0 LANGUAGES CXX)

# 1.2 Enable current directory includes
set(CMAKE_INCLUDE_CURRENT_DIR ON)

# 1.3 Qt configuration
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)

# 1.4 C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 1.4.1 Include LA library utilities
list(APPEND CMAKE_MODULE_PATH "${CMAKE_SOURCE_DIR}/cmake")
include(LALibraryUtils)

# 1.5 Find Qt packages
find_package(
    Qt5
    COMPONENTS
    Core
    Widgets
    Gui
    Sql
    SerialPort
    PrintSupport
    Charts
    Network
    Xml
    REQUIRED
)

# 1.6 Resource files
# set(QRC_FILE resource.qrc)  # 注释掉这行，因为资源文件在ui目录下
# qt5_add_resources(QRC ${QRC_FILE})  # 注释掉这行，因为资源文件在ui目录下

# 1.7 Set LA library include directories
# 设置LA库的统一包含路径，解决头文件引用问题
include_directories(
    ${PROJECT_SOURCE_DIR}
)

# 1.7.1 设置LA库的包含路径映射
# 这样可以使用 #include <LA/Foundation/Core/CommonTypes.h> 的方式引用
set(LA_INCLUDE_MAPPING_DIR "${CMAKE_BINARY_DIR}/include")
file(MAKE_DIRECTORY "${LA_INCLUDE_MAPPING_DIR}/LA")

# 创建符号链接或复制头文件到统一位置
if(WIN32)
    # Windows下创建目录链接
    execute_process(
        COMMAND ${CMAKE_COMMAND} -E create_symlink
        "${PROJECT_SOURCE_DIR}/infrastructure"
        "${LA_INCLUDE_MAPPING_DIR}/LA/Infrastructure"
        ERROR_QUIET
    )
    execute_process(
        COMMAND ${CMAKE_COMMAND} -E create_symlink
        "${PROJECT_SOURCE_DIR}/support"
        "${LA_INCLUDE_MAPPING_DIR}/LA/Support"
        ERROR_QUIET
    )
    execute_process(
        COMMAND ${CMAKE_COMMAND} -E create_symlink
        "${PROJECT_SOURCE_DIR}/support/foundation"
        "${LA_INCLUDE_MAPPING_DIR}/LA/Foundation"
        ERROR_QUIET
    )
    execute_process(
        COMMAND ${CMAKE_COMMAND} -E create_symlink
        "${PROJECT_SOURCE_DIR}/core/settings/include/LA/Settings"
        "${LA_INCLUDE_MAPPING_DIR}/LA/Settings"
        ERROR_QUIET
    )
else()
    # Unix/Linux下创建符号链接
    execute_process(
        COMMAND ${CMAKE_COMMAND} -E create_symlink
        "${PROJECT_SOURCE_DIR}/infrastructure"
        "${LA_INCLUDE_MAPPING_DIR}/LA/Infrastructure"
    )
    execute_process(
        COMMAND ${CMAKE_COMMAND} -E create_symlink
        "${PROJECT_SOURCE_DIR}/support"
        "${LA_INCLUDE_MAPPING_DIR}/LA/Support"
    )
    execute_process(
        COMMAND ${CMAKE_COMMAND} -E create_symlink
        "${PROJECT_SOURCE_DIR}/support/foundation"
        "${LA_INCLUDE_MAPPING_DIR}/LA/Foundation"
    )
    execute_process(
        COMMAND ${CMAKE_COMMAND} -E create_symlink
        "${PROJECT_SOURCE_DIR}/core/settings/include/LA/Settings"
        "${LA_INCLUDE_MAPPING_DIR}/LA/Settings"
    )
endif()

# 添加映射目录到包含路径
include_directories(${LA_INCLUDE_MAPPING_DIR})

# 1.7 Link directories
LINK_DIRECTORIES(${PROJECT_SOURCE_DIR}/../lib)

# 1.8 Source files
set(SOURCE_FILES
    core/application/main.cpp  # 主程序入口
)

############################################# Build Configuration ################################
# 2.1 Build type configuration
option(LOG_TO_CONSOLE "debug build type" ON)
option(INIT_OUTPUT "varibles init value ouput" ON)
option(COMM_OUTPUT "communication cmd output" ON)
option(COMM_ACK_OUTPUT "communication ack" ON)
option(PROCESS_STATUS_OUTPUT "task status output" ON)
option(PROCESS_DATA_OUTPUT "process data output" ON)
option(RESULT_CACHE_OUTPUT "result detail data output" ON)
option(ERROR_TO_LOG "error log output" ON)

set(RELEASE_BUILD_TYPES "Release;MinSizeRel")
if(CMAKE_BUILD_TYPE IN_LIST RELEASE_BUILD_TYPES)
    set(BUILD_MODE "release")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -O3")

    set(LOG_TO_CONSOLE OFF)
    set(INIT_OUTPUT OFF)
    set(COMM_OUTPUT OFF)
    set(COMM_ACK_OUTPUT OFF)
    set(PROCESS_STATUS_OUTPUT OFF)
    set(PROCESS_DATA_OUTPUT OFF)
    set(RESULT_CACHE_OUTPUT ON)
    set(ERROR_TO_LOG ON)

    ADD_DEFINITIONS(-DQT_MESSAGELOGCONTEXT)
else()
    set(BUILD_MODE "debug")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -O0 -g")

    set(LOG_TO_CONSOLE ON)
    set(INIT_OUTPUT ON)
    set(COMM_OUTPUT ON)
    set(COMM_ACK_OUTPUT ON)
    set(PROCESS_STATUS_OUTPUT ON)
    set(PROCESS_DATA_OUTPUT ON)
    set(RESULT_CACHE_OUTPUT ON)
    set(ERROR_TO_LOG ON)
endif()

# 2.2 Debug type configuration
if (LOG_TO_CONSOLE)
    message(STATUS "LOG_TO_CONSOLE ON")
    add_compile_definitions(LOG_TO_CONSOLE)
else()
    message(STATUS "LOG_TO_CONSOLE OFF")
endif()

if (INIT_OUTPUT)
    message(STATUS "INIT_OUTPUT ON")
    ADD_DEFINITIONS(-DINIT_OUTPUT)
else()
    message(STATUS "INIT_OUTPUT OFF")
endif()

# 2.3 Communication output configuration
if (COMM_OUTPUT)
    message(STATUS "COMM_OUTPUT ON")
    ADD_DEFINITIONS(-DCOMM_OUTPUT)
else()
    message(STATUS "COMM_OUTPUT OFF")
endif()

if (COMM_ACK_OUTPUT)
    message(STATUS "COMM_ACK_OUTPUT ON")
    ADD_DEFINITIONS(-DCOMM_ACK_OUTPUT)
else()
    message(STATUS "COMM_ACK_OUTPUT OFF")
endif()

# 2.4 Process output configuration
if (PROCESS_STATUS_OUTPUT)
    message(STATUS "PROCESS_STATUS_OUTPUT ON")
    ADD_DEFINITIONS(-DPROCESS_STATUS_OUTPUT)
else()
    message(STATUS "PROCESS_STATUS_OUTPUT OFF")
endif()

if (PROCESS_DATA_OUTPUT)
    message(STATUS "PROCESS_DATA_OUTPUT ON")
    ADD_DEFINITIONS(-DPROCESS_DATA_OUTPUT)
else()
    message(STATUS "PROCESS_DATA_OUTPUT OFF")
endif()

# 2.5 Result cache configuration
if (RESULT_CACHE_OUTPUT)
    message(STATUS "RESULT_CACHE_OUTPUT ON")
    ADD_DEFINITIONS(-DRESULT_CACHE_OUTPUT)
else()
    message(STATUS "RESULT_CACHE_OUTPUT OFF")
endif()

# 2.6 Error logging configuration
if (ERROR_TO_LOG)
    message(STATUS "ERROR_TO_LOG ON")
    ADD_DEFINITIONS(-DERROR_TO_LOG)
else()
    message(STATUS "ERROR_TO_LOG OFF")
endif()

############################################# Project Structure ################################
# 3.1 Core modules
add_subdirectory(core)

# # 3.2 Feature modules
add_subdirectory(modules)

# 3.3 Support modules (必须在infrastructure之前，因为infrastructure依赖foundation)
add_subdirectory(support)

# 3.4 Infrastructure modules
add_subdirectory(infrastructure)

# 3.5 UI modules
add_subdirectory(ui)

# 3.6 Test modules (optional) - 遵循test_guideline.md
option(BUILD_TESTS "Build test programs" OFF)  # 默认禁用测试，专注主程序编译
# 临时禁用测试编译，修复缺少源文件问题后再启用
# if(BUILD_TESTS)
#     enable_testing()  # 启用CTest支持
#     add_subdirectory(tests)
# endif()

############################################# Output Configuration ################################
# 4.1 Set output directories
set(BASE_OUTPUT_DIR "${CMAKE_BINARY_DIR}")
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${BASE_OUTPUT_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${BASE_OUTPUT_DIR}/bin)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${BASE_OUTPUT_DIR}/lib)

# 4.2 Version handling
# set(Python3_EXECUTABLE "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/python3.exe")  # 注释掉硬编码路径
find_package(Python3 REQUIRED COMPONENTS Interpreter)

# 使用动态找到的Python解释器
execute_process(
    COMMAND ${Python3_EXECUTABLE} ${CMAKE_SOURCE_DIR}/scriptFile/get_version_from_log.py ${CMAKE_CURRENT_SOURCE_DIR}
    WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
    OUTPUT_VARIABLE SW_VERSION
    OUTPUT_STRIP_TRAILING_WHITESPACE
    ERROR_QUIET  # 添加错误静默处理
)

execute_process(
    COMMAND ${Python3_EXECUTABLE} ${CMAKE_SOURCE_DIR}/scriptFile/get_sw_type.py ${CMAKE_SOURCE_DIR}
    WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
    OUTPUT_VARIABLE SW_TYPE
    OUTPUT_STRIP_TRAILING_WHITESPACE
    ERROR_QUIET  # 添加错误静默处理
)

# 4.3 Target configuration
set(PROGRAM_PREFIX "CSPC")
set(PROJECT_VERSION "${PROGRAM_PREFIX}_LA_function_V${SW_VERSION}")
add_definitions(-DMAIN_APP_WINDOW_TITLE="${PROJECT_VERSION}")

string(TIMESTAMP CURRENT_DATE "%Y%m%d")
string(STRIP "${SW_TYPE}" SW_TYPE)
if(SW_TYPE STREQUAL "release")
    set(TARGET_NAME "${PROGRAM_PREFIX}_LA_function_v${SW_VERSION}_${CURRENT_DATE}_${BUILD_MODE}")
else()
    set(TARGET_NAME "${PROGRAM_PREFIX}_LA_function")
endif()

# 4.4 Create executable
if(CMAKE_BUILD_TYPE IN_LIST RELEASE_BUILD_TYPES)
    add_executable(${PROJECT_NAME} WIN32 ${SOURCE_FILES})
else()
    add_executable(${PROJECT_NAME} ${SOURCE_FILES})
endif()

# 4.5 Set output properties
set_target_properties(${PROJECT_NAME} PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}
    LIBRARY_OUTPUT_DIRECTORY ${CMAKE_LIBRARY_OUTPUT_DIRECTORY}
    ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_ARCHIVE_OUTPUT_DIRECTORY}
    OUTPUT_NAME ${TARGET_NAME}
)

# 4.6 Include directories - 使用target_include_directories
target_include_directories(${PROJECT_NAME} PRIVATE
    ${PROJECT_SOURCE_DIR}
    ${CMAKE_CURRENT_BINARY_DIR}
    ${PROJECT_SOURCE_DIR}/infrastructure/data/dataHandle
    ${PROJECT_SOURCE_DIR}/infrastructure/data/dataAnalysis
    ${PROJECT_SOURCE_DIR}/infrastructure/utils
    ${PROJECT_SOURCE_DIR}/support/logging
    ${PROJECT_SOURCE_DIR}/modules/core/device/idevice
    ${PROJECT_SOURCE_DIR}/infrastructure/communication
)

# 4.6.1 Find Eigen3 (optional)
# find_package(Eigen3 QUIET)
# if(Eigen3_FOUND)
#     target_link_libraries(${PROJECT_NAME} PRIVATE Eigen3::Eigen)
#     message(STATUS "Found Eigen3: ${EIGEN3_INCLUDE_DIR}")
# else()
#     message(WARNING "Eigen3 not found. Some features may be disabled.")
# endif()

# 4.7 Link libraries - 按照层次结构组织依赖关系
target_link_libraries(${PROJECT_NAME} PRIVATE
    # QT库
    Qt5::Core
    Qt5::Widgets
    Qt5::Gui
    Qt5::Sql
    Qt5::SerialPort
    Qt5::PrintSupport
    Qt5::Charts
    Qt5::Network
    Qt5::Xml
    
    # Windows平台库
    $<$<PLATFORM_ID:Windows>:${WIN32_LIBS}>
    
    # 主模块库 - 只依赖顶层模块，不直接依赖子模块
    LA_core_lib
    # LA_modules_lib
    LA_infrastructure_lib
    LA_themes_lib      # 主题系统模块
    LA_device_communication_ui_lib  # Linus式设备通信UI库
    # LA_ui_lib
    # LA_support_lib
)

# 4.8 Qt依赖部署
if(WIN32)
    copy_qt_dlls(${PROJECT_NAME})
endif()

# 4.9 Install configuration
install(TARGETS ${PROJECT_NAME}
    RUNTIME DESTINATION bin
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)

# 4.9 自动化库文件管理系统

# 自动复制库文件的通用函数
function(copy_target_to_runtime target_name)
    if(WIN32 AND TARGET ${target_name})
        get_target_property(target_type ${target_name} TYPE)
        if(target_type STREQUAL "SHARED_LIBRARY")
            add_custom_command(TARGET ${target_name} POST_BUILD
                COMMAND ${CMAKE_COMMAND} -E copy_if_different
                $<TARGET_FILE:${target_name}>
                ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}
                COMMENT "Copying ${target_name} to runtime directory"
            )
            message(STATUS "Auto-copy configured for library: ${target_name}")
        endif()
    endif()
endfunction()

# 自动发现并复制所有LA库
function(copy_all_la_libraries)
    # 获取所有目标
    get_property(all_targets DIRECTORY ${CMAKE_SOURCE_DIR} PROPERTY BUILDSYSTEM_TARGETS)

    foreach(target ${all_targets})
        # 只处理LA开头的共享库
        if(target MATCHES "^LA_.*_lib$")
            copy_target_to_runtime(${target})
        endif()
    endforeach()
    message(STATUS "Auto-copy configured for all LA libraries")
endfunction()

if(WIN32)
    # 自动部署Qt依赖 - 临时禁用调试编译问题
    # find_program(WINDEPLOYQT_EXECUTABLE windeployqt HINTS ${Qt5_DIR}/../../../bin)
    # if(WINDEPLOYQT_EXECUTABLE)
    #     add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
    #         COMMAND ${WINDEPLOYQT_EXECUTABLE} --debug --compiler-runtime $<TARGET_FILE:${PROJECT_NAME}>
    #         COMMENT "Deploying Qt libraries"
    #     )
    # endif()

    # 创建分发包目标
    add_custom_target(create_distribution
        COMMENT "Creating distribution package"
    )

    # 复制主程序到分发目录
    add_custom_command(TARGET create_distribution POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E make_directory ${CMAKE_BINARY_DIR}/dist
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        $<TARGET_FILE:${PROJECT_NAME}>
        ${CMAKE_BINARY_DIR}/dist/
        COMMENT "Copying main application to distribution directory"
    )

    # 复制所有依赖库到分发目录
    add_custom_command(TARGET create_distribution POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_directory
        ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}
        ${CMAKE_BINARY_DIR}/dist/
        COMMENT "Copying all dependencies to distribution directory"
    )

    # 创建便携式启动脚本
    add_custom_command(TARGET create_distribution POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E echo "@echo off" > ${CMAKE_BINARY_DIR}/dist/portable_launcher.bat
        COMMAND ${CMAKE_COMMAND} -E echo "set APP_DIR=%~dp0" >> ${CMAKE_BINARY_DIR}/dist/portable_launcher.bat
        COMMAND ${CMAKE_COMMAND} -E echo "set PATH=%APP_DIR%;%PATH%" >> ${CMAKE_BINARY_DIR}/dist/portable_launcher.bat
        COMMAND ${CMAKE_COMMAND} -E echo "start \"\" \"%APP_DIR%\\CSPC_LA_function.exe\"" >> ${CMAKE_BINARY_DIR}/dist/portable_launcher.bat
        COMMENT "Creating portable launcher script"
    )
endif()

# 在主程序配置后调用自动复制
copy_all_la_libraries()

# 新架构测试可执行文件 - 临时注释掉，缺少源文件
# add_executable(test_matching_architecture test_new_matching_architecture.cpp)
# target_link_libraries(test_matching_architecture
#     Qt5::Core
#     Qt5::Network  
#     Qt5::SerialPort
#     LA_device_management_lib
# )
# target_include_directories(test_matching_architecture PRIVATE
#     ${CMAKE_SOURCE_DIR}/modules/device_management/include
#     ${CMAKE_SOURCE_DIR}/infrastructure/communication/include
# )

# 构建完成后的摘要信息
message(STATUS "LA library management system configured")
message(STATUS "Use 'ninja create_distribution' to create distribution package")
