#include "adapters/LegacyInterpolationAdapter.h"
#include "factories/InterpolationFactory.h"
#include "factories/FilterFactory.h"
#include <QDebug>
#include <QVector>

/**
 * @brief 简单的测试程序，验证ImageProcessing模块功能
 */
int main() {
    qDebug() << "=== ImageProcessing Module Test ===";
    
    try {
        // 测试1: 创建传统适配器
        qDebug() << "\n1. Testing LegacyInterpolationAdapter...";
        my_interPolation adapter;
        adapter.setDebugEnabled(true);
        qDebug() << "Adapter version:" << adapter.getVersion();
        
        // 测试2: 创建测试数据
        qDebug() << "\n2. Creating test data...";
        QVector<QVector<uint32_t>> srcData(4);
        for (int i = 0; i < 4; ++i) {
            srcData[i].resize(4);
            for (int j = 0; j < 4; ++j) {
                srcData[i][j] = i * 4 + j + 1; // 1-16的数据
            }
        }
        
        QVector<QVector<uint32_t>> dstData(6);
        for (int i = 0; i < 6; ++i) {
            dstData[i].resize(6);
        }
        
        qDebug() << "Source data created: 4x4";
        qDebug() << "Destination data created: 6x6";
        
        // 测试3: 双线性插值
        qDebug() << "\n3. Testing bilinear interpolation...";
        adapter.bilinear_interpolation(srcData, dstData);
        qDebug() << "Bilinear interpolation completed";
        qDebug() << "Processing time:" << adapter.getLastProcessingTime() << "ms";
        
        // 输出部分结果
        qDebug() << "Sample output values:";
        qDebug() << "dstData[0][0]:" << dstData[0][0];
        qDebug() << "dstData[2][2]:" << dstData[2][2];
        qDebug() << "dstData[5][5]:" << dstData[5][5];
        
        // 测试4: 工厂模式
        qDebug() << "\n4. Testing factory pattern...";
        auto& interpFactory = ImageProcessing::InterpolationFactory::getInstance();
        qDebug() << "InterpolationFactory version:" << interpFactory.getVersion();
        
        auto supportedTypes = interpFactory.getSupportedTypes();
        qDebug() << "Supported interpolation types:" << supportedTypes.size();
        
        auto& filterFactory = ImageProcessing::FilterFactory::getInstance();
        qDebug() << "FilterFactory version:" << filterFactory.getVersion();
        
        auto supportedFilters = filterFactory.getSupportedTypes();
        qDebug() << "Supported filter types:" << supportedFilters.size();
        
        // 测试5: 直接使用新接口
        qDebug() << "\n5. Testing new interface directly...";
        auto interpolator = interpFactory.createInterpolation(ImageProcessing::InterpolationType::Bilinear);
        qDebug() << "Created interpolator:" << interpolator->getAlgorithmName();
        qDebug() << "Description:" << interpolator->getDescription();
        
        // 创建ImageData对象
        ImageProcessing::ImageDataU32 srcImage(4, 4);
        ImageProcessing::ImageDataU32 dstImage(6, 6);
        
        // 填充测试数据
        for (uint32_t y = 0; y < 4; ++y) {
            for (uint32_t x = 0; x < 4; ++x) {
                srcImage.matrix()[y][x] = y * 4 + x + 1;
            }
        }
        
        // 执行插值
        bool success = interpolator->interpolate(srcImage, dstImage);
        qDebug() << "Direct interpolation result:" << (success ? "SUCCESS" : "FAILED");
        
        if (success) {
            qDebug() << "Sample direct output values:";
            qDebug() << "dstImage[0][0]:" << dstImage.matrix()[0][0];
            qDebug() << "dstImage[2][2]:" << dstImage.matrix()[2][2];
            qDebug() << "dstImage[5][5]:" << dstImage.matrix()[5][5];
        }
        
        // 测试6: 滤波器测试
        qDebug() << "\n6. Testing filters...";
        try {
            auto kalmanFilter = filterFactory.createFilter(ImageProcessing::FilterType::Kalman);
            qDebug() << "Created Kalman filter:" << kalmanFilter->getAlgorithmName();
            
            // 应用到测试图像
            ImageProcessing::ImageDataU32 testImage(4, 4);
            for (uint32_t y = 0; y < 4; ++y) {
                for (uint32_t x = 0; x < 4; ++x) {
                    testImage.matrix()[y][x] = (y + x) * 10 + 100; // 测试数据
                }
            }
            
            bool filterSuccess = kalmanFilter->apply(testImage);
            qDebug() << "Kalman filter result:" << (filterSuccess ? "SUCCESS" : "FAILED");
            
        } catch (const ImageProcessing::UnsupportedOperationException& e) {
            qDebug() << "Kalman filter not supported:" << e.qMessage();
        }
        
        try {
            auto convFilter = filterFactory.createFilter(ImageProcessing::FilterType::Convolution);
            qDebug() << "Created Convolution filter:" << convFilter->getAlgorithmName();
            
        } catch (const ImageProcessing::UnsupportedOperationException& e) {
            qDebug() << "Convolution filter not supported:" << e.qMessage();
        }
        
        qDebug() << "\n=== All tests completed successfully! ===";
        return 0;
        
    } catch (const ImageProcessing::ProcessingException& e) {
        qCritical() << "Processing error:" << e.qMessage();
        return -1;
    } catch (const std::exception& e) {
        qCritical() << "Standard error:" << e.what();
        return -1;
    } catch (...) {
        qCritical() << "Unknown error occurred";
        return -1;
    }
}
