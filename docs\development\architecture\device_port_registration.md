# 设备与端口注册及生命周期管理

## 概述

本文档基于Canvas原始架构图，详细描述设备发现、端口匹配、实例注册和生命周期管理的完整流程。采用**最小模块原则**和**单一职责原则**进行架构优化，解决设备与端口的关系处理和生命周期同步问题。

## Canvas原始流程分析

### 原始流程链条

根据Canvas架构图，完整的设备-端口流程包含：

```
可通讯设备列表 → 具体设备 → 明确的端口类型 
                    ↓
端口列表(串口/网口) → 端口配置 → 端口匹配
                    ↓  
打开设备(设备与端口生命周期相同) → 端口实例注册 + 设备实例注册
                    ↓
端口实例统一管理 + 设备实例管理 → 业务数据流
```

### 关键问题识别

**Canvas中的核心问题**：
1. **生命周期绑定**："打开设备(相当于同时端口，两者生命周期相同？)"
2. **职责混淆**：设备发现、端口管理、实例创建混合在一起
3. **依赖耦合**：设备直接依赖端口，端口状态影响设备状态

## 优化后的注册架构

### 1. 发现阶段 - 独立的发现模块

```mermaid
graph TB
    subgraph "设备发现子系统"
        DDS[DeviceDiscoveryService<br/>设备发现服务]
        DDR[DeviceDiscoveryResult<br/>发现结果]
        DT[DeviceType<br/>设备类型识别]
        PT[PortType<br/>端口需求]
    end

    subgraph "端口发现子系统"  
        PDS[PortDiscoveryService<br/>端口发现服务]
        PDR[PortDiscoveryResult<br/>端口列表]
        PC[PortCapability<br/>端口能力]
        PS[PortStatus<br/>端口状态]
    end

    DDS --> DDR
    DDR --> DT
    DT --> PT
    
    PDS --> PDR
    PDR --> PC
    PC --> PS

    classDef discovery fill:#e3f2fd
    class DDS,DDR,DT,PT,PDS,PDR,PC,PS discovery
```

#### 1.1 设备发现服务 (DeviceDiscoveryService)

**职责**: 只负责发现可通讯设备
**不涉及**: 端口操作、设备实例化、连接管理

```cpp
namespace LA::Discovery {

class DeviceDiscoveryService {
public:
    // 设备发现 - 单一职责
    QList<DeviceDiscoveryResult> discoverDevices();
    QList<DeviceDiscoveryResult> discoverDevicesByType(DeviceType type);
    
    // 设备类型识别
    DeviceType identifyDeviceType(const QVariantMap& deviceInfo);
    QStringList getSupportedPortTypes(DeviceType type);
    
    // 发现配置
    void setDiscoveryTimeout(int timeoutMs);
    void setDiscoveryFilters(const QStringList& filters);
    
    // 状态查询
    bool isDiscovering() const;
    int getDiscoveredDeviceCount() const;
    
signals:
    void deviceDiscovered(const DeviceDiscoveryResult& result);
    void discoveryFinished();
    void discoveryError(const QString& error);

private:
    void scanSerialDevices();
    void scanNetworkDevices();
    void scanUSBDevices();
};

struct DeviceDiscoveryResult {
    QString deviceId;
    QString deviceName;
    DeviceType deviceType;
    QString manufacturer;
    QString model;
    QString version;
    QStringList requiredPortTypes;  // 设备需要的端口类型
    QVariantMap properties;
    QDateTime discoveredTime;
};

}
```

#### 1.2 端口发现服务 (PortDiscoveryService)

**职责**: 只负责发现系统可用端口
**不涉及**: 设备逻辑、协议处理、设备连接

```cpp
namespace LA::Discovery {

class PortDiscoveryService {
public:
    // 端口发现 - 单一职责
    QList<PortDiscoveryResult> discoverPorts();
    QList<PortDiscoveryResult> discoverPortsByType(PortType type);
    
    // 端口状态监控
    PortStatus getPortStatus(const QString& portName);
    bool isPortAvailable(const QString& portName);
    
    // 端口能力检测
    PortCapability getPortCapability(const QString& portName);
    QStringList getSupportedBaudRates(const QString& portName);
    
    // 实时监控
    void startPortMonitoring();
    void stopPortMonitoring();
    
signals:
    void portDiscovered(const PortDiscoveryResult& result);
    void portRemoved(const QString& portName);
    void portStatusChanged(const QString& portName, PortStatus status);

private:
    void scanSerialPorts();
    void scanNetworkPorts();
    void scanUSBPorts();
};

struct PortDiscoveryResult {
    QString portName;
    PortType portType;
    QString description;
    QString manufacturer;
    QString location;
    PortStatus status;
    PortCapability capability;
    QVariantMap properties;
    QDateTime discoveredTime;
};

}
```

### 2. 匹配阶段 - 独立的匹配逻辑

```mermaid
graph TB
    subgraph "匹配子系统"
        DPM[DevicePortMatcher<br/>设备端口匹配器]
        MR[MatchingRule<br/>匹配规则]
        MS[MatchingStrategy<br/>匹配策略]
        MResult[MatchingResult<br/>匹配结果]
    end

    DPM --> MR
    MR --> MS  
    MS --> MResult

    classDef matching fill:#f3e5f5
    class DPM,MR,MS,MResult matching
```

#### 2.1 设备端口匹配器 (DevicePortMatcher)

**职责**: 只负责设备和端口的匹配逻辑
**不涉及**: 设备实例化、端口打开、数据传输

```cpp
namespace LA::Matching {

class DevicePortMatcher {
public:
    // 匹配逻辑 - 单一职责
    QList<MatchingResult> matchDevicesToPorts(
        const QList<DeviceDiscoveryResult>& devices,
        const QList<PortDiscoveryResult>& ports);
    
    MatchingResult findBestPortForDevice(
        const DeviceDiscoveryResult& device,
        const QList<PortDiscoveryResult>& availablePorts);
    
    // 匹配规则管理
    void addMatchingRule(const MatchingRule& rule);
    void removeMatchingRule(const QString& ruleId);
    QList<MatchingRule> getMatchingRules() const;
    
    // 匹配策略
    void setMatchingStrategy(MatchingStrategy strategy);
    MatchingStrategy getMatchingStrategy() const;
    
    // 匹配验证
    bool validateMatch(const DeviceDiscoveryResult& device, 
                       const PortDiscoveryResult& port);
    MatchingScore calculateMatchingScore(const DeviceDiscoveryResult& device, 
                                         const PortDiscoveryResult& port);

private:
    QList<MatchingRule> m_rules;
    MatchingStrategy m_strategy;
    
    bool matchPortType(const DeviceDiscoveryResult& device, 
                       const PortDiscoveryResult& port);
    bool matchCapability(const DeviceDiscoveryResult& device, 
                         const PortDiscoveryResult& port);
};

struct MatchingResult {
    DeviceDiscoveryResult device;
    PortDiscoveryResult port;
    MatchingScore score;
    QString matchingRuleId;
    bool isExactMatch;
    QStringList warnings;
    QDateTime matchedTime;
};

enum class MatchingStrategy {
    BestMatch,      // 最佳匹配
    FirstAvailable, // 首个可用
    UserSelection,  // 用户选择
    LoadBalance     // 负载均衡
};

}
```

### 3. 注册阶段 - 统一的实例注册管理

双层注册机制，设备和端口类型的静态注册，实例的动态注册管理

```mermaid
graph TB
    subgraph "注册管理子系统"
        RM[RegistrationManager<br/>注册管理器]
        DR[DeviceRegistry<br/>设备注册表]
        PR[PortRegistry<br/>端口注册表]
        CM[ConnectionMapping<br/>连接映射]
        LM[LifecycleManager<br/>生命周期管理器]
    end

    RM --> DR
    RM --> PR
    RM --> CM
    RM --> LM

    classDef registration fill:#e8f5e8
    class RM,DR,PR,CM,LM registration
```

#### 3.1 注册管理器 (RegistrationManager)

**职责**: 统一管理设备和端口的注册过程
**不涉及**: 具体的设备业务逻辑、数据传输

```cpp
namespace LA::Registration {

class RegistrationManager {
public:
    // 注册管理 - 统一接口
    RegistrationResult registerDeviceConnection(const MatchingResult& match);
    bool unregisterDeviceConnection(const QString& deviceId);
    
    // 批量注册
    QList<RegistrationResult> registerMultipleConnections(
        const QList<MatchingResult>& matches);
    
    // 注册状态查询
    bool isDeviceRegistered(const QString& deviceId);
    bool isPortRegistered(const QString& portName);
    QString getPortForDevice(const QString& deviceId);
    QString getDeviceForPort(const QString& portName);
    
    // 连接映射管理
    QMap<QString, QString> getDevicePortMappings() const;
    bool updateDevicePortMapping(const QString& deviceId, const QString& portName);
    
    // 生命周期协调
    bool openDeviceConnection(const QString& deviceId);
    bool closeDeviceConnection(const QString& deviceId);
    bool reopenDeviceConnection(const QString& deviceId);
    
signals:
    void deviceRegistered(const QString& deviceId, const QString& portName);
    void deviceUnregistered(const QString& deviceId);
    void connectionOpened(const QString& deviceId);
    void connectionClosed(const QString& deviceId);
    void connectionError(const QString& deviceId, const QString& error);

private:
    std::unique_ptr<DeviceRegistry> m_deviceRegistry;
    std::unique_ptr<PortRegistry> m_portRegistry;
    std::unique_ptr<ConnectionMapping> m_connectionMapping;
    std::unique_ptr<LifecycleManager> m_lifecycleManager;
};

struct RegistrationResult {
    QString deviceId;
    QString portName;
    bool success;
    QString error;
    QDateTime registrationTime;
    ConnectionState state;
};

}
```

#### 3.2 生命周期管理器 (LifecycleManager)

**职责**: 协调设备和端口的生命周期同步
**不涉及**: 设备业务逻辑、协议处理

```cpp
namespace LA::Registration {

class LifecycleManager {
public:
    // 生命周期协调 - 单一职责
    bool synchronizeDevicePortLifecycle(const QString& deviceId, const QString& portName);
    
    // 设备生命周期操作
    bool openDevice(const QString& deviceId);
    bool closeDevice(const QString& deviceId);
    bool restartDevice(const QString& deviceId);
    
    // 端口生命周期操作
    bool openPort(const QString& portName);
    bool closePort(const QString& portName);
    bool resetPort(const QString& portName);
    
    // 同步操作
    bool openDeviceConnection(const QString& deviceId);  // 同时开启设备和端口
    bool closeDeviceConnection(const QString& deviceId); // 同时关闭设备和端口
    
    // 状态监控
    LifecycleState getDeviceState(const QString& deviceId);
    LifecycleState getPortState(const QString& portName);
    bool areStatesInSync(const QString& deviceId, const QString& portName);
    
    // 错误恢复
    bool recoverFromError(const QString& deviceId);
    bool attemptReconnection(const QString& deviceId);
    
signals:
    void lifecycleStateChanged(const QString& deviceId, LifecycleState state);
    void synchronizationComplete(const QString& deviceId, const QString& portName);
    void synchronizationFailed(const QString& deviceId, const QString& error);

private:
    QMap<QString, QString> m_devicePortMapping;
    QMap<QString, LifecycleState> m_deviceStates;
    QMap<QString, LifecycleState> m_portStates;
    
    bool ensureStateSync(const QString& deviceId, const QString& portName);
    void handleStateConflict(const QString& deviceId, const QString& portName);
};

enum class LifecycleState {
    Unknown,
    Initializing,
    Ready,
    Opening,
    Open,
    Active,
    Closing,
    Closed,
    Error,
    Recovering
};

}
```

## 完整的注册流程实现

### 流程编排器 (RegistrationOrchestrator)

```cpp
namespace LA::Registration {

class RegistrationOrchestrator {
public:
    // 完整注册流程
    void executeFullRegistrationFlow();
    
private:
    void step1_DiscoverDevices();
    void step2_DiscoverPorts();  
    void step3_MatchDevicesToPorts();
    void step4_RegisterConnections();
    void step5_OpenConnections();
    
private:
    std::unique_ptr<DeviceDiscoveryService> m_deviceDiscovery;
    std::unique_ptr<PortDiscoveryService> m_portDiscovery;
    std::unique_ptr<DevicePortMatcher> m_matcher;
    std::unique_ptr<RegistrationManager> m_registrationManager;
};

}
```

## 关键问题解答

### 1. 设备与端口的生命周期关系

**Canvas问题**: "打开设备(相当于同时端口，两者生命周期相同？)"

**优化后的解答**:
```cpp
// 通过LifecycleManager统一协调，而不是直接绑定
class LifecycleManager {
    bool openDeviceConnection(const QString& deviceId) {
        QString portName = getPortForDevice(deviceId);
        
        // 协调开启，而非绑定
        bool portOpened = openPort(portName);
        bool deviceOpened = openDevice(deviceId);
        
        if (portOpened && deviceOpened) {
            synchronizeStates(deviceId, portName);
            return true;
        }
        
        // 失败回滚
        if (portOpened) closePort(portName);
        if (deviceOpened) closeDevice(deviceId);
        return false;
    }
};
```

### 2. 职责边界清晰化

- **DeviceDiscoveryService**: 只发现设备，不管端口
- **PortDiscoveryService**: 只发现端口，不管设备
- **DevicePortMatcher**: 只做匹配，不做注册
- **RegistrationManager**: 只管注册，不做业务逻辑
- **LifecycleManager**: 只协调生命周期，不处理数据

### 3. 可测试性提升

每个模块都可以独立测试：
```cpp
// 测试设备发现
TEST(DeviceDiscoveryServiceTest, DiscoverSerialDevices) {
    DeviceDiscoveryService service;
    auto results = service.discoverDevicesByType(DeviceType::Serial);
    ASSERT_GT(results.size(), 0);
}

// 测试端口匹配
TEST(DevicePortMatcherTest, MatchSerialDeviceToSerialPort) {
    DevicePortMatcher matcher;
    auto result = matcher.findBestPortForDevice(serialDevice, availablePorts);
    ASSERT_TRUE(result.isExactMatch);
}

// 测试生命周期
TEST(LifecycleManagerTest, SynchronizeDevicePortLifecycle) {
    LifecycleManager manager;
    bool success = manager.openDeviceConnection("device_001");
    ASSERT_TRUE(success);
    ASSERT_TRUE(manager.areStatesInSync("device_001", "COM1"));
}
```

## 总结

基于Canvas原始架构的完整分析，优化后的设备端口注册架构具有以下优势：

1. **完整性**: 覆盖了Canvas中的所有流程环节
2. **职责清晰**: 每个模块只负责一个明确的功能
3. **生命周期解耦**: 通过协调而非绑定管理生命周期
4. **高可测试性**: 每个模块都可以独立单元测试
5. **易扩展性**: 新的设备类型和端口类型容易接入

这个架构完美解决了Canvas中提出的"设备与端口生命周期相同"的问题，通过依赖注入和组合模式实现了职责分离，符合最小模块和单一职责原则。