# architecture

工业软件架构

## 


LA项目采用**5层分层架构**，确保清晰的职责分离和单向依赖关系：

```text
┌─────────────────────────────────────────┐
│              应用层 (Application)        │  ← 应用程序入口和主控制
├─────────────────────────────────────────┤
│              界面层 (UI)                 │  ← 用户界面和交互
├─────────────────────────────────────────┤
│              模块层 (Modules)            │  ← 业务功能和插件
├─────────────────────────────────────────┤
│            基础设施层 (Infrastructure)    │  ← 核心服务和通用功能
├─────────────────────────────────────────┤
│              支持层 (Support)            │  ← 辅助服务和工具
└─────────────────────────────────────────┘
```

####  层次职责定义

| 层次 | 主要职责 | 典型模块 | 依赖方向 |
|------|---------|----------|----------|
| **应用层** | 应用程序生命周期管理、框架协调、全局状态 | 主应用程序、框架系统、插件管理 | ↓ 所有层 |
| **界面层** | 用户界面组件、交互逻辑、视觉呈现 | UI组件、窗口管理、主题系统 | ↓ 模块层、基础设施层、支持层 |
| **模块层** | 业务逻辑实现、功能模块、领域服务 | 设备管理、数据处理、算法模块 | ↓ 基础设施层、支持层 |
| **基础设施层** | 核心服务、通信、数据处理、算法库 | 通信系统、数据库、算法库 | ↓ 支持层 |
| **支持层** | 配置、日志、监控、诊断、工具类 | 配置管理、日志系统、监控 | 无依赖（被依赖） |