#ifndef LA_DEVICE_MANAGEMENT_DEVICE_INSTANCE_MANAGER_H
#define LA_DEVICE_MANAGEMENT_DEVICE_INSTANCE_MANAGER_H

#include <QObject>
#include <QString>
#include <QStringList>
#include <QVariantMap>
#include <QDateTime>
#include <QMap>
#include <QMutex>
#include <memory>

// 包含统一的类型定义
#include "Core/DeviceManagementTypes.h"

// 前置声明
namespace LA::Device::Core {
    class IDevice;
}

namespace LA::DeviceManagement {

// InstanceLifecycle 枚举已在 Core/DeviceManagementTypes.h 中定义

// 设备实例信息结构已在 Core/DeviceManagementTypes.h 中定义

/**
 * @brief 设备实例数据（内部使用）
 */
struct DeviceInstanceData {
    QString instanceId;                  // 实例唯一ID
    QString deviceType;                  // 设备类型
    QVariantMap instanceConfig;          // 实例特定配置
    LA::Device::Core::IDevice* deviceObject; // 设备对象指针
    InstanceLifecycle lifecycle;         // 生命周期状态
    QDateTime createTime;                // 创建时间
    QDateTime lastActiveTime;            // 最后活动时间
    QString lastError;                   // 最后错误
    QVariantMap runtimeData;             // 运行时数据
    
    DeviceInstanceData() 
        : deviceObject(nullptr)
        , lifecycle(InstanceLifecycle::Created)
        , createTime(QDateTime::currentDateTime())
        , lastActiveTime(QDateTime::currentDateTime()) {}
};

/**
 * @brief 设备实例管理器 - 运行时动态管理
 * 
 * 遵循Linus式双层架构设计：
 * 1. 专注于设备实例的运行时管理，不处理设备类型定义
 * 2. 管理具体创建的设备对象实例，生命周期管理
 * 3. 与设备类型注册表协作，但职责清晰分离
 */
class DeviceInstanceManager : public QObject {
    Q_OBJECT
    
public:
    explicit DeviceInstanceManager(QObject* parent = nullptr);
    virtual ~DeviceInstanceManager();
    
    // ====== 实例创建和销毁 ======
    QString createDeviceInstance(const QString& deviceType, const QVariantMap& config = {});
    bool destroyDeviceInstance(const QString& instanceId);
    
    // ====== 实例查询 ======
    QStringList getActiveInstanceIds() const;                     // 当前有几个设备实例在运行
    QStringList getInstancesByType(const QString& deviceType) const;
    DeviceInstanceInfo getInstanceInfo(const QString& instanceId) const;
    LA::Device::Core::IDevice* getDeviceObject(const QString& instanceId) const;
    
    // ====== 实例操作 ======
    bool connectInstance(const QString& instanceId);
    bool disconnectInstance(const QString& instanceId);
    bool isInstanceConnected(const QString& instanceId) const;
    
    // ====== 生命周期管理 ======
    InstanceLifecycle getInstanceLifecycle(const QString& instanceId) const;
    bool startInstance(const QString& instanceId);
    bool stopInstance(const QString& instanceId);
    bool initializeInstance(const QString& instanceId);
    
    // ====== 状态管理 ======
    bool setInstanceConfig(const QString& instanceId, const QVariantMap& config);
    QVariantMap getInstanceConfig(const QString& instanceId) const;
    bool setInstanceRuntimeData(const QString& instanceId, const QVariantMap& data);
    QVariantMap getInstanceRuntimeData(const QString& instanceId) const;
    
    // ====== 统计信息 ======
    int getTotalInstanceCount() const;
    int getInstanceCountByType(const QString& deviceType) const;
    QStringList getSupportedDeviceTypes() const;
    QString getLastError() const;
    
signals:
    void instanceCreated(const QString& instanceId);
    void instanceDestroyed(const QString& instanceId);
    void instanceConnected(const QString& instanceId);
    void instanceDisconnected(const QString& instanceId);
    void instanceLifecycleChanged(const QString& instanceId, InstanceLifecycle newState);
    void instanceError(const QString& instanceId, const QString& error);
    
private slots:
    void onDeviceError(const QString& error);
    void onDeviceDataReceived(const QVariantMap& data);
    
private:
    // ====== 内部管理 ======
    QString generateInstanceId(const QString& deviceType);
    bool validateInstanceId(const QString& instanceId) const;
    bool validateDeviceType(const QString& deviceType) const;
    void updateInstanceActivity(const QString& instanceId);
    void setInstanceLifecycle(const QString& instanceId, InstanceLifecycle lifecycle);
    void setInstanceError(const QString& instanceId, const QString& error);
    
    // ====== 设备对象管理 ======
    LA::Device::Core::IDevice* createDeviceObject(const QString& deviceType, const QString& instanceId);
    void destroyDeviceObject(LA::Device::Core::IDevice* device);
    void connectDeviceSignals(LA::Device::Core::IDevice* device, const QString& instanceId);
    void disconnectDeviceSignals(LA::Device::Core::IDevice* device);
    
private:
    QMap<QString, DeviceInstanceData> m_activeInstances; // 运行时管理
    mutable QMutex m_mutex;                              // 线程安全
    QString m_lastError;                                 // 最后错误
    int m_nextInstanceNumber;                            // 实例编号计数器
};

} // namespace LA::DeviceManagement

// 注册元类型以支持信号槽
Q_DECLARE_METATYPE(LA::DeviceManagement::InstanceLifecycle)
Q_DECLARE_METATYPE(LA::DeviceManagement::DeviceInstanceInfo)

#endif // LA_DEVICE_MANAGEMENT_DEVICE_INSTANCE_MANAGER_H
