#include "LA/Settings/panels/ThemeSettingsPanel.h"
#include <LA/Themes/ThemeManager.h>
#include <QColorDialog>
#include <QDebug>
#include <QGridLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QMessageBox>
#include <QVBoxLayout>

namespace LA {
namespace Settings {

ThemeSettingsPanel::ThemeSettingsPanel(QWidget *parent)
    : SettingsPanel("theme", tr("主题设置"), tr("配置应用程序的外观主题，包括颜色方案、字体等"), ":/icons/settings/theme.png", tr("外观"), 20, parent),
      m_themeGroup(nullptr),
      m_themeComboBox(nullptr),
      m_themeDescriptionLabel(nullptr),
      m_colorGroup(nullptr),
      m_colorSchemeComboBox(nullptr),
      m_customColorButton(nullptr),
      m_colorPreviewFrame(nullptr),
      m_colorPreviewLabel(nullptr),
      m_fontGroup(nullptr),
      m_fontFamilyComboBox(nullptr),
      m_fontSizeSpinBox(nullptr),
      m_fontPreviewLabel(nullptr),
      m_previewGroup(nullptr),
      m_previewWidget(nullptr),
      m_previewTextLabel(nullptr),
      m_previewButton(nullptr),
      m_buttonLayout(nullptr),
      m_previewThemeButton(nullptr),
      m_resetThemeButton(nullptr),
      m_applyButton(nullptr),
      m_themeManager(nullptr),
      m_currentTheme("default"),
      m_currentColorScheme("light"),
      m_customColor(QColor(64, 128, 255)),
      m_useCustomColor(false) {
    // 设置默认字体
    m_currentFont = QFont(QStringLiteral("Microsoft YaHei"), 10);

    // 初始化主题管理器
    m_themeManager = &LA::Themes::ThemeManager::instance();

    // 创建UI
    createUI();

    // 连接信号
    connectSignals();
}

void ThemeSettingsPanel::createUI() {
    qDebug() << "ThemeSettingsPanel::createUI() 开始";

    // 获取基类已经创建的内容布局
    QVBoxLayout *contentLayout = getContentLayout();
    if (!contentLayout) {
        qWarning() << "ThemeSettingsPanel: 无法获取内容布局";
        return;
    }

    // 主题选择
    qDebug() << "ThemeSettingsPanel: 创建主题选择组";
    setupThemeSelection();
    if (m_themeGroup) {
        qDebug() << "ThemeSettingsPanel: m_themeGroup创建成功:" << m_themeGroup;
        qDebug() << "ThemeSettingsPanel: m_themeGroup尺寸:" << m_themeGroup->size();
        qDebug() << "ThemeSettingsPanel: m_themeGroup尺寸策略:" << m_themeGroup->sizePolicy().horizontalPolicy() << m_themeGroup->sizePolicy().verticalPolicy();
        qDebug() << "ThemeSettingsPanel: contentLayout指针:" << contentLayout;
        contentLayout->addWidget(m_themeGroup);
        qDebug() << "ThemeSettingsPanel: 主题选择组已添加";
    } else {
        qWarning() << "ThemeSettingsPanel: m_themeGroup创建失败！";
    }

    // 颜色方案
    qDebug() << "ThemeSettingsPanel: 创建颜色方案组";
    setupColorScheme();
    if (m_colorGroup) {
        contentLayout->addWidget(m_colorGroup);
        qDebug() << "ThemeSettingsPanel: 颜色方案组已添加";
    }

    // 字体设置
    qDebug() << "ThemeSettingsPanel: 创建字体设置组";
    setupFontSettings();
    if (m_fontGroup) {
        contentLayout->addWidget(m_fontGroup);
        qDebug() << "ThemeSettingsPanel: 字体设置组已添加";
    }

    // 预览区域
    qDebug() << "ThemeSettingsPanel: 创建预览区域组";
    setupPreviewArea();
    if (m_previewGroup) {
        contentLayout->addWidget(m_previewGroup);
        qDebug() << "ThemeSettingsPanel: 预览区域组已添加";
    }

    // 操作按钮已在SettingsDialog底部统一管理，无需在面板内部重复添加

    // 添加弹性空间
    contentLayout->addStretch();
    qDebug() << "ThemeSettingsPanel::createUI() 完成";
}

void ThemeSettingsPanel::setupThemeSelection() {
    m_themeGroup        = new QGroupBox(tr("主题选择"), this);
    // 调试：为QGroupBox添加黄色背景
    // 屏蔽子容器色块 - m_.*Group->setStyleSheet("QGroupBox { background-color: yellow; border: 2px solid orange; }");
    // 设置正确的尺寸策略，让其扩展填充可用空间
    m_themeGroup->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
    // 移除固定尺寸限制，让布局系统自动管理
    // m_themeGroup->setMinimumSize(500, 100);  // 移除固定最小尺寸
    // m_themeGroup->resize(600, 150);          // 移除固定尺寸
    
    QVBoxLayout *layout = new QVBoxLayout(m_themeGroup);

    // 主题选择下拉框，应用主题系统样式
    QHBoxLayout *themeLayout = new QHBoxLayout();
    int spacing = m_themeManager->getMetric(LA::Themes::ThemeManager::Metric::SpacingMedium);
    themeLayout->setSpacing(spacing);
    
    themeLayout->addWidget(new QLabel(tr("当前主题:"), this));

    m_themeComboBox = new QComboBox(this);
    loadAvailableThemes();
    // 应用主题系统样式到下拉框
    m_themeManager->applyThemeToWidget(m_themeComboBox, LA::Themes::ThemeManager::ComponentType::ComboBox);
    
    themeLayout->addWidget(m_themeComboBox);
    themeLayout->addStretch();

    layout->addLayout(themeLayout);

    // 主题描述，使用主题系统样式替换硬编码
    m_themeDescriptionLabel = new QLabel(tr("选择应用程序的整体外观主题"), this);
    
    // 使用主题系统的语义化颜色和字体
    auto themeManager = &LA::Themes::ThemeManager::instance();
    QColor textMuted = themeManager->getSemanticColor(LA::Themes::ThemeManager::ColorRole::TextMuted);
    QFont captionFont = themeManager->getFont(LA::Themes::ThemeManager::FontRole::Caption);
    
    m_themeDescriptionLabel->setStyleSheet(QString("color: %1;").arg(textMuted.name()));
    m_themeDescriptionLabel->setFont(captionFont);
    
    layout->addWidget(m_themeDescriptionLabel);
}

void ThemeSettingsPanel::setupColorScheme() {
    m_colorGroup        = new QGroupBox(tr("颜色方案"), this);
    // 调试：为QGroupBox添加黄色背景
    // 屏蔽子容器色块 - m_.*Group->setStyleSheet("QGroupBox { background-color: yellow; border: 2px solid orange; }");
    // 设置正确的尺寸策略，让其扩展填充可用空间
    m_colorGroup->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
    // 移除固定尺寸限制，让布局系统自动管理
    // m_colorGroup->setMinimumSize(500, 100);  // 移除固定最小尺寸
    // m_colorGroup->resize(600, 150);          // 移除固定尺寸
    
    QVBoxLayout *layout = new QVBoxLayout(m_colorGroup);

    // 颜色方案选择，应用主题系统样式
    QHBoxLayout *schemeLayout = new QHBoxLayout();
    int spacing = m_themeManager->getMetric(LA::Themes::ThemeManager::Metric::SpacingMedium);
    schemeLayout->setSpacing(spacing);
    schemeLayout->addWidget(new QLabel(tr("配色方案:"), this));

    m_colorSchemeComboBox = new QComboBox(this);
    loadAvailableColorSchemes();
    // 应用主题系统样式
    m_themeManager->applyThemeToWidget(m_colorSchemeComboBox, LA::Themes::ThemeManager::ComponentType::ComboBox);
    
    schemeLayout->addWidget(m_colorSchemeComboBox);
    schemeLayout->addStretch();

    layout->addLayout(schemeLayout);

    // 自定义颜色，应用主题系统样式
    QHBoxLayout *customColorLayout = new QHBoxLayout();
    customColorLayout->setSpacing(spacing);
    
    m_customColorButton = new QPushButton(tr("自定义颜色"), this);
    // 应用主题系统样式
    m_themeManager->applyThemeToWidget(m_customColorButton, LA::Themes::ThemeManager::ComponentType::Button);
    
    customColorLayout->addWidget(m_customColorButton);

    // 颜色预览
    m_colorPreviewFrame = new QFrame(this);
    m_colorPreviewFrame->setFixedSize(40, 20);
    m_colorPreviewFrame->setFrameStyle(QFrame::Box);
    updateColorPreview(m_customColor);
    customColorLayout->addWidget(m_colorPreviewFrame);

    customColorLayout->addStretch();
    layout->addLayout(customColorLayout);

    // 颜色预览标签，使用主题系统样式
    m_colorPreviewLabel = new QLabel(tr("当前颜色预览"), this);
    
    // 使用主题系统颜色和字体
    QColor textMuted = m_themeManager->getSemanticColor(LA::Themes::ThemeManager::ColorRole::TextMuted);
    QFont captionFont = m_themeManager->getFont(LA::Themes::ThemeManager::FontRole::Caption);
    
    m_colorPreviewLabel->setStyleSheet(QString("color: %1;").arg(textMuted.name()));
    m_colorPreviewLabel->setFont(captionFont);
    
    layout->addWidget(m_colorPreviewLabel);
}

void ThemeSettingsPanel::setupFontSettings() {
    m_fontGroup         = new QGroupBox(tr("字体设置"), this);
    // 调试：为QGroupBox添加黄色背景
    // 屏蔽子容器色块 - m_.*Group->setStyleSheet("QGroupBox { background-color: yellow; border: 2px solid orange; }");
    // 设置正确的尺寸策略，让其扩展填充可用空间
    m_fontGroup->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
    // 移除固定尺寸限制，让布局系统自动管理
    // m_fontGroup->setMinimumSize(500, 100);  // 移除固定最小尺寸
    // m_fontGroup->resize(600, 150);          // 移除固定尺寸
    
    QGridLayout *layout = new QGridLayout(m_fontGroup);

    // 字体族，应用主题系统样式
    int spacing = m_themeManager->getMetric(LA::Themes::ThemeManager::Metric::SpacingMedium);
    layout->setSpacing(spacing);
    
    layout->addWidget(new QLabel(tr("字体:"), this), 0, 0);
    m_fontFamilyComboBox = new QFontComboBox(this);
    m_themeManager->applyThemeToWidget(m_fontFamilyComboBox, LA::Themes::ThemeManager::ComponentType::ComboBox);
    layout->addWidget(m_fontFamilyComboBox, 0, 1);

    // 字体大小
    layout->addWidget(new QLabel(tr("大小:"), this), 1, 0);
    m_fontSizeSpinBox = new QSpinBox(this);
    m_fontSizeSpinBox->setRange(8, 72);
    m_fontSizeSpinBox->setSuffix(tr(" pt"));
    // 应用主题系统样式到数字输入框
    m_themeManager->applyThemeToWidget(m_fontSizeSpinBox, LA::Themes::ThemeManager::ComponentType::InputField);
    layout->addWidget(m_fontSizeSpinBox, 1, 1);

    // 字体预览，使用主题系统样式替换硬编码
    m_fontPreviewLabel = new QLabel(tr("字体预览 Font Preview 123"), this);
    
    // 使用主题系统的语义化颜色和度量
    QColor borderColor = m_themeManager->getSemanticColor(LA::Themes::ThemeManager::ColorRole::BorderPrimary);
    QColor bgColor = m_themeManager->getSemanticColor(LA::Themes::ThemeManager::ColorRole::Surface);
    QColor textColor = m_themeManager->getSemanticColor(LA::Themes::ThemeManager::ColorRole::TextPrimary);
    int borderRadius = m_themeManager->getMetric(LA::Themes::ThemeManager::Metric::BorderRadiusMedium);
    int padding = m_themeManager->getMetric(LA::Themes::ThemeManager::Metric::PaddingMedium);
    
    m_fontPreviewLabel->setStyleSheet(QString(
        "border: 1px solid %1; "
        "border-radius: %2px; "
        "padding: %3px; "
        "background-color: %4; "
        "color: %5;"
    ).arg(borderColor.name()).arg(borderRadius).arg(padding).arg(bgColor.name()).arg(textColor.name()));
    
    m_fontPreviewLabel->setMinimumHeight(50);
    layout->addWidget(m_fontPreviewLabel, 2, 0, 1, 2);
}

void ThemeSettingsPanel::setupPreviewArea() {
    m_previewGroup      = new QGroupBox(tr("主题预览"), this);
    // 调试：为QGroupBox添加黄色背景
    // 屏蔽子容器色块 - m_.*Group->setStyleSheet("QGroupBox { background-color: yellow; border: 2px solid orange; }");
    // 设置正确的尺寸策略，让其扩展填充可用空间
    m_previewGroup->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
    // 移除固定尺寸限制，让布局系统自动管理
    // m_previewGroup->setMinimumSize(500, 100);  // 移除固定最小尺寸
    // m_previewGroup->resize(600, 150);          // 移除固定尺寸
    
    QVBoxLayout *layout = new QVBoxLayout(m_previewGroup);

    // 预览widget，使用主题系统样式
    m_previewWidget = new QWidget(this);
    m_previewWidget->setMinimumHeight(100);
    
    // 使用主题系统的语义化颜色和度量
    QColor borderColor = m_themeManager->getSemanticColor(LA::Themes::ThemeManager::ColorRole::BorderPrimary);
    QColor bgColor = m_themeManager->getSemanticColor(LA::Themes::ThemeManager::ColorRole::Surface);
    int borderRadius = m_themeManager->getMetric(LA::Themes::ThemeManager::Metric::BorderRadiusMedium);
    
    m_previewWidget->setStyleSheet(QString(
        "border: 1px solid %1; "
        "border-radius: %2px; "
        "background-color: %3;"
    ).arg(borderColor.name()).arg(borderRadius).arg(bgColor.name()));

    QVBoxLayout *previewLayout = new QVBoxLayout(m_previewWidget);

    m_previewTextLabel = new QLabel(tr("这是主题预览文本\nThis is theme preview text"), this);
    m_previewTextLabel->setAlignment(Qt::AlignCenter);
    previewLayout->addWidget(m_previewTextLabel);

    // 移除无功能的装饰按钮，符合Linux式简洁设计原则
    // m_previewButton = new QPushButton(tr("预览按钮"), this);
    // m_themeManager->applyThemeToWidget(m_previewButton, LA::Themes::ThemeManager::ComponentType::Button);
    // previewLayout->addWidget(m_previewButton);

    layout->addWidget(m_previewWidget);
}

void ThemeSettingsPanel::setupActionButtons() {
    m_buttonLayout = new QHBoxLayout();

    // 应用主题系统样式到所有按钮
    int spacing = m_themeManager->getMetric(LA::Themes::ThemeManager::Metric::SpacingMedium);
    m_buttonLayout->setSpacing(spacing);
    
    m_previewThemeButton = new QPushButton(tr("预览主题"), this);
    m_themeManager->applyThemeToWidget(m_previewThemeButton, LA::Themes::ThemeManager::ComponentType::Button);
    m_buttonLayout->addWidget(m_previewThemeButton);

    m_resetThemeButton = new QPushButton(tr("重置主题"), this);
    m_themeManager->applyThemeToWidget(m_resetThemeButton, LA::Themes::ThemeManager::ComponentType::Button);
    m_buttonLayout->addWidget(m_resetThemeButton);

    m_buttonLayout->addStretch();

    m_applyButton = new QPushButton(tr("应用"), this);
    m_applyButton->setDefault(true);
    // 使用主要按钮样式
    m_themeManager->applyThemeToWidget(m_applyButton, LA::Themes::ThemeManager::ComponentType::Button);
    m_buttonLayout->addWidget(m_applyButton);
}

void ThemeSettingsPanel::connectSignals() {
    connect(m_themeComboBox, QOverload<const QString &>::of(&QComboBox::currentTextChanged), this, &ThemeSettingsPanel::onThemeChanged);

    connect(m_colorSchemeComboBox, QOverload<const QString &>::of(&QComboBox::currentTextChanged), this, &ThemeSettingsPanel::onColorSchemeChanged);

    connect(m_customColorButton, &QPushButton::clicked, this, &ThemeSettingsPanel::onCustomColorClicked);

    connect(m_fontFamilyComboBox, &QFontComboBox::currentFontChanged, this, &ThemeSettingsPanel::onFontFamilyChanged);

    connect(m_fontSizeSpinBox, QOverload<int>::of(&QSpinBox::valueChanged), this, &ThemeSettingsPanel::onFontSizeChanged);

    // 操作按钮的信号连接已移至SettingsDialog统一管理
    // connect(m_previewThemeButton, &QPushButton::clicked, this, &ThemeSettingsPanel::onPreviewTheme);
    // connect(m_resetThemeButton, &QPushButton::clicked, this, &ThemeSettingsPanel::onResetTheme);
    // connect(m_applyButton, &QPushButton::clicked, this, &ThemeSettingsPanel::applySettings);
}

void ThemeSettingsPanel::loadAvailableThemes() {
    m_themeComboBox->clear();

    if (m_themeManager) {
        // 使用主题管理器的实际主题列表
        QStringList themes = m_themeManager->getAvailableThemes();
        for (const QString &theme : themes) {
            if (theme == "industrial") {
                m_themeComboBox->addItem(tr("工业蓝主题 - 专业稳重的工业界面风格"), theme);
            } else if (theme == "modern") {
                m_themeComboBox->addItem(tr("现代主题 - 简洁清爽的现代设计"), theme);
            } else if (theme == "dark") {
                m_themeComboBox->addItem(tr("深色主题 - 护眼的暗色界面"), theme);
            } else if (theme == "light") {
                m_themeComboBox->addItem(tr("浅色主题 - 明亮经典的浅色界面"), theme);
            } else if (theme == "baseline") {
                m_themeComboBox->addItem(tr("基准主题 - 系统默认标准主题"), theme);
            } else {
                m_themeComboBox->addItem(QString("%1 - %2").arg(theme, tr("自定义主题")), theme);
            }
        }
    } else {
        // 备用方案 - 提供更完整的主题列表
        m_themeComboBox->addItem(tr("工业蓝主题 - 专业稳重的工业界面风格"), QStringLiteral("industrial"));
        m_themeComboBox->addItem(tr("现代主题 - 简洁清爽的现代设计"), QStringLiteral("modern"));
        m_themeComboBox->addItem(tr("深色主题 - 护眼的暗色界面"), QStringLiteral("dark"));
        m_themeComboBox->addItem(tr("浅色主题 - 明亮经典的浅色界面"), QStringLiteral("light"));
        m_themeComboBox->addItem(tr("基准主题 - 系统默认标准主题"), QStringLiteral("baseline"));
    }
}

void ThemeSettingsPanel::loadAvailableColorSchemes() {
    m_colorSchemeComboBox->clear();
    m_colorSchemeComboBox->addItem(tr("浅色"), QStringLiteral("light"));
    m_colorSchemeComboBox->addItem(tr("深色"), QStringLiteral("dark"));
    m_colorSchemeComboBox->addItem(tr("自动"), QStringLiteral("auto"));
    m_colorSchemeComboBox->addItem(tr("自定义"), QStringLiteral("custom"));
}

void ThemeSettingsPanel::loadSpecificSettings() {
    auto settings = getSettings();
    if (!settings)
        return;

    // 从主题管理器获取当前主题信息
    if (m_themeManager) {
        // 获取当前主题名称
        QString currentThemeName;
        auto    currentTheme = m_themeManager->getCurrentTheme();
        switch (currentTheme) {
        case LA::Themes::ThemeManager::ThemeType::Industrial:
            currentThemeName = "industrial";
            break;
        case LA::Themes::ThemeManager::ThemeType::Modern:
            currentThemeName = "modern";
            break;
        case LA::Themes::ThemeManager::ThemeType::Dark:
            currentThemeName = "dark";
            break;
        case LA::Themes::ThemeManager::ThemeType::Light:
            currentThemeName = "light";
            break;
        default:
            currentThemeName = "industrial";
            break;
        }

        m_currentTheme       = settings->value("theme", currentThemeName).toString();
        m_currentColorScheme = m_themeManager->isDarkMode() ? "dark" : "light";
    } else {
        // 备用方案：从设置文件加载
        m_currentTheme       = settings->value("theme", "industrial").toString();
        m_currentColorScheme = settings->value("colorScheme", "light").toString();
    }

    // 更新UI控件
    int themeIndex = m_themeComboBox->findData(m_currentTheme);
    if (themeIndex >= 0) {
        m_themeComboBox->setCurrentIndex(themeIndex);
    }

    int colorIndex = m_colorSchemeComboBox->findData(m_currentColorScheme);
    if (colorIndex >= 0) {
        m_colorSchemeComboBox->setCurrentIndex(colorIndex);
    }

    // 加载自定义颜色
    m_customColor    = settings->value("customColor", QColor(64, 128, 255)).value<QColor>();
    m_useCustomColor = settings->value("useCustomColor", false).toBool();
    updateColorPreview(m_customColor);

    // 加载字体设置
    QString fontFamily = settings->value("fontFamily", "Microsoft YaHei").toString();
    int     fontSize   = settings->value("fontSize", 10).toInt();
    m_currentFont      = QFont(fontFamily, fontSize);

    m_fontFamilyComboBox->setCurrentFont(m_currentFont);
    m_fontSizeSpinBox->setValue(fontSize);

    updatePreview();
}

void ThemeSettingsPanel::saveSpecificSettings() {
    auto settings = getSettings();
    if (!settings)
        return;

    settings->setValue("theme", m_currentTheme);
    settings->setValue("colorScheme", m_currentColorScheme);
    settings->setValue("customColor", m_customColor);
    settings->setValue("useCustomColor", m_useCustomColor);
    settings->setValue("fontFamily", m_currentFont.family());
    settings->setValue("fontSize", m_currentFont.pointSize());
}

void ThemeSettingsPanel::resetSpecificSettings() {
    m_currentTheme       = "default";
    m_currentColorScheme = "light";
    m_customColor        = QColor(64, 128, 255);
    m_useCustomColor     = false;
    m_currentFont        = QFont("Microsoft YaHei", 10);

    // 更新UI
    int themeIndex = m_themeComboBox->findData(m_currentTheme);
    if (themeIndex >= 0) {
        m_themeComboBox->setCurrentIndex(themeIndex);
    }

    int colorIndex = m_colorSchemeComboBox->findData(m_currentColorScheme);
    if (colorIndex >= 0) {
        m_colorSchemeComboBox->setCurrentIndex(colorIndex);
    }

    updateColorPreview(m_customColor);
    m_fontFamilyComboBox->setCurrentFont(m_currentFont);
    m_fontSizeSpinBox->setValue(m_currentFont.pointSize());

    updatePreview();
}

bool ThemeSettingsPanel::validateSpecificSettings() {
    // 主题设置通常不需要特殊验证
    return true;
}

void ThemeSettingsPanel::applySpecificSettings() {
    if (m_themeManager) {
        qDebug() << "Applying theme settings:" << m_currentTheme << m_currentColorScheme;

        // 应用主题
        m_themeManager->loadTheme(m_currentTheme);

        // 应用深色模式设置
        if (m_currentColorScheme == "dark") {
            m_themeManager->setDarkMode(true);
        } else if (m_currentColorScheme == "light") {
            m_themeManager->setDarkMode(false);
        }

        // 应用字体设置（如果主题管理器支持）
        // 注意：当前主题管理器可能不支持字体设置，这里先记录日志
        qDebug() << "Font settings:" << m_currentFont.family() << m_currentFont.pointSize();

        // 强制应用主题到应用程序
        m_themeManager->applyThemeToApplication();

        qDebug() << "Theme settings applied successfully";
    } else {
        qWarning() << "ThemeManager is null, cannot apply settings";
    }
}

void ThemeSettingsPanel::onThemeChanged(const QString &themeName) {
    QString themeData = m_themeComboBox->currentData().toString();
    if (m_currentTheme != themeData) {
        m_currentTheme = themeData;
        updatePreview();
        markAsModified();
    }
}

void ThemeSettingsPanel::onColorSchemeChanged(const QString &scheme) {
    QString schemeData = m_colorSchemeComboBox->currentData().toString();
    if (m_currentColorScheme != schemeData) {
        m_currentColorScheme = schemeData;
        m_useCustomColor     = (schemeData == QStringLiteral("custom"));
        updatePreview();
        markAsModified();
    }
}

void ThemeSettingsPanel::onFontFamilyChanged(const QFont &font) {
    if (m_currentFont.family() != font.family()) {
        m_currentFont.setFamily(font.family());
        updatePreview();
        markAsModified();
    }
}

void ThemeSettingsPanel::onFontSizeChanged(int size) {
    if (m_currentFont.pointSize() != size) {
        m_currentFont.setPointSize(size);
        updatePreview();
        markAsModified();
    }
}

void ThemeSettingsPanel::onCustomColorClicked() {
    QColor color = QColorDialog::getColor(m_customColor, this, tr("选择自定义颜色"));
    if (color.isValid() && color != m_customColor) {
        m_customColor = color;
        updateColorPreview(color);
        if (m_currentColorScheme == QStringLiteral("custom")) {
            updatePreview();
        }
        markAsModified();
    }
}

void ThemeSettingsPanel::onPreviewTheme() {
    updatePreview();
    QMessageBox::information(this, tr("主题预览"), tr("当前主题预览已更新，点击应用按钮保存设置"));
}

void ThemeSettingsPanel::onResetTheme() {
    int ret = QMessageBox::question(this, tr("重置主题"), tr("确定要重置主题设置为默认值吗？"), QMessageBox::Yes | QMessageBox::No);
    if (ret == QMessageBox::Yes) {
        resetSpecificSettings();
    }
}

void ThemeSettingsPanel::updatePreview() {
    // 更新字体预览
    m_fontPreviewLabel->setFont(m_currentFont);

    // 更新主题预览，使用主题系统样式替换硬编码
    QColor borderColor = m_themeManager->getSemanticColor(LA::Themes::ThemeManager::ColorRole::BorderPrimary);
    QColor bgColor, textColor;
    int borderRadius = m_themeManager->getMetric(LA::Themes::ThemeManager::Metric::BorderRadiusMedium);
    int padding = m_themeManager->getMetric(LA::Themes::ThemeManager::Metric::PaddingMedium);
    
    if (m_currentColorScheme == QStringLiteral("dark")) {
        bgColor = m_themeManager->getSemanticColor(LA::Themes::ThemeManager::ColorRole::BackgroundAlternate);
        textColor = m_themeManager->getSemanticColor(LA::Themes::ThemeManager::ColorRole::TextPrimary);
    } else if (m_currentColorScheme == QStringLiteral("custom") && m_useCustomColor) {
        bgColor = m_customColor;
        textColor = QColor(255, 255, 255);  // 白色文本
    } else {
        bgColor = m_themeManager->getSemanticColor(LA::Themes::ThemeManager::ColorRole::Surface);
        textColor = m_themeManager->getSemanticColor(LA::Themes::ThemeManager::ColorRole::TextPrimary);
    }
    
    QString previewStyle = QString(
        "border: 1px solid %1; "
        "border-radius: %2px; "
        "padding: %3px; "
        "background-color: %4; "
        "color: %5;"
    ).arg(borderColor.name()).arg(borderRadius).arg(padding).arg(bgColor.name()).arg(textColor.name());

    m_previewWidget->setStyleSheet(previewStyle);
    m_previewTextLabel->setFont(m_currentFont);
    // m_previewButton已移除，无需设置字体
}


void ThemeSettingsPanel::updateColorPreview(const QColor &color) {
    // 使用主题系统替换硬编码边框颜色
    auto themeManager = &LA::Themes::ThemeManager::instance();
    QColor borderColor = themeManager->getSemanticColor(LA::Themes::ThemeManager::ColorRole::BorderPrimary);
    int borderRadius = themeManager->getMetric(LA::Themes::ThemeManager::Metric::BorderRadiusSmall);
    
    QString style = QStringLiteral("background-color: %1; border: 1px solid %2; border-radius: %3px;")
        .arg(color.name())
        .arg(borderColor.name())
        .arg(borderRadius);
    m_colorPreviewFrame->setStyleSheet(style);
}

}  // namespace Settings
}  // namespace LA

// MOC文件由CMake AutoMoc自动处理，无需手动包含
