/**
 * @file ICommandProvider.cpp
 * @brief 设备指令提供者接口实现 - <PERSON><PERSON> "Do One Thing" 原则
 * 
 * 职责：
 * - 设备指令生成的统一接口
 * - 响应解析的标准化处理
 * - 配置驱动的协议实现
 */

#include "ICommandProvider.h"

namespace LA::Device::Command {

// CommandResult implementation matching the header structure

QMap<QString, CommandProviderFactory::ProviderCreator> CommandProviderFactory::s_creators;

std::unique_ptr<ICommandProvider> CommandProviderFactory::createProvider(const QString& deviceType, 
                                                                        const QVariantMap& configData)
{
    auto it = s_creators.find(deviceType);
    if (it != s_creators.end()) {
        return it.value()(configData);
    }
    return nullptr;
}

void CommandProviderFactory::registerProvider(const QString& deviceType, ProviderCreator creator)
{
    s_creators[deviceType] = creator;
}

QStringList CommandProviderFactory::getSupportedDeviceTypes()
{
    return s_creators.keys();
}

} // namespace LA::Device::Command