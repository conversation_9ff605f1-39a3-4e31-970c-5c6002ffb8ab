#include "ConfigTester.h"
#include "ConfigDeployer.h"
#include "ConfigService.h"
#include "DynamicConfigManager.h"
#include <QApplication>
#include <QDebug>
#include <QDir>
#include <QElapsedTimer>
#include <QFile>
#include <QFileInfo>
#include <QJsonDocument>
#include <QJsonObject>

namespace Config {

ConfigTester::ConfigTester() {
    m_verbose = true;  // 默认开启详细输出
}

ConfigTester::TestResult ConfigTester::runAllTests() {
    TestResult    result;
    QElapsedTimer timer;
    timer.start();

    qDebug() << "[ConfigTester] Starting comprehensive configuration tests...";

    // 运行各项测试
    TestResult loadResult          = testConfigLoading();
    TestResult validationResult    = testConfigValidation();
    TestResult compatibilityResult = testBackwardCompatibility();
    TestResult performanceResult   = testConfigPerformance();
    TestResult integrationResult   = testConfigIntegration();

    // 合并测试结果
    result.passedTests.append(loadResult.passedTests);
    result.passedTests.append(validationResult.passedTests);
    result.passedTests.append(compatibilityResult.passedTests);
    result.passedTests.append(performanceResult.passedTests);
    result.passedTests.append(integrationResult.passedTests);

    result.failedTests.append(loadResult.failedTests);
    result.failedTests.append(validationResult.failedTests);
    result.failedTests.append(compatibilityResult.failedTests);
    result.failedTests.append(performanceResult.failedTests);
    result.failedTests.append(integrationResult.failedTests);

    result.warnings.append(loadResult.warnings);
    result.warnings.append(validationResult.warnings);
    result.warnings.append(compatibilityResult.warnings);
    result.warnings.append(performanceResult.warnings);
    result.warnings.append(integrationResult.warnings);

    result.totalTime = timer.elapsed();
    result.success   = result.failedTests.isEmpty();

    result.message = QString("Tests completed in %1ms - Passed: %2, Failed: %3, Warnings: %4")
                         .arg(result.totalTime)
                         .arg(result.passedTests.size())
                         .arg(result.failedTests.size())
                         .arg(result.warnings.size());

    qDebug() << "[ConfigTester]" << result.message;

    return result;
}

ConfigTester::TestResult ConfigTester::testConfigLoading() {
    TestResult    result;
    QElapsedTimer timer;
    timer.start();

    qDebug() << "[ConfigTester] Testing configuration loading...";

    // 测试配置服务初始化
    ConfigService &       configService  = ConfigService::getInstance();
    DynamicConfigManager &dynamicManager = configService.getDynamicManager();
    result.passedTests.append("ConfigService initialization");

    // 测试配置文件部署
    ConfigDeployer               deployer;
    ConfigDeployer::DeployResult deployResult = deployer.deployAllConfigs(ConfigDeployer::DeployStrategy::SkipExisting);
    if (deployResult.success) {
        result.passedTests.append("Configuration deployment");
        qDebug() << "[ConfigTester] Deployed configs:" << deployResult.deployedFiles.size();
    } else {
        result.failedTests.append("Configuration deployment failed: " + deployResult.message);
    }

    // 测试各个配置文件加载
    QStringList configFiles = getTestConfigFiles();
    for (const QString &configFile : configFiles) {
        QStringList errors;
        if (testSingleConfigLoad(configFile, errors)) {
            result.passedTests.append("Load " + QFileInfo(configFile).baseName());
        } else {
            result.failedTests.append("Load " + QFileInfo(configFile).baseName() + ": " + errors.join(", "));
        }
    }

    // 测试完整配置加载
    ConfigResult loadResult = dynamicManager.loadAllConfigs();
    if (loadResult.success) {
        result.passedTests.append("Complete configuration loading");
    } else {
        result.failedTests.append("Complete configuration loading failed: " + loadResult.message);
    }

    result.totalTime = timer.elapsed();
    result.success   = result.failedTests.isEmpty();

    return result;
}

ConfigTester::TestResult ConfigTester::testConfigValidation() {
    TestResult    result;
    QElapsedTimer timer;
    timer.start();

    qDebug() << "[ConfigTester] Testing configuration validation...";

    ConfigService &       configService  = ConfigService::getInstance();
    DynamicConfigManager &dynamicManager = configService.getDynamicManager();

    // 测试配置验证
    ConfigResult validateResult = dynamicManager.validateAllConfigs();
    if (validateResult.success) {
        result.passedTests.append("Configuration validation successful");
    } else {
        result.failedTests.append("Configuration validation failed: " + validateResult.message);
    }

    // 测试已注册配置数量
    QStringList configs = dynamicManager.getAllConfigs();
    if (configs.size() > 0) {
        result.passedTests.append(QString("Found %1 registered configurations").arg(configs.size()));
    } else {
        result.failedTests.append("No configurations registered");
    }

    // 测试配置保存功能
    ConfigResult saveResult = dynamicManager.saveAllConfigs();
    if (saveResult.success) {
        result.passedTests.append("Configuration save test successful");
    } else {
        result.warnings.append("Configuration save test failed: " + saveResult.message);
    }

    result.totalTime = timer.elapsed();
    result.success   = result.failedTests.isEmpty();

    return result;
}

ConfigTester::TestResult ConfigTester::testBackwardCompatibility() {
    TestResult    result;
    QElapsedTimer timer;
    timer.start();

    qDebug() << "[ConfigTester] Testing backward compatibility...";

    ConfigService &       configService  = ConfigService::getInstance();
    DynamicConfigManager &dynamicManager = configService.getDynamicManager();

    // 测试配置系统兼容性
    QStringList configs = dynamicManager.getAllConfigs();
    if (!configs.isEmpty()) {
        result.passedTests.append("Configuration system compatibility");
    } else {
        result.warnings.append("No configurations available for compatibility test");
    }

    // 测试配置初始化功能
    ConfigResult initResult = dynamicManager.initializeAllConfigs();
    if (initResult.success) {
        result.passedTests.append("Configuration initialization");
    } else {
        result.warnings.append("Configuration initialization issues: " + initResult.message);
    }

    result.totalTime = timer.elapsed();
    result.success   = result.failedTests.isEmpty();

    return result;
}

ConfigTester::TestResult ConfigTester::testConfigPerformance() {
    TestResult    result;
    QElapsedTimer timer;
    timer.start();

    qDebug() << "[ConfigTester] Testing configuration performance...";

    ConfigService &       configService  = ConfigService::getInstance();
    DynamicConfigManager &dynamicManager = configService.getDynamicManager();

    // 测试配置加载性能
    QElapsedTimer loadTimer;
    loadTimer.start();

    for (int i = 0; i < 10; ++i) {
        ConfigResult loadResult = dynamicManager.loadAllConfigs();
        if (!loadResult.success) {
            result.failedTests.append("Performance test iteration " + QString::number(i));
        }
    }

    double avgLoadTime = loadTimer.elapsed() / 10.0;

    if (avgLoadTime < 100.0) {  // 平均加载时间小于100ms
        result.passedTests.append("Configuration loading performance (avg: " + QString::number(avgLoadTime, 'f', 2) + "ms)");
    } else {
        result.warnings.append("Configuration loading is slow (avg: " + QString::number(avgLoadTime, 'f', 2) + "ms)");
    }

    result.totalTime = timer.elapsed();
    result.success   = result.failedTests.isEmpty();

    return result;
}

ConfigTester::TestResult ConfigTester::testConfigIntegration() {
    TestResult    result;
    QElapsedTimer timer;
    timer.start();

    qDebug() << "[ConfigTester] Testing configuration integration...";

    ConfigService &       configService  = ConfigService::getInstance();
    DynamicConfigManager &dynamicManager = configService.getDynamicManager();

    // 测试配置系统集成
    QStringList configs = dynamicManager.getAllConfigs();
    if (!configs.isEmpty()) {
        result.passedTests.append("Configuration system integration");
    } else {
        result.failedTests.append("No configurations available for integration test");
    }

    // 测试配置加载和验证集成
    ConfigResult loadResult     = dynamicManager.loadAllConfigs();
    ConfigResult validateResult = dynamicManager.validateAllConfigs();

    if (loadResult.success && validateResult.success) {
        result.passedTests.append("Load and validation integration");
    } else {
        result.failedTests.append("Integration test failed - Load: " + QString::number(loadResult.success) +
                                  ", Validate: " + QString::number(validateResult.success));
    }

    result.totalTime = timer.elapsed();
    result.success   = result.failedTests.isEmpty();

    return result;
}

bool ConfigTester::testSingleConfigLoad(const QString &configPath, QStringList &errors) {
    if (!QFile::exists(configPath)) {
        errors.append("File not found: " + configPath);
        return false;
    }

    QFile file(configPath);
    if (!file.open(QIODevice::ReadOnly)) {
        errors.append("Cannot open file: " + configPath);
        return false;
    }

    if (configPath.endsWith(".json")) {
        QJsonParseError parseError;
        QJsonDocument   doc = QJsonDocument::fromJson(file.readAll(), &parseError);
        if (parseError.error != QJsonParseError::NoError) {
            errors.append("JSON parse error: " + parseError.errorString());
            return false;
        }
    }

    return true;
}

bool ConfigTester::testConfigMigration(QStringList &errors) {
    ConfigService &       configService  = ConfigService::getInstance();
    DynamicConfigManager &dynamicManager = configService.getDynamicManager();

    // 测试配置初始化（相当于迁移功能）
    ConfigResult result = dynamicManager.initializeAllConfigs();

    if (!result.success) {
        errors.append(result.message);
        return false;
    }

    return true;
}

QStringList ConfigTester::getTestConfigFiles() const {
    QStringList files;
    QString     configDir = QApplication::applicationDirPath() + "/config";

    files << configDir + "/system/system_config.json";
    files << configDir + "/modules/lenAdjust/facula_config.json";
    files << configDir + "/modules/lenAdjust/algorithm_config.json";
    files << configDir + "/modules/lenAdjust/hardware_config.json";

    return files;
}

}  // namespace Config
