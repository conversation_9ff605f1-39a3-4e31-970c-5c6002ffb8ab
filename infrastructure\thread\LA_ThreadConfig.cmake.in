@PACKAGE_INIT@

set(LA_Thread_VERSION @PROJECT_VERSION@)

# 设置组件路径
set_and_check(LA_Thread_INCLUDE_DIR "@PACKAGE_INCLUDE_DIR@")
set_and_check(LA_Thread_LIB_DIR "@PACKAGE_LIB_DIR@")

# 检查必需的组件
set(_LA_Thread_supported_components )

foreach(_comp ${LA_Thread_FIND_COMPONENTS})
    if (NOT _comp IN_LIST _LA_Thread_supported_components)
        set(LA_Thread_FOUND False)
        set(LA_Thread_NOT_FOUND_MESSAGE "Unsupported component: ${_comp}")
    endif()
endforeach()

# 包含目标文件
include("${CMAKE_CURRENT_LIST_DIR}/LA_ThreadTargets.cmake")

# 设置库目标
set(LA_Thread_LIBRARIES LA::LA_infrastructure_thread_lib)
set(LA_Thread_INCLUDE_DIRS ${LA_Thread_INCLUDE_DIR})

check_required_components(LA_Thread)