#pragma once

#include "LA/Settings/SettingsPanel.h"
#include <QCheckBox>
#include <QComboBox>
#include <QGroupBox>
#include <QHBoxLayout>
#include <QLabel>
#include <QLineEdit>
#include <QPushButton>
#include <QSpinBox>
#include <QTabWidget>
#include <QTableWidget>
#include <QTextEdit>

namespace LA {
namespace Settings {

/**
 * @brief 系统设置面板
 * 
 * 提供工业软件必要的系统配置功能，包括：
 * - 用户账号和权限管理
 * - 硬件设备配置
 * - 安全和认证设置
 * - 系统性能设置
 */
class SystemSettingsPanel : public SettingsPanel {
    Q_OBJECT

public:
    explicit SystemSettingsPanel(QWidget *parent = nullptr);

protected:
    // 基类虚函数实现
    void createUI() override;
    void connectSignals() override;
    void loadSpecificSettings() override;
    void saveSpecificSettings() override;
    void resetSpecificSettings() override;
    bool validateSpecificSettings() override;
    void applySpecificSettings() override;

private slots:
    // 用户账号相关槽函数
    void onUserAccountChanged();
    void onPermissionLevelChanged();
    void onPasswordPolicyChanged();
    void onLoginAttemptsChanged();
    void onSessionTimeoutChanged();
    void onChangePasswordClicked();
    void onManageUsersClicked();

    // 硬件设备相关槽函数
    void onDeviceConfigChanged();
    void onCommunicationPortChanged();
    void onBaudRateChanged();
    void onTimeoutChanged();
    void onAutoDetectDevicesClicked();
    void onTestConnectionClicked();

    // 安全设置相关槽函数
    void onSecurityLevelChanged();
    void onEncryptionChanged();
    void onAuditLogChanged();
    void onBackupSettingsChanged();

    // 系统性能相关槽函数
    void onPerformanceModeChanged();
    void onMemoryLimitChanged();
    void onThreadCountChanged();
    void onCacheSettingsChanged();

    // 操作按钮
    void onResetToDefaults();

private:
    void setupUserAccountSettings();
    void setupHardwareSettings();
    void setupSecuritySettings();
    void setupPerformanceSettings();
    void setupActionButtons();

    void updatePermissionUI();
    void validateUserCredentials();
    void loadHardwareDevices();
    void testDeviceConnection();
    void exportSystemConfig();
    void importSystemConfig();

    // UI组件
    QTabWidget *m_tabWidget;

    // 用户账号设置页面
    QWidget *m_accountPage;
    QGroupBox *m_userAccountGroup;
    QLineEdit *m_usernameLineEdit;
    QLineEdit *m_currentPasswordLineEdit;
    QComboBox *m_permissionLevelComboBox;
    QLabel *m_permissionDescriptionLabel;
    
    QGroupBox *m_passwordPolicyGroup;
    QSpinBox *m_minPasswordLengthSpinBox;
    QCheckBox *m_requireSpecialCharsCheckBox;
    QCheckBox *m_requireNumbersCheckBox;
    QCheckBox *m_requireUppercaseCheckBox;
    QSpinBox *m_maxLoginAttemptsSpinBox;
    QSpinBox *m_sessionTimeoutSpinBox;
    
    QPushButton *m_changePasswordButton;
    QPushButton *m_manageUsersButton;

    // 硬件设备设置页面
    QWidget *m_hardwarePage;
    QGroupBox *m_deviceConfigGroup;
    QComboBox *m_deviceTypeComboBox;
    QComboBox *m_communicationPortComboBox;
    QComboBox *m_baudRateComboBox;
    QSpinBox *m_timeoutSpinBox;
    QCheckBox *m_autoReconnectCheckBox;
    
    QGroupBox *m_deviceListGroup;
    QTableWidget *m_deviceTable;
    QPushButton *m_autoDetectButton;
    QPushButton *m_testConnectionButton;
    QPushButton *m_addDeviceButton;
    QPushButton *m_removeDeviceButton;

    // 安全设置页面
    QWidget *m_securityPage;
    QGroupBox *m_securityLevelGroup;
    QComboBox *m_securityLevelComboBox;
    QCheckBox *m_enableEncryptionCheckBox;
    QComboBox *m_encryptionAlgorithmComboBox;
    
    QGroupBox *m_auditGroup;
    QCheckBox *m_enableAuditLogCheckBox;
    QSpinBox *m_auditLogRetentionSpinBox;
    QTextEdit *m_auditLogPreview;
    
    QGroupBox *m_backupGroup;
    QCheckBox *m_enableAutoBackupCheckBox;
    QSpinBox *m_backupIntervalSpinBox;
    QLineEdit *m_backupPathLineEdit;
    QPushButton *m_browseBackupPathButton;

    // 系统性能设置页面
    QWidget *m_performancePage;
    QGroupBox *m_performanceModeGroup;
    QComboBox *m_performanceModeComboBox;
    QLabel *m_performanceModeDescription;
    
    QGroupBox *m_memoryGroup;
    QSpinBox *m_memoryLimitSpinBox;
    QLabel *m_memoryUsageLabel;
    QCheckBox *m_enableMemoryOptimizationCheckBox;
    
    QGroupBox *m_processingGroup;
    QSpinBox *m_threadCountSpinBox;
    QCheckBox *m_enableMultithreadingCheckBox;
    QComboBox *m_priorityLevelComboBox;
    
    QGroupBox *m_cacheGroup;
    QSpinBox *m_cacheSizeSpinBox;
    QCheckBox *m_enableCacheCheckBox;
    QPushButton *m_clearCacheButton;

    // 操作按钮
    QHBoxLayout *m_buttonLayout;
    QPushButton *m_exportConfigButton;
    QPushButton *m_importConfigButton;
    QPushButton *m_resetToDefaultsButton;
    QPushButton *m_applyButton;

    // 数据成员
    QString m_currentUsername;
    QString m_currentPermissionLevel;
    int m_minPasswordLength;
    bool m_requireSpecialChars;
    bool m_requireNumbers;
    bool m_requireUppercase;
    int m_maxLoginAttempts;
    int m_sessionTimeout;

    QString m_deviceType;
    QString m_communicationPort;
    int m_baudRate;
    int m_connectionTimeout;
    bool m_autoReconnect;

    QString m_securityLevel;
    bool m_enableEncryption;
    QString m_encryptionAlgorithm;
    bool m_enableAuditLog;
    int m_auditLogRetention;
    bool m_enableAutoBackup;
    int m_backupInterval;
    QString m_backupPath;

    QString m_performanceMode;
    int m_memoryLimit;
    bool m_enableMemoryOptimization;
    int m_threadCount;
    bool m_enableMultithreading;
    QString m_priorityLevel;
    int m_cacheSize;
    bool m_enableCache;
};

} // namespace Settings
} // namespace LA