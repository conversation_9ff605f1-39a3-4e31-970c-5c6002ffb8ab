#include "CommunicationThread.h"
#include <QDebug>
#include <QDateTime>

namespace LA {
namespace Thread {

CommunicationThread::CommunicationThread(const QString& deviceId, 
                                       const ThreadConfig& config,
                                       QObject* parent)
    : ICommunicationThread(parent)
    , m_deviceId(deviceId)
    , m_deviceName(deviceId)
    , m_config(config)
    , m_state(CommunicationState::Disconnected)
    , m_communicationType(CommunicationType::Interaction)
    , m_isRunning(false)
    , m_isPaused(false)
    , m_autoReconnect(true)
    , m_reconnectInterval(5000)
    , m_dataTimeout(30000)
    , m_bytesSent(0)
    , m_bytesReceived(0)
    , m_messagesSent(0)
    , m_messagesReceived(0)
    , m_connectionStartTime(0)
    , m_errorCount(0)
{
    setObjectName(QString("CommThread_%1").arg(deviceId));
    
    // 创建定时器
    m_reconnectTimer = new QTimer();
    m_reconnectTimer->setSingleShot(true);
    connect(m_reconnectTimer, &QTimer::timeout, this, &CommunicationThread::onReconnectTimer);
    
    m_dataTimeoutTimer = new QTimer();
    m_dataTimeoutTimer->setSingleShot(true);
    connect(m_dataTimeoutTimer, &QTimer::timeout, this, &CommunicationThread::onDataTimeoutTimer);
    
    qDebug() << "CommunicationThread created for device:" << deviceId;
}

CommunicationThread::~CommunicationThread()
{
    stopCommunication();
    quit();
    wait(5000);
    
    delete m_reconnectTimer;
    delete m_dataTimeoutTimer;
    
    qDebug() << "CommunicationThread destroyed for device:" << m_deviceId;
}

QString CommunicationThread::getDeviceId() const
{
    return m_deviceId;
}

QString CommunicationThread::getDeviceName() const
{
    QMutexLocker locker(&m_mutex);
    return m_deviceName;
}

CommunicationThread::CommunicationState CommunicationThread::getCommunicationState() const
{
    QMutexLocker locker(&m_mutex);
    return m_state;
}

CommunicationThread::CommunicationType CommunicationThread::getCommunicationType() const
{
    QMutexLocker locker(&m_mutex);
    return m_communicationType;
}

bool CommunicationThread::connectToDevice()
{
    QMutexLocker locker(&m_mutex);
    
    if (m_state == CommunicationState::Connected || m_state == CommunicationState::Connecting) {
        return true;
    }
    
    setState(CommunicationState::Connecting);
    locker.unlock();
    
    bool result = establishConnection();
    
    locker.relock();
    if (result) {
        setState(CommunicationState::Connected);
        m_connectionStartTime = QDateTime::currentMSecsSinceEpoch();
        emit deviceConnected(m_deviceId);
    } else {
        setState(CommunicationState::Error);
        emit connectionError(m_deviceId, m_lastError);
    }
    
    return result;
}

void CommunicationThread::disconnectFromDevice()
{
    QMutexLocker locker(&m_mutex);
    
    if (m_state == CommunicationState::Disconnected) {
        return;
    }
    
    setState(CommunicationState::Disconnected);
    m_connectionStartTime = 0;
    
    locker.unlock();
    emit deviceDisconnected(m_deviceId);
}

bool CommunicationThread::isConnected() const
{
    QMutexLocker locker(&m_mutex);
    return m_state == CommunicationState::Connected || m_state == CommunicationState::Communicating;
}

bool CommunicationThread::isConnecting() const
{
    QMutexLocker locker(&m_mutex);
    return m_state == CommunicationState::Connecting;
}

void CommunicationThread::startCommunication()
{
    QMutexLocker locker(&m_mutex);
    
    if (m_isRunning) {
        qDebug() << "Communication already running for device:" << m_deviceId;
        return;
    }
    
    m_isRunning = true;
    
    locker.unlock();
    
    if (!isRunning()) {
        start();
    }
    
    emit communicationStarted(m_deviceId);
    qDebug() << "Communication started for device:" << m_deviceId;
}

void CommunicationThread::stopCommunication()
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_isRunning) {
        return;
    }
    
    m_isRunning = false;
    m_dataCondition.wakeAll();
    
    locker.unlock();
    
    emit communicationStopped(m_deviceId);
    qDebug() << "Communication stopped for device:" << m_deviceId;
}

bool CommunicationThread::isCommunicating() const
{
    QMutexLocker locker(&m_mutex);
    return m_isRunning && (m_state == CommunicationState::Communicating);
}

void CommunicationThread::pauseCommunication()
{
    QMutexLocker locker(&m_mutex);
    m_isPaused = true;
    qDebug() << "Communication paused for device:" << m_deviceId;
}

void CommunicationThread::resumeCommunication()
{
    QMutexLocker locker(&m_mutex);
    m_isPaused = false;
    m_dataCondition.wakeAll();
    qDebug() << "Communication resumed for device:" << m_deviceId;
}

bool CommunicationThread::sendData(const QByteArray& data)
{
    return sendDataInternal(data, false);
}

bool CommunicationThread::sendCommand(const QByteArray& command)
{
    return sendDataInternal(command, true);
}

void CommunicationThread::clearSendQueue()
{
    QMutexLocker locker(&m_mutex);
    m_sendQueue.clear();
    m_commandQueue.clear();
    qDebug() << "Send queue cleared for device:" << m_deviceId;
}

int CommunicationThread::getSendQueueSize() const
{
    QMutexLocker locker(&m_mutex);
    return m_sendQueue.size() + m_commandQueue.size();
}

void CommunicationThread::setCommunicationType(CommunicationType type)
{
    QMutexLocker locker(&m_mutex);
    m_communicationType = type;
}

void CommunicationThread::setAutoReconnect(bool enable)
{
    QMutexLocker locker(&m_mutex);
    m_autoReconnect = enable;
}

bool CommunicationThread::isAutoReconnectEnabled() const
{
    QMutexLocker locker(&m_mutex);
    return m_autoReconnect;
}

void CommunicationThread::setReconnectInterval(int intervalMs)
{
    QMutexLocker locker(&m_mutex);
    m_reconnectInterval = intervalMs;
}

void CommunicationThread::setDataTimeout(int timeoutMs)
{
    QMutexLocker locker(&m_mutex);
    m_dataTimeout = timeoutMs;
}

qint64 CommunicationThread::getBytesSent() const
{
    QMutexLocker locker(&m_mutex);
    return m_bytesSent;
}

qint64 CommunicationThread::getBytesReceived() const
{
    QMutexLocker locker(&m_mutex);
    return m_bytesReceived;
}

qint64 CommunicationThread::getMessagesSent() const
{
    QMutexLocker locker(&m_mutex);
    return m_messagesSent;
}

qint64 CommunicationThread::getMessagesReceived() const
{
    QMutexLocker locker(&m_mutex);
    return m_messagesReceived;
}

qint64 CommunicationThread::getConnectionTime() const
{
    QMutexLocker locker(&m_mutex);
    
    if (m_connectionStartTime == 0) {
        return 0;
    }
    
    return QDateTime::currentMSecsSinceEpoch() - m_connectionStartTime;
}

int CommunicationThread::getErrorCount() const
{
    QMutexLocker locker(&m_mutex);
    return m_errorCount;
}

QString CommunicationThread::getLastError() const
{
    QMutexLocker locker(&m_mutex);
    return m_lastError;
}

void CommunicationThread::clearErrors()
{
    QMutexLocker locker(&m_mutex);
    m_errorCount = 0;
    m_lastError.clear();
}

void CommunicationThread::resetStatistics()
{
    QMutexLocker locker(&m_mutex);
    m_bytesSent = 0;
    m_bytesReceived = 0;
    m_messagesSent = 0;
    m_messagesReceived = 0;
    m_connectionStartTime = QDateTime::currentMSecsSinceEpoch();
    m_errorCount = 0;
    m_lastError.clear();
}

void CommunicationThread::handleExternalCommand(const QByteArray& command)
{
    sendCommand(command);
}

void CommunicationThread::handleConfigurationChange(const QString& key, const QVariant& value)
{
    if (key == "reconnectInterval") {
        setReconnectInterval(value.toInt());
    } else if (key == "dataTimeout") {
        setDataTimeout(value.toInt());
    } else if (key == "autoReconnect") {
        setAutoReconnect(value.toBool());
    }
}

void CommunicationThread::handleEmergencyStop()
{
    QMutexLocker locker(&m_mutex);
    
    m_isRunning = false;
    m_sendQueue.clear();
    m_commandQueue.clear();
    m_dataCondition.wakeAll();
    
    setState(CommunicationState::Disconnected);
    
    qWarning() << "Emergency stop triggered for device:" << m_deviceId;
}

void CommunicationThread::run()
{
    qDebug() << "Communication thread started for device:" << m_deviceId;
    
    initializeDevice();
    
    while (m_isRunning) {
        QMutexLocker locker(&m_mutex);
        
        if (m_isPaused) {
            m_dataCondition.wait(&m_mutex, 1000);
            continue;
        }
        
        // 检查连接状态
        if (m_state == CommunicationState::Disconnected && m_autoReconnect) {
            locker.unlock();
            attemptReconnection();
            locker.relock();
        }
        
        // 处理数据队列
        if (isConnected()) {
            locker.unlock();
            processDataQueue();
            locker.relock();
            setState(CommunicationState::Communicating);
        }
        
        // 等待新数据或退出信号
        if (m_sendQueue.isEmpty() && m_commandQueue.isEmpty()) {
            m_dataCondition.wait(&m_mutex, 100);
        }
        
        locker.unlock();
        updateStatistics();
    }
    
    disconnectFromDevice();
    qDebug() << "Communication thread finished for device:" << m_deviceId;
}

void CommunicationThread::initializeDevice()
{
    // 子类实现具体的设备初始化
    qDebug() << "Initializing device:" << m_deviceId;
}

bool CommunicationThread::establishConnection()
{
    // 子类实现具体的连接建立
    qDebug() << "Establishing connection to device:" << m_deviceId;
    return true; // 临时返回true
}

void CommunicationThread::processReceivedData(const QByteArray& data)
{
    // 子类实现具体的数据处理
    QMutexLocker locker(&m_mutex);
    m_bytesReceived += data.size();
    m_messagesReceived++;
    
    locker.unlock();
    emit dataReceived(m_deviceId, data);
}

bool CommunicationThread::parseProtocolFrame(const QByteArray& frame, QMap<QString, QString>& parsedData)
{
    // 子类实现具体的协议解析
    Q_UNUSED(frame)
    Q_UNUSED(parsedData)
    return true; // 临时返回true
}

void CommunicationThread::handleCommunicationError(const QString& error)
{
    QMutexLocker locker(&m_mutex);
    
    m_lastError = error;
    m_errorCount++;
    setState(CommunicationState::Error);
    
    locker.unlock();
    
    emit connectionError(m_deviceId, error);
    qWarning() << "Communication error for device" << m_deviceId << ":" << error;
}

void CommunicationThread::setState(CommunicationState newState)
{
    CommunicationState oldState = m_state;
    m_state = newState;
    
    if (oldState != newState) {
        emit connectionStateChanged(oldState, newState);
    }
}

void CommunicationThread::updateStatistics()
{
    emit statisticsUpdated(m_deviceId, m_bytesSent, m_bytesReceived);
}

void CommunicationThread::attemptReconnection()
{
    if (!m_autoReconnect || m_state == CommunicationState::Connecting) {
        return;
    }
    
    qDebug() << "Attempting reconnection to device:" << m_deviceId;
    connectToDevice();
    
    if (!isConnected()) {
        m_reconnectTimer->start(m_reconnectInterval);
    }
}

void CommunicationThread::processDataQueue()
{
    QMutexLocker locker(&m_mutex);
    
    // 优先处理命令队列
    while (!m_commandQueue.isEmpty()) {
        QByteArray command = m_commandQueue.dequeue();
        locker.unlock();
        
        // 这里应该实际发送命令到设备
        emit commandSent(m_deviceId, command);
        
        locker.relock();
        m_bytesSent += command.size();
        m_messagesSent++;
    }
    
    // 处理普通数据队列
    while (!m_sendQueue.isEmpty()) {
        QByteArray data = m_sendQueue.dequeue();
        locker.unlock();
        
        // 这里应该实际发送数据到设备
        emit dataSent(m_deviceId, data);
        
        locker.relock();
        m_bytesSent += data.size();
        m_messagesSent++;
    }
}

bool CommunicationThread::sendDataInternal(const QByteArray& data, bool isCommand)
{
    if (data.isEmpty()) {
        return false;
    }
    
    QMutexLocker locker(&m_mutex);
    
    if (isCommand) {
        m_commandQueue.enqueue(data);
    } else {
        m_sendQueue.enqueue(data);
    }
    
    m_dataCondition.wakeOne();
    
    return true;
}

void CommunicationThread::onReconnectTimer()
{
    if (m_autoReconnect && !isConnected()) {
        attemptReconnection();
    }
}

void CommunicationThread::onDataTimeoutTimer()
{
    handleCommunicationError("Data timeout");
}

} // namespace Thread
} // namespace LA