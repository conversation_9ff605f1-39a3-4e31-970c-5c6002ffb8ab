#include "ConfigValidator.h"
#include <QDir>
#include <QFile>
#include <QFileInfo>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QStandardPaths>
#include <QElapsedTimer>
#include <QRegularExpression>
#include <QUuid>
#include <QDebug>
#include <algorithm>

namespace LA {
namespace Support {
namespace Config {

ConfigValidator::ConfigValidator(QObject* parent)
    : IConfigValidator(parent)
    , m_timeoutTimer(nullptr)
    , m_threadPool(nullptr)
    , m_enableParallelValidation(false)
    , m_maxThreads(4)
    , m_defaultTimeoutMs(30000.0)
    , m_saveStatistics(true)
    , m_enableCaching(false)
    , m_maxCacheSize(1000)
    , m_totalValidations(0)
    , m_successfulValidations(0)
    , m_failedValidations(0)
    , m_averageExecutionTime(0.0)
    , m_initialized(false)
    , m_validationInProgress(false)
{
}

ConfigValidator::~ConfigValidator()
{
    shutdown();
}

SimpleResult ConfigValidator::initialize(const ConfigParameters& config)
{
    QMutexLocker locker(&m_mutex);
    
    if (m_initialized) {
        return SimpleResult(true);
    }

    // 设置配置选项
    m_storageDirectory = config.value("storage_directory", 
        QStandardPaths::writableLocation(QStandardPaths::AppConfigLocation) + "/validation").toString();
    m_ruleSetDirectory = config.value("ruleset_directory", m_storageDirectory + "/rulesets").toString();
    m_enableParallelValidation = config.value("enable_parallel", false).toBool();
    m_maxThreads = config.value("max_threads", 4).toInt();
    m_defaultTimeoutMs = config.value("default_timeout", 30000.0).toDouble();
    m_saveStatistics = config.value("save_statistics", true).toBool();
    m_enableCaching = config.value("enable_caching", false).toBool();
    m_maxCacheSize = config.value("max_cache_size", 1000).toInt();

    // 初始化存储
    if (!initializeRuleStorage()) {
        qCritical() << "Failed to initialize rule storage";
        return SimpleResult::error("Failed to initialize rule storage");
    }

    // 初始化内置规则
    if (!initializeBuiltinRules()) {
        qCritical() << "Failed to initialize builtin rules";
        return SimpleResult::error("Failed to initialize builtin rules");
    }

    // 初始化自定义验证器
    if (!initializeCustomValidators()) {
        qCritical() << "Failed to initialize custom validators";
        return SimpleResult::error("Failed to initialize custom validators");
    }

    // 初始化线程池
    if (m_enableParallelValidation) {
        m_threadPool = new QThreadPool(this);
        m_threadPool->setMaxThreadCount(m_maxThreads);
    }

    // 初始化超时计时器
    m_timeoutTimer = new QTimer(this);
    connect(m_timeoutTimer, &QTimer::timeout, this, &ConfigValidator::onValidationTimeout);

    m_initialized = true;
    return SimpleResult(true);
}

SimpleResult ConfigValidator::shutdown()
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_initialized) {
        return SimpleResult(true);
    }

    // 停止正在进行的验证
    if (m_validationInProgress) {
        m_validationInProgress = false;
    }

    // 清理超时计时器
    if (m_timeoutTimer) {
        m_timeoutTimer->stop();
        delete m_timeoutTimer;
        m_timeoutTimer = nullptr;
    }

    // 清理线程池
    if (m_threadPool) {
        m_threadPool->waitForDone();
        delete m_threadPool;
        m_threadPool = nullptr;
    }

    // 清理资源
    cleanupRuleStorage();
    cleanupCustomValidators();

    m_initialized = false;
    return SimpleResult(true);
}

bool ConfigValidator::isInitialized() const
{
    QMutexLocker locker(&m_mutex);
    return m_initialized;
}

StatusInfoList ConfigValidator::getStatus() const
{
    QMutexLocker locker(&m_mutex);
    
    StatusInfoList status;
    status.append(StatusInfo("initialized", m_initialized ? "true" : "false"));
    status.append(StatusInfo("rule_count", QString::number(m_rules.size())));
    status.append(StatusInfo("ruleset_count", QString::number(m_ruleSets.size())));
    status.append(StatusInfo("custom_validator_count", QString::number(m_customValidators.size())));
    status.append(StatusInfo("total_validations", QString::number(m_totalValidations)));
    status.append(StatusInfo("successful_validations", QString::number(m_successfulValidations)));
    status.append(StatusInfo("failed_validations", QString::number(m_failedValidations)));
    status.append(StatusInfo("average_execution_time", QString::number(m_averageExecutionTime, 'f', 2) + " ms"));
    status.append(StatusInfo("parallel_validation", m_enableParallelValidation ? "enabled" : "disabled"));
    status.append(StatusInfo("max_threads", QString::number(m_maxThreads)));
    
    return status;
}

VersionInfo ConfigValidator::getVersion() const
{
    VersionInfo version;
    version.major = 1;
    version.minor = 0;
    version.patch = 0;
    version.build = "stable";
    return version;
}

SimpleResult ConfigValidator::addRule(const ValidationRule& rule)
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_initialized) {
        return SimpleResult::error("Validator not initialized");
    }

    if (!isRuleValid(rule)) {
        return SimpleResult::error("Invalid rule");
    }

    QString ruleId = rule.id;
    if (ruleId.isEmpty()) {
        ruleId = generateRuleId();
    }

    if (m_rules.find(ruleId) != m_rules.end()) {
        return SimpleResult::error("Rule with ID already exists: " + ruleId);
    }

    ValidationRule newRule = rule;
    newRule.id = ruleId;
    newRule.modified = QDateTime::currentDateTime();

    m_rules[ruleId] = RuleEntry(newRule);

    emit ruleAdded(newRule);
    return SimpleResult::success(true);
}

SimpleResult ConfigValidator::addRules(const QList<ValidationRule>& rules)
{
    QMutexLocker locker(&m_mutex);
    
    // 验证所有规则
    for (const auto& rule : rules) {
        if (!isRuleValid(rule)) {
            return SimpleResult::error("Invalid rule found");
        }
    }

    // 批量添加
    for (const auto& rule : rules) {
        auto result = addRule(rule);
        if (!result.isSuccess()) {
            return result;
        }
    }

    return SimpleResult::success(true);
}

SimpleResult ConfigValidator::removeRule(const QString& ruleId)
{
    QMutexLocker locker(&m_mutex);
    
    auto it = m_rules.find(ruleId);
    if (it == m_rules.end()) {
        return SimpleResult::error("Rule not found: " + ruleId);
    }

    m_rules.erase(it);
    emit ruleRemoved(ruleId);
    return SimpleResult::success(true);
}

Result<ValidationRule> ConfigValidator::getRule(const QString& ruleId) const
{
    QMutexLocker locker(&m_mutex);
    
    auto it = m_rules.find(ruleId);
    if (it == m_rules.end()) {
        return Result<ValidationRule>::error("Rule not found: " + ruleId);
    }

    return Result<ValidationRule>::success(it->second.rule);
}

QList<ValidationRule> ConfigValidator::getAllRules() const
{
    QMutexLocker locker(&m_mutex);
    
    QList<ValidationRule> rules;
    for (const auto& pair : m_rules) {
        rules.append(pair.second.rule);
    }
    
    return rules;
}

QList<ValidationRule> ConfigValidator::queryRules(const QString& category,
                                                 const QString& group,
                                                 ValidationRuleType type) const
{
    QMutexLocker locker(&m_mutex);
    
    QList<ValidationRule> matchedRules;
    for (const auto& pair : m_rules) {
        const ValidationRule& rule = pair.second.rule;
        if (matchesRuleFilter(rule, category, group, type)) {
            matchedRules.append(rule);
        }
    }
    
    return matchedRules;
}

Result<ValidationReport> ConfigValidator::validate(const QVariantMap& data, 
                                                  const ValidationOptions& options)
{
    if (!m_initialized) {
        return Result<ValidationReport>::error("Validator not initialized");
    }

    QElapsedTimer timer;
    timer.start();

    m_validationInProgress = true;

    // 设置超时
    if (options.timeoutMs > 0) {
        m_timeoutTimer->start(static_cast<int>(options.timeoutMs));
    }

    emit validationStarted("ConfigData");

    QList<ValidationResult> results;
    
    try {
        if (m_enableParallelValidation && options.parallel) {
            results = validateParallel(data, options);
        } else {
            results = validateSequential(data, options);
        }
    } catch (const std::exception& e) {
        m_validationInProgress = false;
        m_timeoutTimer->stop();
        return Result<ValidationReport>::error("Validation failed: " + QString::fromStdString(e.what()));
    }

    m_validationInProgress = false;
    m_timeoutTimer->stop();

    double totalTime = timer.elapsed();
    ValidationReport report = generateReport("ConfigData", "", results, totalTime);

    // 更新统计信息
    {
        QMutexLocker locker(&m_mutex);
        m_totalValidations++;
        if (report.isValid) {
            m_successfulValidations++;
        } else {
            m_failedValidations++;
        }
        
        // 更新平均执行时间
        m_averageExecutionTime = (m_averageExecutionTime * (m_totalValidations - 1) + totalTime) / m_totalValidations;
        m_lastValidationTime = QDateTime::currentDateTime();
    }

    emit validationCompleted(report);
    return Result<ValidationReport>::success(report);
}

QList<ValidationResult> ConfigValidator::validateSequential(const QVariantMap& data,
                                                           const ValidationOptions& options)
{
    QList<ValidationResult> results;
    ValidationContext context;
    context.fullData = data;
    context.currentScope = data;
    context.timestamp = QDateTime::currentDateTime();

    // 遍历所有字段
    traverseData(data, "", [&](const QString& fieldPath, const QVariant& value) {
        if (!m_validationInProgress) {
            return; // 验证被中断
        }

        context.currentPath = fieldPath;
        auto fieldResults = validateSingleField(fieldPath, value, context, options);
        results.append(fieldResults);

        // 检查是否达到错误上限
        if (options.stopOnFirstError && !fieldResults.isEmpty()) {
            for (const auto& result : fieldResults) {
                if (result.status == ValidationStatus::FAILED || result.status == ValidationStatus::ERROR) {
                    m_validationInProgress = false;
                    return;
                }
            }
        }
    });

    return results;
}

QList<ValidationResult> ConfigValidator::validateSingleField(const QString& fieldPath,
                                                           const QVariant& value,
                                                           const ValidationContext& context,
                                                           const ValidationOptions& options)
{
    QList<ValidationResult> results;

    // 查找适用于此字段的规则
    for (const auto& pair : m_rules) {
        const ValidationRule& rule = pair.second.rule;
        
        if (!rule.enabled) {
            continue;
        }

        // 检查规则是否适用于此字段
        if (!rule.fieldPath.isEmpty() && rule.fieldPath != fieldPath) {
            // 支持通配符匹配
            QRegularExpression regex(QRegularExpression::wildcardToRegularExpression(rule.fieldPath));
            if (!regex.match(fieldPath).hasMatch()) {
                continue;
            }
        }

        // 检查规则过滤器
        if (!options.enabledRules.isEmpty() && !options.enabledRules.contains(rule.id)) {
            continue;
        }

        if (options.disabledRules.contains(rule.id)) {
            continue;
        }

        if (!options.enabledCategories.isEmpty() && !options.enabledCategories.contains(rule.category)) {
            continue;
        }

        if (options.disabledCategories.contains(rule.category)) {
            continue;
        }

        // 检查严重级别
        if (rule.severity < options.minSeverity) {
            continue;
        }

        // 执行规则验证
        ValidationResult result = executeRule(rule, value, context);
        results.append(result);

        emit ruleValidated(result);

        // 检查是否需要停止
        if (options.stopOnFirstError && (result.status == ValidationStatus::FAILED || result.status == ValidationStatus::ERROR)) {
            break;
        }
    }

    return results;
}

ValidationResult ConfigValidator::executeRule(const ValidationRule& rule, 
                                             const QVariant& value,
                                             const ValidationContext& context)
{
    QElapsedTimer timer;
    timer.start();

    ValidationResult result(rule.id, ValidationStatus::UNKNOWN, "");
    result.ruleName = rule.name;
    result.severity = rule.severity;
    result.fieldPath = context.currentPath;
    result.actualValue = value;
    result.expectedValue = rule.expectedValue;

    try {
        // 根据规则类型执行验证
        switch (rule.type) {
        case ValidationRuleType::TYPE_CHECK:
            result = validateTypeRule(value, rule, context);
            break;
        case ValidationRuleType::RANGE_CHECK:
            result = validateRangeRule(value, rule, context);
            break;
        case ValidationRuleType::LENGTH_CHECK:
            result = validateLengthRule(value, rule, context);
            break;
        case ValidationRuleType::PATTERN_CHECK:
            result = validatePatternRule(value, rule, context);
            break;
        case ValidationRuleType::ENUM_CHECK:
            result = validateEnumRule(value, rule, context);
            break;
        case ValidationRuleType::REQUIRED_CHECK:
            result = validateRequiredRule(value, rule, context);
            break;
        case ValidationRuleType::DEPENDENCY_CHECK:
            result = validateDependencyRule(value, rule, context);
            break;
        case ValidationRuleType::CUSTOM_CHECK:
            result = validateWithCustomFunction(rule, value, context);
            break;
        default:
            result.status = ValidationStatus::SKIPPED;
            result.message = "Unsupported rule type";
            break;
        }
    } catch (const std::exception& e) {
        result.status = ValidationStatus::ERROR;
        result.message = "Rule execution failed: " + QString::fromStdString(e.what());
    }

    double executionTime = timer.elapsed();
    result.executionTime = executionTime;

    // 更新规则统计信息
    updateRuleStatistics(rule.id, executionTime);

    return result;
}

ValidationResult ConfigValidator::validateTypeRule(const QVariant& value, const ValidationRule& rule, const ValidationContext& context)
{
    Q_UNUSED(context)
    
    ValidationResult result(rule.id, ValidationStatus::PASSED, "Type validation passed");
    result.ruleName = rule.name;
    result.severity = rule.severity;
    result.actualValue = value;

    QString expectedType = rule.expectedValue.toString().toLower();
    QVariant::Type actualType = value.type();

    bool typeMatches = false;

    if (expectedType == "string" && actualType == QVariant::String) {
        typeMatches = true;
    } else if (expectedType == "int" && (actualType == QVariant::Int || actualType == QVariant::LongLong)) {
        typeMatches = true;
    } else if (expectedType == "double" && (actualType == QVariant::Double || actualType == QVariant::Int)) {
        typeMatches = true;
    } else if (expectedType == "bool" && actualType == QVariant::Bool) {
        typeMatches = true;
    } else if (expectedType == "list" && actualType == QVariant::List) {
        typeMatches = true;
    } else if (expectedType == "map" && actualType == QVariant::Map) {
        typeMatches = true;
    }

    if (!typeMatches) {
        result.status = ValidationStatus::FAILED;
        result.message = QString("Type mismatch: expected %1, got %2")
                        .arg(expectedType)
                        .arg(QVariant::typeToName(actualType));
    }

    return result;
}

ValidationResult ConfigValidator::validateRangeRule(const QVariant& value, const ValidationRule& rule, const ValidationContext& context)
{
    Q_UNUSED(context)
    
    ValidationResult result(rule.id, ValidationStatus::PASSED, "Range validation passed");
    result.ruleName = rule.name;
    result.severity = rule.severity;
    result.actualValue = value;

    bool ok;
    double numValue = value.toDouble(&ok);
    
    if (!ok) {
        result.status = ValidationStatus::ERROR;
        result.message = "Cannot convert value to number for range check";
        return result;
    }

    bool hasMin = rule.minValue.isValid();
    bool hasMax = rule.maxValue.isValid();

    if (hasMin) {
        double minValue = rule.minValue.toDouble();
        if (numValue < minValue) {
            result.status = ValidationStatus::FAILED;
            result.message = QString("Value %1 is below minimum %2").arg(numValue).arg(minValue);
            return result;
        }
    }

    if (hasMax) {
        double maxValue = rule.maxValue.toDouble();
        if (numValue > maxValue) {
            result.status = ValidationStatus::FAILED;
            result.message = QString("Value %1 is above maximum %2").arg(numValue).arg(maxValue);
            return result;
        }
    }

    return result;
}

ValidationResult ConfigValidator::validateLengthRule(const QVariant& value, const ValidationRule& rule, const ValidationContext& context)
{
    Q_UNUSED(context)
    
    ValidationResult result(rule.id, ValidationStatus::PASSED, "Length validation passed");
    result.ruleName = rule.name;
    result.severity = rule.severity;
    result.actualValue = value;

    int length = 0;
    
    if (value.type() == QVariant::String) {
        length = value.toString().length();
    } else if (value.type() == QVariant::List) {
        length = value.toList().size();
    } else if (value.type() == QVariant::Map) {
        length = value.toMap().size();
    } else {
        result.status = ValidationStatus::ERROR;
        result.message = "Length check not applicable to this value type";
        return result;
    }

    bool hasMin = rule.minValue.isValid();
    bool hasMax = rule.maxValue.isValid();

    if (hasMin) {
        int minLength = rule.minValue.toInt();
        if (length < minLength) {
            result.status = ValidationStatus::FAILED;
            result.message = QString("Length %1 is below minimum %2").arg(length).arg(minLength);
            return result;
        }
    }

    if (hasMax) {
        int maxLength = rule.maxValue.toInt();
        if (length > maxLength) {
            result.status = ValidationStatus::FAILED;
            result.message = QString("Length %1 is above maximum %2").arg(length).arg(maxLength);
            return result;
        }
    }

    return result;
}

ValidationResult ConfigValidator::validatePatternRule(const QVariant& value, const ValidationRule& rule, const ValidationContext& context)
{
    Q_UNUSED(context)
    
    ValidationResult result(rule.id, ValidationStatus::PASSED, "Pattern validation passed");
    result.ruleName = rule.name;
    result.severity = rule.severity;
    result.actualValue = value;

    if (rule.pattern.isEmpty()) {
        result.status = ValidationStatus::ERROR;
        result.message = "No pattern specified for pattern validation";
        return result;
    }

    QString stringValue = value.toString();
    QRegularExpression regex(rule.pattern);
    
    if (!regex.isValid()) {
        result.status = ValidationStatus::ERROR;
        result.message = "Invalid regular expression pattern: " + rule.pattern;
        return result;
    }

    if (!regex.match(stringValue).hasMatch()) {
        result.status = ValidationStatus::FAILED;
        result.message = QString("Value '%1' does not match pattern '%2'").arg(stringValue, rule.pattern);
    }

    return result;
}

ValidationResult ConfigValidator::validateEnumRule(const QVariant& value, const ValidationRule& rule, const ValidationContext& context)
{
    Q_UNUSED(context)
    
    ValidationResult result(rule.id, ValidationStatus::PASSED, "Enum validation passed");
    result.ruleName = rule.name;
    result.severity = rule.severity;
    result.actualValue = value;

    if (rule.allowedValues.isEmpty()) {
        result.status = ValidationStatus::ERROR;
        result.message = "No allowed values specified for enum validation";
        return result;
    }

    QString stringValue = value.toString();
    if (!rule.allowedValues.contains(stringValue)) {
        result.status = ValidationStatus::FAILED;
        result.message = QString("Value '%1' is not in allowed values: [%2]")
                        .arg(stringValue, rule.allowedValues.join(", "));
    }

    return result;
}

// 其他内部方法的简化实现...
bool ConfigValidator::initializeRuleStorage()
{
    QDir dir;
    return dir.mkpath(m_storageDirectory) && dir.mkpath(m_ruleSetDirectory);
}

bool ConfigValidator::initializeBuiltinRules()
{
    // 添加内置验证规则
    return true;
}

bool ConfigValidator::initializeCustomValidators()
{
    // 初始化自定义验证器映射
    return true;
}

void ConfigValidator::cleanupRuleStorage()
{
    // 清理规则存储
}

void ConfigValidator::cleanupCustomValidators()
{
    // 清理自定义验证器
}

QString ConfigValidator::generateRuleId() const
{
    return QUuid::createUuid().toString(QUuid::WithoutBraces);
}

void ConfigValidator::updateRuleStatistics(const QString& ruleId, double executionTime)
{
    auto it = m_rules.find(ruleId);
    if (it != m_rules.end()) {
        RuleEntry& entry = it->second;
        entry.usageCount++;
        entry.averageExecutionTime = (entry.averageExecutionTime * (entry.usageCount - 1) + executionTime) / entry.usageCount;
        entry.lastUsed = QDateTime::currentDateTime();
    }
}

bool ConfigValidator::isRuleValid(const ValidationRule& rule) const
{
    return !rule.name.isEmpty() && rule.type != ValidationRuleType::UNKNOWN;
}

void ConfigValidator::traverseData(const QVariantMap& data,
                                  const QString& basePath,
                                  std::function<void(const QString&, const QVariant&)> callback)
{
    for (auto it = data.begin(); it != data.end(); ++it) {
        QString currentPath = basePath.isEmpty() ? it.key() : basePath + "." + it.key();
        QVariant value = it.value();
        
        callback(currentPath, value);
        
        // 递归处理嵌套对象
        if (value.type() == QVariant::Map) {
            traverseData(value.toMap(), currentPath, callback);
        }
    }
}

ValidationReport ConfigValidator::generateReport(const QString& configName,
                                               const QString& configPath,
                                               const QList<ValidationResult>& results,
                                               double totalTime)
{
    ValidationReport report;
    report.reportId = QUuid::createUuid().toString(QUuid::WithoutBraces);
    report.configName = configName;
    report.configPath = configPath;
    report.timestamp = QDateTime::currentDateTime();
    report.totalTime = totalTime;
    report.results = results;
    
    calculateReportStatistics(report);
    
    return report;
}

void ConfigValidator::calculateReportStatistics(ValidationReport& report)
{
    report.totalRules = report.results.size();
    report.passedRules = 0;
    report.failedRules = 0;
    report.warningCount = 0;
    report.errorCount = 0;

    for (const auto& result : report.results) {
        if (result.status == ValidationStatus::PASSED) {
            report.passedRules++;
        } else if (result.status == ValidationStatus::FAILED || result.status == ValidationStatus::ERROR) {
            report.failedRules++;
        }

        if (result.severity == ValidationSeverity::WARNING) {
            report.warningCount++;
        } else if (result.severity == ValidationSeverity::ERROR || result.severity == ValidationSeverity::CRITICAL) {
            report.errorCount++;
        }
    }

    report.isValid = (report.errorCount == 0);
}

bool ConfigValidator::matchesRuleFilter(const ValidationRule& rule, 
                                       const QString& category,
                                       const QString& group,
                                       ValidationRuleType type) const
{
    if (!category.isEmpty() && rule.category != category) {
        return false;
    }
    
    if (!group.isEmpty() && rule.group != group) {
        return false;
    }
    
    if (type != ValidationRuleType::UNKNOWN && rule.type != type) {
        return false;
    }
    
    return true;
}

void ConfigValidator::onValidationTimeout()
{
    m_validationInProgress = false;
    emit validationError("Validation timeout");
}

// 占位符实现，实际项目中需要完整实现
SimpleResult ConfigValidator::updateRule(const ValidationRule& rule) { return SimpleResult::error("Not implemented"); }
SimpleResult ConfigValidator::enableRule(const QString& ruleId) { return SimpleResult::error("Not implemented"); }
SimpleResult ConfigValidator::disableRule(const QString& ruleId) { return SimpleResult::error("Not implemented"); }
Result<QList<ValidationResult>> ConfigValidator::validateField(const QString& fieldPath, const QVariant& value, const QVariantMap& context) { return Result<QList<ValidationResult>>::error("Not implemented"); }
Result<ValidationReport> ConfigValidator::validateFile(const QString& filePath, const ValidationOptions& options) { return Result<ValidationReport>::error("Not implemented"); }
Result<ValidationReport> ConfigValidator::validateSchema(const QVariantMap& data, const QVariantMap& schema, const ValidationOptions& options) { return Result<ValidationReport>::error("Not implemented"); }
SimpleResult ConfigValidator::createRuleSet(const QString& name, const QList<ValidationRule>& rules) { return SimpleResult::error("Not implemented"); }
SimpleResult ConfigValidator::loadRuleSet(const QString& name) { return SimpleResult::error("Not implemented"); }
SimpleResult ConfigValidator::saveRuleSet(const QString& name, const QString& filePath) { return SimpleResult::error("Not implemented"); }
ByteArrayResult ConfigValidator::exportRules(const QString& format) { return ByteArrayResult::error("Not implemented"); }
SimpleResult ConfigValidator::importRules(const QByteArray& data, const QString& format, bool overwrite) { return SimpleResult::error("Not implemented"); }
SimpleResult ConfigValidator::clearRules() { return SimpleResult::error("Not implemented"); }
QList<ValidationRuleType> ConfigValidator::getSupportedRuleTypes() const { return QList<ValidationRuleType>(); }
SimpleResult ConfigValidator::registerCustomValidator(const QString& name, std::function<ValidationResult(const QVariant&, const ValidationRule&, const ValidationContext&)> validator) { return SimpleResult::error("Not implemented"); }
SimpleResult ConfigValidator::unregisterCustomValidator(const QString& name) { return SimpleResult::error("Not implemented"); }
QList<ValidationResult> ConfigValidator::validateParallel(const QVariantMap& data, const ValidationOptions& options) { return QList<ValidationResult>(); }
ValidationResult ConfigValidator::validateRequiredRule(const QVariant& value, const ValidationRule& rule, const ValidationContext& context) { return ValidationResult(rule.id, ValidationStatus::ERROR, "Not implemented"); }
ValidationResult ConfigValidator::validateDependencyRule(const QVariant& value, const ValidationRule& rule, const ValidationContext& context) { return ValidationResult(rule.id, ValidationStatus::ERROR, "Not implemented"); }
ValidationResult ConfigValidator::validateWithCustomFunction(const ValidationRule& rule, const QVariant& value, const ValidationContext& context) { return ValidationResult(rule.id, ValidationStatus::ERROR, "Not implemented"); }

// 工厂实现
std::shared_ptr<IConfigValidator> ConfigValidatorFactory::createConfigValidator(const ConfigParameters& config)
{
    auto validator = std::make_shared<ConfigValidator>();
    validator->initialize(config);
    return validator;
}

// 工具函数实现
QString validationRuleTypeToString(ValidationRuleType type)
{
    switch (type) {
    case ValidationRuleType::TYPE_CHECK: return "TYPE_CHECK";
    case ValidationRuleType::RANGE_CHECK: return "RANGE_CHECK";
    case ValidationRuleType::LENGTH_CHECK: return "LENGTH_CHECK";
    case ValidationRuleType::PATTERN_CHECK: return "PATTERN_CHECK";
    case ValidationRuleType::ENUM_CHECK: return "ENUM_CHECK";
    case ValidationRuleType::REQUIRED_CHECK: return "REQUIRED_CHECK";
    case ValidationRuleType::DEPENDENCY_CHECK: return "DEPENDENCY_CHECK";
    case ValidationRuleType::CUSTOM_CHECK: return "CUSTOM_CHECK";
    case ValidationRuleType::SCHEMA_CHECK: return "SCHEMA_CHECK";
    default: return "UNKNOWN";
    }
}

ValidationRuleType stringToValidationRuleType(const QString& typeStr)
{
    QString upper = typeStr.toUpper();
    if (upper == "TYPE_CHECK") return ValidationRuleType::TYPE_CHECK;
    if (upper == "RANGE_CHECK") return ValidationRuleType::RANGE_CHECK;
    if (upper == "LENGTH_CHECK") return ValidationRuleType::LENGTH_CHECK;
    if (upper == "PATTERN_CHECK") return ValidationRuleType::PATTERN_CHECK;
    if (upper == "ENUM_CHECK") return ValidationRuleType::ENUM_CHECK;
    if (upper == "REQUIRED_CHECK") return ValidationRuleType::REQUIRED_CHECK;
    if (upper == "DEPENDENCY_CHECK") return ValidationRuleType::DEPENDENCY_CHECK;
    if (upper == "CUSTOM_CHECK") return ValidationRuleType::CUSTOM_CHECK;
    if (upper == "SCHEMA_CHECK") return ValidationRuleType::SCHEMA_CHECK;
    return ValidationRuleType::UNKNOWN;
}

}  // namespace Config
}  // namespace Support
}  // namespace LA