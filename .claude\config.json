{"servers": [{"type": "git", "command": "uvx", "args": ["mcp-server-git"]}, {"type": "sequential-thinking", "command": "cmd", "args": ["/c", "npx", "-y", "@modelcontextprotocol/server-sequential-thinking"]}, {"type": "mcp-feedback-enhanced", "command": "uvx", "args": ["mcp-feedback-enhanced@latest"], "env": {"FORCE_WEB": "true", "MCP_DEBUG": "false"}}, {"type": "filesystem", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", ".", "F:/101_link-notebook/Obsidian-Vault/KA/Product-development/development/software_process", "f:/13_<PERSON><PERSON>-<PERSON><PERSON>-DTof2dMS/development/tool/LA-T5/"]}, {"type": "product-development-complete", "command": "python", "args": ["F:/101_link-notebook/Obsidian-Vault/KA/Product-development/build-tools/mcp-server_local_integrations/unified/product-development-complete/server.py"], "env": {"PYTHONIOENCODING": "utf-8", "PYTHONPATH": "F:/101_link-notebook/Obsidian-Vault/KA/Product-development/build-tools/scripts"}}, {"type": "obsidian", "command": "uvx", "args": ["mcp-obsidian", "F:\\101_link-notebook\\Obsidian-Vault"], "env": {"OBSIDIAN_API_KEY": "7cdb02bfd348a98af18927690537af0e57843eca1f65ca8fb482e99039e2d84a", "OBSIDIAN_HOST": "127.0.0.1", "OBSIDIAN_PORT": "27123"}}, {"type": "mcp-excalidraw", "command": "npx", "args": ["-y", "excalidraw-mcp"]}]}