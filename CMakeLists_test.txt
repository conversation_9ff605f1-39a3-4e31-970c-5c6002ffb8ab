cmake_minimum_required(VERSION 3.16)
project(TestNewArchitecture)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find Qt5
find_package(Qt5 COMPONENTS Core Network SerialPort Concurrent REQUIRED)

# Include directories
include_directories(
    ${CMAKE_SOURCE_DIR}/modules/device_management/include
    ${CMAKE_SOURCE_DIR}/infrastructure/communication/include
)

# Link directories
link_directories(${CMAKE_BINARY_DIR}/build/bin)

# Create test executable
add_executable(test_matching_architecture test_new_matching_architecture.cpp)

# Link libraries
target_link_libraries(test_matching_architecture
    Qt5::Core
    Qt5::Network  
    Qt5::SerialPort
    Qt5::Concurrent
    LA_device_management
)