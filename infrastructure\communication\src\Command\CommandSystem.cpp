#include "CommandSystem.h"
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QRegularExpression>
#include <QFile>
#include <QDebug>
#include <QMutexLocker>
#include <QUuid>
#include <QtEndian>

namespace LA {
namespace Communication {
namespace Command {

// === CommandTemplateEngine实现 ===

const QRegularExpression CommandTemplateEngine::s_parameterRegex(R"(\{\{(\w+)\}\})");

QString CommandTemplateEngine::renderTemplate(const QString& templateString, const QVariantMap& parameters)
{
    QString result = templateString;
    
    // 替换所有 {{parameter}} 占位符
    QRegularExpressionMatchIterator iter = s_parameterRegex.globalMatch(templateString);
    
    while (iter.hasNext()) {
        QRegularExpressionMatch match = iter.next();
        QString placeholder = match.captured(0);    // {{parameter}}
        QString paramName = match.captured(1);      // parameter
        
        if (parameters.contains(paramName)) {
            QString value = parameters[paramName].toString();
            result.replace(placeholder, value);
        } else {
            qWarning() << "模板参数缺失:" << paramName;
        }
    }
    
    return result;
}

QStringList CommandTemplateEngine::extractParameters(const QString& templateString)
{
    QStringList parameters;
    QRegularExpressionMatchIterator iter = s_parameterRegex.globalMatch(templateString);
    
    while (iter.hasNext()) {
        QRegularExpressionMatch match = iter.next();
        QString paramName = match.captured(1);
        if (!parameters.contains(paramName)) {
            parameters.append(paramName);
        }
    }
    
    return parameters;
}

bool CommandTemplateEngine::validateTemplate(const QString& templateString)
{
    // 检查模板语法是否正确
    QRegularExpression braceRegex(R"(\{\{|\}\})");
    QRegularExpressionMatchIterator iter = braceRegex.globalMatch(templateString);
    
    int openCount = 0;
    while (iter.hasNext()) {
        QRegularExpressionMatch match = iter.next();
        if (match.captured(0) == "{{") {
            openCount++;
        } else if (match.captured(0) == "}}") {
            openCount--;
            if (openCount < 0) {
                return false; // 未匹配的结束括号
            }
        }
    }
    
    return openCount == 0; // 所有括号都匹配
}

// === CommandParser实现 ===

ParsedCommand CommandParser::parseJsonResponse(const QString& deviceId, 
                                              const QString& commandId,
                                              const QByteArray& data)
{
    ParsedCommand result;
    result.deviceId = deviceId;
    result.commandId = commandId;
    result.parseMethod = "JSON";
    
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(data, &error);
    
    if (error.error != QJsonParseError::NoError) {
        result.isValid = false;
        result.errorMessage = "JSON解析错误: " + error.errorString();
        return result;
    }
    
    QJsonObject obj = doc.object();
    for (auto it = obj.begin(); it != obj.end(); ++it) {
        result.parsedData[it.key()] = it.value().toVariant();
    }
    
    result.isValid = true;
    return result;
}

ParsedCommand CommandParser::parseTextResponse(const QString& deviceId,
                                              const QString& commandId, 
                                              const QByteArray& data)
{
    ParsedCommand result;
    result.deviceId = deviceId;
    result.commandId = commandId;
    result.parseMethod = "Text";
    
    QString text = QString::fromUtf8(data).trimmed();
    
    // 解析键值对格式：key=value 或 key:value
    QRegularExpression kvRegex(R"((\w+)[=:](.+))");
    QStringList lines = text.split('\n');
    
    for (const QString& line : lines) {
        QString trimmedLine = line.trimmed();
        if (trimmedLine.isEmpty()) continue;
        
        QRegularExpressionMatch match = kvRegex.match(trimmedLine);
        if (match.hasMatch()) {
            QString key = match.captured(1).trimmed();
            QString value = match.captured(2).trimmed();
            result.parsedData[key] = value;
        } else {
            // 如果不是键值对，存储为原始数据
            result.rawFields["line_" + QString::number(result.rawFields.size())] = trimmedLine;
        }
    }
    
    result.isValid = !result.parsedData.isEmpty() || !result.rawFields.isEmpty();
    if (!result.isValid) {
        result.errorMessage = "无法解析文本格式数据";
    }
    
    return result;
}

ParsedCommand CommandParser::parseBinaryResponse(const QString& deviceId,
                                                const QString& commandId,
                                                const QByteArray& data)
{
    ParsedCommand result;
    result.deviceId = deviceId;
    result.commandId = commandId;
    result.parseMethod = "Binary";
    
    // 基本的二进制数据解析
    result.parsedData["dataLength"] = data.length();
    result.parsedData["hexData"] = data.toHex();
    
    // 如果是ASCII可打印字符，也提供文本形式
    bool isPrintable = true;
    for (char c : data) {
        if (c < 32 || c > 126) {
            isPrintable = false;
            break;
        }
    }
    
    if (isPrintable && !data.isEmpty()) {
        result.parsedData["textData"] = QString::fromLatin1(data);
    }
    
    // 提供一些基本的数值解析（如果长度合适）
    if (data.length() >= 2) {
        quint16 uint16Value = qFromBigEndian<quint16>(data.constData());
        result.parsedData["uint16_be"] = uint16Value;
    }
    
    if (data.length() >= 4) {
        quint32 uint32Value = qFromBigEndian<quint32>(data.constData());
        result.parsedData["uint32_be"] = uint32Value;
    }
    
    result.isValid = true;
    return result;
}

ParsedCommand CommandParser::parseCustomResponse(const QString& deviceId,
                                                const QString& commandId,
                                                const QByteArray& data,
                                                const QString& format)
{
    ParsedCommand result;
    result.deviceId = deviceId;
    result.commandId = commandId;
    result.parseMethod = "Custom:" + format;
    
    // 根据自定义格式解析（这里提供基本实现，实际中可能需要更复杂的解析器）
    if (format.toLower() == "json") {
        return parseJsonResponse(deviceId, commandId, data);
    } else if (format.toLower() == "text" || format.toLower() == "kv") {
        return parseTextResponse(deviceId, commandId, data);
    } else if (format.toLower() == "binary" || format.toLower() == "hex") {
        return parseBinaryResponse(deviceId, commandId, data);
    } else {
        // 未知格式，尝试智能解析
        // 先尝试JSON
        QJsonParseError error;
        QJsonDocument::fromJson(data, &error);
        if (error.error == QJsonParseError::NoError) {
            return parseJsonResponse(deviceId, commandId, data);
        }
        
        // 再尝试文本
        result = parseTextResponse(deviceId, commandId, data);
        if (result.isValid) {
            return result;
        }
        
        // 最后按二进制处理
        return parseBinaryResponse(deviceId, commandId, data);
    }
}

// === CommandSystem实现 ===

CommandSystem::CommandSystem(QSharedPointer<Registry::IDeviceRegistry> deviceRegistry,
                            QObject* parent)
    : ICommandSystem(parent)
    , m_deviceRegistry(deviceRegistry)
    , m_historyEnabled(false)
    , m_maxHistorySize(DEFAULT_HISTORY_SIZE)
    , m_statsStartTime(QDateTime::currentDateTime())
{
    // 初始化全局统计
    m_globalStats["totalCommands"] = 0;
    m_globalStats["successfulCommands"] = 0;
    m_globalStats["failedCommands"] = 0;
    m_globalStats["startTime"] = m_statsStartTime.toString(Qt::ISODate);
    
    qDebug() << "CommandSystem initialized";
}

CommandSystem::~CommandSystem()
{
    QMutexLocker locker(&m_mutex);
    m_commandHistory.clear();
    m_commandTemplates.clear();
    m_deviceStats.clear();
    
    qDebug() << "CommandSystem destroyed";
}

QSharedPointer<BuiltCommand> CommandSystem::buildCommand(
    const QString& deviceId,
    const QString& commandId,
    const QVariantMap& parameters,
    const CommandContext& context)
{
    QMutexLocker locker(&m_mutex);
    
    // 验证设备存在
    if (!m_deviceRegistry->hasDevice(deviceId)) {
        qWarning() << "设备不存在:" << deviceId;
        return QSharedPointer<BuiltCommand>();
    }
    
    // 获取指令定义
    CommandDefinition definition = getCommandDefinitionFromRegistry(deviceId, commandId);
    if (definition.commandId.isEmpty()) {
        qWarning() << "指令不存在:" << commandId << "for device:" << deviceId;
        return QSharedPointer<BuiltCommand>();
    }
    
    // 验证参数
    CommandValidation validation = validateParameters(definition, parameters);
    if (!validation.isValid) {
        qWarning() << "指令参数验证失败:" << validation.errors;
        emit commandValidationFailed(deviceId, commandId, validation);
        return QSharedPointer<BuiltCommand>();
    }
    
    // 创建指令对象
    auto command = QSharedPointer<BuiltCommand>::create();
    command->commandId = commandId;
    command->deviceId = deviceId;
    command->deviceType = getDeviceType(deviceId);
    command->definition = definition;
    command->context = context;
    command->parameters = applyDefaultParameters(definition, parameters);
    command->createdBy = "CommandSystem";
    
    // 构建指令内容
    command->rawCommand = buildCommandContent(definition, command->parameters);
    
    // 设置二进制指令（如果需要）
    command->binaryCommand = command->rawCommand.toUtf8();
    
    // 记录历史
    if (m_historyEnabled) {
        recordCommandHistory(*command);
    }
    
    emit commandBuilt(*command);
    
    qDebug() << QString("指令构建成功: %1 -> %2").arg(deviceId).arg(commandId);
    
    return command;
}

QList<QSharedPointer<BuiltCommand>> CommandSystem::buildBatchCommands(
    const QString& deviceId,
    const QList<QPair<QString, QVariantMap>>& commands,
    const CommandContext& context)
{
    QList<QSharedPointer<BuiltCommand>> builtCommands;
    
    for (const auto& cmdPair : commands) {
        auto command = buildCommand(deviceId, cmdPair.first, cmdPair.second, context);
        if (command) {
            builtCommands.append(command);
        }
    }
    
    qDebug() << QString("批量构建指令完成: %1/%2 成功").arg(builtCommands.size()).arg(commands.size());
    
    return builtCommands;
}

QSharedPointer<BuiltCommand> CommandSystem::buildFromTemplate(
    const QString& deviceId,
    const QString& commandId,
    const QVariantMap& templateData)
{
    QMutexLocker locker(&m_mutex);
    
    DeviceType deviceType = getDeviceType(deviceId);
    QString templateString = getCommandTemplate(deviceType, commandId);
    
    if (templateString.isEmpty()) {
        qWarning() << "未找到指令模板:" << commandId << "for device type:" << static_cast<int>(deviceType);
        return QSharedPointer<BuiltCommand>();
    }
    
    // 从模板提取参数
    QStringList requiredParams = CommandTemplateEngine::extractParameters(templateString);
    QVariantMap parameters = templateData;
    
    // 检查必需参数
    for (const QString& param : requiredParams) {
        if (!parameters.contains(param)) {
            qWarning() << "模板参数缺失:" << param;
            return QSharedPointer<BuiltCommand>();
        }
    }
    
    // 渲染模板
    QString renderedCommand = CommandTemplateEngine::renderTemplate(templateString, parameters);
    
    // 构建指令
    auto command = buildCommand(deviceId, commandId, parameters);
    if (command) {
        command->rawCommand = renderedCommand;
        command->binaryCommand = renderedCommand.toUtf8();
    }
    
    return command;
}

ParsedCommand CommandSystem::parseResponse(
    const QString& deviceId,
    const QString& commandId,
    const QByteArray& responseData)
{
    return selectAndParse(deviceId, commandId, responseData);
}

ParsedCommand CommandSystem::parseRawCommand(
    const QString& deviceId,
    const QByteArray& rawData)
{
    return selectAndParse(deviceId, QString(), rawData);
}

QList<ParsedCommand> CommandSystem::autoParseData(const QByteArray& rawData)
{
    QList<ParsedCommand> results;
    
    // 获取所有设备，尝试解析
    QStringList deviceIds = m_deviceRegistry->getAllDeviceIds();
    
    for (const QString& deviceId : deviceIds) {
        ParsedCommand parsed = selectAndParse(deviceId, QString(), rawData);
        if (parsed.isValid) {
            results.append(parsed);
        }
    }
    
    return results;
}

CommandValidation CommandSystem::validateCommand(
    const QString& deviceId,
    const QString& commandId,
    const QVariantMap& parameters)
{
    CommandValidation validation;
    
    // 检查设备存在
    if (!m_deviceRegistry->hasDevice(deviceId)) {
        validation.isValid = false;
        validation.errors.append("设备不存在: " + deviceId);
        return validation;
    }
    
    // 检查指令支持
    if (!m_deviceRegistry->supportsCommand(deviceId, commandId)) {
        validation.isValid = false;
        validation.errors.append("设备不支持指令: " + commandId);
        return validation;
    }
    
    // 获取指令定义
    CommandDefinition definition = getCommandDefinitionFromRegistry(deviceId, commandId);
    if (definition.commandId.isEmpty()) {
        validation.isValid = false;
        validation.errors.append("指令定义不存在: " + commandId);
        return validation;
    }
    
    // 验证参数
    return validateParameters(definition, parameters);
}

CommandValidation CommandSystem::validateBuiltCommand(const BuiltCommand& command)
{
    CommandValidation validation;
    
    // 基本验证
    if (command.commandId.isEmpty()) {
        validation.isValid = false;
        validation.errors.append("指令ID为空");
    }
    
    if (command.deviceId.isEmpty()) {
        validation.isValid = false;
        validation.errors.append("设备ID为空");
    }
    
    if (command.rawCommand.isEmpty() && command.binaryCommand.isEmpty()) {
        validation.isValid = false;
        validation.errors.append("指令内容为空");
    }
    
    // 验证参数
    if (!command.definition.commandId.isEmpty()) {
        CommandValidation paramValidation = validateParameters(command.definition, command.parameters);
        if (!paramValidation.isValid) {
            validation.isValid = false;
            validation.errors.append(paramValidation.errors);
            validation.warnings.append(paramValidation.warnings);
        }
    }
    
    return validation;
}

QList<CommandDefinition> CommandSystem::getAvailableCommands(const QString& deviceId)
{
    if (!m_deviceRegistry->hasDevice(deviceId)) {
        return QList<CommandDefinition>();
    }
    
    return m_deviceRegistry->getDeviceCommands(deviceId);
}

CommandDefinition CommandSystem::getCommandDefinition(const QString& deviceId, const QString& commandId)
{
    return getCommandDefinitionFromRegistry(deviceId, commandId);
}

bool CommandSystem::supportsCommand(const QString& deviceId, const QString& commandId)
{
    return m_deviceRegistry->supportsCommand(deviceId, commandId);
}

bool CommandSystem::registerCommandTemplate(
    DeviceType deviceType,
    const QString& commandId,
    const QString& templateString)
{
    QMutexLocker locker(&m_mutex);
    
    if (!CommandTemplateEngine::validateTemplate(templateString)) {
        qWarning() << "模板语法错误:" << templateString;
        return false;
    }
    
    m_commandTemplates[deviceType][commandId] = templateString;
    
    emit templateRegistered(deviceType, commandId);
    
    qDebug() << QString("注册指令模板: %1 -> %2").arg(static_cast<int>(deviceType)).arg(commandId);
    
    return true;
}

QString CommandSystem::getCommandTemplate(DeviceType deviceType, const QString& commandId)
{
    QMutexLocker locker(&m_mutex);
    
    // 先查找设备特定模板
    if (m_commandTemplates.contains(deviceType) && 
        m_commandTemplates[deviceType].contains(commandId)) {
        return m_commandTemplates[deviceType][commandId];
    }
    
    // 查找全局模板
    if (m_globalTemplates.contains(commandId)) {
        return m_globalTemplates[commandId];
    }
    
    // 尝试从指令定义生成默认模板
    auto device = m_deviceRegistry->getDevicesByType(deviceType);
    if (!device.isEmpty()) {
        CommandDefinition definition = getCommandDefinitionFromRegistry(device.first(), commandId);
        if (!definition.requestFormat.isEmpty()) {
            return definition.requestFormat;
        }
    }
    
    return QString();
}

QVariantMap CommandSystem::getCommandStatistics(const QString& deviceId)
{
    QMutexLocker locker(&m_mutex);
    
    if (deviceId.isEmpty()) {
        // 返回全局统计
        QVariantMap stats = m_globalStats;
        stats["upTime"] = m_statsStartTime.secsTo(QDateTime::currentDateTime());
        return stats;
    } else {
        // 返回设备特定统计
        return m_deviceStats.value(deviceId, QVariantMap());
    }
}

QList<BuiltCommand> CommandSystem::getRecentCommands(const QString& deviceId, int limit)
{
    QMutexLocker locker(&m_mutex);
    
    QList<BuiltCommand> commands = m_commandHistory.value(deviceId);
    
    // 返回最近的指令（按时间降序）
    if (commands.size() > limit) {
        commands = commands.mid(commands.size() - limit);
    }
    
    return commands;
}

void CommandSystem::clearCommandHistory(const QString& deviceId, const QDateTime& olderThan)
{
    QMutexLocker locker(&m_mutex);
    
    if (deviceId.isEmpty()) {
        // 清除所有设备的历史
        if (olderThan.isValid()) {
            for (auto& history : m_commandHistory) {
                history.erase(std::remove_if(history.begin(), history.end(),
                    [&olderThan](const BuiltCommand& cmd) {
                        return cmd.createdTime < olderThan;
                    }), history.end());
            }
        } else {
            m_commandHistory.clear();
        }
    } else {
        // 清除指定设备的历史
        if (olderThan.isValid()) {
            auto& history = m_commandHistory[deviceId];
            history.erase(std::remove_if(history.begin(), history.end(),
                [&olderThan](const BuiltCommand& cmd) {
                    return cmd.createdTime < olderThan;
                }), history.end());
        } else {
            m_commandHistory.remove(deviceId);
        }
    }
}

// === 扩展功能实现 ===

int CommandSystem::registerBatchTemplates(const QMap<DeviceType, QMap<QString, QString>>& templates)
{
    int successCount = 0;
    
    for (auto deviceIter = templates.constBegin(); deviceIter != templates.constEnd(); ++deviceIter) {
        DeviceType deviceType = deviceIter.key();
        const auto& deviceTemplates = deviceIter.value();
        
        for (auto cmdIter = deviceTemplates.constBegin(); cmdIter != deviceTemplates.constEnd(); ++cmdIter) {
            if (registerCommandTemplate(deviceType, cmdIter.key(), cmdIter.value())) {
                successCount++;
            }
        }
    }
    
    qDebug() << QString("批量注册模板完成，成功: %1").arg(successCount);
    return successCount;
}

bool CommandSystem::loadTemplatesFromFile(const QString& filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        qWarning() << "无法打开模板文件:" << filePath;
        return false;
    }
    
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(file.readAll(), &error);
    if (error.error != QJsonParseError::NoError) {
        qWarning() << "模板文件解析错误:" << error.errorString();
        return false;
    }
    
    QJsonObject root = doc.object();
    int loadedCount = 0;
    
    // 加载设备特定模板
    if (root.contains("deviceTemplates")) {
        QJsonObject deviceTemplates = root["deviceTemplates"].toObject();
        
        for (auto deviceIter = deviceTemplates.begin(); deviceIter != deviceTemplates.end(); ++deviceIter) {
            bool ok;
            int deviceTypeInt = deviceIter.key().toInt(&ok);
            if (!ok) continue;
            
            DeviceType deviceType = static_cast<DeviceType>(deviceTypeInt);
            QJsonObject commands = deviceIter.value().toObject();
            
            for (auto cmdIter = commands.begin(); cmdIter != commands.end(); ++cmdIter) {
                QString commandId = cmdIter.key();
                QString templateString = cmdIter.value().toString();
                
                if (registerCommandTemplate(deviceType, commandId, templateString)) {
                    loadedCount++;
                }
            }
        }
    }
    
    // 加载全局模板
    if (root.contains("globalTemplates")) {
        QJsonObject globalTemplates = root["globalTemplates"].toObject();
        
        for (auto iter = globalTemplates.begin(); iter != globalTemplates.end(); ++iter) {
            m_globalTemplates[iter.key()] = iter.value().toString();
            loadedCount++;
        }
    }
    
    qDebug() << QString("从文件加载模板完成: %1 (加载了 %2 个模板)").arg(filePath).arg(loadedCount);
    return loadedCount > 0;
}

bool CommandSystem::saveTemplatesToFile(const QString& filePath) const
{
    QMutexLocker locker(&m_mutex);
    
    QJsonObject root;
    
    // 保存设备特定模板
    QJsonObject deviceTemplates;
    for (auto deviceIter = m_commandTemplates.constBegin(); deviceIter != m_commandTemplates.constEnd(); ++deviceIter) {
        QString deviceTypeKey = QString::number(static_cast<int>(deviceIter.key()));
        QJsonObject commands;
        
        const auto& deviceCommands = deviceIter.value();
        for (auto cmdIter = deviceCommands.constBegin(); cmdIter != deviceCommands.constEnd(); ++cmdIter) {
            commands[cmdIter.key()] = cmdIter.value();
        }
        
        deviceTemplates[deviceTypeKey] = commands;
    }
    root["deviceTemplates"] = deviceTemplates;
    
    // 保存全局模板
    QJsonObject globalTemplates;
    for (auto iter = m_globalTemplates.constBegin(); iter != m_globalTemplates.constEnd(); ++iter) {
        globalTemplates[iter.key()] = iter.value();
    }
    root["globalTemplates"] = globalTemplates;
    
    // 添加元数据
    root["version"] = "1.0";
    root["saveTime"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    
    QJsonDocument doc(root);
    
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly)) {
        qWarning() << "无法写入模板文件:" << filePath;
        return false;
    }
    
    file.write(doc.toJson());
    
    qDebug() << QString("模板已保存到: %1").arg(filePath);
    return true;
}

void CommandSystem::setHistoryTracking(bool enabled, int maxHistorySize)
{
    QMutexLocker locker(&m_mutex);
    
    m_historyEnabled = enabled;
    m_maxHistorySize = maxHistorySize;
    
    if (!enabled) {
        m_commandHistory.clear();
    }
    
    qDebug() << QString("指令历史记录: %1 (最大: %2)").arg(enabled ? "启用" : "禁用").arg(maxHistorySize);
}

// === 私有方法实现 ===

CommandDefinition CommandSystem::getCommandDefinitionFromRegistry(const QString& deviceId, const QString& commandId)
{
    auto commands = m_deviceRegistry->getDeviceCommands(deviceId);
    
    for (const auto& cmd : commands) {
        if (cmd.commandId == commandId) {
            return cmd;
        }
    }
    
    return CommandDefinition();
}

DeviceType CommandSystem::getDeviceType(const QString& deviceId)
{
    auto device = m_deviceRegistry->getDevice(deviceId);
    return device ? device->deviceType : DeviceType::Unknown;
}

QString CommandSystem::buildCommandContent(const CommandDefinition& definition, 
                                          const QVariantMap& parameters)
{
    QString content = definition.requestFormat;
    
    if (content.isEmpty()) {
        // 如果没有请求格式，生成默认格式
        QStringList parts;
        parts.append(definition.commandId);
        
        for (auto iter = parameters.constBegin(); iter != parameters.constEnd(); ++iter) {
            parts.append(QString("%1=%2").arg(iter.key()).arg(iter.value().toString()));
        }
        
        content = parts.join(" ");
    } else {
        // 使用模板引擎渲染
        content = CommandTemplateEngine::renderTemplate(content, parameters);
    }
    
    return content;
}

CommandValidation CommandSystem::validateParameters(const CommandDefinition& definition,
                                                   const QVariantMap& parameters)
{
    CommandValidation validation;
    
    // 检查必需参数
    for (const QString& requiredParam : definition.requiredParameters) {
        if (!parameters.contains(requiredParam)) {
            validation.isValid = false;
            validation.missingParameters.append(requiredParam);
            validation.errors.append("缺少必需参数: " + requiredParam);
        }
    }
    
    // 检查参数验证规则
    for (auto iter = parameters.constBegin(); iter != parameters.constEnd(); ++iter) {
        QString paramName = iter.key();
        QVariant paramValue = iter.value();
        
        if (definition.parameterValidation.contains(paramName)) {
            QVariantMap rules = definition.parameterValidation[paramName].toMap();
            
            // 检查类型
            if (rules.contains("type")) {
                QString expectedType = rules["type"].toString();
                QVariant::Type actualType = paramValue.type();
                
                // 简单的类型检查
                if (expectedType == "int" && actualType != QVariant::Int) {
                    validation.warnings.append(QString("参数 %1 类型不匹配，期望: %2").arg(paramName).arg(expectedType));
                }
            }
            
            // 检查范围
            if (rules.contains("min") || rules.contains("max")) {
                bool ok;
                double value = paramValue.toDouble(&ok);
                if (ok) {
                    if (rules.contains("min") && value < rules["min"].toDouble()) {
                        validation.isValid = false;
                        validation.errors.append(QString("参数 %1 值太小，最小值: %2").arg(paramName).arg(rules["min"].toString()));
                    }
                    if (rules.contains("max") && value > rules["max"].toDouble()) {
                        validation.isValid = false;
                        validation.errors.append(QString("参数 %1 值太大，最大值: %2").arg(paramName).arg(rules["max"].toString()));
                    }
                }
            }
        }
    }
    
    return validation;
}

QVariantMap CommandSystem::applyDefaultParameters(const CommandDefinition& definition,
                                                 const QVariantMap& parameters)
{
    QVariantMap result = parameters;
    
    // 应用默认值（如果定义中有默认参数）
    // 这里可以扩展以支持更复杂的默认值逻辑
    
    return result;
}

void CommandSystem::recordCommandHistory(const BuiltCommand& command)
{
    if (!m_historyEnabled) return;
    
    auto& history = m_commandHistory[command.deviceId];
    history.append(command);
    
    // 限制历史记录大小
    while (history.size() > m_maxHistorySize) {
        history.removeFirst();
    }
}

void CommandSystem::updateStatistics(const QString& deviceId, const QString& commandId, bool success)
{
    // 更新全局统计
    m_globalStats["totalCommands"] = m_globalStats["totalCommands"].toInt() + 1;
    if (success) {
        m_globalStats["successfulCommands"] = m_globalStats["successfulCommands"].toInt() + 1;
    } else {
        m_globalStats["failedCommands"] = m_globalStats["failedCommands"].toInt() + 1;
    }
    
    // 更新设备统计
    auto& deviceStats = m_deviceStats[deviceId];
    if (deviceStats.isEmpty()) {
        deviceStats["deviceId"] = deviceId;
        deviceStats["totalCommands"] = 0;
        deviceStats["successfulCommands"] = 0;
        deviceStats["failedCommands"] = 0;
        deviceStats["commandCounts"] = QVariantMap();
    }
    
    deviceStats["totalCommands"] = deviceStats["totalCommands"].toInt() + 1;
    if (success) {
        deviceStats["successfulCommands"] = deviceStats["successfulCommands"].toInt() + 1;
    } else {
        deviceStats["failedCommands"] = deviceStats["failedCommands"].toInt() + 1;
    }
    
    // 更新指令计数
    QVariantMap commandCounts = deviceStats["commandCounts"].toMap();
    commandCounts[commandId] = commandCounts[commandId].toInt() + 1;
    deviceStats["commandCounts"] = commandCounts;
}

ParsedCommand CommandSystem::selectAndParse(const QString& deviceId, 
                                           const QString& commandId,
                                           const QByteArray& data)
{
    // 智能选择解析方法
    
    // 首先尝试JSON解析
    if (data.trimmed().startsWith('{') || data.trimmed().startsWith('[')) {
        auto result = CommandParser::parseJsonResponse(deviceId, commandId, data);
        if (result.isValid) {
            emit commandParsed(result);
            return result;
        }
    }
    
    // 尝试文本解析（包含=或:的行）
    QString text = QString::fromUtf8(data);
    if (text.contains('=') || text.contains(':')) {
        auto result = CommandParser::parseTextResponse(deviceId, commandId, data);
        if (result.isValid) {
            emit commandParsed(result);
            return result;
        }
    }
    
    // 最后使用二进制解析
    auto result = CommandParser::parseBinaryResponse(deviceId, commandId, data);
    emit commandParsed(result);
    return result;
}

// === 工厂实现 ===

QSharedPointer<ICommandSystem> CommandSystemFactory::createCommandSystem(
    QSharedPointer<Registry::IDeviceRegistry> deviceRegistry)
{
    return QSharedPointer<ICommandSystem>(new CommandSystem(deviceRegistry));
}

} // namespace Command
} // namespace Communication
} // namespace LA

