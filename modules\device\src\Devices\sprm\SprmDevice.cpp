/**
 * @file SprmDevice.cpp
 * @brief SPRM设备具体实现类 - 四层架构统一设备
 */

#include "SprmDevice.h"
#include <QDebug>
#include <QJsonDocument>
#include <QJsonObject>
#include <QFile>
#include <QStandardPaths>
#include <QDir>
#include <QDateTime>
#include <QThread>

namespace LA::Device::Devices {

SprmDevice::SprmDevice(const QString& deviceModel, QObject* parent)
    : LA::Device::Core::IDevice(parent)
    , m_deviceModel(deviceModel)
    , m_initialized(false)
    , m_currentEnvironment("indoor")
    , m_sprmDriver(nullptr)
    , m_rangingCapability(nullptr)
    , m_kalmanFilter(nullptr)
    , m_scriptE<PERSON>ine(nullptr)
{
    // 设置默认配置
    m_rangingConfig["mode"] = "single";
    m_rangingConfig["average_samples"] = 3;
    m_rangingConfig["accuracy_threshold"] = 1.0;
    m_rangingConfig["timeout_ms"] = 5000;
    m_rangingConfig["continuous_interval_ms"] = 100;
    m_rangingConfig["min_range"] = 50.0;
    m_rangingConfig["max_range"] = 2000.0;
    m_rangingConfig["enable_filtering"] = true;
    m_rangingConfig["enable_validation"] = true;

    m_filteringConfig["process_noise"] = 0.01;
    m_filteringConfig["measurement_noise"] = 0.1;
    m_filteringConfig["initial_covariance"] = 1.0;

    qDebug() << "[SprmDevice] Created SPRM device:" << m_deviceModel;
}

bool SprmDevice::initialize(const QVariantMap& config) {
    if (m_initialized) {
        qDebug() << "[SprmDevice] Device already initialized";
        return true;
    }

    qDebug() << "[SprmDevice] Initializing device with config:" << config;

    // 保存配置
    m_deviceConfig = config;

    // 初始化四层架构组件
    if (!initializeComponents()) {
        qCritical() << "[SprmDevice] Failed to initialize components";
        return false;
    }

    // 应用配置
    if (config.contains("ranging")) {
        updateRangingConfig(config["ranging"].toMap());
    }

    if (config.contains("filtering")) {
        updateFilteringConfig(config["filtering"].toMap());
    }

    if (config.contains("environment")) {
        QString env = config["environment"].toString();
        if (!env.isEmpty()) {
            m_currentEnvironment = env;
            // 环境配置将在连接时应用
        }
    }

    // 加载脚本
    if (config.contains("script_file")) {
        QString scriptFile = config["script_file"].toString();
        if (!loadDeviceScript(scriptFile)) {
            qWarning() << "[SprmDevice] Failed to load device script:" << scriptFile;
        }
    }

    m_initialized = true;
    logDeviceEvent("device_initialized", config);

    qDebug() << "[SprmDevice] Device initialized successfully";
    return true;
}

bool SprmDevice::connect() {
    if (!m_initialized) {
        emit deviceError("Device not initialized");
        return false;
    }

    qDebug() << "[SprmDevice] Connecting to device...";

    // 连接驱动层
    if (!m_sprmDriver->connect()) {
        emit deviceError("Failed to connect driver");
        return false;
    }

    // 应用环境配置
    if (m_deviceConfig.contains("environments")) {
        QVariantMap environments = m_deviceConfig["environments"].toMap();
        if (environments.contains(m_currentEnvironment)) {
            applyEnvironmentConfig(environments[m_currentEnvironment].toMap());
        }
    }

    // 执行脚本初始化事件
    executeScriptEvent("device_connected", {{"model", m_deviceModel}});

    updateDeviceStatus("connected");
    logDeviceEvent("device_connected");

    qDebug() << "[SprmDevice] Device connected successfully";
    return true;
}

bool SprmDevice::disconnect() {
    qDebug() << "[SprmDevice] Disconnecting device...";

    // 停止连续测距
    stopContinuousRanging();

    // 执行脚本清理事件
    executeScriptEvent("device_disconnecting", {{"model", m_deviceModel}});

    // 断开驱动层
    if (m_sprmDriver) {
        m_sprmDriver->disconnect();
    }

    updateDeviceStatus("disconnected");
    logDeviceEvent("device_disconnected");

    qDebug() << "[SprmDevice] Device disconnected";
    return true;
}

QVariantMap SprmDevice::sendCommand(const QString& command, const QVariantMap& params) {
    if (!isConnected()) {
        return createErrorResult("Device not connected");
    }

    qDebug() << "[SprmDevice] Sending command:" << command << "with params:" << params;

    // 首先执行脚本预处理
    QVariantMap scriptParams = params;
    scriptParams["command"] = command;
    scriptParams["device_model"] = m_deviceModel;

    auto scriptResult = executeScriptEvent("before_command", scriptParams);
    if (!scriptResult["success"].toBool()) {
        qWarning() << "[SprmDevice] Script preprocessing failed:" << scriptResult["error"];
        // 继续执行，但记录警告
    } else {
        // 使用脚本处理后的参数
        if (scriptResult.contains("modified_params")) {
            scriptParams = scriptResult["modified_params"].toMap();
        }
    }

    // 处理SPRM特定命令
    QVariantMap result;
    if (command == "ranging" || command == "measure") {
        QString mode = params.value("mode", "single").toString();
        result = performRanging(mode, scriptParams);
    } else if (command == "start_continuous") {
        int interval = params.value("interval", 100).toInt();
        result = startContinuousRanging(interval);
    } else if (command == "stop_continuous") {
        result = stopContinuousRanging();
    } else if (command == "calibrate") {
        double refDistance = params.value("reference_distance", 1000.0).toDouble();
        QString calibType = params.value("calibration_type", "single_point").toString();
        result = calibrateDevice(refDistance, calibType);
    } else if (command == "set_laser_power") {
        double power = params.value("power", 5.0).toDouble();
        result = setLaserPower(power);
    } else if (command == "get_status") {
        result = getDeviceStatus();
    } else {
        // 未识别的命令
        result = createErrorResult("Unknown command: " + command);
    }

    // 执行脚本后处理
    QVariantMap postProcessParams;
    postProcessParams["command"] = command;
    postProcessParams["result"] = result;
    postProcessParams["original_params"] = params;

    auto postScriptResult = executeScriptEvent("after_command", postProcessParams);
    if (postScriptResult["success"].toBool() && postScriptResult.contains("modified_result")) {
        result = postScriptResult["modified_result"].toMap();
    }

    logDeviceEvent("command_executed", {{"command", command}, {"success", result["success"]}});
    return result;
}

bool SprmDevice::isConnected() const {
    return m_sprmDriver && m_sprmDriver->isConnected();
}

QVariantMap SprmDevice::performRanging(const QString& mode, const QVariantMap& params) {
    if (!m_rangingCapability) {
        return createErrorResult("Ranging capability not available");
    }

    qDebug() << "[SprmDevice] Performing ranging in mode:" << mode;

    // 根据模式选择合适的测距方法
    QVariantMap result;
    if (mode == "single") {
        result = m_rangingCapability->executeCapability("single_measurement", params, m_sprmDriver);
    } else if (mode == "continuous") {
        result = m_rangingCapability->executeCapability("continuous_measurement", params, m_sprmDriver);
    } else if (mode == "average") {
        result = m_rangingCapability->executeCapability("average_measurement", params, m_sprmDriver);
    } else if (mode == "multipoint") {
        result = m_rangingCapability->executeCapability("multipoint_measurement", params, m_sprmDriver);
    } else {
        result = createErrorResult(QString("Unknown ranging mode: %1").arg(mode));
    }

    // 应用滤波策略（如果启用）
    if (result["success"].toBool() && m_rangingConfig["enable_filtering"].toBool() && m_kalmanFilter) {
        QVariantMap filterInput;
        filterInput["measurement"] = result["distance"];
        filterInput["timestamp"] = result["timestamp"];

        auto filterResult = m_kalmanFilter->executeStrategy(filterInput);
        if (filterResult["success"].toBool()) {
            result["filtered_distance"] = filterResult["filtered_value"];
            result["kalman_gain"] = filterResult["kalman_gain"];
        }
    }

    // 触发信号
    if (result["success"].toBool()) {
        emit rangingCompleted(result);
    } else {
        emit deviceError(result["error"].toString());
    }

    return result;
}

QVariantMap SprmDevice::startContinuousRanging(int interval) {
    if (!m_rangingCapability) {
        return createErrorResult("Ranging capability not available");
    }

    qDebug() << "[SprmDevice] Starting continuous ranging with interval:" << interval;

    QVariantMap params;
    params["interval"] = interval;

    auto result = m_rangingCapability->executeCapability("continuous_measurement", params, m_sprmDriver);

    if (result["success"].toBool()) {
        // 连接连续数据信号
        QObject::connect(m_rangingCapability, &Capability::LaserRangingCapability::continuousMeasurementData,
                this, &SprmDevice::onContinuousRangingData, Qt::UniqueConnection);

        executeScriptEvent("continuous_ranging_started", params);
    }

    return result;
}

QVariantMap SprmDevice::stopContinuousRanging() {
    if (!m_rangingCapability) {
        return createErrorResult("Ranging capability not available");
    }

    qDebug() << "[SprmDevice] Stopping continuous ranging";

    auto result = m_rangingCapability->executeCapability("stop_measurement", {}, m_sprmDriver);

    if (result["success"].toBool()) {
        // 断开连续数据信号
        QObject::disconnect(m_rangingCapability, &Capability::LaserRangingCapability::continuousMeasurementData,
                   this, &SprmDevice::onContinuousRangingData);

        executeScriptEvent("continuous_ranging_stopped", {});
    }

    return result;
}

QVariantMap SprmDevice::calibrateDevice(double referenceDistance, const QString& calibrationType) {
    if (!m_sprmDriver) {
        return createErrorResult("Driver not available");
    }

    qDebug() << "[SprmDevice] Calibrating device with reference distance:" << referenceDistance;

    QVariantMap params;
    params["reference_distance"] = referenceDistance;
    params["calibration_type"] = calibrationType;

    // 执行脚本预校准处理
    auto preCalibResult = executeScriptEvent("before_calibration", params);
    if (preCalibResult["success"].toBool() && preCalibResult.contains("modified_params")) {
        params = preCalibResult["modified_params"].toMap();
    }

    auto result = m_sprmDriver->sendCommand("CALIBRATE", params);

    // 执行脚本后校准处理
    QVariantMap postParams = params;
    postParams["calibration_result"] = result;
    executeScriptEvent("after_calibration", postParams);

    if (result["success"].toBool()) {
        emit calibrationCompleted(result);
        logDeviceEvent("device_calibrated", params);
    }

    return result;
}

QVariantMap SprmDevice::setLaserPower(double power) {
    if (!m_sprmDriver) {
        return createErrorResult("Driver not available");
    }

    qDebug() << "[SprmDevice] Setting laser power to:" << power;

    // 验证功率范围
    if (power < 1.0 || power > 10.0) {
        return createErrorResult(QString("Invalid laser power: %1 (range: 1.0-10.0 mW)").arg(power));
    }

    QVariantMap params;
    params["power_level"] = power;

    // 执行脚本功率调整处理
    auto scriptResult = executeScriptEvent("laser_power_changing", params);
    if (scriptResult["success"].toBool() && scriptResult.contains("adjusted_power")) {
        power = scriptResult["adjusted_power"].toDouble();
        params["power_level"] = power;
    }

    auto result = m_sprmDriver->sendCommand("SET_LASER_POWER", params);

    if (result["success"].toBool()) {
        m_sprmDriver->setLaserConfig(power);
        logDeviceEvent("laser_power_changed", {{"power", power}});
    }

    return result;
}

QVariantMap SprmDevice::getDeviceStatus() const {
    QVariantMap status;
    status["device_type"] = getDeviceType();
    status["device_model"] = getDeviceModel();
    status["connected"] = isConnected();
    status["initialized"] = m_initialized;
    status["current_environment"] = m_currentEnvironment;

    if (m_sprmDriver) {
        auto driverStatus = m_sprmDriver->sendCommand("GET_STATUS", {});
        if (driverStatus["success"].toBool()) {
            status["driver_status"] = driverStatus;
        }

        status["device_specs"] = m_sprmDriver->getDeviceSpecs();
    }

    if (m_rangingCapability) {
        status["ranging_info"] = m_rangingCapability->getCapabilityInfo();
    }

    if (m_kalmanFilter) {
        status["filter_state"] = m_kalmanFilter->getFilterState();
    }

    // Script engine status
    // TODO: Implement script engine status when available
    status["script_version"] = "1.0";

    status["timestamp"] = QDateTime::currentDateTime().toString();
    return status;
}

QVariantList SprmDevice::getHistoryData(int count) {
    if (m_rangingCapability) {
        return m_rangingCapability->getHistoryData(count);
    }
    return QVariantList();
}

bool SprmDevice::loadConfiguration(const QString& configFile) {
    QFile file(configFile);
    if (!file.open(QIODevice::ReadOnly)) {
        qCritical() << "[SprmDevice] Cannot open config file:" << configFile;
        return false;
    }

    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(file.readAll(), &error);
    if (error.error != QJsonParseError::NoError) {
        qCritical() << "[SprmDevice] JSON parse error:" << error.errorString();
        return false;
    }

    QVariantMap config = doc.object().toVariantMap();
    if (!validateConfiguration(config)) {
        qCritical() << "[SprmDevice] Invalid configuration";
        return false;
    }

    return initialize(config);
}

void SprmDevice::updateRangingConfig(const QVariantMap& config) {
    for (auto it = config.constBegin(); it != config.constEnd(); ++it) {
        m_rangingConfig[it.key()] = it.value();
    }

    if (m_rangingCapability) {
        m_rangingCapability->setRangingConfig(m_rangingConfig);
    }

    qDebug() << "[SprmDevice] Ranging configuration updated:" << config;
}

void SprmDevice::updateFilteringConfig(const QVariantMap& config) {
    for (auto it = config.constBegin(); it != config.constEnd(); ++it) {
        m_filteringConfig[it.key()] = it.value();
    }

    if (m_kalmanFilter) {
        m_kalmanFilter->setStrategyConfig(m_filteringConfig);
    }

    qDebug() << "[SprmDevice] Filtering configuration updated:" << config;
}

QVariantMap SprmDevice::switchEnvironmentMode(const QString& environment) {
    if (m_currentEnvironment == environment) {
        return createErrorResult("Already in environment: " + environment);
    }

    qDebug() << "[SprmDevice] Switching environment from" << m_currentEnvironment << "to" << environment;

    if (!m_deviceConfig.contains("environments")) {
        return createErrorResult("No environment configurations available");
    }

    QVariantMap environments = m_deviceConfig["environments"].toMap();
    if (!environments.contains(environment)) {
        return createErrorResult("Unknown environment: " + environment);
    }

    m_currentEnvironment = environment;
    applyEnvironmentConfig(environments[environment].toMap());

    // 执行脚本环境切换处理
    QVariantMap params;
    params["old_environment"] = m_currentEnvironment;
    params["new_environment"] = environment;
    executeScriptEvent("environment_changed", params);

    logDeviceEvent("environment_switched", params);

    QVariantMap result;
    result["success"] = true;
    result["current_environment"] = m_currentEnvironment;
    result["message"] = QString("Switched to %1 environment").arg(environment);

    return result;
}

bool SprmDevice::loadDeviceScript(const QString& scriptFile) {
    qDebug() << "[SprmDevice] Loading device script:" << scriptFile;
    
    // TODO: Implement script loading when script engine is available
    bool success = true; // Placeholder implementation
    if (success) {
        logDeviceEvent("script_loaded", {{"script_file", scriptFile}});
    }

    return success;
}

QVariantMap SprmDevice::executeScriptEvent(const QString& event, const QVariantMap& params) {
    qDebug() << "[SprmDevice] Executing script event:" << event;
    
    // TODO: Implement script event handling when script engine is available
    QVariantMap result;
    result["success"] = true;
    result["event"] = event;
    result["params"] = params;
    return result;
}

QVariantMap SprmDevice::getDeviceSpecifications() const {
    QVariantMap specs;
    specs["device_type"] = getDeviceType();
    specs["device_model"] = getDeviceModel();
    specs["architecture"] = "four_layer_composite";

    if (m_sprmDriver) {
        specs["hardware_specs"] = m_sprmDriver->getDeviceSpecs();
    }

    specs["ranging_modes"] = QStringList{"single", "continuous", "average", "multipoint"};
    specs["filtering_strategies"] = QStringList{"none", "moving_average", "kalman"};
    specs["calibration_types"] = QStringList{"single_point", "multi_point"};
    specs["supported_environments"] = QStringList{"indoor", "outdoor", "industrial"};

    return specs;
}

void SprmDevice::onRangingCompleted(const QVariantMap& result) {
    emit rangingCompleted(result);
    executeScriptEvent("ranging_completed", result);
}

void SprmDevice::onContinuousRangingData(const QVariantMap& data) {
    emit continuousRangingData(data);
    executeScriptEvent("continuous_ranging_data", data);
}

void SprmDevice::onDeviceError(const QString& error) {
    emit deviceError(error);
    executeScriptEvent("device_error", {{"error", error}});
}

void SprmDevice::onDriverError(const QString& error) {
    qCritical() << "[SprmDevice] Driver error:" << error;
    emit deviceError("Driver error: " + error);
    updateDeviceStatus("error");
}

void SprmDevice::onConnectionStatusChanged(bool connected) {
    QString status = connected ? "connected" : "disconnected";
    updateDeviceStatus(status);
    
    QVariantMap params;
    params["connected"] = connected;
    executeScriptEvent("connection_status_changed", params);
}

bool SprmDevice::initializeComponents() {
    try {
        // 第1层: 初始化驱动
        auto driver = std::make_unique<Driver::SprmA1Driver>(this);
        m_sprmDriver = driver.get();
        
        // 连接驱动信号
        QObject::connect(m_sprmDriver, &Driver::SprmA1Driver::errorOccurred,
                this, &SprmDevice::onDriverError);
        QObject::connect(m_sprmDriver, &Driver::SprmA1Driver::connectionStatusChanged,
                this, &SprmDevice::onConnectionStatusChanged);

        if (!driver->initialize()) {
            qCritical() << "[SprmDevice] Failed to initialize driver";
            return false;
        }
        // Store driver reference (driver is already stored in m_sprmDriver)

        // 第2层: 初始化能力
        auto rangingCapability = std::make_unique<Capability::LaserRangingCapability>(this);
        m_rangingCapability = rangingCapability.get();
        
        // 连接能力信号
        QObject::connect(m_rangingCapability, &Capability::LaserRangingCapability::measurementCompleted,
                this, &SprmDevice::onRangingCompleted);
        QObject::connect(m_rangingCapability, &Capability::LaserRangingCapability::measurementFailed,
                this, &SprmDevice::onDeviceError);

        // Store capability reference (capability is already stored in m_rangingCapability)

        // 第3层: 初始化策略
        auto kalmanFilter = std::make_unique<Strategy::KalmanFilter>(this);
        m_kalmanFilter = kalmanFilter.get();
        
        // Store strategy reference (strategy is already stored in m_kalmanFilter)

        // 第4层: 初始化脚本引擎
        auto scriptEngine = std::make_unique<Script::ScriptEngine>(this);
        
        // 连接脚本引擎信号
        QObject::connect(scriptEngine.get(), &Script::ScriptEngine::scriptError,
                this, &SprmDevice::onDeviceError);

        // 脚本引擎通过loadScript方法间接设置
        // setScriptEngine(std::move(scriptEngine));

        qDebug() << "[SprmDevice] All components initialized successfully";
        return true;
        
    } catch (const std::exception& e) {
        qCritical() << "[SprmDevice] Exception during component initialization:" << e.what();
        return false;
    }
}

void SprmDevice::applyEnvironmentConfig(const QVariantMap& environment) {
    qDebug() << "[SprmDevice] Applying environment config:" << environment;

    // 应用推荐功率
    if (environment.contains("recommended_power")) {
        double power = environment["recommended_power"].toDouble();
        if (power > 0) {
            setLaserPower(power);
        }
    }

    // 应用滤波策略
    if (environment.contains("filter_strategy")) {
        QString strategy = environment["filter_strategy"].toString();
        // TODO: 动态切换滤波策略
        qDebug() << "[SprmDevice] Should switch to filter strategy:" << strategy;
    }

    // 应用校准间隔
    if (environment.contains("calibration_interval")) {
        int interval = environment["calibration_interval"].toInt();
        // TODO: 设置自动校准定时器
        qDebug() << "[SprmDevice] Should set calibration interval:" << interval << "hours";
    }

    // 应用其他环境特定配置
    if (environment.contains("vibration_compensation") && environment["vibration_compensation"].toBool()) {
        // TODO: 启用震动补偿
        qDebug() << "[SprmDevice] Should enable vibration compensation";
    }
}

QVariantMap SprmDevice::processScriptResult(const QVariantMap& result, 
                                          const QString& originalCommand,
                                          const QVariantMap& originalParams) {
    Q_UNUSED(originalCommand)
    Q_UNUSED(originalParams)
    
    // 脚本结果后处理逻辑
    if (!result["success"].toBool()) {
        qWarning() << "[SprmDevice] Script execution failed:" << result["error"];
    }

    return result;
}

bool SprmDevice::validateConfiguration(const QVariantMap& config) {
    // 验证必要的配置项
    QStringList requiredFields = {"basic_info", "hardware_specs", "communication"};
    
    for (const QString& field : requiredFields) {
        if (!config.contains(field)) {
            qCritical() << "[SprmDevice] Missing required configuration field:" << field;
            return false;
        }
    }

    return true;
}

QVariantMap SprmDevice::createErrorResult(const QString& error) {
    QVariantMap result;
    result["success"] = false;
    result["error"] = error;
    result["device_type"] = getDeviceType();
    result["device_model"] = getDeviceModel();
    result["timestamp"] = QDateTime::currentDateTime().toString();
    return result;
}

void SprmDevice::logDeviceEvent(const QString& event, const QVariantMap& data) {
    QVariantMap logData = data;
    logData["event"] = event;
    logData["device_type"] = getDeviceType();
    logData["device_model"] = getDeviceModel();
    logData["timestamp"] = QDateTime::currentDateTime().toString();

    qDebug() << "[SprmDevice] Event:" << event << "Data:" << logData;
    
    // TODO: 发送到日志系统
}

void SprmDevice::updateDeviceStatus(const QString& status) {
    qDebug() << "[SprmDevice] Status changed to:" << status;
    emit deviceStatusChanged(status);
    logDeviceEvent("status_changed", {{"status", status}});
}

// === 增强IDevice接口实现 ===

QString SprmDevice::getSerialNumber() const {
    if (m_sprmDriver) {
        QVariantMap info = m_sprmDriver->getDeviceInfo();
        return info.value("serial_number", "SN-UNKNOWN").toString();
    }
    return "SN-UNKNOWN";
}

QVariantMap SprmDevice::getVersionInfo() const {
    QVariantMap versions;
    versions["hardware"] = "1.2.0";
    versions["firmware"] = "2.1.3";
    versions["software"] = "3.0.1";
    versions["api"] = "1.0.0";
    
    if (m_sprmDriver) {
        QVariantMap driverInfo = m_sprmDriver->getDeviceInfo();
        if (driverInfo.contains("firmware_version")) {
            versions["firmware"] = driverInfo["firmware_version"];
        }
        if (driverInfo.contains("hardware_version")) {
            versions["hardware"] = driverInfo["hardware_version"];
        }
    }
    
    return versions;
}

QStringList SprmDevice::getSupportedCapabilities() const {
    QStringList capabilities;
    capabilities << "ranging" << "calibration" << "data_processing";
    capabilities << "filtering" << "scripting" << "status_monitoring";
    capabilities << "configuration_management" << "error_handling";
    capabilities << "statistics" << "self_test";
    return capabilities;
}

bool SprmDevice::hasCapability(const QString& capability) const {
    return getSupportedCapabilities().contains(capability);
}

QVariantMap SprmDevice::getCapabilityConfig(const QString& capability) const {
    QVariantMap config;
    
    if (capability == "ranging") {
        config = m_rangingConfig;
    } else if (capability == "filtering") {
        config = m_filteringConfig;
    } else if (capability == "calibration") {
        config["reference_distance"] = 1000.0;
        config["calibration_points"] = 3;
        config["accuracy_threshold"] = 1.0;
    } else if (capability == "scripting") {
        config["enabled"] = (m_scriptEngine != nullptr);
        config["script_file"] = m_deviceConfig.value("script_file", "").toString();
    }
    
    return config;
}

QVariantMap SprmDevice::getCurrentConfig() const {
    QVariantMap fullConfig;
    fullConfig["device_config"] = m_deviceConfig;
    fullConfig["ranging_config"] = m_rangingConfig;
    fullConfig["filtering_config"] = m_filteringConfig;
    fullConfig["environment"] = m_currentEnvironment;
    fullConfig["initialized"] = m_initialized;
    return fullConfig;
}

bool SprmDevice::updateConfig(const QVariantMap& config) {
    try {
        if (config.contains("ranging_config")) {
            updateRangingConfig(config["ranging_config"].toMap());
        }
        if (config.contains("filtering_config")) {
            updateFilteringConfig(config["filtering_config"].toMap());
        }
        if (config.contains("environment")) {
            switchEnvironmentMode(config["environment"].toString());
        }
        
        // 合并配置
        for (auto it = config.begin(); it != config.end(); ++it) {
            m_deviceConfig[it.key()] = it.value();
        }
        
        emit configurationChanged("device_config", config);
        return true;
    } catch (...) {
        qCritical() << "[SprmDevice] Failed to update configuration";
        return false;
    }
}

bool SprmDevice::resetToDefaultConfig() {
    try {
        // 重置测距配置
        m_rangingConfig.clear();
        m_rangingConfig["mode"] = "single";
        m_rangingConfig["average_samples"] = 3;
        m_rangingConfig["accuracy_threshold"] = 1.0;
        m_rangingConfig["timeout_ms"] = 5000;
        m_rangingConfig["continuous_interval_ms"] = 100;
        
        // 重置滤波配置
        m_filteringConfig.clear();
        m_filteringConfig["process_noise"] = 0.01;
        m_filteringConfig["measurement_noise"] = 0.1;
        m_filteringConfig["initial_covariance"] = 1.0;
        
        // 重置环境
        m_currentEnvironment = "indoor";
        
        // 清除设备配置
        m_deviceConfig.clear();
        
        emit configurationChanged("reset", QVariant());
        return true;
    } catch (...) {
        return false;
    }
}

QVariantMap SprmDevice::validateConfig(const QVariantMap& config) const {
    QVariantMap result;
    result["valid"] = true;
    QStringList errors;
    
    // 验证测距配置
    if (config.contains("ranging_config")) {
        QVariantMap rangingConfig = config["ranging_config"].toMap();
        if (rangingConfig.contains("timeout_ms")) {
            int timeout = rangingConfig["timeout_ms"].toInt();
            if (timeout < 100 || timeout > 30000) {
                errors << "Invalid timeout_ms: must be between 100 and 30000";
            }
        }
        if (rangingConfig.contains("average_samples")) {
            int samples = rangingConfig["average_samples"].toInt();
            if (samples < 1 || samples > 100) {
                errors << "Invalid average_samples: must be between 1 and 100";
            }
        }
    }
    
    // 验证环境设置
    if (config.contains("environment")) {
        QString env = config["environment"].toString();
        QStringList validEnvs = {"indoor", "outdoor", "industrial"};
        if (!validEnvs.contains(env)) {
            errors << "Invalid environment: must be one of " + validEnvs.join(", ");
        }
    }
    
    if (!errors.isEmpty()) {
        result["valid"] = false;
        result["errors"] = errors;
    }
    
    return result;
}

QVariantMap SprmDevice::performSelfTest() {
    QVariantMap result;
    result["success"] = true;
    result["timestamp"] = QDateTime::currentDateTime().toString();
    
    QVariantList tests;
    
    // 测试驱动器连接
    QVariantMap driverTest;
    driverTest["name"] = "Driver Connection";
    driverTest["result"] = (m_sprmDriver != nullptr);
    driverTest["message"] = m_sprmDriver ? "Driver available" : "Driver not initialized";
    tests << driverTest;
    
    // 测试设备连接
    QVariantMap connectionTest;
    connectionTest["name"] = "Device Connection";
    connectionTest["result"] = isConnected();
    connectionTest["message"] = isConnected() ? "Device connected" : "Device not connected";
    tests << connectionTest;
    
    // 测试能力组件
    QVariantMap capabilityTest;
    capabilityTest["name"] = "Capabilities";
    capabilityTest["result"] = (m_rangingCapability != nullptr);
    capabilityTest["message"] = m_rangingCapability ? "Ranging capability available" : "Ranging capability not initialized";
    tests << capabilityTest;
    
    // 统计成功的测试
    int passedTests = 0;
    for (const QVariant& test : tests) {
        if (test.toMap()["result"].toBool()) {
            passedTests++;
        }
    }
    
    result["tests"] = tests;
    result["passed_tests"] = passedTests;
    result["total_tests"] = tests.size();
    result["success"] = (passedTests == tests.size());
    
    emit selfTestCompleted(result);
    return result;
}

QVariantMap SprmDevice::getStatistics() const {
    QVariantMap stats;
    
    // 基础统计
    stats["device_type"] = getDeviceType();
    stats["device_model"] = getDeviceModel();
    stats["serial_number"] = getSerialNumber();
    stats["uptime_seconds"] = 0; // TODO: 实现运行时间计算
    stats["commands_executed"] = 0; // TODO: 计数器
    stats["errors_count"] = 0; // TODO: 错误计数器
    stats["successful_operations"] = 0; // TODO: 成功操作计数
    
    // 测距统计
    stats["ranging_operations"] = 0; // TODO: 测距次数
    stats["continuous_sessions"] = 0; // TODO: 连续测距会话数
    stats["calibration_count"] = 0; // TODO: 校准次数
    
    // 状态统计
    stats["current_status"] = isConnected() ? "connected" : "disconnected";
    stats["initialized"] = m_initialized;
    stats["current_environment"] = m_currentEnvironment;
    
    return stats;
}

QList<QVariantMap> SprmDevice::getRecentErrors(int count) const {
    Q_UNUSED(count)
    // TODO: 实现错误历史存储
    QList<QVariantMap> errors;
    
    // 示例错误
    if (!isConnected()) {
        QVariantMap error;
        error["timestamp"] = QDateTime::currentDateTime().toString();
        error["type"] = "connection";
        error["message"] = "Device not connected";
        error["severity"] = "warning";
        errors << error;
    }
    
    return errors;
}

bool SprmDevice::clearErrors() {
    // TODO: 清除错误历史
    qDebug() << "[SprmDevice] Cleared error history";
    return true;
}

bool SprmDevice::attemptRecovery() {
    qDebug() << "[SprmDevice] Attempting device recovery";
    
    try {
        // 1. 断开连接
        if (isConnected()) {
            disconnect();
            QThread::msleep(1000); // 等待断开
        }
        
        // 2. 重新连接
        if (!connect()) {
            qCritical() << "[SprmDevice] Recovery failed: cannot reconnect";
            return false;
        }
        
        // 3. 执行自检
        QVariantMap selfTestResult = performSelfTest();
        if (!selfTestResult["success"].toBool()) {
            qCritical() << "[SprmDevice] Recovery failed: self-test failed";
            return false;
        }
        
        qDebug() << "[SprmDevice] Recovery successful";
        emit deviceStatusChanged("recovered");
        return true;
        
    } catch (...) {
        qCritical() << "[SprmDevice] Recovery failed: exception occurred";
        return false;
    }
}

// SprmDeviceFactory 实现

std::unique_ptr<SprmDevice> SprmDeviceFactory::createDevice(const QString& model, const QVariantMap& config) {
    auto device = std::make_unique<SprmDevice>(model);
    
    if (!config.isEmpty()) {
        if (!device->initialize(config)) {
            qCritical() << "[SprmDeviceFactory] Failed to initialize device:" << model;
            return nullptr;
        }
    }
    
    qDebug() << "[SprmDeviceFactory] Created device:" << model;
    return device;
}

std::unique_ptr<SprmDevice> SprmDeviceFactory::createFromConfig(const QString& configFile) {
    QFile file(configFile);
    if (!file.open(QIODevice::ReadOnly)) {
        qCritical() << "[SprmDeviceFactory] Cannot open config file:" << configFile;
        return nullptr;
    }

    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(file.readAll(), &error);
    if (error.error != QJsonParseError::NoError) {
        qCritical() << "[SprmDeviceFactory] JSON parse error:" << error.errorString();
        return nullptr;
    }

    QVariantMap config = doc.object().toVariantMap();
    
    // 从配置中获取设备型号
    QString model = "SPRM-A1"; // 默认型号
    if (config.contains("basic_info")) {
        QVariantMap basicInfo = config["basic_info"].toMap();
        if (basicInfo.contains("model")) {
            model = basicInfo["model"].toString();
        }
    }

    return createDevice(model, config);
}

QStringList SprmDeviceFactory::getSupportedModels() {
    return {"SPRM-A1", "SPRM-A2", "SPRM-A3"};
}

// SprmDeviceManager 实现

SprmDeviceManager::SprmDeviceManager(QObject* parent)
    : QObject(parent)
{
    qDebug() << "[SprmDeviceManager] Created device manager";
}

bool SprmDeviceManager::registerDevice(const QString& deviceId, std::unique_ptr<SprmDevice> device) {
    if (m_devices.find(deviceId) != m_devices.end()) {
        qWarning() << "[SprmDeviceManager] Device already registered:" << deviceId;
        return false;
    }

    qDebug() << "[SprmDeviceManager] Registering device:" << deviceId;
    
    // 连接设备信号
    QObject::connect(device.get(), &SprmDevice::deviceStatusChanged,
            this, [this, deviceId](const QString& status) {
                if (status == "connected") {
                    emit deviceConnected(deviceId);
                } else if (status == "disconnected") {
                    emit deviceDisconnected(deviceId);
                }
            });

    m_devices[deviceId] = std::move(device);
    emit deviceRegistered(deviceId);
    
    return true;
}

SprmDevice* SprmDeviceManager::getDevice(const QString& deviceId) {
    auto it = m_devices.find(deviceId);
    return (it != m_devices.end()) ? it->second.get() : nullptr;
}

bool SprmDeviceManager::removeDevice(const QString& deviceId) {
    auto it = m_devices.find(deviceId);
    if (it == m_devices.end()) {
        return false;
    }

    qDebug() << "[SprmDeviceManager] Removing device:" << deviceId;
    
    // 断开设备连接
    it->second->disconnect();
    
    m_devices.erase(it);
    emit deviceRemoved(deviceId);
    
    return true;
}

QStringList SprmDeviceManager::getDeviceIds() const {
    QStringList ids;
    for (const auto& pair : m_devices) {
        ids << pair.first;
    }
    return ids;
}

QVariantMap SprmDeviceManager::connectAllDevices() {
    QVariantMap result;
    result["success"] = true;
    result["connected_count"] = 0;
    result["failed_count"] = 0;
    QVariantList failures;

    for (const auto& pair : m_devices) {
        const QString& deviceId = pair.first;
        SprmDevice* device = pair.second.get();

        if (device->connect()) {
            result["connected_count"] = result["connected_count"].toInt() + 1;
        } else {
            result["failed_count"] = result["failed_count"].toInt() + 1;
            failures.append(deviceId);
        }
    }

    if (!failures.isEmpty()) {
        result["failures"] = failures;
        result["success"] = (result["failed_count"].toInt() == 0);
    }

    return result;
}

QVariantMap SprmDeviceManager::disconnectAllDevices() {
    QVariantMap result;
    result["success"] = true;
    result["disconnected_count"] = 0;
    
    for (const auto& pair : m_devices) {
        pair.second->disconnect();
        result["disconnected_count"] = result["disconnected_count"].toInt() + 1;
    }

    return result;
}

} // namespace LA::Device::Devices