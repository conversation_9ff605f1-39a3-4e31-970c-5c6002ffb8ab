#ifndef FEATURE_CONFIG_WIDGET_H
#define FEATURE_CONFIG_WIDGET_H

#include <QWidget>
#include <QTreeWidget>
#include <QTreeWidgetItem>
#include <QJsonObject>
#include <QJsonArray>
#include <QSplitter>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QToolBar>
#include <QStatusBar>
#include <QPushButton>
#include <QLabel>
#include <QComboBox>
#include "LA/Themes/ThemeManager.h"

class FeatureTreeWidget;
class FeaturePropertyPanel;
class PermissionManager;

/**
 * @brief 功能配置主界面
 * 
 * 提供功能项的可视化编辑界面，支持：
 * - 树状结构展示功能项
 * - 拖拽操作重新排序
 * - 参数属性编辑面板
 * - 实时配置验证
 * - 多角色权限控制
 */
class FeatureConfigWidget : public QWidget
{
    Q_OBJECT

public:
    explicit FeatureConfigWidget(QWidget *parent = nullptr);
    virtual ~FeatureConfigWidget();

    /**
     * @brief 设置配置数据
     * @param configData JSON格式的配置数据
     */
    void setConfigData(const QJsonObject& configData);
    
    /**
     * @brief 获取当前配置数据
     * @return JSON格式的配置数据
     */
    QJsonObject getConfigData() const;
    
    /**
     * @brief 设置权限管理器
     * @param permissionManager 权限管理器实例
     */
    void setPermissionManager(PermissionManager* permissionManager);
    
    /**
     * @brief 刷新UI显示
     */
    void refreshUI();
    
    /**
     * @brief 更新权限状态
     */
    void updatePermissionState();

public slots:
    void onAddFeature();
    void onRemoveFeature();
    void onFeatureUp();
    void onFeatureDown();
    void onExpandAll();
    void onCollapseAll();
    void onSaveConfig();
    void onLoadConfig();
    void onSyncToHeader();
    void onImportFromHeader();

signals:
    /**
     * @brief 配置数据发生变更
     */
    void configChanged();
    
    /**
     * @brief 请求同步到文件
     */
    void syncRequested();

private slots:
    void onFeatureSelectionChanged();
    void onFeatureItemChanged(QTreeWidgetItem* item, int column);
    void onFeaturePropertyChanged();
    void onTargetChanged(const QString& targetName);

private:
    void setupUI();
    void setupToolBar();
    void setupStatusBar();
    void setupConnections();
    
    void updateFeatureTree();
    void updatePropertyPanel();
    void updateStatusInfo();
    
    // 数据处理方法
    void loadFeaturesFromJson(const QJsonArray& features);
    void loadChildFeatures(QTreeWidgetItem* parentItem, const QJsonArray& children);
    QJsonArray saveFeaturesToJson() const;
    void saveChildFeatures(const QTreeWidgetItem* parentItem, QJsonArray& children) const;
    
    // 树形控件操作
    QTreeWidgetItem* createFeatureItem(const QJsonObject& feature);
    void updateFeatureItem(QTreeWidgetItem* item, const QJsonObject& feature);
    QJsonObject getFeatureFromItem(const QTreeWidgetItem* item) const;
    
    // 权限检查
    bool checkEditPermission() const;
    bool checkDeletePermission() const;
    bool checkSyncPermission() const;

private:
    // UI组件
    QSplitter* m_mainSplitter;
    QWidget* m_leftPanel;
    QWidget* m_rightPanel;
    
    // 工具栏和状态栏
    QToolBar* m_toolBar;
    QStatusBar* m_statusBar;
    QComboBox* m_targetSelector;
    QLabel* m_statusLabel;
    QLabel* m_permissionLabel;
    
    // 主要功能组件
    FeatureTreeWidget* m_featureTree;
    FeaturePropertyPanel* m_propertyPanel;
    
    // 操作按钮
    QPushButton* m_addFeatureBtn;
    QPushButton* m_removeFeatureBtn;
    QPushButton* m_featureUpBtn;
    QPushButton* m_featureDownBtn;
    QPushButton* m_expandAllBtn;
    QPushButton* m_collapseAllBtn;
    QPushButton* m_saveBtn;
    QPushButton* m_loadBtn;
    QPushButton* m_syncBtn;
    QPushButton* m_importBtn;
    
    // 数据
    QJsonObject m_configData;
    QString m_currentTarget;
    PermissionManager* m_permissionManager;
    
    // 状态
    bool m_isLoading;
    bool m_hasUnsavedChanges;
};

/**
 * @brief 增强的功能树控件
 * 
 * 支持拖拽操作的功能项树控件
 */
class FeatureTreeWidget : public QTreeWidget
{
    Q_OBJECT

public:
    explicit FeatureTreeWidget(QWidget* parent = nullptr);

protected:
    void dragEnterEvent(QDragEnterEvent* event) override;
    void dragMoveEvent(QDragMoveEvent* event) override;
    void dropEvent(QDropEvent* event) override;
    void startDrag(Qt::DropActions supportedActions) override;

signals:
    void featuresMoved();

private:
    bool canDropOn(QTreeWidgetItem* item, QTreeWidgetItem* target) const;
    void moveFeature(QTreeWidgetItem* item, QTreeWidgetItem* newParent, int index);
};

/**
 * @brief 功能属性编辑面板
 * 
 * 显示和编辑选中功能项的详细属性
 */
class FeaturePropertyPanel : public QWidget
{
    Q_OBJECT

public:
    explicit FeaturePropertyPanel(QWidget* parent = nullptr);

    void setFeature(const QJsonObject& feature);
    QJsonObject getFeature() const;
    
    void setTarget(const QString& target);
    void setReadOnly(bool readOnly);
    
    void clear();

signals:
    void propertyChanged();

private slots:
    void onPropertyValueChanged();

private:
    void setupUI();
    void updateUI();
    void createPropertyEditors(const QJsonObject& feature);
    
private:
    QVBoxLayout* m_layout;
    QWidget* m_scrollContent;
    
    QJsonObject m_currentFeature;
    QString m_currentTarget;
    bool m_readOnly;
    bool m_updating;
    
    // 动态创建的编辑器映射
    QHash<QString, QWidget*> m_propertyEditors;
};

#endif // FEATURE_CONFIG_WIDGET_H