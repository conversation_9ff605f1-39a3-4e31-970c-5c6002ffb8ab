#pragma once

/**
 * @file ConnectionFactory.h
 * @brief 连接工厂类定义
 * 
 * 提供各种连接类型的工厂实现
 */

#include "IConnection.h"
#include "ConnectionTypes.h"
#include "../DataStructures/CommunicationAttributes.h"
#include <memory>

namespace LA {
namespace Communication {
namespace Connection {

/**
 * @brief 串口连接工厂
 */
class SerialConnectionFactory : public IConnectionFactory {
public:
    SerialConnectionFactory() = default;
    
    // IConnectionFactory接口实现
    std::shared_ptr<IConnection> createConnection(const ConnectionConfig& config) override;
    bool supportsConnection(const ConnectionConfig& config) const override;
};

/**
 * @brief 网络连接工厂
 */
class NetworkConnectionFactory : public IConnectionFactory {
public:
    NetworkConnectionFactory() = default;
    
    // IConnectionFactory接口实现
    std::shared_ptr<IConnection> createConnection(const ConnectionConfig& config) override;
    bool supportsConnection(const ConnectionConfig& config) const override;
};

} // namespace Connection
} // namespace Communication
} // namespace LA