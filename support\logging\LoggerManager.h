#pragma once

#include "logger/ILogger.h"
#include <QObject>
#include <QMutex>
#include <QStringList>
#include <memory>
#include <map>

namespace LA {
namespace Support {
namespace Logging {

class Logger;

/**
 * @brief 日志管理器
 * 
 * 负责管理所有日志记录器的生命周期
 */
class LoggerManager : public QObject {
    Q_OBJECT

public:
    static LoggerManager& getInstance();

    /**
     * @brief 创建日志记录器
     */
    std::shared_ptr<Logger> createLogger(const QString& name, 
                                        const LoggerConfig& config = LoggerConfig());

    /**
     * @brief 获取日志记录器
     */
    std::shared_ptr<Logger> getLogger(const QString& name);

    /**
     * @brief 获取根日志记录器
     */
    std::shared_ptr<Logger> getRootLogger();

    /**
     * @brief 销毁日志记录器
     */
    void destroyLogger(const QString& name);

    /**
     * @brief 销毁所有日志记录器
     */
    void destroyAllLoggers();

    /**
     * @brief 设置全局日志配置
     */
    void setGlobalConfig(const LoggerConfig& config);

    /**
     * @brief 获取全局日志配置
     */
    LoggerConfig getGlobalConfig() const;

    /**
     * @brief 刷新所有日志记录器
     */
    void flushAll();

    /**
     * @brief 获取所有日志记录器名称
     */
    QStringList getAllLoggerNames() const;

    /**
     * @brief 获取统计信息
     */
    QVariantMap getStatistics() const;

private:
    LoggerManager();
    ~LoggerManager();

    // 禁用拷贝构造和赋值
    LoggerManager(const LoggerManager&) = delete;
    LoggerManager& operator=(const LoggerManager&) = delete;

    void createRootLogger();

private:
    std::map<QString, std::shared_ptr<Logger>> m_loggers;       // 日志记录器映射
    std::shared_ptr<Logger> m_rootLogger;                       // 根日志记录器
    LoggerConfig m_globalConfig;                                // 全局日志配置
    mutable QMutex m_mutex;                                     // 线程安全锁
};

}  // namespace Logging
}  // namespace Support
}  // namespace LA