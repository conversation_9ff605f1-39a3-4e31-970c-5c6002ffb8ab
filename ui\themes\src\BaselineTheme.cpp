#include "BaselineTheme.h"
#include <LA/Themes/ThemeManager.h>
#include <QDebug>

namespace LA {
namespace Themes {

BaselineTheme::BaselineTheme(QObject* parent)
    : QObject(parent)
{
}

void BaselineTheme::applyBaselineTheme(ThemeManager* themeManager)
{
    if (!themeManager) {
        qDebug() << "BaselineTheme::applyBaselineTheme() - themeManager 为空指针!";
        return;
    }

    qDebug() << "BaselineTheme::applyBaselineTheme() - 开始配置 Baseline 主题";

    // 设置 Baseline 主题的配色方案 - 基于 Obsidian Baseline 风格
    qDebug() << "设置 Baseline 配色方案...";
    setupBaselineColors(themeManager);
    
    qDebug() << "设置 Baseline 尺寸系统...";
    setupBaselineMetrics(themeManager);
    
    qDebug() << "设置 Baseline 字体系统...";
    setupBaselineFonts(themeManager);
    
    qDebug() << "设置 Baseline 组件模板...";
    setupBaselineComponentTemplates(themeManager);
    
    qDebug() << "BaselineTheme::applyBaselineTheme() - Baseline 主题配置完成";
}

void BaselineTheme::setupBaselineColors(ThemeManager* themeManager)
{
    // Baseline 暗色主题 - 基于 Obsidian 风格的现代暗色配色
    themeManager->setSemanticColor(ThemeManager::ColorRole::Primary, QColor("#7c3aed"));      // 紫色主色
    themeManager->setSemanticColor(ThemeManager::ColorRole::PrimaryVariant, QColor("#5b21b6"));
    themeManager->setSemanticColor(ThemeManager::ColorRole::Secondary, QColor("#059669"));     // 翠绿色
    themeManager->setSemanticColor(ThemeManager::ColorRole::SecondaryVariant, QColor("#047857"));
    themeManager->setSemanticColor(ThemeManager::ColorRole::Accent, QColor("#f97316"));        // 橙色强调

    // 暗色背景系统 - Obsidian 风格
    themeManager->setSemanticColor(ThemeManager::ColorRole::Background, QColor("#1e1e1e"));           // 主背景 - 深色
    themeManager->setSemanticColor(ThemeManager::ColorRole::BackgroundAlternate, QColor("#2d2d30")); // 备用背景
    themeManager->setSemanticColor(ThemeManager::ColorRole::BackgroundAccent, QColor("#252526"));     // 强调背景
    themeManager->setSemanticColor(ThemeManager::ColorRole::Surface, QColor("#2d2d30"));              // 表面色
    themeManager->setSemanticColor(ThemeManager::ColorRole::SurfaceElevated, QColor("#3e3e42"));      // 提升表面
    themeManager->setSemanticColor(ThemeManager::ColorRole::SurfaceDisabled, QColor("#37373d"));      // 禁用表面

    // 暗色文字系统 - 高对比度暗色主题
    themeManager->setSemanticColor(ThemeManager::ColorRole::TextPrimary, QColor("#cccccc"));      // 主文字 - 亮灰
    themeManager->setSemanticColor(ThemeManager::ColorRole::TextSecondary, QColor("#9ca3af"));    // 次文字 - 中灰  
    themeManager->setSemanticColor(ThemeManager::ColorRole::TextAccent, QColor("#a78bfa"));       // 强调文字 - 紫色
    themeManager->setSemanticColor(ThemeManager::ColorRole::TextMuted, QColor("#6b7280"));        // 静音文字
    themeManager->setSemanticColor(ThemeManager::ColorRole::TextDisabled, QColor("#4b5563"));     // 禁用文字
    themeManager->setSemanticColor(ThemeManager::ColorRole::TextOnPrimary, QColor("#ffffff"));    // 主色上文字
    themeManager->setSemanticColor(ThemeManager::ColorRole::TextOnSecondary, QColor("#ffffff"));  // 次色上文字
    themeManager->setSemanticColor(ThemeManager::ColorRole::TextOnAccent, QColor("#ffffff"));     // 强调色上文字

    // 暗色边框系统 - 微妙的分隔
    themeManager->setSemanticColor(ThemeManager::ColorRole::BorderPrimary, QColor("#3e3e42"));    // 主边框
    themeManager->setSemanticColor(ThemeManager::ColorRole::BorderSecondary, QColor("#2d2d30"));  // 次边框
    themeManager->setSemanticColor(ThemeManager::ColorRole::BorderAccent, QColor("#7c3aed"));     // 强调边框
    themeManager->setSemanticColor(ThemeManager::ColorRole::BorderFocus, QColor("#a78bfa"));      // 焦点边框

    // 暗色状态系统 - 适配暗色主题的状态颜色
    themeManager->setSemanticColor(ThemeManager::ColorRole::Success, QColor("#22c55e"));          // 成功 - 明亮绿
    themeManager->setSemanticColor(ThemeManager::ColorRole::Warning, QColor("#f59e0b"));          // 警告 - 明亮橙
    themeManager->setSemanticColor(ThemeManager::ColorRole::Error, QColor("#ef4444"));            // 错误 - 明亮红
    themeManager->setSemanticColor(ThemeManager::ColorRole::Info, QColor("#3b82f6"));             // 信息 - 明亮蓝
    
    themeManager->setSemanticColor(ThemeManager::ColorRole::SuccessBackground, QColor("#14532d"));  // 成功背景 - 深绿
    themeManager->setSemanticColor(ThemeManager::ColorRole::WarningBackground, QColor("#451a03"));  // 警告背景 - 深橙
    themeManager->setSemanticColor(ThemeManager::ColorRole::ErrorBackground, QColor("#450a0a"));    // 错误背景 - 深红
    themeManager->setSemanticColor(ThemeManager::ColorRole::InfoBackground, QColor("#1e3a8a"));     // 信息背景 - 深蓝

    // 暗色交互系统 - 暗色主题交互反馈
    themeManager->setSemanticColor(ThemeManager::ColorRole::Hover, QColor("#8b5cf6"));              // 悬停 - 亮紫
    themeManager->setSemanticColor(ThemeManager::ColorRole::HoverBackground, QColor("#374151"));     // 悬停背景
    themeManager->setSemanticColor(ThemeManager::ColorRole::Pressed, QColor("#6d28d9"));            // 按下
    themeManager->setSemanticColor(ThemeManager::ColorRole::PressedBackground, QColor("#4b5563"));  // 按下背景
    themeManager->setSemanticColor(ThemeManager::ColorRole::Selected, QColor("#7c3aed"));           // 选中
    themeManager->setSemanticColor(ThemeManager::ColorRole::SelectedBackground, QColor("#422006")); // 选中背景
    themeManager->setSemanticColor(ThemeManager::ColorRole::Focus, QColor("#a78bfa"));              // 焦点
    themeManager->setSemanticColor(ThemeManager::ColorRole::FocusBackground, QColor("#3730a3"));    // 焦点背景

    // 暗色控件专用配色
    themeManager->setSemanticColor(ThemeManager::ColorRole::ButtonBackground, QColor("#7c3aed"));  // 紫色按钮
    themeManager->setSemanticColor(ThemeManager::ColorRole::ButtonBorder, QColor("#7c3aed"));
    themeManager->setSemanticColor(ThemeManager::ColorRole::ButtonText, QColor("#ffffff"));
    
    themeManager->setSemanticColor(ThemeManager::ColorRole::InputBackground, QColor("#2d2d30"));   // 暗色输入框
    themeManager->setSemanticColor(ThemeManager::ColorRole::InputBorder, QColor("#3e3e42"));
    themeManager->setSemanticColor(ThemeManager::ColorRole::InputText, QColor("#cccccc"));
    
    themeManager->setSemanticColor(ThemeManager::ColorRole::MenuBackground, QColor("#2d2d30"));    // 暗色菜单
    themeManager->setSemanticColor(ThemeManager::ColorRole::MenuBorder, QColor("#3e3e42"));
    themeManager->setSemanticColor(ThemeManager::ColorRole::MenuText, QColor("#cccccc"));
    
    themeManager->setSemanticColor(ThemeManager::ColorRole::ToolbarBackground, QColor("#252526")); // 暗色工具栏
    themeManager->setSemanticColor(ThemeManager::ColorRole::ToolbarBorder, QColor("#3e3e42"));
    
    themeManager->setSemanticColor(ThemeManager::ColorRole::SidebarBackground, QColor("#252526")); // 暗色侧边栏
    themeManager->setSemanticColor(ThemeManager::ColorRole::SidebarBorder, QColor("#3e3e42"));
    
    themeManager->setSemanticColor(ThemeManager::ColorRole::SplitterBackground, QColor("#1e1e1e"));
    themeManager->setSemanticColor(ThemeManager::ColorRole::SplitterHandle, QColor("#3e3e42"));
}

void BaselineTheme::setupBaselineMetrics(ThemeManager* themeManager)
{
    // Baseline 风格的尺寸系统 - 简约但不失精致
    
    // 圆角 - 适度圆润，现代简约
    themeManager->setMetric(ThemeManager::Metric::BorderRadiusSmall, 6);      // 小圆角
    themeManager->setMetric(ThemeManager::Metric::BorderRadiusMedium, 8);     // 中圆角 - 主要控件
    themeManager->setMetric(ThemeManager::Metric::BorderRadiusLarge, 12);     // 大圆角 - 卡片容器
    
    // 边框 - 极简轻量
    themeManager->setMetric(ThemeManager::Metric::BorderWidthThin, 1);        // 标准边框
    themeManager->setMetric(ThemeManager::Metric::BorderWidthMedium, 1);      // 保持一致的轻量边框
    themeManager->setMetric(ThemeManager::Metric::BorderWidthThick, 2);       // 强调边框
    
    // 间距 - 舒适的呼吸感
    themeManager->setMetric(ThemeManager::Metric::SpacingXSmall, 4);          // 最小间距
    themeManager->setMetric(ThemeManager::Metric::SpacingSmall, 8);           // 小间距
    themeManager->setMetric(ThemeManager::Metric::SpacingMedium, 16);         // 标准间距
    themeManager->setMetric(ThemeManager::Metric::SpacingLarge, 24);          // 大间距
    themeManager->setMetric(ThemeManager::Metric::SpacingXLarge, 32);         // 超大间距
    
    // 内边距 - 舒适的内部空间
    themeManager->setMetric(ThemeManager::Metric::PaddingXSmall, 4);          // 最小内边距
    themeManager->setMetric(ThemeManager::Metric::PaddingSmall, 8);           // 小内边距
    themeManager->setMetric(ThemeManager::Metric::PaddingMedium, 12);         // 标准内边距 - 平衡舒适性
    themeManager->setMetric(ThemeManager::Metric::PaddingLarge, 16);          // 大内边距
    themeManager->setMetric(ThemeManager::Metric::PaddingXLarge, 24);         // 超大内边距
    
    // 图标尺寸 - 清晰的图标系统
    themeManager->setMetric(ThemeManager::Metric::IconSizeSmall, 16);         // 小图标
    themeManager->setMetric(ThemeManager::Metric::IconSizeMedium, 20);        // 标准图标
    themeManager->setMetric(ThemeManager::Metric::IconSizeLarge, 24);         // 大图标
    themeManager->setMetric(ThemeManager::Metric::IconSizeXLarge, 32);        // 超大图标
    
    // 控件高度 - 现代化的触控友好尺寸
    themeManager->setMetric(ThemeManager::Metric::ControlHeightSmall, 32);    // 小控件
    themeManager->setMetric(ThemeManager::Metric::ControlHeightMedium, 40);   // 标准控件 - 触控友好
    themeManager->setMetric(ThemeManager::Metric::ControlHeightLarge, 48);    // 大控件
}

void BaselineTheme::setupBaselineFonts(ThemeManager* themeManager)
{
    // Baseline 字体系统 - 优雅可读
    
    // 系统字体优先级：Inter → SF Pro → Segoe UI → Microsoft YaHei → sans-serif
    QString primaryFont = "Inter, -apple-system, 'SF Pro Display', 'Segoe UI', 'Microsoft YaHei', sans-serif";
    QString monoFont = "'SF Mono', 'Monaco', 'Cascadia Code', 'Consolas', 'Courier New', monospace";
    
    // 基础字体设置
    themeManager->setFont(ThemeManager::FontRole::Default, QFont("Microsoft YaHei", 9));
    themeManager->setFont(ThemeManager::FontRole::Body, QFont("Microsoft YaHei", 9));            // 正文
    themeManager->setFont(ThemeManager::FontRole::Caption, QFont("Microsoft YaHei", 8));         // 说明文字
    
    // 标题字体 - 更好的层次感
    themeManager->setFont(ThemeManager::FontRole::Title, QFont("Microsoft YaHei", 14, QFont::DemiBold));     // 大标题
    themeManager->setFont(ThemeManager::FontRole::Subtitle, QFont("Microsoft YaHei", 11, QFont::Medium));    // 小标题
    
    // 控件字体
    themeManager->setFont(ThemeManager::FontRole::Button, QFont("Microsoft YaHei", 9, QFont::Medium));       // 按钮 - 略粗
    themeManager->setFont(ThemeManager::FontRole::Menu, QFont("Microsoft YaHei", 9));                        // 菜单
    themeManager->setFont(ThemeManager::FontRole::Toolbar, QFont("Microsoft YaHei", 8));                     // 工具栏
    themeManager->setFont(ThemeManager::FontRole::Input, QFont("Microsoft YaHei", 9));                       // 输入框
    themeManager->setFont(ThemeManager::FontRole::Status, QFont("Microsoft YaHei", 8));                      // 状态栏
    
    // 特殊字体
    themeManager->setFont(ThemeManager::FontRole::Monospace, QFont("Consolas", 9));              // 等宽字体
    themeManager->setFont(ThemeManager::FontRole::Bold, QFont("Microsoft YaHei", 9, QFont::Bold)); // 粗体
    themeManager->setFont(ThemeManager::FontRole::Light, QFont("Microsoft YaHei", 9, QFont::Light)); // 细体
}

void BaselineTheme::setupBaselineComponentTemplates(ThemeManager* themeManager)
{
    // Baseline 暗色主题组件模板 - 符合 Obsidian 风格
    
    // 主按钮 - 暗色风格的现代按钮
    QString primaryButtonTemplate = 
        "QPushButton {"
        "    background-color: #7c3aed;"                                  // 紫色主按钮
        "    color: #ffffff;"
        "    border: none;"
        "    border-radius: 6px;"
        "    padding: 8px 16px;"
        "    font-weight: 500;"
        "    min-height: 36px;"
        "    min-width: 80px;"
        "}"
        "QPushButton:hover {"
        "    background-color: #8b5cf6;"                                  // 悬停变亮
        "}"
        "QPushButton:pressed {"
        "    background-color: #6d28d9;"                                  // 按下变深
        "}"
        "QPushButton:disabled {"
        "    background-color: #37373d;"
        "    color: #6b7280;"
        "}";
        
    // 次要按钮 - 暗色边框风格
    QString secondaryButtonTemplate = 
        "QPushButton {"
        "    background-color: transparent;"
        "    color: #cccccc;"
        "    border: 1px solid #3e3e42;"
        "    border-radius: 6px;"
        "    padding: 8px 16px;"
        "    font-weight: 500;"
        "    min-height: 36px;"
        "    min-width: 80px;"
        "}"
        "QPushButton:hover {"
        "    background-color: #374151;"
        "    border-color: #7c3aed;"
        "    color: #ffffff;"
        "}"
        "QPushButton:pressed {"
        "    background-color: #4b5563;"
        "    border-color: #6d28d9;"
        "}";
    
    themeManager->registerComponentTemplate(ThemeManager::ComponentType::Button, "primary", primaryButtonTemplate);
    themeManager->registerComponentTemplate(ThemeManager::ComponentType::Button, "secondary", secondaryButtonTemplate);
    themeManager->registerComponentTemplate(ThemeManager::ComponentType::Button, "default", primaryButtonTemplate);
    
    // 输入框 - 暗色现代风格
    QString inputFieldTemplate = 
        "QLineEdit {"
        "    background-color: #2d2d30;"
        "    color: #cccccc;"
        "    border: 1px solid #3e3e42;"
        "    border-radius: 6px;"
        "    padding: 8px 12px;"
        "    min-height: 36px;"
        "    selection-background-color: #422006;"
        "}"
        "QLineEdit:focus {"
        "    border-color: #7c3aed;"
        "    background-color: #2d2d30;"
        "    outline: 1px solid #a78bfa;"
        "    outline-offset: 1px;"
        "}"
        "QLineEdit:disabled {"
        "    background-color: #37373d;"
        "    color: #6b7280;"
        "    border-color: #37373d;"
        "}";
    
    themeManager->registerComponentTemplate(ThemeManager::ComponentType::InputField, "default", inputFieldTemplate);
    
    // 表格 - 暗色卡片式设计
    QString tableTemplate = 
        "QTableWidget {"
        "    background-color: #2d2d30;"
        "    alternate-background-color: #252526;"
        "    gridline-color: #3e3e42;"
        "    border: 1px solid #3e3e42;"
        "    border-radius: 8px;"
        "    selection-background-color: #422006;"
        "    color: #cccccc;"
        "    outline: none;"
        "}"
        "QTableWidget::item {"
        "    padding: 12px;"
        "    border: none;"
        "}"
        "QTableWidget::item:selected {"
        "    background-color: #422006;"
        "    color: #ffffff;"
        "}"
        "QTableWidget::item:hover {"
        "    background-color: #374151;"
        "}"
        "QHeaderView::section {"
        "    background-color: #252526;"
        "    color: #cccccc;"
        "    padding: 12px;"
        "    border: none;"
        "    border-bottom: 1px solid #3e3e42;"
        "    font-weight: 600;"
        "}";
    
    themeManager->registerComponentTemplate(ThemeManager::ComponentType::Table, "default", tableTemplate);
    
    // 对话框 - 现代浮动卡片风格  
    QString dialogTemplate = 
        "QDialog {"
        "    background-color: {{BACKGROUND_ALTERNATE}};"
        "    border: {{BORDER_WIDTH_THIN}}px solid {{BORDER_PRIMARY}};"
        "    border-radius: {{BORDER_RADIUS_LARGE}}px;"
        "    padding: {{PADDING_LARGE}}px;"
        "}";
        
    themeManager->registerComponentTemplate(ThemeManager::ComponentType::Dialog, "default", dialogTemplate);
}

} // namespace Themes  
} // namespace LA