#include "LA/Settings/Settings.h"
#include "LA/Settings/SettingsManager.h"
#include "LA/Settings/SettingsPanel.h"
// #include "LA/Settings/SettingsPanelFactory.h"  // 已移除，避免循环依赖
#include "LA/Settings/panels/GeneralSettingsPanel.h"
#include "LA/Settings/panels/SystemSettingsPanel.h"
#include "LA/Settings/panels/ThemeSettingsPanel.h"
#include <QApplication>
#include <QDebug>
#include <QDesktopWidget>
#include <QDialog>
#include <QDialogButtonBox>
#include <QHBoxLayout>
#include <QLabel>
#include <QListWidget>
#include <QMessageBox>
#include <QPushButton>
#include <QSplitter>
#include <QStackedWidget>
#include <QTimer>
#include <QVBoxLayout>
#include <memory>

namespace LA {
namespace Settings {

// 静态变量
static bool s_initialized = false;

bool initializeSettingsSystem() {
    if (s_initialized) {
        qDebug() << "Settings system already initialized";
        return true;
    }

    try {
        // 获取设置管理器实例（会自动初始化）
        SettingsManager *manager = SettingsManager::instance();
        if (!manager) {
            qWarning() << "Failed to create SettingsManager instance";
            return false;
        }

        // 面板注册移到 SettingsDialog 构造函数中进行
        // 这样可以传入正确的父容器（m_panelStack）

        s_initialized = true;
        qDebug() << "Settings system initialized successfully";
        return true;

    } catch (const std::exception &e) {
        qWarning() << "Exception during settings system initialization:" << e.what();
        return false;
    }
}

void cleanupSettingsSystem() {
    if (!s_initialized) {
        return;
    }

    try {
        // 保存所有设置
        SettingsManager *manager = SettingsManager::instance();
        if (manager) {
            manager->saveAllSettings();
        }

        s_initialized = false;
        qDebug() << "Settings system cleaned up";

    } catch (const std::exception &e) {
        qWarning() << "Exception during settings system cleanup:" << e.what();
    }
}

SettingsManager *getSettingsManager() {
    if (!s_initialized) {
        qWarning() << "Settings system not initialized, call initializeSettingsSystem() first";
        return nullptr;
    }

    return SettingsManager::instance();
}

bool registerDefaultPanels(QWidget *parentContainer) {
    SettingsManager *manager = SettingsManager::instance();
    if (!manager) {
        qWarning() << "SettingsManager instance is null";
        return false;
    }

    bool success = true;

    try {
        // 先取消注册现有面板（如果存在）
        // manager->unregisterPanel("general");
        // manager->unregisterPanel("theme");
        // manager->unregisterPanel("system");
        // qDebug() << "Existing panels unregistered (if any)";

        // 注册通用设置面板，传入正确的父容器
        auto generalPanel = std::make_shared<GeneralSettingsPanel>(parentContainer);
        if (!manager->registerPanel(generalPanel)) {
            qWarning() << "Failed to register GeneralSettingsPanel";
            success = false;
        } else {
            qDebug() << "GeneralSettingsPanel registered successfully";
        }

        // 注册主题设置面板，传入正确的父容器
        auto themePanel = std::make_shared<ThemeSettingsPanel>(parentContainer);
        if (!manager->registerPanel(themePanel)) {
            qWarning() << "Failed to register ThemeSettingsPanel";
            success = false;
        } else {
            qDebug() << "ThemeSettingsPanel registered successfully";
        }

        // 注册系统设置面板，传入正确的父容器
        auto systemPanel = std::make_shared<SystemSettingsPanel>(parentContainer);
        if (!manager->registerPanel(systemPanel)) {
            qWarning() << "Failed to register SystemSettingsPanel";
            success = false;
        } else {
            qDebug() << "SystemSettingsPanel registered successfully";
        }

    } catch (const std::exception &e) {
        qWarning() << "Exception during panel registration:" << e.what();
        success = false;
    }

    if (success) {
        qDebug() << "All default panels registered successfully";
    }

    return success;
}

/**
 * @brief 设置对话框类
 */
class SettingsDialog : public QDialog {
    Q_OBJECT

  public:
    explicit SettingsDialog(QWidget *parent = nullptr)
        : QDialog(parent), m_categoryList(nullptr), m_panelStack(nullptr), m_buttonBox(nullptr), m_splitter(nullptr), m_manager(nullptr) {
        qDebug() << "=== SettingsDialog 构造函数开始 ===";
        qDebug() << "SettingsDialog: 父窗口:" << parent;
        qDebug() << "SettingsDialog: this指针:" << this;

        try {
            qDebug() << "SettingsDialog: 调用setupUI()";
            setupUI();
            qDebug() << "SettingsDialog: setupUI()完成";

            qDebug() << "SettingsDialog: 调用registerDefaultPanels()";
            if (!registerDefaultPanels(m_panelStack)) {
                qWarning() << "SettingsDialog: 注册默认面板失败";
            }
            qDebug() << "SettingsDialog: registerDefaultPanels()完成";

            qDebug() << "SettingsDialog: 调用loadPanels()";
            loadPanels();
            qDebug() << "SettingsDialog: loadPanels()完成";

            qDebug() << "SettingsDialog: 调用connectSignals()";
            connectSignals();
            qDebug() << "SettingsDialog: connectSignals()完成";

        } catch (const std::exception &e) {
            qWarning() << "SettingsDialog: 构造函数异常:" << e.what();
        }

        qDebug() << "=== SettingsDialog 构造函数完成 ===";
    }

    ~SettingsDialog() {
        qDebug() << "=== SettingsDialog 析构函数开始 ===";

        // 采用最简单的方式：让Qt的parent-child系统处理清理
        // 只做必要的断开连接，不进行复杂的手动清理
        try {
            // 只断开可能导致崩溃的信号连接
            if (m_buttonBox) {
                disconnect(m_buttonBox, nullptr, this, nullptr);
            }
            if (m_categoryList) {
                disconnect(m_categoryList, nullptr, this, nullptr);
            }

            // 重置指针为nullptr，但不删除对象
            m_manager = nullptr;

            qDebug() << "SettingsDialog: 简化清理完成";

        } catch (...) {
            // 静默处理所有异常，避免析构函数抛出异常
        }

        qDebug() << "=== SettingsDialog 析构函数完成 ===";
    }

  private slots:
    void onCategoryChanged(int row) {
        if (row >= 0 && row < m_panelStack->count()) {
            m_panelStack->setCurrentIndex(row);

            // 确保新面板正确显示和布局
            QWidget *currentWidget = m_panelStack->currentWidget();
            if (currentWidget) {
                currentWidget->setVisible(true);
                currentWidget->show();
                currentWidget->updateGeometry();

                // 强制刷新布局
                m_panelStack->updateGeometry();

                // 关键调试信息：详细尺寸分析
                qDebug() << "=== 面板切换详细尺寸分析 ===";
                qDebug() << "SettingsDialog: 切换到面板" << row;
                qDebug() << "QStackedWidget尺寸:" << m_panelStack->size();
                qDebug() << "QStackedWidget几何:" << m_panelStack->geometry();
                qDebug() << "当前面板控件尺寸:" << currentWidget->size();
                qDebug() << "当前面板控件几何:" << currentWidget->geometry();
                qDebug() << "当前面板控件尺寸策略:" << currentWidget->sizePolicy().horizontalPolicy() << currentWidget->sizePolicy().verticalPolicy();
                qDebug() << "QStackedWidget边距:" << m_panelStack->contentsMargins().left() << m_panelStack->contentsMargins().top()
                         << m_panelStack->contentsMargins().right() << m_panelStack->contentsMargins().bottom();

                // 如果当前控件是SettingsPanel，检查其内部布局
                if (currentWidget->layout()) {
                    QLayout *layout = currentWidget->layout();
                    qDebug() << "面板控件布局类型:" << layout->metaObject()->className();
                    qDebug() << "面板控件布局边距:" << layout->contentsMargins().left() << layout->contentsMargins().top() << layout->contentsMargins().right()
                             << layout->contentsMargins().bottom();
                    qDebug() << "面板控件布局几何:" << layout->geometry();
                }
                qDebug() << "=== 面板切换尺寸分析完毕 ===";
            }
        }
    }

    void onAccepted() {
        // 保存所有设置
        if (m_manager) {
            m_manager->saveAllSettings();
        }
        accept();
    }

    void onRejected() {
        qDebug() << "SettingsDialog::onRejected() 开始";

        // 检查是否有未保存的更改
        if (m_manager && m_manager->hasUnsavedChanges()) {
            qDebug() << "SettingsDialog: 检测到未保存的更改，显示确认对话框";
            int ret = QMessageBox::question(
                this, tr("未保存的更改"), tr("有未保存的设置更改，是否要保存？"), QMessageBox::Save | QMessageBox::Discard | QMessageBox::Cancel);
            if (ret == QMessageBox::Save) {
                qDebug() << "SettingsDialog: 用户选择保存更改";
                m_manager->saveAllSettings();
                accept();
                return;
            } else if (ret == QMessageBox::Discard) {
                qDebug() << "SettingsDialog: 用户选择丢弃更改";
                // 重新加载设置
                m_manager->loadAllSettings();
                reject();
                return;
            } else {
                qDebug() << "SettingsDialog: 用户选择取消，不关闭对话框";
                // Cancel - 不关闭对话框
                return;
            }
        }

        qDebug() << "SettingsDialog: 没有未保存更改，直接关闭对话框";
        reject();
    }

    void onApplyClicked() {
        if (m_manager) {
            m_manager->saveAllSettings();
            m_manager->applyAllSettings();
        }
    }

    void onResetClicked() {
        int ret = QMessageBox::question(this, tr("重置所有设置"), tr("确定要重置所有设置为默认值吗？此操作不可撤销。"), QMessageBox::Yes | QMessageBox::No);
        if (ret == QMessageBox::Yes && m_manager) {
            m_manager->resetAllSettings();
        }
    }

  private:
    void setupUI() {
        qDebug() << "SettingsDialog: setupUI() 开始";

        setWindowTitle(tr("应用程序设置"));
        setModal(true);
        // 设置合理的对话框尺寸
        resize(950, 650);          // 合理尺寸，既能完整显示内容又不会过大
        setMinimumSize(900, 600);  // 合理的最小尺寸

        // 设置对话框在屏幕中央
        move(QApplication::desktop()->screen()->rect().center() - rect().center());
        qDebug() << "SettingsDialog: 基本属性设置完成，尺寸: 1000x700，最小尺寸: 900x650";

        // 主垂直布局 - 优化边距和间距
        QVBoxLayout *mainLayout = new QVBoxLayout(this);
        mainLayout->setContentsMargins(10, 10, 10, 10);  // 减小外边距
        mainLayout->setSpacing(8);                       // 减小组件间距
        qDebug() << "SettingsDialog: 主垂直布局创建完成";

        // 创建分割器
        m_splitter = new QSplitter(Qt::Horizontal, this);
        qDebug() << "SettingsDialog: 分割器创建完成";

        // 左侧类别列表 - 优化样式
        m_categoryList = new QListWidget(this);
        m_categoryList->setMinimumWidth(150);  // 设置合理的最小宽度
        m_categoryList->setMaximumWidth(200);  // 限制最大宽度，给设置面板更多空间
        m_categoryList->setSizePolicy(QSizePolicy::Fixed, QSizePolicy::Expanding);

        // 简化类别列表的样式，去除所有背景
        m_categoryList->setStyleSheet("QListWidget {"
                                      "    background-color: transparent;"
                                      "    border: none;"
                                      "    padding: 8px;"
                                      "}"
                                      "QListWidget::item {"
                                      "    padding: 10px 15px;"
                                      "    border: none;"
                                      "    border-radius: 4px;"
                                      "    margin: 1px 0px;"
                                      "    background-color: transparent;"
                                      "}"
                                      "QListWidget::item:selected {"
                                      "    background-color: transparent;"
                                      "    color: #1976d2;"
                                      "}"
                                      "QListWidget::item:hover {"
                                      "    background-color: transparent;"
                                      "}");

        m_splitter->addWidget(m_categoryList);
        qDebug() << "SettingsDialog: 类别列表创建完成，优化样式";

        // 右侧面板堆栈 - 给设置内容更多空间
        m_panelStack = new QStackedWidget(this);
        m_panelStack->setMinimumWidth(650);  // 合理的面板区域最小宽度
        m_panelStack->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);

        // 关键：确保QStackedWidget完全填充可用空间
        m_panelStack->setContentsMargins(0, 0, 0, 0);  // 去除所有边距，让内容充满整个区域

        // m_panelStack背景确认没问题，移除红色背景调试
        m_panelStack->setStyleSheet("QStackedWidget {"
                                    "    background-color: red;"
                                    "    border: 2px solid darkred;"
                                    "    margin: 0px;"
                                    "    padding: 0px;"
                                    "}");

        // 调试：记录QStackedWidget的尺寸信息
        qDebug() << "SettingsDialog: QStackedWidget创建，初始尺寸:" << m_panelStack->size();
        m_splitter->addWidget(m_panelStack);
        qDebug() << "SettingsDialog: 面板堆栈创建完成，最小宽度: 600px";

        // 设置分割器比例 - 给设置面板更多空间
        m_splitter->setStretchFactor(0, 0);  // 类别列表固定宽度
        m_splitter->setStretchFactor(1, 1);  // 设置面板区域占用剩余空间

        // 设置初始分割器尺寸 - 根据对话框实际尺寸动态计算
        QList<int> sizes;
        int        dialogWidth   = 950;                               // 对话框总宽度
        int        categoryWidth = 170;                               // 类别列表固定宽度
        int        panelWidth    = dialogWidth - categoryWidth - 20;  // 设置面板宽度 = 总宽度 - 类别宽度 - 边距
        sizes << categoryWidth << panelWidth;                         // 类别列表170px，设置面板占用剩余空间
        m_splitter->setSizes(sizes);

        qDebug() << "SettingsDialog: QSplitter尺寸设置 - 类别:" << categoryWidth << "px, 面板:" << panelWidth << "px";

        // 设置分割器范围限制，确保设置面板有足够空间
        m_splitter->setCollapsible(0, false);  // 防止类别列表被完全折叠
        m_splitter->setCollapsible(1, false);  // 防止设置面板被完全折叠

        // 设置分割器的最小尺寸，确保设置面板有足够显示空间
        m_splitter->widget(0)->setMinimumWidth(150);
        m_splitter->widget(1)->setMinimumWidth(650);
        qDebug() << "SettingsDialog: 分割器比例设置完成，比例: 1:4";

        mainLayout->addWidget(m_splitter, 1);  // stretch=1，分割器占用主要垂直空间
        qDebug() << "SettingsDialog: 分割器添加到主布局，stretch=1";

        // 底部按钮
        qDebug() << "SettingsDialog: 调用setupButtons()";
        setupButtons();
        qDebug() << "SettingsDialog: setupButtons()完成";

        qDebug() << "SettingsDialog: setupUI() 完成";
    }

    void setupButtons() {
        qDebug() << "SettingsDialog: setupButtons() 开始";

        QVBoxLayout *mainLayout = qobject_cast<QVBoxLayout *>(layout());
        if (!mainLayout) {
            qWarning() << "SettingsDialog: 无法获取主布局，创建新的垂直布局";
            mainLayout = new QVBoxLayout(this);
        }
        qDebug() << "SettingsDialog: 主布局获取成功";

        // 按钮布局
        QHBoxLayout *buttonLayout = new QHBoxLayout();
        qDebug() << "SettingsDialog: 按钮布局创建完成";

        // 创建紧凑的按钮布局，减少高度占用
        QPushButton *resetButton = new QPushButton(tr("重置"), this);
        resetButton->setMaximumWidth(80);  // 限制按钮宽度
        buttonLayout->addWidget(resetButton);
        qDebug() << "SettingsDialog: 重置按钮添加完成";

        buttonLayout->addStretch();

        m_buttonBox = new QDialogButtonBox(QDialogButtonBox::Ok | QDialogButtonBox::Apply | QDialogButtonBox::Cancel, this);
        // 设置按钮组的最大高度，防止占用过多空间
        m_buttonBox->setMaximumHeight(40);
        m_buttonBox->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Fixed);
        buttonLayout->addWidget(m_buttonBox);
        qDebug() << "SettingsDialog: 对话框按钮组添加完成";

        // 设置按钮布局的最大高度，给设置面板更多空间
        buttonLayout->setContentsMargins(15, 10, 15, 15);
        buttonLayout->setSpacing(10);

        // 创建一个容器widget来包裵按钮布局，控制其高度
        QWidget *buttonContainer = new QWidget(this);
        buttonContainer->setLayout(buttonLayout);
        buttonContainer->setMaximumHeight(60);  // 限制按钮区域高度
        buttonContainer->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Fixed);

        mainLayout->addWidget(buttonContainer, 0);  // stretch=0，按钮区域固定高度，不占用额外空间
        qDebug() << "SettingsDialog: 按钮布局添加到主布局，stretch=0";

        // 连接按钮信号
        connect(resetButton, &QPushButton::clicked, this, &SettingsDialog::onResetClicked);
        connect(m_buttonBox, &QDialogButtonBox::accepted, this, &SettingsDialog::onAccepted);
        connect(m_buttonBox, &QDialogButtonBox::rejected, this, &SettingsDialog::onRejected);
        connect(m_buttonBox, &QDialogButtonBox::clicked, this, [this](QAbstractButton *button) {
            if (m_buttonBox->standardButton(button) == QDialogButtonBox::Apply) {
                onApplyClicked();
            }
        });

        // 简化按钮样式，去除背景和边框
        QString buttonStyle = "QPushButton {"
                              "    padding: 8px 16px;"
                              "    border: none;"
                              "    background: transparent;"
                              "    font-size: 13px;"
                              "    border-radius: 4px;"
                              "}"
                              "QPushButton:hover {"
                              "    background-color: #f0f0f0;"
                              "}"
                              "QPushButton:pressed {"
                              "    background-color: #e0e0e0;"
                              "}";
        resetButton->setStyleSheet(buttonStyle);
        qDebug() << "SettingsDialog: 按钮信号连接完成";

        qDebug() << "SettingsDialog: setupButtons() 完成";
    }

    void loadPanels() {
        qDebug() << "SettingsDialog: loadPanels() 开始";

        m_manager = SettingsManager::instance();
        if (!m_manager) {
            qWarning() << "SettingsDialog: SettingsManager实例为空";
            return;
        }
        qDebug() << "SettingsDialog: SettingsManager实例获取成功:" << m_manager;

        // 按类别组织面板
        QStringList categories = m_manager->getAllCategories();
        qDebug() << "SettingsDialog: 获取到类别数量:" << categories.size();
        qDebug() << "SettingsDialog: 类别列表:" << categories;

        int totalPanels = 0;
        for (const QString &category : categories) {
            qDebug() << "SettingsDialog: 处理类别:" << category;
            QStringList panelIds = m_manager->getPanelsByCategory(category);
            qDebug() << "SettingsDialog: 类别" << category << "中的面板数量:" << panelIds.size();
            qDebug() << "SettingsDialog: 面板ID列表:" << panelIds;

            for (const QString &panelId : panelIds) {
                qDebug() << "SettingsDialog: 处理面板ID:" << panelId;
                auto panel = m_manager->getPanel(panelId);
                if (panel) {
                    qDebug() << "SettingsDialog: 面板获取成功:" << panel.get();
                    qDebug() << "SettingsDialog: 面板显示名称:" << panel->getDisplayName();

                    // 添加到类别列表
                    QListWidgetItem *item = new QListWidgetItem(panel->getDisplayName());
                    item->setData(Qt::UserRole, panelId);
                    if (!panel->getIconPath().isEmpty()) {
                        item->setIcon(QIcon(panel->getIconPath()));
                        qDebug() << "SettingsDialog: 设置面板图标:" << panel->getIconPath();
                    }
                    m_categoryList->addItem(item);
                    qDebug() << "SettingsDialog: 面板添加到类别列表";

                    // 添加到面板堆栈
                    QWidget *panelWidget = panel->getWidget();
                    if (panelWidget) {
                        // 面板已在创建时设置了正确的父容器，无需重新设置

                        // 调试输出（可选）
                        qDebug() << "panel parent:" << panelWidget->parent() << "windowFlags:" << panelWidget->windowFlags()
                                 << "min/max:" << panelWidget->minimumSize() << panelWidget->maximumSize() << "sizeHint:" << panelWidget->sizeHint()
                                 << "hasLayout:" << (panelWidget->layout() != nullptr);

                        // 强制设置面板Widget的尺寸策略，移除尺寸限制
                        panelWidget->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);

                        // 调试：记录添加前的面板信息
                        qDebug() << "=== 添加面板到QStackedWidget调试 ===";
                        qDebug() << "面板ID:" << panelId;
                        qDebug() << "QStackedWidget尺寸:" << m_panelStack->size();
                        qDebug() << "面板Widget尺寸:" << panelWidget->size();
                        qDebug() << "面板Widget几何:" << panelWidget->geometry();

                        qDebug() << "【关键】添加到QStackedWidget的widget类型:" << panelWidget->metaObject()->className();
                        qDebug() << "【关键】widget指针:" << panelWidget;
                        qDebug() << "【关键】是否是SettingsPanel:" << (qobject_cast<SettingsPanel *>(panelWidget) != nullptr);

                        m_panelStack->addWidget(panelWidget);

                        qDebug() << "【关键】QStackedWidget当前widget数量:" << m_panelStack->count();

                        // 让QStackedWidget的布局系统自动管理面板尺寸
                        // 移除强制尺寸设置，这是导致布局问题的根源

                        // 强制更新几何信息
                        panelWidget->updateGeometry();
                        m_panelStack->updateGeometry();

                        totalPanels++;
                    } else {
                        qWarning() << "SettingsDialog: 面板控件为空:" << panelId;
                    }
                } else {
                    qWarning() << "SettingsDialog: 无法获取面板:" << panelId;
                }
            }
        }

        qDebug() << "SettingsDialog: 总共加载面板数量:" << totalPanels;
        qDebug() << "SettingsDialog: 类别列表项目数量:" << m_categoryList->count();
        qDebug() << "SettingsDialog: 面板堆栈数量:" << m_panelStack->count();

        // 选择第一个面板
        if (m_categoryList->count() > 0) {
            m_categoryList->setCurrentRow(0);
            m_panelStack->setCurrentIndex(0);
            qDebug() << "SettingsDialog: 选择第一个面板";

            // 确保当前面板可见并正确设置尺寸
            QWidget *currentWidget = m_panelStack->currentWidget();
            if (currentWidget) {
                currentWidget->setVisible(true);
                currentWidget->show();
                currentWidget->updateGeometry();  // 更新几何信息

                // 强制更新布局
                m_panelStack->updateGeometry();
                m_splitter->updateGeometry();
                updateGeometry();

                qDebug() << "SettingsDialog: 当前面板尺寸:" << currentWidget->size();
                qDebug() << "SettingsDialog: 面板堆栈尺寸:" << m_panelStack->size();
            }
        } else {
            qWarning() << "SettingsDialog: 没有可用的面板";
        }

        qDebug() << "SettingsDialog: loadPanels() 完成";
    }

    void connectSignals() {
        connect(m_categoryList, &QListWidget::currentRowChanged, this, &SettingsDialog::onCategoryChanged);
    }

  private:
    QListWidget *     m_categoryList;
    QStackedWidget *  m_panelStack;
    QDialogButtonBox *m_buttonBox;
    QSplitter *       m_splitter;
    SettingsManager * m_manager;
};

QDialog *createSettingsDialog(QWidget *parent) {
    qDebug() << "=== createSettingsDialog 开始 ===";
    qDebug() << "Settings: 父窗口指针:" << parent;
    qDebug() << "Settings: 系统初始化状态:" << s_initialized;

    if (!s_initialized) {
        qWarning() << "Settings: 系统未初始化，尝试初始化";
        if (!initializeSettingsSystem()) {
            qWarning() << "Settings: 初始化失败";
            return nullptr;
        }
        qDebug() << "Settings: 初始化成功";
    }

    qDebug() << "Settings: 创建SettingsDialog";
    SettingsDialog *dialog = new SettingsDialog(parent);
    qDebug() << "Settings: SettingsDialog创建完成，地址:" << dialog;
    qDebug() << "=== createSettingsDialog 完成 ===";

    return dialog;
}

// registerSettingsToSidebar函数已移除
// Settings库保持独立，UI集成由使用方（主程序）负责

}  // namespace Settings
}  // namespace LA

// SettingsDialog是在.cpp文件中定义的内部类，需要手动包含MOC文件
#include "Settings.moc"
