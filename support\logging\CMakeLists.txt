# ====================================================
# LA Support Logging Module CMakeLists.txt
# 日志系统模块构建配置
# ====================================================

cmake_minimum_required(VERSION 3.16)

# 项目信息
project(LA_Support_Logging
    VERSION 1.0.0
    DESCRIPTION "LA Support Layer - Logging System Module"
    LANGUAGES CXX
)

# 查找依赖
find_package(Qt5 REQUIRED COMPONENTS 
    Core 
    Network
)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 头文件
set(LOGGING_HEADERS
    # 接口定义
    logger/ILogger.h
    formatter/ILogFormatter.h
    output/ILogOutput.h
    
    # 实现类
    Logger.h
    LoggerManager.h
)

# 源文件
set(LOGGING_SOURCES
    # 实现文件
    Logger.cpp
    LoggerManager.cpp
    formatter/LogFormatter.cpp
    # output/LogOutput.cpp  # Temporarily disabled due to interface mismatches
)

# 创建库目标 - 使用标准命名约定
add_library(LA_support_logging_lib SHARED
    ${LOGGING_HEADERS}
    ${LOGGING_SOURCES}
)

# 为兼容性创建别名
add_library(LA_Support_Logging ALIAS LA_support_logging_lib)

# 设置库属性
set_target_properties(LA_support_logging_lib PROPERTIES
    VERSION ${PROJECT_VERSION}
    SOVERSION 1
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
    POSITION_INDEPENDENT_CODE ON
    AUTOMOC ON
)

# 包含目录
target_include_directories(LA_support_logging_lib
    PUBLIC
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/..>
        $<INSTALL_INTERFACE:include>
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}
)

# 链接依赖
target_link_libraries(LA_support_logging_lib
    PUBLIC
        Qt5::Core
        Qt5::Network
        # LA_Foundation_Core  # 临时移除，待接口问题解决
    PRIVATE
        # 私有依赖
)

# 编译定义
target_compile_definitions(LA_support_logging_lib
    PRIVATE
        LA_SUPPORT_LOGGING_LIBRARY
    PUBLIC
        $<$<CONFIG:Debug>:LA_DEBUG>
        $<$<CONFIG:Release>:LA_RELEASE>
)

# 编译选项
if(MSVC)
    target_compile_options(LA_support_logging_lib PRIVATE
        /W4                    # 警告级别
        /utf-8                 # UTF-8编码
        /permissive-           # 严格C++标准
    )
else()
    target_compile_options(LA_support_logging_lib PRIVATE
        -Wall -Wextra         # 启用警告
        -Wpedantic            # 严格标准检查
        -finput-charset=UTF-8 # 输入编码
        -fexec-charset=UTF-8  # 执行编码
    )
endif()

# 安装配置
install(TARGETS LA_support_logging_lib
    EXPORT LA_Support_LoggingTargets
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
    ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
    INCLUDES DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}
)

# 安装头文件
install(FILES ${LOGGING_HEADERS}
    DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}/LA/Support/Logging
)

# 安装子目录头文件
install(DIRECTORY logger/ 
    DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}/LA/Support/Logging/logger
    FILES_MATCHING PATTERN "*.h"
)

install(DIRECTORY formatter/ 
    DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}/LA/Support/Logging/formatter
    FILES_MATCHING PATTERN "*.h"
)

install(DIRECTORY output/ 
    DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}/LA/Support/Logging/output
    FILES_MATCHING PATTERN "*.h"
)

# 创建并安装包配置文件
include(CMakePackageConfigHelpers)

write_basic_package_version_file(
    "${CMAKE_CURRENT_BINARY_DIR}/LA_Support_LoggingConfigVersion.cmake"
    VERSION ${PROJECT_VERSION}
    COMPATIBILITY SameMajorVersion
)

configure_package_config_file(
    "${CMAKE_CURRENT_SOURCE_DIR}/LA_Support_LoggingConfig.cmake.in"
    "${CMAKE_CURRENT_BINARY_DIR}/LA_Support_LoggingConfig.cmake"
    INSTALL_DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/LA_Support_Logging
)

install(EXPORT LA_Support_LoggingTargets
    FILE LA_Support_LoggingTargets.cmake
    NAMESPACE LA::Support::
    DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/LA_Support_Logging
)

install(FILES
    "${CMAKE_CURRENT_BINARY_DIR}/LA_Support_LoggingConfig.cmake"
    "${CMAKE_CURRENT_BINARY_DIR}/LA_Support_LoggingConfigVersion.cmake"
    DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/LA_Support_Logging
)

# 创建pkg-config文件
configure_file(
    "${CMAKE_CURRENT_SOURCE_DIR}/LA_Support_Logging.pc.in"
    "${CMAKE_CURRENT_BINARY_DIR}/LA_Support_Logging.pc"
    @ONLY
)

install(FILES
    "${CMAKE_CURRENT_BINARY_DIR}/LA_Support_Logging.pc"
    DESTINATION ${CMAKE_INSTALL_LIBDIR}/pkgconfig
)

# 测试支持
if(BUILD_TESTING)
    enable_testing()
    add_subdirectory(tests)
endif()

# 打印配置信息
message(STATUS "LA Support Logging Module Configuration:")
message(STATUS "  Version: ${PROJECT_VERSION}")
message(STATUS "  Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  Install Prefix: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "  C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  Qt5 Version: ${Qt5_VERSION}")

# 开发者调试信息
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    message(STATUS "Debug Build - Additional Information:")
    message(STATUS "  Source Directory: ${CMAKE_CURRENT_SOURCE_DIR}")
    message(STATUS "  Binary Directory: ${CMAKE_CURRENT_BINARY_DIR}")
    get_target_property(LOGGING_INCLUDE_DIRS LA_Support_Logging INCLUDE_DIRECTORIES)
    message(STATUS "  Include Directories: ${LOGGING_INCLUDE_DIRS}")
endif()