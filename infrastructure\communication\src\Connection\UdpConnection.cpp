#include <LA/Communication/Connection/NetworkConnection.h>
#include <QDebug>
#include <QMutexLocker>
#include <QDateTime>

namespace LA {
namespace Communication {
namespace Connection {

UdpConnection::UdpConnection(QObject* parent)
    : IConnection(parent)
    , m_socket(new QUdpSocket(this))
    , m_status(ConnectionStatus::Disconnected)
    , m_remotePort(0)
{
    // 连接Qt UDP Socket信号到我们的槽
    connect(m_socket, &QUdpSocket::readyRead, 
            this, &UdpConnection::onReadyRead);
    connect(m_socket, QOverload<QAbstractSocket::SocketError>::of(&QUdpSocket::error),
            this, &UdpConnection::onErrorOccurred);
    
    resetStatistics();
}

UdpConnection::~UdpConnection() {
    if (isOpen()) {
        close();
    }
}

// === IConnection接口实现 ===

SimpleResult UdpConnection::open(const ConnectionConfig& config) {
    QMutexLocker locker(&m_mutex);
    
    if (m_status == ConnectionStatus::Connected) {
        return SimpleResult::success(true);
    }
    
    m_config = config;
    
    // 获取本地端口（用于绑定）
    int localPort = config.parameters.value("localPort", 0).toInt();
    
    // 获取远程地址和端口（用于发送数据）
    QString remoteAddress = config.hostAddress;
    if (remoteAddress.isEmpty()) {
        remoteAddress = config.parameters.value("remoteAddress").toString();
    }
    
    int remotePort = config.port;
    if (remotePort == 0) {
        remotePort = config.parameters.value("remotePort", 8080).toInt();
    }
    
    setStatus(ConnectionStatus::Connecting);
    
    // 绑定到本地端口
    if (localPort > 0) {
        if (!m_socket->bind(localPort)) {
            m_lastError = QString("Failed to bind to local port %1: %2")
                          .arg(localPort)
                          .arg(m_socket->errorString());
            setStatus(ConnectionStatus::Error);
            return SimpleResult::failure(m_lastError);
        }
    } else {
        // 自动分配端口
        if (!m_socket->bind()) {
            m_lastError = QString("Failed to bind UDP socket: %1")
                          .arg(m_socket->errorString());
            setStatus(ConnectionStatus::Error);
            return SimpleResult::failure(m_lastError);
        }
    }
    
    // 设置远程地址和端口（用于发送）
    if (!remoteAddress.isEmpty() && remotePort > 0) {
        m_remoteAddress = QHostAddress(remoteAddress);
        m_remotePort = remotePort;
        
        if (m_remoteAddress.isNull()) {
            m_lastError = QString("Invalid remote address: %1").arg(remoteAddress);
            setStatus(ConnectionStatus::Error);
            return SimpleResult::failure(m_lastError);
        }
    }
    
    setStatus(ConnectionStatus::Connected);
    m_lastError.clear();
    
    return SimpleResult::success(true);
}

SimpleResult UdpConnection::close() {
    QMutexLocker locker(&m_mutex);
    
    if (m_status == ConnectionStatus::Disconnected) {
        return SimpleResult::success(true);
    }
    
    if (m_socket->state() == QAbstractSocket::BoundState) {
        m_socket->close();
    }
    
    m_remoteAddress = QHostAddress();
    m_remotePort = 0;
    
    setStatus(ConnectionStatus::Disconnected);
    return SimpleResult::success(true);
}

ConnectionStatus UdpConnection::status() const {
    QMutexLocker locker(&m_mutex);
    return m_status;
}

bool UdpConnection::isOpen() const {
    QMutexLocker locker(&m_mutex);
    return m_status == ConnectionStatus::Connected && 
           m_socket && m_socket->state() == QAbstractSocket::BoundState;
}

qint64 UdpConnection::write(const QByteArray& data) {
    QMutexLocker locker(&m_mutex);
    
    if (!isOpen()) {
        m_lastError = "UDP socket not bound";
        return -1;
    }
    
    // 检查是否设置了远程地址
    if (m_remoteAddress.isNull() || m_remotePort == 0) {
        m_lastError = "Remote address not set for UDP connection";
        return -1;
    }
    
    qint64 bytesWritten = m_socket->writeDatagram(data, m_remoteAddress, m_remotePort);
    if (bytesWritten == -1) {
        m_lastError = QString("Write datagram failed: %1").arg(m_socket->errorString());
        return -1;
    }
    
    // 更新统计信息
    updateStatistics(0, bytesWritten);
    
    return bytesWritten;
}

QByteArray UdpConnection::read(qint64 maxBytes) {
    QMutexLocker locker(&m_mutex);
    
    if (!isOpen()) {
        return QByteArray();
    }
    
    QByteArray data;
    QHostAddress sender;
    quint16 senderPort;
    
    if (m_socket->hasPendingDatagrams()) {
        qint64 datagramSize = m_socket->pendingDatagramSize();
        
        // 如果指定了最大字节数，使用较小的值
        if (maxBytes > 0 && datagramSize > maxBytes) {
            datagramSize = maxBytes;
        }
        
        data.resize(datagramSize);
        qint64 bytesRead = m_socket->readDatagram(data.data(), datagramSize, &sender, &senderPort);
        
        if (bytesRead > 0) {
            data.resize(bytesRead);
            updateStatistics(bytesRead, 0);
            
            // 如果没有设置远程地址，使用第一个发送方的地址
            if (m_remoteAddress.isNull()) {
                m_remoteAddress = sender;
                m_remotePort = senderPort;
            }
        } else {
            data.clear();
        }
    }
    
    return data;
}

qint64 UdpConnection::bytesAvailable() const {
    QMutexLocker locker(&m_mutex);
    return m_socket && m_socket->hasPendingDatagrams() ? m_socket->pendingDatagramSize() : 0;
}

bool UdpConnection::waitForReadyRead(int timeout) {
    QMutexLocker locker(&m_mutex);
    return m_socket ? m_socket->waitForReadyRead(timeout) : false;
}

bool UdpConnection::waitForBytesWritten(int timeout) {
    QMutexLocker locker(&m_mutex);
    return m_socket ? m_socket->waitForBytesWritten(timeout) : false;
}

DeviceStatistics UdpConnection::getStatistics() const {
    QMutexLocker locker(&m_mutex);
    return m_statistics;
}

QString UdpConnection::errorString() const {
    QMutexLocker locker(&m_mutex);
    return m_lastError;
}

void UdpConnection::resetStatistics() {
    QMutexLocker locker(&m_mutex);
    m_statistics = DeviceStatistics();
    m_statistics.lastActivity = QDateTime::currentMSecsSinceEpoch();
}

// === UDP特定功能 ===

QHostAddress UdpConnection::getRemoteAddress() const {
    QMutexLocker locker(&m_mutex);
    return m_remoteAddress;
}

quint16 UdpConnection::getRemotePort() const {
    QMutexLocker locker(&m_mutex);
    return m_remotePort;
}

// === 私有槽函数 ===

void UdpConnection::onReadyRead() {
    // 发出信号通知有数据可读
    emit readyRead();
}

void UdpConnection::onErrorOccurred(QAbstractSocket::SocketError error) {
    if (error != QAbstractSocket::SocketTimeoutError) {
        QMutexLocker locker(&m_mutex);
        
        m_lastError = m_socket->errorString();
        m_statistics.errorsCount++;
        
        // 对于严重错误，改变连接状态
        if (error != QAbstractSocket::SocketTimeoutError && 
            error != QAbstractSocket::TemporaryError) {
            setStatus(ConnectionStatus::Error);
        }
        
        emit errorOccurred(m_lastError);
    }
}

// === 私有辅助函数 ===

void UdpConnection::updateStatistics(qint64 bytesRead, qint64 bytesWritten) {
    qint64 currentTime = QDateTime::currentMSecsSinceEpoch();
    
    if (bytesRead > 0) {
        m_statistics.bytesReceived += bytesRead;
        m_statistics.packetsReceived++;
        m_statistics.lastActivity = currentTime;
    }
    
    if (bytesWritten > 0) {
        m_statistics.bytesSent += bytesWritten;
        m_statistics.packetsSent++;
        m_statistics.lastActivity = currentTime;
    }
}

void UdpConnection::setStatus(ConnectionStatus newStatus) {
    if (m_status != newStatus) {
        ConnectionStatus previousStatus = m_status;
        m_status = newStatus;
        
        // 发出状态变化信号
        emit statusChanged(newStatus);
        
        // 根据状态发出特定信号
        if (newStatus == ConnectionStatus::Connected && 
            previousStatus != ConnectionStatus::Connected) {
            emit readyRead(); // 可能有数据等待读取
        }
    }
}

} // namespace Connection
} // namespace Communication
} // namespace LA