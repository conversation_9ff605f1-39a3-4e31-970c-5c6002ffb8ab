# LA项目脚本和工具
cmake_minimum_required(VERSION 3.16)
project(LA_Scripts VERSION 1.0.0 LANGUAGES CXX)

# C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 查找依赖
find_package(Qt5 REQUIRED COMPONENTS Core Widgets)

# 设置CMAKE模块路径以找到LA库
list(APPEND CMAKE_MODULE_PATH ${CMAKE_CURRENT_SOURCE_DIR}/../cmake)

# 查找LA库
find_package(LA_themes REQUIRED)
find_package(LA_sidebar REQUIRED)

# 包含目录
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/../)

# 主题系统集成测试
add_executable(test_theme_integration
    test_theme_integration.cpp
)

target_link_libraries(test_theme_integration
    PRIVATE
        Qt5::Core
        Qt5::Widgets
        LA::Themes
        LA::SideBar
)

# 设置输出目录
set_target_properties(test_theme_integration PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin/tests
    OUTPUT_NAME "theme_integration_test"
)

# 如果在Debug模式下，启用调试信息
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_definitions(test_theme_integration PRIVATE
        QT_DEBUG
        LA_DEBUG_MODE
    )
endif()

# 复制到测试目录
install(TARGETS test_theme_integration
    RUNTIME DESTINATION bin/tests
)

# 添加测试
enable_testing()
add_test(NAME ThemeIntegrationTest 
         COMMAND test_theme_integration
         WORKING_DIRECTORY ${CMAKE_BINARY_DIR}/bin/tests)

# 设置测试环境变量
set_tests_properties(ThemeIntegrationTest PROPERTIES
    ENVIRONMENT "QT_QPA_PLATFORM=offscreen"  # 无头模式测试
    TIMEOUT 30  # 30秒超时
)

message(STATUS "LA Scripts and Tools configured successfully")
message(STATUS "  - Theme Integration Test: ${CMAKE_BINARY_DIR}/bin/tests/theme_integration_test")