# LA Foundation Layer CMakeLists.txt
# 基础类型定义层 - 零依赖的底层模块

cmake_minimum_required(VERSION 3.16)

# 设置项目信息
project(LA_Foundation VERSION 1.0.0 LANGUAGES CXX)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 查找Qt5核心组件（仅依赖最基础的Qt组件）
find_package(Qt5 REQUIRED COMPONENTS Core)

# 定义Foundation模块
set(FOUNDATION_TARGET LA_Foundation)

# 收集头文件
file(GLOB_RECURSE FOUNDATION_HEADERS
    "core/*.h"
)

# Foundation层是header-only库，创建INTERFACE库
add_library(${FOUNDATION_TARGET} INTERFACE)

# 设置目标别名
add_library(LA::Foundation ALIAS ${FOUNDATION_TARGET})

# 设置目标属性
set_target_properties(${FOUNDATION_TARGET} PROPERTIES
    VERSION ${PROJECT_VERSION}
    EXPORT_NAME "Foundation"
    OUTPUT_NAME "la_foundation"
)

# 设置包含目录 - 重要：确保其他模块可以通过 LA/Foundation/Core/CommonTypes.h 访问
target_include_directories(${FOUNDATION_TARGET}
    INTERFACE
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/..>  # support目录
        $<BUILD_INTERFACE:${CMAKE_SOURCE_DIR}>              # 项目根目录
        $<INSTALL_INTERFACE:include>
)

# 链接Qt库
target_link_libraries(${FOUNDATION_TARGET}
    INTERFACE
        Qt5::Core
)

# 编译定义
target_compile_definitions(${FOUNDATION_TARGET}
    INTERFACE
        LA_FOUNDATION_VERSION="${PROJECT_VERSION}"
)

# 打印构建信息
message(STATUS "LA Foundation Layer Configuration:")
message(STATUS "  Version: ${PROJECT_VERSION}")
message(STATUS "  Target: ${FOUNDATION_TARGET}")
message(STATUS "  Headers: Foundation core types")
message(STATUS "  Library Type: INTERFACE (header-only)")
message(STATUS "  Dependencies: Qt5::Core only")

# 导出变量供上层使用
set(LA_FOUNDATION_LIBRARY ${FOUNDATION_TARGET} PARENT_SCOPE)
set(LA_FOUNDATION_INCLUDE_DIR ${CMAKE_CURRENT_SOURCE_DIR} PARENT_SCOPE)
set(LA_FOUNDATION_VERSION ${PROJECT_VERSION} PARENT_SCOPE)

# 安装配置（如果需要）
if(LA_INSTALL_FOUNDATION)
    # 安装头文件
    install(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/
        DESTINATION include/LA/Foundation
        FILES_MATCHING PATTERN "*.h"
        PATTERN "CMakeFiles" EXCLUDE
        PATTERN "*.cmake" EXCLUDE
    )
    
    # 安装库目标
    install(TARGETS ${FOUNDATION_TARGET}
        EXPORT LA_Foundation_Targets
        INCLUDES DESTINATION include
    )
    
    # 安装导出文件
    install(EXPORT LA_Foundation_Targets
        FILE LA_Foundation_Targets.cmake
        NAMESPACE LA::
        DESTINATION lib/cmake/LA_Foundation
    )
    
    # 创建配置文件
    include(CMakePackageConfigHelpers)
    write_basic_package_version_file(
        "${CMAKE_CURRENT_BINARY_DIR}/LA_Foundation_ConfigVersion.cmake"
        VERSION ${PROJECT_VERSION}
        COMPATIBILITY SameMajorVersion
    )
    
    configure_file(
        "${CMAKE_CURRENT_SOURCE_DIR}/LA_Foundation_Config.cmake.in"
        "${CMAKE_CURRENT_BINARY_DIR}/LA_Foundation_Config.cmake"
        @ONLY
    )
    
    # 安装配置文件
    install(FILES
        "${CMAKE_CURRENT_BINARY_DIR}/LA_Foundation_Config.cmake"
        "${CMAKE_CURRENT_BINARY_DIR}/LA_Foundation_ConfigVersion.cmake"
        DESTINATION lib/cmake/LA_Foundation
    )
endif()

# 创建格式化目标（如果需要）
find_program(CLANG_FORMAT_EXECUTABLE clang-format)
if(CLANG_FORMAT_EXECUTABLE)
    add_custom_target(format_foundation
        COMMAND ${CLANG_FORMAT_EXECUTABLE} -i ${FOUNDATION_HEADERS}
        COMMENT "Formatting foundation layer code"
        VERBATIM
    )
endif()