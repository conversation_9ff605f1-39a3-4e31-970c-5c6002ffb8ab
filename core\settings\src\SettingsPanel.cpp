#include "LA/Settings/SettingsPanel.h"
#include <QApplication>
#include <QDateTime>
#include <QDebug>
#include <QDir>
#include <QFile>
#include <QLabel>
#include <QResizeEvent>
#include <QScrollArea>
#include <QStandardPaths>
#include <QTextStream>

namespace LA {
namespace Settings {

SettingsPanel::SettingsPanel(const QString &panelId,
                             const QString &displayName,
                             const QString &description,
                             const QString &iconPath,
                             const QString &category,
                             int            priority,
                             QWidget *      parent)
    : QWidget(parent),
      m_panelId(panelId),
      m_displayName(displayName),
      m_description(description),
      m_iconPath(iconPath),
      m_category(category),
      m_priority(priority),
      m_isVisible(true),
      m_isEnabled(true),
      m_hasUnsavedChanges(false),
      m_mainLayout(nullptr),
      m_contentLayout(nullptr) {
    initializeSettings();
    setupBaseUI();
    connectSignals();
    // 注意：不在构造函数中调用loadSettings()，因为它会调用纯虚函数loadSpecificSettings()
    // loadSettings()应该在派生类构造完成后手动调用
}

void SettingsPanel::initializeSettings() {
    QString configDir = QStandardPaths::writableLocation(QStandardPaths::AppConfigLocation);
    QDir().mkpath(configDir);
    QString settingsFile = configDir + "/settings_" + m_panelId + ".ini";
    m_settings           = std::make_shared<QSettings>(settingsFile, QSettings::IniFormat);

    qDebug() << "Settings panel" << m_panelId << "initialized with config file:" << settingsFile;
}

void SettingsPanel::setupBaseUI() {
    qDebug() << "=== SettingsPanel::setupBaseUI() 开始 ===";
    // 创建主布局
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(0, 0, 0, 0);
    m_mainLayout->setSpacing(0);
    qDebug() << "SettingsPanel: 主布局创建完成:" << m_mainLayout;

    // 调试：为主布局设置绿色背景，测试SettingsPanel的实际占用区域 - DISABLED
    // setStyleSheet("SettingsPanel { background-color: green; border: 5px solid darkgreen; }");

    // 直接创建内容布局，不使用滚动区域
    m_contentLayout = new QVBoxLayout();
    m_contentLayout->setContentsMargins(10, 10, 10, 10);  // 减小边距，最大化内容显示区域
    m_contentLayout->setSpacing(10);                      // 适度组件间距
    qDebug() << "SettingsPanel: 内容布局创建完成:" << m_contentLayout;

    // 将内容布局添加到主布局，设置stretch=1让内容布局占用所有可用空间
    m_mainLayout->addLayout(m_contentLayout, 1);
    qDebug() << "SettingsPanel: 内容布局已添加到主布局，stretch=1";

    // 设置面板的尺寸策略，让其填充整个可用空间
    setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);  // 设置尺寸策略为可扩展

    /*
    setStyleSheet(R"(
        QScrollArea {
            background-color: transparent;
            border: none;
        }
        QWidget {
            background-color: transparent;
        }
        QGroupBox {
            font-weight: bold;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            margin-top: 12px;
            padding-top: 15px;
            margin-bottom: 10px;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 15px;
            padding: 0 8px 0 8px;
            background-color: transparent;
        }
    )");
    */
}

void SettingsPanel::loadSettings() {
    if (!m_settings) {
        qWarning() << "Settings object is null for panel:" << m_panelId;
        return;
    }

    try {
        loadSpecificSettings();
        markAsSaved();  // 加载后标记为已保存状态
        qDebug() << "Settings loaded for panel:" << m_panelId;
    } catch (const std::exception &e) {
        qWarning() << "Failed to load settings for panel" << m_panelId << ":" << e.what();
    }
}

void SettingsPanel::saveSettings() {
    if (!m_settings) {
        qWarning() << "Settings object is null for panel:" << m_panelId;
        return;
    }

    try {
        saveSpecificSettings();
        m_settings->sync();
        markAsSaved();
        emit settingsSaved();
        qDebug() << "Settings saved for panel:" << m_panelId;
    } catch (const std::exception &e) {
        qWarning() << "Failed to save settings for panel" << m_panelId << ":" << e.what();
    }
}

void SettingsPanel::resetSettings() {
    try {
        resetSpecificSettings();
        markAsModified();
        emit settingsReset();
        qDebug() << "Settings reset for panel:" << m_panelId;
    } catch (const std::exception &e) {
        qWarning() << "Failed to reset settings for panel" << m_panelId << ":" << e.what();
    }
}

bool SettingsPanel::validateSettings() {
    try {
        return validateSpecificSettings();
    } catch (const std::exception &e) {
        qWarning() << "Failed to validate settings for panel" << m_panelId << ":" << e.what();
        return false;
    }
}

void SettingsPanel::applySettings() {
    try {
        applySpecificSettings();
        qDebug() << "Settings applied for panel:" << m_panelId;
    } catch (const std::exception &e) {
        qWarning() << "Failed to apply settings for panel" << m_panelId << ":" << e.what();
    }
}

QJsonObject SettingsPanel::getSettingsData() const {
    QJsonObject data;

    if (!m_settings) {
        return data;
    }

    // 获取所有设置键值对
    QStringList keys = m_settings->allKeys();
    for (const QString &key : keys) {
        QVariant value = m_settings->value(key);

        // 根据类型转换为JSON值
        if (value.type() == QVariant::String) {
            data[key] = value.toString();
        } else if (value.type() == QVariant::Int) {
            data[key] = value.toInt();
        } else if (value.type() == QVariant::Bool) {
            data[key] = value.toBool();
        } else if (value.type() == QVariant::Double) {
            data[key] = value.toDouble();
        } else {
            // 其他类型转换为字符串
            data[key] = value.toString();
        }
    }

    return data;
}

void SettingsPanel::setSettingsData(const QJsonObject &data) {
    if (!m_settings) {
        return;
    }

    // 设置所有键值对
    for (auto it = data.begin(); it != data.end(); ++it) {
        const QString &   key   = it.key();
        const QJsonValue &value = it.value();

        if (value.isString()) {
            m_settings->setValue(key, value.toString());
        } else if (value.isDouble()) {
            m_settings->setValue(key, value.toDouble());
        } else if (value.isBool()) {
            m_settings->setValue(key, value.toBool());
        } else {
            m_settings->setValue(key, value.toVariant());
        }
    }

    m_settings->sync();
    loadSettings();  // 重新加载设置到UI
}

void SettingsPanel::onSettingChanged() {
    markAsModified();
}

void SettingsPanel::resizeEvent(QResizeEvent* event) {
    QWidget::resizeEvent(event);
    qDebug() << "SettingsPanel resized to:" << size();
}

}  // namespace Settings
}  // namespace LA

// MOC文件由CMake AutoMoc自动处理，无需手动包含
