#pragma once

#include "IMatchingAlgorithm.h"
#include <QMap>

namespace LA {
namespace DeviceManagement {
namespace Matching {

/**
 * @brief 最佳匹配算法 - IMatchingAlgorithm的具体实现
 * 
 * Linus: "只做一件事：计算端口与设备的最优匹配"
 * 基于多种因素计算匹配置信度
 */
class BestMatchAlgorithm : public IMatchingAlgorithm {
public:
    BestMatchAlgorithm();
    virtual ~BestMatchAlgorithm() = default;

    // ====== IMatchingAlgorithm接口实现 ======
    float calculateMatchConfidence(const PortInfo& port, const DeviceInfo& device) override;
    MatchPair findBestPortForDevice(const DeviceInfo& device, const QList<PortInfo>& availablePorts, const MatchingConfig& config) override;
    MatchingResult matchDevicesToPorts(const QList<DeviceInfo>& devices, const QList<PortInfo>& ports, const MatchingConfig& config) override;
    bool validateMatch(const PortInfo& port, const DeviceInfo& device) override;
    void setMatchingStrategy(MatchingStrategy strategy) override;
    MatchingStrategy getMatchingStrategy() const override;

private:
    // ====== 匹配因子计算 ======
    float calculatePortTypeScore(const PortInfo& port, const DeviceInfo& device);
    float calculateCapabilityScore(const PortInfo& port, const DeviceInfo& device);
    float calculateCompatibilityScore(const PortInfo& port, const DeviceInfo& device);
    float calculatePreferenceScore(const PortInfo& port, const DeviceInfo& device);
    
    // ====== 具体匹配策略 ======
    MatchingResult performBestMatching(const QList<DeviceInfo>& devices, const QList<PortInfo>& ports, const MatchingConfig& config);
    MatchingResult performFirstAvailableMatching(const QList<DeviceInfo>& devices, const QList<PortInfo>& ports, const MatchingConfig& config);
    MatchingResult performLoadBalanceMatching(const QList<DeviceInfo>& devices, const QList<PortInfo>& ports, const MatchingConfig& config);
    
    // ====== 工具方法 ======
    bool isPortTypeCompatible(const QString& portType, const DeviceInfo& device);
    bool hasRequiredCapabilities(const PortInfo& port, const DeviceInfo& device);
    QString generateMatchReason(const PortInfo& port, const DeviceInfo& device, float confidence);
    void sortMatchesByConfidence(QList<MatchPair>& matches);
    
    // ====== 匹配权重配置 ======
    void initializeWeights();
    void initializeCompatibilityMatrix();
    void initializePreferenceRules();
    
    // ====== 数据成员 ======
    MatchingStrategy m_strategy;
    
    // 匹配权重
    float m_portTypeWeight = 0.4f;
    float m_capabilityWeight = 0.3f;
    float m_compatibilityWeight = 0.2f;
    float m_preferenceWeight = 0.1f;
    
    // 兼容性矩阵：[设备类型][端口类型] = 兼容性分数
    QMap<QString, QMap<QString, float>> m_compatibilityMatrix;
    
    // 偏好规则：设备类型对端口类型的偏好
    QMap<QString, QStringList> m_preferenceRules;
};

} // namespace Matching
} // namespace DeviceManagement
} // namespace LA