# CMakeLists.txt for SPRM Device Four-Layer Architecture
# 四层架构SPRM设备模块构建配置

cmake_minimum_required(VERSION 3.16)
project(LA_Device_SPRM VERSION 1.0.0 LANGUAGES CXX)

# 设置C++17标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置构建类型
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE "Release" CACHE STRING "Choose the type of build" FORCE)
endif()

# 查找依赖包
find_package(Qt5 REQUIRED COMPONENTS Core Network SerialPort Test)

# Lua支持 (可选)
find_package(PkgConfig QUIET)
if(PKG_CONFIG_FOUND)
    pkg_check_modules(LUA lua5.4 QUIET)
    if(NOT LUA_FOUND)
        pkg_check_modules(LUA lua5.3 QUIET)
    endif()
    if(NOT LUA_FOUND)
        pkg_check_modules(LUA lua QUIET)
    endif()
endif()

if(NOT LUA_FOUND)
    # 手动查找Lua
    find_path(LUA_INCLUDE_DIR lua.h
        HINTS
        ENV LUA_DIR
        PATH_SUFFIXES include/lua5.4 include/lua5.3 include/lua include
        PATHS
        ~/Library/Frameworks
        /Library/Frameworks
        /usr/local
        /usr
        /sw
        /opt/local
        /opt/csw
        /opt
    )
    
    find_library(LUA_LIBRARY
        NAMES lua5.4 lua5.3 lua lua54 lua53
        HINTS
        ENV LUA_DIR
        PATH_SUFFIXES lib
        PATHS
        ~/Library/Frameworks
        /Library/Frameworks
        /usr/local
        /usr
        /sw
        /opt/local
        /opt/csw
        /opt
    )
    
    if(LUA_INCLUDE_DIR AND LUA_LIBRARY)
        set(LUA_FOUND TRUE)
        set(LUA_INCLUDE_DIRS ${LUA_INCLUDE_DIR})
        set(LUA_LIBRARIES ${LUA_LIBRARY})
    endif()
endif()

# 输出配置信息
message(STATUS "Building LA Device SPRM Module")
message(STATUS "Qt5 Core: ${Qt5Core_VERSION}")
message(STATUS "C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "Build Type: ${CMAKE_BUILD_TYPE}")

if(LUA_FOUND)
    message(STATUS "Lua Support: Enabled")
    message(STATUS "Lua Include: ${LUA_INCLUDE_DIRS}")
    message(STATUS "Lua Library: ${LUA_LIBRARIES}")
else()
    message(STATUS "Lua Support: Disabled (Lua not found)")
endif()

# 编译选项
if(WIN32)
    add_compile_definitions(NOMINMAX WIN32_LEAN_AND_MEAN)
endif()

if(MSVC)
    add_compile_options(/W3)
else()
    add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# ============================================
# 第1层：驱动层 (Driver Layer)
# ============================================

set(DRIVER_HEADERS
    src/Drivers/sprm/SprmA1Driver.h
)

set(DRIVER_SOURCES  
    src/Drivers/sprm/SprmA1Driver.cpp
)

add_library(LA_Device_Drivers STATIC
    ${DRIVER_HEADERS}
    ${DRIVER_SOURCES}
)

target_include_directories(LA_Device_Drivers PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_SOURCE_DIR}/src/Core
)

target_link_libraries(LA_Device_Drivers
    Qt5::Core
    Qt5::SerialPort
)

set_target_properties(LA_Device_Drivers PROPERTIES
    AUTOMOC ON
)

# ============================================
# 第2层：能力层 (Capability Layer)
# ============================================

set(CAPABILITY_HEADERS
    src/Capabilities/ranging/LaserRangingCapability.h
    # TODO: Re-enable when infrastructure dependencies are resolved
    # src/Capabilities/communication/CommunicationCapability.h
    # src/Capabilities/calibration/CalibrationCapability.h
    # src/Capabilities/data_processing/DataProcessingCapability.h
    src/Capabilities/command/ICommandProvider.h
    # src/Capabilities/command/sprm/SprmCommandProvider.h
)

set(CAPABILITY_SOURCES
    src/Capabilities/ranging/LaserRangingCapability.cpp
    # TODO: Re-enable when infrastructure dependencies are resolved
    # src/Capabilities/communication/CommunicationCapability_stub.cpp
    # src/Capabilities/data_processing/DataProcessingCapability.cpp
)

add_library(LA_Device_Capabilities STATIC
    ${CAPABILITY_HEADERS}
    ${CAPABILITY_SOURCES}
)

target_include_directories(LA_Device_Capabilities PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_SOURCE_DIR}/src/Core
    ${CMAKE_CURRENT_SOURCE_DIR}/src/Capabilities
    ${CMAKE_CURRENT_SOURCE_DIR}/../infrastructure/communication/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../infrastructure/algorithm/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../support/data_sharing
)

target_link_libraries(LA_Device_Capabilities
    Qt5::Core
    Qt5::SerialPort
    LA_Device_Drivers
)

set_target_properties(LA_Device_Capabilities PROPERTIES
    AUTOMOC ON
)

# ============================================
# 第3层：策略层 (Strategy Layer)
# ============================================

set(STRATEGY_HEADERS
    src/Strategies/filtering/KalmanFilter.h
)

set(STRATEGY_SOURCES
    src/Strategies/filtering/KalmanFilter.cpp
)

# 如果需要，添加其他策略文件
if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/src/Strategies/filtering/KalmanFilter.cpp")
    list(APPEND STRATEGY_SOURCES src/Strategies/filtering/KalmanFilter.cpp)
endif()

add_library(LA_Device_Strategies STATIC
    ${STRATEGY_HEADERS}
    ${STRATEGY_SOURCES}
)

target_include_directories(LA_Device_Strategies PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_SOURCE_DIR}/src/Core
)

target_link_libraries(LA_Device_Strategies
    Qt5::Core
)

set_target_properties(LA_Device_Strategies PROPERTIES
    AUTOMOC ON
)

# ============================================
# 第4层：脚本层 (Script Layer)
# ============================================

set(SCRIPT_HEADERS
    src/Scripts/engine/ScriptEngine.h
)

set(SCRIPT_SOURCES
    src/Scripts/engine/ScriptEngine_stub.cpp
)

add_library(LA_Device_Scripts STATIC
    ${SCRIPT_HEADERS}
    ${SCRIPT_SOURCES}
)

target_include_directories(LA_Device_Scripts PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_SOURCE_DIR}/src/Core
)

target_link_libraries(LA_Device_Scripts
    Qt5::Core
)

set_target_properties(LA_Device_Scripts PROPERTIES
    AUTOMOC ON
)

# 如果找到Lua，启用脚本支持
if(LUA_FOUND)
    target_compile_definitions(LA_Device_Scripts PRIVATE ENABLE_LUA_SUPPORT)
    target_include_directories(LA_Device_Scripts PRIVATE ${LUA_INCLUDE_DIRS})
    target_link_libraries(LA_Device_Scripts ${LUA_LIBRARIES})
else()
    message(WARNING "Lua not found. Script engine will have limited functionality.")
endif()

# ============================================
# 核心设备类 (Core Device)
# ============================================

set(CORE_HEADERS
    include/LA/Device/Core/Device.h
    include/LA/Device/Core/IDevice.h
)

set(CORE_SOURCES
    src/Core/Device.cpp
    src/Core/IDevice.cpp
)

# 检查核心Device.cpp是否存在，如果不存在创建基础实现
if(NOT EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/src/Core/Device.cpp")
    message(STATUS "Creating basic Device.cpp implementation")
    file(WRITE "${CMAKE_CURRENT_SOURCE_DIR}/src/Core/Device.cpp"
        "#include \"Device.h\"\n"
        "#include <QDebug>\n"
        "namespace LA::Device {\n"
        "Device::Device(const QString& deviceType, const QString& deviceId, QObject* parent)\n"
        "    : QObject(parent), m_deviceType(deviceType), m_deviceId(deviceId), m_state(DeviceState::Uninitialized) {\n"
        "    qDebug() << \"Device created:\" << deviceType << deviceId;\n"
        "}\n"
        "bool Device::initialize() { return true; }\n"
        "bool Device::connect() { return true; }\n"
        "bool Device::disconnect() { return true; }\n"
        "bool Device::start() { return true; }\n"
        "bool Device::stop() { return true; }\n"
        "void Device::setState(DeviceState newState) { m_state = newState; }\n"
        "QVariantMap Device::executeCommand(const QString&, const QVariantMap&) { return QVariantMap(); }\n"
        "QVariantMap Device::handleScriptEvent(const QString&, const QVariantMap&) { return QVariantMap(); }\n"
        "void Device::setDriver(std::unique_ptr<Driver::IDriver>) {}\n"
        "void Device::addCapability(std::unique_ptr<Capability::ICapability>) {}\n"
        "void Device::setStrategy(const QString&, std::unique_ptr<Strategy::IStrategy>) {}\n"
        "bool Device::loadScript(const QString&) { return false; }\n"
        "void Device::setScriptVariable(const QString&, const QVariant&) {}\n"
        "QVariant Device::getScriptVariable(const QString&) { return QVariant(); }\n"
        "bool Device::reloadScript() { return false; }\n"
        "QVariant Device::callScriptFunction(const QString&, const QVariantList&) { return QVariant(); }\n"
        "std::unique_ptr<Device> Device::createFromConfig(const QVariantMap&) { return nullptr; }\n"
        "QVariantMap Device::getAllProperties() const { return m_properties; }\n"
        "QStringList Device::getSupportedCommands() const { return QStringList(); }\n"
        "bool Device::hasCommand(const QString&) const { return false; }\n"
        "QVariant Device::getSpec(const QString& key) const { return m_config.value(key); }\n"
        "void Device::buildCommandRoutes() {}\n"
        "Capability::ICapability* Device::findCapabilityForCommand(const QString&) const { return nullptr; }\n"
        "QVariantMap Device::executeCommandThroughLayers(const QString&, const QVariantMap&) { return QVariantMap(); }\n"
        "QVariantMap Device::applyScriptSelectedStrategy(const QString&, const QVariantMap&) { return QVariantMap(); }\n"
        "bool Device::validateComponents() const { return true; }\n"
        "void Device::onScriptError(const QString&) {}\n"
        "void Device::onScriptTimeout() {}\n"
        "}\n"
    )
endif()

add_library(LA_Device_Core STATIC
    ${CORE_HEADERS}
    ${CORE_SOURCES}
)

target_include_directories(LA_Device_Core PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_SOURCE_DIR}/src/Core
)

target_link_libraries(LA_Device_Core
    Qt5::Core
    LA_Device_Drivers
    LA_Device_Capabilities
    LA_Device_Strategies
    LA_Device_Scripts
)

# ============================================
# SPRM设备实现 (SPRM Device Implementation)
# ============================================

set(SPRM_HEADERS
    src/Devices/sprm/SprmDevice.h
)

set(SPRM_SOURCES
    src/Devices/sprm/SprmDevice.cpp
)

add_library(LA_Device_SPRM STATIC
    ${SPRM_HEADERS}
    ${SPRM_SOURCES}
)

target_include_directories(LA_Device_SPRM PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_SOURCE_DIR}/src/Core
    ${CMAKE_CURRENT_SOURCE_DIR}/../core/device/idevice
)

target_link_libraries(LA_Device_SPRM
    Qt5::Core
    LA_Device_Core
    LA_Device_Drivers
    LA_Device_Capabilities
    LA_Device_Strategies
    LA_Device_Scripts
)

set_target_properties(LA_Device_SPRM PROPERTIES
    AUTOMOC ON
)

# ============================================
# 主库目标 (Main Library Target)
# ============================================

add_library(LA_Device_FourLayer STATIC)

# 收集所有头文件
set(ALL_HEADERS
    ${DRIVER_HEADERS}
    ${CAPABILITY_HEADERS}
    ${STRATEGY_HEADERS}
    ${SCRIPT_HEADERS}
    ${CORE_HEADERS}
    ${SPRM_HEADERS}
)

# 收集所有源文件
set(ALL_SOURCES
    ${DRIVER_SOURCES}
    ${CAPABILITY_SOURCES}
    ${STRATEGY_SOURCES}
    ${SCRIPT_SOURCES}
    ${CORE_SOURCES}
    ${SPRM_SOURCES}
)

target_sources(LA_Device_FourLayer PRIVATE
    ${ALL_HEADERS}
    ${ALL_SOURCES}
)

target_include_directories(LA_Device_FourLayer PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_SOURCE_DIR}/src/Core
)

target_link_libraries(LA_Device_FourLayer
    Qt5::Core
    Qt5::Network
    Qt5::SerialPort
    LA_Device_Core
    LA_Device_Drivers
    LA_Device_Capabilities
    LA_Device_Strategies
    LA_Device_Scripts
    LA_Device_SPRM
)

if(LUA_FOUND)
    target_link_libraries(LA_Device_FourLayer ${LUA_LIBRARIES})
    target_include_directories(LA_Device_FourLayer PRIVATE ${LUA_INCLUDE_DIRS})
endif()

set_target_properties(LA_Device_FourLayer PROPERTIES
    AUTOMOC ON
)

# ============================================
# 测试目标 (Test Targets)
# ============================================

option(BUILD_TESTS "Build test executables" ON)

if(BUILD_TESTS)
    enable_testing()
    
    # 设置测试输出目录到 build/test/ (符合 test_guideline.md 规范)
    set(CMAKE_RUNTIME_OUTPUT_DIRECTORY_DEBUG ${CMAKE_BINARY_DIR}/test)
    set(CMAKE_RUNTIME_OUTPUT_DIRECTORY_RELEASE ${CMAKE_BINARY_DIR}/test)
    set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/test)
    
    # SPRM设备测试 - 注意：测试文件已迁移到全局tests目录
    # 这里保持兼容性，实际测试文件在 ../../tests/unit/devices/sprm/
    if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/../../tests/unit/devices/sprm/SprmDeviceTest.cpp")
        add_executable(SprmDeviceTest
            ../../tests/unit/devices/sprm/SprmDeviceTest.h
            ../../tests/unit/devices/sprm/SprmDeviceTest.cpp
        )
        
        target_include_directories(SprmDeviceTest PRIVATE
            ${CMAKE_CURRENT_SOURCE_DIR}
            ${CMAKE_CURRENT_SOURCE_DIR}/include
            ${CMAKE_CURRENT_SOURCE_DIR}/src/Core
        )
        
        target_link_libraries(SprmDeviceTest
            Qt5::Core
            Qt5::Test
            LA_Device_FourLayer
        )
        
        # 添加自动MOC处理
        set_target_properties(SprmDeviceTest PROPERTIES
            AUTOMOC ON
            RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/test
        )
        
        add_test(NAME SprmDeviceTest COMMAND SprmDeviceTest)
    endif()
    
    # 简单的功能测试
    add_executable(simple_sprm_test
        tests/simple_sprm_test.cpp
    )
    
    # 创建简单测试
    if(NOT EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/tests/simple_sprm_test.cpp")
        message(STATUS "Creating simple SPRM test")
        file(WRITE "${CMAKE_CURRENT_SOURCE_DIR}/tests/simple_sprm_test.cpp"
            "#include <QCoreApplication>\n"
            "#include <QDebug>\n"
            "#include <iostream>\n"
            "#include \"devices/sprm/SprmDevice.h\"\n"
            "\n"
            "int main(int argc, char* argv[]) {\n"
            "    QCoreApplication app(argc, argv);\n"
            "    \n"
            "    qDebug() << \"SPRM Device Four-Layer Architecture Test\";\n"
            "    \n"
            "    try {\n"
            "        auto device = LA::Device::Devices::SprmDeviceFactory::createDevice(\"SPRM-A1\");\n"
            "        qDebug() << \"SprmDevice created successfully\";\n"
            "        \n"
            "        // Test basic functionality\n"
            "        QVariantMap config;\n"
            "        config[\"basic_info\"] = QVariantMap{{\"model\", \"SPRM-A1\"}};\n"
            "        config[\"hardware_specs\"] = QVariantMap{{\"laser_power\", 5.0}};\n"
            "        config[\"communication\"] = QVariantMap{{\"protocol\", \"RS485\"}};\n"
            "        bool initResult = device->initialize(config);\n"
            "        qDebug() << \"Device initialization:\" << initResult;\n"
            "        \n"
            "        // Test device status\n"
            "        QVariantMap status = device->getDeviceStatus();\n"
            "        qDebug() << \"Device status retrieved:\" << !status.isEmpty();\n"
            "        \n"
            "        qDebug() << \"Test passed!\";\n"
            "        return 0;\n"
            "    } catch (const std::exception& e) {\n"
            "        qCritical() << \"Test failed:\" << e.what();\n"
            "        return 1;\n"
            "    } catch (...) {\n"
            "        qCritical() << \"Test failed with unknown exception\";\n"
            "        return 1;\n"
            "    }\n"
            "}\n"
        )
    endif()
    
    target_link_libraries(simple_sprm_test
        Qt5::Core
        LA_Device_FourLayer
    )
    
    add_test(NAME simple_sprm_test COMMAND simple_sprm_test)
endif()

# ============================================
# 示例应用程序 (Example Application)
# ============================================

option(BUILD_EXAMPLES "Build example applications" ON)

if(BUILD_EXAMPLES)
    add_executable(sprm_device_example
        examples/sprm_device_example.cpp
    )
    
    # 如果示例文件不存在，创建一个
    if(NOT EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/examples/sprm_device_example.cpp")
        file(MAKE_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}/examples")
        file(WRITE "${CMAKE_CURRENT_SOURCE_DIR}/examples/sprm_device_example.cpp"
            "#include <QCoreApplication>\n"
            "#include <QDebug>\n"
            "#include \"devices/sprm/SprmDevice.h\"\n"
            "\n"
            "int main(int argc, char *argv[]) {\n"
            "    QCoreApplication app(argc, argv);\n"
            "    \n"
            "    qDebug() << \"SPRM Device Four-Layer Architecture Example\";\n"
            "    \n"
            "    // 创建SPRM设备\n"
            "    auto device = LA::Device::Devices::SprmDeviceFactory::createDevice(\"SPRM-A1\");\n"
            "    if (!device) {\n"
            "        qCritical() << \"Failed to create device\";\n"
            "        return 1;\n"
            "    }\n"
            "    \n"
            "    qDebug() << \"Device created:\" << device->getDeviceType();\n"
            "    qDebug() << \"Model:\" << device->getDeviceModel();\n"
            "    \n"
            "    // 获取设备规格\n"
            "    auto specs = device->getDeviceSpecifications();\n"
            "    qDebug() << \"Device specifications:\" << specs;\n"
            "    \n"
            "    // 初始化设备（使用默认配置）\n"
            "    QVariantMap config;\n"
            "    config[\"basic_info\"] = QVariantMap{{\"model\", \"SPRM-A1\"}};\n"
            "    config[\"hardware_specs\"] = QVariantMap{{\"laser_power\", 5.0}};\n"
            "    config[\"communication\"] = QVariantMap{{\"protocol\", \"RS485\"}};\n"
            "    \n"
            "    if (device->initialize(config)) {\n"
            "        qDebug() << \"Device initialized successfully\";\n"
            "        \n"
            "        // 获取设备状态\n"
            "        auto status = device->getDeviceStatus();\n"
            "        qDebug() << \"Device status:\" << status;\n"
            "    } else {\n"
            "        qWarning() << \"Device initialization failed\";\n"
            "    }\n"
            "    \n"
            "    qDebug() << \"Example completed\";\n"
            "    return 0;\n"
            "}\n"
        )
    endif()
    
    target_link_libraries(sprm_device_example
        Qt5::Core
        LA_Device_FourLayer
    )
    
    set_target_properties(sprm_device_example PROPERTIES
        AUTOMOC ON
    )
endif()

# ============================================
# 安装配置 (Installation)
# ============================================

# 安装头文件
install(FILES ${ALL_HEADERS}
    DESTINATION include/LA/Device
)

# 安装库文件
install(TARGETS LA_Device_FourLayer
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
)

# 安装配置文件
install(DIRECTORY config/
    DESTINATION share/LA/Device/config
    FILES_MATCHING PATTERN "*.json" PATTERN "*.yml"
)

# 安装脚本文件
install(DIRECTORY scripts/lua/
    DESTINATION share/LA/Device/scripts
    FILES_MATCHING PATTERN "*.lua"
)

# ============================================
# 包配置 (Package Configuration)  
# ============================================

include(CMakePackageConfigHelpers)

# 生成版本文件
write_basic_package_version_file(
    "${CMAKE_CURRENT_BINARY_DIR}/LA_Device_FourLayerConfigVersion.cmake"
    VERSION ${PROJECT_VERSION}
    COMPATIBILITY AnyNewerVersion
)

# 生成配置文件
configure_package_config_file(
    "${CMAKE_CURRENT_SOURCE_DIR}/cmake/LA_Device_FourLayerConfig.cmake.in"
    "${CMAKE_CURRENT_BINARY_DIR}/LA_Device_FourLayerConfig.cmake"
    INSTALL_DESTINATION lib/cmake/LA_Device_FourLayer
)

# 安装配置文件
install(FILES
    "${CMAKE_CURRENT_BINARY_DIR}/LA_Device_FourLayerConfig.cmake"
    "${CMAKE_CURRENT_BINARY_DIR}/LA_Device_FourLayerConfigVersion.cmake"
    DESTINATION lib/cmake/LA_Device_FourLayer
)

# ============================================
# 调试信息
# ============================================

if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    message(STATUS "=== Debug Build Information ===")
    message(STATUS "All Headers: ${ALL_HEADERS}")
    message(STATUS "All Sources: ${ALL_SOURCES}")
    message(STATUS "Include Directories: ${CMAKE_CURRENT_SOURCE_DIR}")
    message(STATUS "================================")
endif()

# 显示构建摘要
message(STATUS "")
message(STATUS "=== LA Device SPRM Module Build Summary ===")
message(STATUS "Project: ${PROJECT_NAME}")
message(STATUS "Version: ${PROJECT_VERSION}")
message(STATUS "Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "Qt5 Version: ${Qt5Core_VERSION}")
message(STATUS "Lua Support: ${LUA_FOUND}")
message(STATUS "Tests: ${BUILD_TESTS}")
message(STATUS "Examples: ${BUILD_EXAMPLES}")
message(STATUS "==========================================")
message(STATUS "")