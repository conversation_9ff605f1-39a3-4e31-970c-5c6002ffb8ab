#include "LA/Settings/panels/SystemSettingsPanel.h"
#include "LA/Themes/ThemeManager.h"
#include <QApplication>
#include <QDebug>
#include <QFileDialog>
#include <QGridLayout>
#include <QHeaderView>
#include <QMessageBox>
#include <QStandardPaths>
#include <QTabWidget>
#include <QVBoxLayout>

namespace LA {
namespace Settings {

SystemSettingsPanel::SystemSettingsPanel(QWidget *parent)
    : SettingsPanel("system", tr("系统设置"), tr("配置工业软件的系统参数，包括用户权限、硬件设备、安全认证等"), ":/icons/settings/system.png", tr("系统"), 30, parent),
      m_tabWidget(nullptr),
      m_accountPage(nullptr),
      m_hardwarePage(nullptr),
      m_securityPage(nullptr),
      m_performancePage(nullptr),
      m_currentUsername("admin"),
      m_currentPermissionLevel("administrator"),
      m_minPasswordLength(8),
      m_requireSpecialChars(true),
      m_requireNumbers(true),
      m_requireUppercase(true),
      m_maxLoginAttempts(3),
      m_sessionTimeout(30),
      m_deviceType("Serial"),
      m_communicationPort("COM1"),
      m_baudRate(9600),
      m_connectionTimeout(5000),
      m_autoReconnect(true),
      m_securityLevel("medium"),
      m_enableEncryption(true),
      m_encryptionAlgorithm("AES-256"),
      m_enableAuditLog(true),
      m_auditLogRetention(30),
      m_enableAutoBackup(true),
      m_backupInterval(24),
      m_performanceMode("balanced"),
      m_memoryLimit(2048),
      m_enableMemoryOptimization(true),
      m_threadCount(4),
      m_enableMultithreading(true),
      m_priorityLevel("normal"),
      m_cacheSize(256),
      m_enableCache(true) {

    // 设置默认备份路径
    m_backupPath = QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation) + "/LA_Backups";

    qDebug() << "!!! SystemSettingsPanel 构造函数 - 这是新的调试信息 !!!";
    
    // 创建UI
    createUI();

    // 连接信号
    connectSignals();

    // 加载设置
    loadSettings();
    
    // Ensure no action buttons are created
    // DO NOT call setupActionButtons() - it creates the purple button issue
}

void SystemSettingsPanel::createUI() {
    qDebug() << "=== SystemSettingsPanel::createUI() 开始 ===";

    // 获取基类已经创建的内容布局
    QVBoxLayout *contentLayout = getContentLayout();
    if (!contentLayout) {
        qWarning() << "SystemSettingsPanel: 无法获取内容布局";
        return;
    }
    qDebug() << "SystemSettingsPanel: contentLayout获取成功:" << contentLayout;

    // 创建标签页控件
    m_tabWidget = new QTabWidget(this);
    qDebug() << "SystemSettingsPanel: TabWidget创建完成:" << m_tabWidget;
    
    // 设置TabWidget填充整个可用空间
    m_tabWidget->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
    qDebug() << "SystemSettingsPanel: TabWidget尺寸策略设置完成";
    
    // 临时禁用主题样式以隔离SettingsPanel容器色块问题
    // auto themeManager = &LA::Themes::ThemeManager::instance();
    // themeManager->applyThemeToWidget(m_tabWidget, LA::Themes::ThemeManager::ComponentType::Tab);
    qDebug() << "SystemSettingsPanel: TabWidget主题样式应用已禁用以调试色块问题";

    // 创建各个设置页面
    setupUserAccountSettings();
    setupHardwareSettings();
    setupSecuritySettings();
    setupPerformanceSettings();

    // 添加页面到标签页控件
    if (m_accountPage) {
        m_tabWidget->addTab(m_accountPage, tr("用户账号"));
    }
    if (m_hardwarePage) {
        m_tabWidget->addTab(m_hardwarePage, tr("硬件设备"));
    }
    if (m_securityPage) {
        m_tabWidget->addTab(m_securityPage, tr("安全认证"));
    }
    if (m_performancePage) {
        m_tabWidget->addTab(m_performancePage, tr("系统性能"));
    }

    qDebug() << "SystemSettingsPanel: 准备将TabWidget添加到contentLayout";
    qDebug() << "SystemSettingsPanel: TabWidget父对象:" << m_tabWidget->parent();
    qDebug() << "SystemSettingsPanel: TabWidget尺寸:" << m_tabWidget->size();
    
    contentLayout->addWidget(m_tabWidget, 1);  // stretch=1，让TabWidget占用主要空间
    qDebug() << "SystemSettingsPanel: TabWidget已添加到contentLayout，stretch=1";
    qDebug() << "SystemSettingsPanel: TabWidget尺寸:" << m_tabWidget->size();
    qDebug() << "SystemSettingsPanel: accountPage尺寸:" << (m_accountPage ? m_accountPage->size() : QSize(-1,-1));

    // 检查是否还在调用setupActionButtons
    qDebug() << "SystemSettingsPanel: 检查是否调用setupActionButtons - 不应该调用";
    
    qDebug() << "SystemSettingsPanel: contentLayout子项数量:" << contentLayout->count();
    for (int i = 0; i < contentLayout->count(); ++i) {
        QLayoutItem* item = contentLayout->itemAt(i);
        if (item) {
            qDebug() << "SystemSettingsPanel: contentLayout项目" << i << ":" << item->widget();
        }
    }

    qDebug() << "=== SystemSettingsPanel::createUI() 完成 ===";
}

void SystemSettingsPanel::setupUserAccountSettings() {
    m_accountPage = new QWidget();
    m_accountPage->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
    // 屏蔽子容器色块，专注排查SettingsPanel容器问题
    // m_accountPage->setStyleSheet("QWidget { background-color: yellow; border: 2px solid orange; margin: 0px; padding: 0px; }");
    QVBoxLayout *layout = new QVBoxLayout(m_accountPage);
    layout->setContentsMargins(5, 5, 5, 5);  // 设置小的边距确保内容不贴边

    // 临时禁用主题管理器以隔离SettingsPanel容器色块问题
    // auto themeManager = &LA::Themes::ThemeManager::instance();
    // int spacing = themeManager->getMetric(LA::Themes::ThemeManager::Metric::SpacingMedium);
    int spacing = 10; // 使用固定值替代主题间距
    layout->setSpacing(spacing);

    // 用户账号组
    m_userAccountGroup = new QGroupBox(tr("用户账号管理"), m_accountPage);
    QGridLayout *accountLayout = new QGridLayout(m_userAccountGroup);

    accountLayout->addWidget(new QLabel(tr("用户名:"), this), 0, 0);
    m_usernameLineEdit = new QLineEdit(this);
    m_usernameLineEdit->setReadOnly(true); // 当前用户名只读
    // 临时禁用主题以隔离容器色块问题
    // themeManager->applyThemeToWidget(m_usernameLineEdit, LA::Themes::ThemeManager::ComponentType::InputField);
    accountLayout->addWidget(m_usernameLineEdit, 0, 1);

    accountLayout->addWidget(new QLabel(tr("当前密码:"), this), 1, 0);
    m_currentPasswordLineEdit = new QLineEdit(this);
    m_currentPasswordLineEdit->setEchoMode(QLineEdit::Password);
    // 临时禁用主题以隔离容器色块问题
    // themeManager->applyThemeToWidget(m_currentPasswordLineEdit, LA::Themes::ThemeManager::ComponentType::InputField);
    accountLayout->addWidget(m_currentPasswordLineEdit, 1, 1);

    accountLayout->addWidget(new QLabel(tr("权限级别:"), this), 2, 0);
    m_permissionLevelComboBox = new QComboBox(this);
    m_permissionLevelComboBox->addItem(tr("管理员"), "administrator");
    m_permissionLevelComboBox->addItem(tr("操作员"), "operator");
    m_permissionLevelComboBox->addItem(tr("观察员"), "observer");
    // 临时禁用主题以隔离容器色块问题
    // themeManager->applyThemeToWidget(m_permissionLevelComboBox, LA::Themes::ThemeManager::ComponentType::ComboBox);
    accountLayout->addWidget(m_permissionLevelComboBox, 2, 1);

    m_permissionDescriptionLabel = new QLabel(tr("管理员拥有系统的完全访问权限"), this);
    // 临时禁用主题颜色以隔离容器色块问题
    // QColor mutedColor = themeManager->getSemanticColor(LA::Themes::ThemeManager::ColorRole::TextMuted);
    // QFont captionFont = themeManager->getFont(LA::Themes::ThemeManager::FontRole::Caption);
    // m_permissionDescriptionLabel->setStyleSheet(QString("color: %1;").arg(mutedColor.name()));
    // m_permissionDescriptionLabel->setFont(captionFont);
    accountLayout->addWidget(m_permissionDescriptionLabel, 3, 0, 1, 2);

    QHBoxLayout *accountButtonLayout = new QHBoxLayout();
    m_changePasswordButton = new QPushButton(tr("修改密码"), this);
    m_manageUsersButton = new QPushButton(tr("用户管理"), this);
    // 临时禁用按钮主题以隔离容器色块问题
    // themeManager->applyThemeToWidget(m_changePasswordButton, LA::Themes::ThemeManager::ComponentType::Button);
    // themeManager->applyThemeToWidget(m_manageUsersButton, LA::Themes::ThemeManager::ComponentType::Button);
    accountButtonLayout->addWidget(m_changePasswordButton);
    accountButtonLayout->addWidget(m_manageUsersButton);
    accountButtonLayout->addStretch();
    accountLayout->addLayout(accountButtonLayout, 4, 0, 1, 2);

    layout->addWidget(m_userAccountGroup);

    // 密码策略组
    m_passwordPolicyGroup = new QGroupBox(tr("密码策略"), m_accountPage);
    QGridLayout *policyLayout = new QGridLayout(m_passwordPolicyGroup);

    policyLayout->addWidget(new QLabel(tr("最小长度:"), this), 0, 0);
    m_minPasswordLengthSpinBox = new QSpinBox(this);
    m_minPasswordLengthSpinBox->setRange(4, 20);
    // 临时禁用主题以隔离容器色块问题
    // themeManager->applyThemeToWidget(m_minPasswordLengthSpinBox, LA::Themes::ThemeManager::ComponentType::InputField);
    policyLayout->addWidget(m_minPasswordLengthSpinBox, 0, 1);

    m_requireSpecialCharsCheckBox = new QCheckBox(tr("要求特殊字符"), this);
    policyLayout->addWidget(m_requireSpecialCharsCheckBox, 1, 0);

    m_requireNumbersCheckBox = new QCheckBox(tr("要求数字"), this);
    policyLayout->addWidget(m_requireNumbersCheckBox, 1, 1);

    m_requireUppercaseCheckBox = new QCheckBox(tr("要求大写字母"), this);
    policyLayout->addWidget(m_requireUppercaseCheckBox, 2, 0);

    policyLayout->addWidget(new QLabel(tr("最大登录尝试:"), this), 3, 0);
    m_maxLoginAttemptsSpinBox = new QSpinBox(this);
    m_maxLoginAttemptsSpinBox->setRange(1, 10);
    // 临时禁用主题 - themeManager->applyThemeToWidget(m_maxLoginAttemptsSpinBox, LA::Themes::ThemeManager::ComponentType::InputField);
    policyLayout->addWidget(m_maxLoginAttemptsSpinBox, 3, 1);

    policyLayout->addWidget(new QLabel(tr("会话超时(分钟):"), this), 4, 0);
    m_sessionTimeoutSpinBox = new QSpinBox(this);
    m_sessionTimeoutSpinBox->setRange(5, 120);
    // 临时禁用主题 - themeManager->applyThemeToWidget(m_sessionTimeoutSpinBox, LA::Themes::ThemeManager::ComponentType::InputField);
    policyLayout->addWidget(m_sessionTimeoutSpinBox, 4, 1);

    layout->addWidget(m_passwordPolicyGroup);
    layout->addStretch();
}

void SystemSettingsPanel::setupHardwareSettings() {
    m_hardwarePage = new QWidget();
    m_hardwarePage->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
    QVBoxLayout *layout = new QVBoxLayout(m_hardwarePage);

    // 临时禁用主题管理器以隔离SettingsPanel容器色块问题
    // auto themeManager = &LA::Themes::ThemeManager::instance();
    // int spacing = themeManager->getMetric(LA::Themes::ThemeManager::Metric::SpacingMedium);
    int spacing = 10; // 使用固定值替代主题间距
    layout->setSpacing(spacing);

    // 设备配置组
    m_deviceConfigGroup = new QGroupBox(tr("设备配置"), m_hardwarePage);
    QGridLayout *configLayout = new QGridLayout(m_deviceConfigGroup);

    configLayout->addWidget(new QLabel(tr("设备类型:"), this), 0, 0);
    m_deviceTypeComboBox = new QComboBox(this);
    m_deviceTypeComboBox->addItem(tr("串口设备"), "Serial");
    m_deviceTypeComboBox->addItem(tr("TCP/IP设备"), "TCP");
    m_deviceTypeComboBox->addItem(tr("USB设备"), "USB");
    // 临时禁用主题 - themeManager->applyThemeToWidget(m_deviceTypeComboBox, LA::Themes::ThemeManager::ComponentType::ComboBox);
    configLayout->addWidget(m_deviceTypeComboBox, 0, 1);

    configLayout->addWidget(new QLabel(tr("通信端口:"), this), 1, 0);
    m_communicationPortComboBox = new QComboBox(this);
    m_communicationPortComboBox->addItem("COM1");
    m_communicationPortComboBox->addItem("COM2");
    m_communicationPortComboBox->addItem("COM3");
    m_communicationPortComboBox->addItem("COM4");
    m_communicationPortComboBox->setEditable(true);
    // 临时禁用主题 - themeManager->applyThemeToWidget(m_communicationPortComboBox, LA::Themes::ThemeManager::ComponentType::ComboBox);
    configLayout->addWidget(m_communicationPortComboBox, 1, 1);

    configLayout->addWidget(new QLabel(tr("波特率:"), this), 2, 0);
    m_baudRateComboBox = new QComboBox(this);
    m_baudRateComboBox->addItem("9600", 9600);
    m_baudRateComboBox->addItem("19200", 19200);
    m_baudRateComboBox->addItem("38400", 38400);
    m_baudRateComboBox->addItem("57600", 57600);
    m_baudRateComboBox->addItem("115200", 115200);
    // 临时禁用主题 - themeManager->applyThemeToWidget(m_baudRateComboBox, LA::Themes::ThemeManager::ComponentType::ComboBox);
    configLayout->addWidget(m_baudRateComboBox, 2, 1);

    configLayout->addWidget(new QLabel(tr("超时时间(ms):"), this), 3, 0);
    m_timeoutSpinBox = new QSpinBox(this);
    m_timeoutSpinBox->setRange(1000, 30000);
    m_timeoutSpinBox->setSingleStep(1000);
    // 临时禁用主题 - themeManager->applyThemeToWidget(m_timeoutSpinBox, LA::Themes::ThemeManager::ComponentType::InputField);
    configLayout->addWidget(m_timeoutSpinBox, 3, 1);

    m_autoReconnectCheckBox = new QCheckBox(tr("自动重连"), this);
    configLayout->addWidget(m_autoReconnectCheckBox, 4, 0, 1, 2);

    layout->addWidget(m_deviceConfigGroup);

    // 设备列表组
    m_deviceListGroup = new QGroupBox(tr("已连接设备"), m_hardwarePage);
    QVBoxLayout *listLayout = new QVBoxLayout(m_deviceListGroup);

    m_deviceTable = new QTableWidget(this);
    m_deviceTable->setColumnCount(4);
    QStringList headers;
    headers << tr("设备名称") << tr("类型") << tr("端口") << tr("状态");
    m_deviceTable->setHorizontalHeaderLabels(headers);
    m_deviceTable->horizontalHeader()->setStretchLastSection(true);
    // 临时禁用主题 - themeManager->applyThemeToWidget(m_deviceTable, LA::Themes::ThemeManager::ComponentType::Table);
    listLayout->addWidget(m_deviceTable);

    QHBoxLayout *deviceButtonLayout = new QHBoxLayout();
    m_autoDetectButton = new QPushButton(tr("自动检测"), this);
    m_testConnectionButton = new QPushButton(tr("测试连接"), this);
    m_addDeviceButton = new QPushButton(tr("添加设备"), this);
    m_removeDeviceButton = new QPushButton(tr("移除设备"), this);

    // 临时禁用主题 - themeManager->applyThemeToWidget(m_autoDetectButton, LA::Themes::ThemeManager::ComponentType::Button);
    // 临时禁用主题 - themeManager->applyThemeToWidget(m_testConnectionButton, LA::Themes::ThemeManager::ComponentType::Button);
    // 临时禁用主题 - themeManager->applyThemeToWidget(m_addDeviceButton, LA::Themes::ThemeManager::ComponentType::Button);
    // 临时禁用主题 - themeManager->applyThemeToWidget(m_removeDeviceButton, LA::Themes::ThemeManager::ComponentType::Button);

    deviceButtonLayout->addWidget(m_autoDetectButton);
    deviceButtonLayout->addWidget(m_testConnectionButton);
    deviceButtonLayout->addWidget(m_addDeviceButton);
    deviceButtonLayout->addWidget(m_removeDeviceButton);
    deviceButtonLayout->addStretch();

    listLayout->addLayout(deviceButtonLayout);
    layout->addWidget(m_deviceListGroup);
    layout->addStretch();
}

void SystemSettingsPanel::setupSecuritySettings() {
    m_securityPage = new QWidget();
    m_securityPage->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
    QVBoxLayout *layout = new QVBoxLayout(m_securityPage);

    // 临时禁用主题管理器以隔离SettingsPanel容器色块问题
    // auto themeManager = &LA::Themes::ThemeManager::instance();
    // int spacing = themeManager->getMetric(LA::Themes::ThemeManager::Metric::SpacingMedium);
    int spacing = 10; // 使用固定值替代主题间距
    layout->setSpacing(spacing);

    // 安全级别组
    m_securityLevelGroup = new QGroupBox(tr("安全级别"), m_securityPage);
    QGridLayout *securityLayout = new QGridLayout(m_securityLevelGroup);

    securityLayout->addWidget(new QLabel(tr("安全级别:"), this), 0, 0);
    m_securityLevelComboBox = new QComboBox(this);
    m_securityLevelComboBox->addItem(tr("低"), "low");
    m_securityLevelComboBox->addItem(tr("中"), "medium");
    m_securityLevelComboBox->addItem(tr("高"), "high");
    // 临时禁用主题 - themeManager->applyThemeToWidget(m_securityLevelComboBox, LA::Themes::ThemeManager::ComponentType::ComboBox);
    securityLayout->addWidget(m_securityLevelComboBox, 0, 1);

    m_enableEncryptionCheckBox = new QCheckBox(tr("启用数据加密"), this);
    securityLayout->addWidget(m_enableEncryptionCheckBox, 1, 0);

    securityLayout->addWidget(new QLabel(tr("加密算法:"), this), 2, 0);
    m_encryptionAlgorithmComboBox = new QComboBox(this);
    m_encryptionAlgorithmComboBox->addItem("AES-128", "AES-128");
    m_encryptionAlgorithmComboBox->addItem("AES-256", "AES-256");
    m_encryptionAlgorithmComboBox->addItem("RSA-2048", "RSA-2048");
    // 临时禁用主题 - themeManager->applyThemeToWidget(m_encryptionAlgorithmComboBox, LA::Themes::ThemeManager::ComponentType::ComboBox);
    securityLayout->addWidget(m_encryptionAlgorithmComboBox, 2, 1);

    layout->addWidget(m_securityLevelGroup);

    // 审计日志组
    m_auditGroup = new QGroupBox(tr("审计日志"), m_securityPage);
    QVBoxLayout *auditLayout = new QVBoxLayout(m_auditGroup);

    m_enableAuditLogCheckBox = new QCheckBox(tr("启用操作审计"), this);
    auditLayout->addWidget(m_enableAuditLogCheckBox);

    QHBoxLayout *retentionLayout = new QHBoxLayout();
    retentionLayout->addWidget(new QLabel(tr("保留天数:"), this));
    m_auditLogRetentionSpinBox = new QSpinBox(this);
    m_auditLogRetentionSpinBox->setRange(1, 365);
    // 临时禁用主题 - themeManager->applyThemeToWidget(m_auditLogRetentionSpinBox, LA::Themes::ThemeManager::ComponentType::InputField);
    retentionLayout->addWidget(m_auditLogRetentionSpinBox);
    retentionLayout->addStretch();
    auditLayout->addLayout(retentionLayout);

    m_auditLogPreview = new QTextEdit(this);
    m_auditLogPreview->setMaximumHeight(100);
    m_auditLogPreview->setReadOnly(true);
    m_auditLogPreview->setPlainText(tr("2024-01-15 10:30:45 - 用户 admin 登录系统\n2024-01-15 10:31:20 - 用户 admin 修改设备配置\n2024-01-15 10:32:15 - 用户 admin 执行数据备份"));
    // 临时禁用主题 - themeManager->applyThemeToWidget(m_auditLogPreview, LA::Themes::ThemeManager::ComponentType::InputField);
    auditLayout->addWidget(m_auditLogPreview);

    layout->addWidget(m_auditGroup);

    // 备份设置组
    m_backupGroup = new QGroupBox(tr("数据备份"), m_securityPage);
    QGridLayout *backupLayout = new QGridLayout(m_backupGroup);

    m_enableAutoBackupCheckBox = new QCheckBox(tr("启用自动备份"), this);
    backupLayout->addWidget(m_enableAutoBackupCheckBox, 0, 0, 1, 2);

    backupLayout->addWidget(new QLabel(tr("备份间隔(小时):"), this), 1, 0);
    m_backupIntervalSpinBox = new QSpinBox(this);
    m_backupIntervalSpinBox->setRange(1, 168);
    // 临时禁用主题 - themeManager->applyThemeToWidget(m_backupIntervalSpinBox, LA::Themes::ThemeManager::ComponentType::InputField);
    backupLayout->addWidget(m_backupIntervalSpinBox, 1, 1);

    backupLayout->addWidget(new QLabel(tr("备份路径:"), this), 2, 0);
    QHBoxLayout *pathLayout = new QHBoxLayout();
    m_backupPathLineEdit = new QLineEdit(this);
    // 临时禁用主题 - themeManager->applyThemeToWidget(m_backupPathLineEdit, LA::Themes::ThemeManager::ComponentType::InputField);
    m_browseBackupPathButton = new QPushButton(tr("浏览..."), this);
    // 临时禁用主题 - themeManager->applyThemeToWidget(m_browseBackupPathButton, LA::Themes::ThemeManager::ComponentType::Button);
    pathLayout->addWidget(m_backupPathLineEdit);
    pathLayout->addWidget(m_browseBackupPathButton);
    backupLayout->addLayout(pathLayout, 2, 1);

    layout->addWidget(m_backupGroup);
    layout->addStretch();
}

void SystemSettingsPanel::setupPerformanceSettings() {
    m_performancePage = new QWidget();
    m_performancePage->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
    QVBoxLayout *layout = new QVBoxLayout(m_performancePage);

    // 临时禁用主题管理器以隔离SettingsPanel容器色块问题
    // auto themeManager = &LA::Themes::ThemeManager::instance();
    // int spacing = themeManager->getMetric(LA::Themes::ThemeManager::Metric::SpacingMedium);
    int spacing = 10; // 使用固定值替代主题间距
    layout->setSpacing(spacing);

    // 性能模式组
    m_performanceModeGroup = new QGroupBox(tr("性能模式"), m_performancePage);
    QVBoxLayout *modeLayout = new QVBoxLayout(m_performanceModeGroup);

    QHBoxLayout *modeSelectionLayout = new QHBoxLayout();
    modeSelectionLayout->addWidget(new QLabel(tr("运行模式:"), this));
    m_performanceModeComboBox = new QComboBox(this);
    m_performanceModeComboBox->addItem(tr("节能模式"), "power_save");
    m_performanceModeComboBox->addItem(tr("平衡模式"), "balanced");
    m_performanceModeComboBox->addItem(tr("高性能模式"), "high_performance");
    // 临时禁用主题 - themeManager->applyThemeToWidget(m_performanceModeComboBox, LA::Themes::ThemeManager::ComponentType::ComboBox);
    modeSelectionLayout->addWidget(m_performanceModeComboBox);
    modeSelectionLayout->addStretch();
    modeLayout->addLayout(modeSelectionLayout);

    m_performanceModeDescription = new QLabel(tr("平衡模式在性能和功耗之间取得最佳平衡"), this);
    // 临时禁用主题颜色以隔离容器色块问题
    // QColor mutedColor = themeManager->getSemanticColor(LA::Themes::ThemeManager::ColorRole::TextMuted);
    // QFont captionFont = themeManager->getFont(LA::Themes::ThemeManager::FontRole::Caption);
    // m_performanceModeDescription->setStyleSheet(QString("color: %1;").arg(mutedColor.name()));
    // m_performanceModeDescription->setFont(captionFont);
    modeLayout->addWidget(m_performanceModeDescription);

    layout->addWidget(m_performanceModeGroup);

    // 内存管理组
    m_memoryGroup = new QGroupBox(tr("内存管理"), m_performancePage);
    QGridLayout *memoryLayout = new QGridLayout(m_memoryGroup);

    memoryLayout->addWidget(new QLabel(tr("内存限制(MB):"), this), 0, 0);
    m_memoryLimitSpinBox = new QSpinBox(this);
    m_memoryLimitSpinBox->setRange(512, 8192);
    m_memoryLimitSpinBox->setSingleStep(256);
    // 临时禁用主题 - themeManager->applyThemeToWidget(m_memoryLimitSpinBox, LA::Themes::ThemeManager::ComponentType::InputField);
    memoryLayout->addWidget(m_memoryLimitSpinBox, 0, 1);

    m_memoryUsageLabel = new QLabel(tr("当前使用: 1024MB / 2048MB"), this);
    // 临时禁用主题颜色以隔离容器色块问题  
    // m_memoryUsageLabel->setStyleSheet(QString("color: %1;").arg(mutedColor.name()));
    // m_memoryUsageLabel->setFont(captionFont);
    memoryLayout->addWidget(m_memoryUsageLabel, 1, 0, 1, 2);

    m_enableMemoryOptimizationCheckBox = new QCheckBox(tr("启用内存优化"), this);
    memoryLayout->addWidget(m_enableMemoryOptimizationCheckBox, 2, 0, 1, 2);

    layout->addWidget(m_memoryGroup);

    // 处理设置组
    m_processingGroup = new QGroupBox(tr("处理设置"), m_performancePage);
    QGridLayout *processingLayout = new QGridLayout(m_processingGroup);

    processingLayout->addWidget(new QLabel(tr("线程数量:"), this), 0, 0);
    m_threadCountSpinBox = new QSpinBox(this);
    m_threadCountSpinBox->setRange(1, 16);
    // 临时禁用主题 - themeManager->applyThemeToWidget(m_threadCountSpinBox, LA::Themes::ThemeManager::ComponentType::InputField);
    processingLayout->addWidget(m_threadCountSpinBox, 0, 1);

    m_enableMultithreadingCheckBox = new QCheckBox(tr("启用多线程处理"), this);
    processingLayout->addWidget(m_enableMultithreadingCheckBox, 1, 0);

    processingLayout->addWidget(new QLabel(tr("优先级:"), this), 2, 0);
    m_priorityLevelComboBox = new QComboBox(this);
    m_priorityLevelComboBox->addItem(tr("低"), "low");
    m_priorityLevelComboBox->addItem(tr("正常"), "normal");
    m_priorityLevelComboBox->addItem(tr("高"), "high");
    // 临时禁用主题 - themeManager->applyThemeToWidget(m_priorityLevelComboBox, LA::Themes::ThemeManager::ComponentType::ComboBox);
    processingLayout->addWidget(m_priorityLevelComboBox, 2, 1);

    layout->addWidget(m_processingGroup);

    // 缓存设置组
    m_cacheGroup = new QGroupBox(tr("缓存设置"), m_performancePage);
    QGridLayout *cacheLayout = new QGridLayout(m_cacheGroup);

    m_enableCacheCheckBox = new QCheckBox(tr("启用数据缓存"), this);
    cacheLayout->addWidget(m_enableCacheCheckBox, 0, 0, 1, 2);

    cacheLayout->addWidget(new QLabel(tr("缓存大小(MB):"), this), 1, 0);
    m_cacheSizeSpinBox = new QSpinBox(this);
    m_cacheSizeSpinBox->setRange(64, 1024);
    m_cacheSizeSpinBox->setSingleStep(64);
    // 临时禁用主题 - themeManager->applyThemeToWidget(m_cacheSizeSpinBox, LA::Themes::ThemeManager::ComponentType::InputField);
    cacheLayout->addWidget(m_cacheSizeSpinBox, 1, 1);

    m_clearCacheButton = new QPushButton(tr("清除缓存"), this);
    // 临时禁用主题 - themeManager->applyThemeToWidget(m_clearCacheButton, LA::Themes::ThemeManager::ComponentType::Button);
    cacheLayout->addWidget(m_clearCacheButton, 2, 0, 1, 2);

    layout->addWidget(m_cacheGroup);
    layout->addStretch();
}

void SystemSettingsPanel::setupActionButtons() {
    // DISABLED - This method should not be called to avoid purple button issue
    qDebug() << "!!! SystemSettingsPanel::setupActionButtons() 被调用了 - 这不应该发生 !!!";
    return; // Exit early to prevent button creation
    m_buttonLayout = new QHBoxLayout();

    auto themeManager = &LA::Themes::ThemeManager::instance();
    int spacing = themeManager->getMetric(LA::Themes::ThemeManager::Metric::SpacingMedium);
    m_buttonLayout->setSpacing(spacing);

    m_exportConfigButton = new QPushButton(tr("导出配置"), this);
    m_importConfigButton = new QPushButton(tr("导入配置"), this);
    m_resetToDefaultsButton = new QPushButton(tr("恢复默认"), this);

    // 临时禁用主题 - themeManager->applyThemeToWidget(m_exportConfigButton, LA::Themes::ThemeManager::ComponentType::Button);
    // 临时禁用主题 - themeManager->applyThemeToWidget(m_importConfigButton, LA::Themes::ThemeManager::ComponentType::Button);
    // 临时禁用主题 - themeManager->applyThemeToWidget(m_resetToDefaultsButton, LA::Themes::ThemeManager::ComponentType::Button);

    m_buttonLayout->addWidget(m_exportConfigButton);
    m_buttonLayout->addWidget(m_importConfigButton);
    m_buttonLayout->addWidget(m_resetToDefaultsButton);
    m_buttonLayout->addStretch();

    m_applyButton = new QPushButton(tr("应用"), this);
    m_applyButton->setDefault(true);
    // 临时禁用主题 - themeManager->applyThemeToWidget(m_applyButton, LA::Themes::ThemeManager::ComponentType::Button);
    m_buttonLayout->addWidget(m_applyButton);
}

void SystemSettingsPanel::connectSignals() {
    // 用户账号相关信号
    connect(m_permissionLevelComboBox, QOverload<const QString &>::of(&QComboBox::currentTextChanged),
            this, &SystemSettingsPanel::onPermissionLevelChanged);
    connect(m_changePasswordButton, &QPushButton::clicked, this, &SystemSettingsPanel::onChangePasswordClicked);
    connect(m_manageUsersButton, &QPushButton::clicked, this, &SystemSettingsPanel::onManageUsersClicked);

    // 密码策略相关信号
    connect(m_minPasswordLengthSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &SystemSettingsPanel::onPasswordPolicyChanged);
    connect(m_requireSpecialCharsCheckBox, &QCheckBox::toggled, this, &SystemSettingsPanel::onPasswordPolicyChanged);
    connect(m_requireNumbersCheckBox, &QCheckBox::toggled, this, &SystemSettingsPanel::onPasswordPolicyChanged);
    connect(m_requireUppercaseCheckBox, &QCheckBox::toggled, this, &SystemSettingsPanel::onPasswordPolicyChanged);
    connect(m_maxLoginAttemptsSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &SystemSettingsPanel::onLoginAttemptsChanged);
    connect(m_sessionTimeoutSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &SystemSettingsPanel::onSessionTimeoutChanged);

    // 硬件设备相关信号
    connect(m_deviceTypeComboBox, QOverload<const QString &>::of(&QComboBox::currentTextChanged),
            this, &SystemSettingsPanel::onDeviceConfigChanged);
    connect(m_communicationPortComboBox, QOverload<const QString &>::of(&QComboBox::currentTextChanged),
            this, &SystemSettingsPanel::onCommunicationPortChanged);
    connect(m_baudRateComboBox, QOverload<const QString &>::of(&QComboBox::currentTextChanged),
            this, &SystemSettingsPanel::onBaudRateChanged);
    connect(m_timeoutSpinBox, QOverload<int>::of(&QSpinBox::valueChanged), this, &SystemSettingsPanel::onTimeoutChanged);
    connect(m_autoDetectButton, &QPushButton::clicked, this, &SystemSettingsPanel::onAutoDetectDevicesClicked);
    connect(m_testConnectionButton, &QPushButton::clicked, this, &SystemSettingsPanel::onTestConnectionClicked);

    // 安全设置相关信号
    connect(m_securityLevelComboBox, QOverload<const QString &>::of(&QComboBox::currentTextChanged),
            this, &SystemSettingsPanel::onSecurityLevelChanged);
    connect(m_enableEncryptionCheckBox, &QCheckBox::toggled, this, &SystemSettingsPanel::onEncryptionChanged);
    connect(m_enableAuditLogCheckBox, &QCheckBox::toggled, this, &SystemSettingsPanel::onAuditLogChanged);
    connect(m_enableAutoBackupCheckBox, &QCheckBox::toggled, this, &SystemSettingsPanel::onBackupSettingsChanged);
    connect(m_browseBackupPathButton, &QPushButton::clicked, [this]() {
        QString dir = QFileDialog::getExistingDirectory(this, tr("选择备份目录"), m_backupPath);
        if (!dir.isEmpty()) {
            m_backupPathLineEdit->setText(dir);
            m_backupPath = dir;
            markAsModified();
        }
    });

    // 系统性能相关信号
    connect(m_performanceModeComboBox, QOverload<const QString &>::of(&QComboBox::currentTextChanged),
            this, &SystemSettingsPanel::onPerformanceModeChanged);
    connect(m_memoryLimitSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &SystemSettingsPanel::onMemoryLimitChanged);
    connect(m_threadCountSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &SystemSettingsPanel::onThreadCountChanged);
    connect(m_enableCacheCheckBox, &QCheckBox::toggled, this, &SystemSettingsPanel::onCacheSettingsChanged);
    connect(m_clearCacheButton, &QPushButton::clicked, [this]() {
        QMessageBox::information(this, tr("清除缓存"), tr("缓存已清除"));
    });

    // 操作按钮 - DISABLED to fix purple button issue
    // connect(m_resetToDefaultsButton, &QPushButton::clicked, this, &SystemSettingsPanel::onResetToDefaults);
    // connect(m_applyButton, &QPushButton::clicked, this, &SystemSettingsPanel::applySettings);
}

void SystemSettingsPanel::loadSpecificSettings() {
    auto settings = getSettings();
    if (!settings) return;

    // 加载用户账号设置
    m_currentUsername = settings->value("username", "admin").toString();
    m_currentPermissionLevel = settings->value("permissionLevel", "administrator").toString();
    
    m_usernameLineEdit->setText(m_currentUsername);
    int permissionIndex = m_permissionLevelComboBox->findData(m_currentPermissionLevel);
    if (permissionIndex >= 0) {
        m_permissionLevelComboBox->setCurrentIndex(permissionIndex);
    }

    // 加载密码策略设置
    m_minPasswordLength = settings->value("minPasswordLength", 8).toInt();
    m_requireSpecialChars = settings->value("requireSpecialChars", true).toBool();
    m_requireNumbers = settings->value("requireNumbers", true).toBool();
    m_requireUppercase = settings->value("requireUppercase", true).toBool();
    m_maxLoginAttempts = settings->value("maxLoginAttempts", 3).toInt();
    m_sessionTimeout = settings->value("sessionTimeout", 30).toInt();

    m_minPasswordLengthSpinBox->setValue(m_minPasswordLength);
    m_requireSpecialCharsCheckBox->setChecked(m_requireSpecialChars);
    m_requireNumbersCheckBox->setChecked(m_requireNumbers);
    m_requireUppercaseCheckBox->setChecked(m_requireUppercase);
    m_maxLoginAttemptsSpinBox->setValue(m_maxLoginAttempts);
    m_sessionTimeoutSpinBox->setValue(m_sessionTimeout);

    // 加载硬件设备设置
    m_deviceType = settings->value("deviceType", "Serial").toString();
    m_communicationPort = settings->value("communicationPort", "COM1").toString();
    m_baudRate = settings->value("baudRate", 9600).toInt();
    m_connectionTimeout = settings->value("connectionTimeout", 5000).toInt();
    m_autoReconnect = settings->value("autoReconnect", true).toBool();

    int deviceTypeIndex = m_deviceTypeComboBox->findData(m_deviceType);
    if (deviceTypeIndex >= 0) {
        m_deviceTypeComboBox->setCurrentIndex(deviceTypeIndex);
    }
    m_communicationPortComboBox->setCurrentText(m_communicationPort);
    int baudRateIndex = m_baudRateComboBox->findData(m_baudRate);
    if (baudRateIndex >= 0) {
        m_baudRateComboBox->setCurrentIndex(baudRateIndex);
    }
    m_timeoutSpinBox->setValue(m_connectionTimeout);
    m_autoReconnectCheckBox->setChecked(m_autoReconnect);

    // 加载安全设置
    m_securityLevel = settings->value("securityLevel", "medium").toString();
    m_enableEncryption = settings->value("enableEncryption", true).toBool();
    m_encryptionAlgorithm = settings->value("encryptionAlgorithm", "AES-256").toString();
    m_enableAuditLog = settings->value("enableAuditLog", true).toBool();
    m_auditLogRetention = settings->value("auditLogRetention", 30).toInt();
    m_enableAutoBackup = settings->value("enableAutoBackup", true).toBool();
    m_backupInterval = settings->value("backupInterval", 24).toInt();
    m_backupPath = settings->value("backupPath", m_backupPath).toString();

    int securityLevelIndex = m_securityLevelComboBox->findData(m_securityLevel);
    if (securityLevelIndex >= 0) {
        m_securityLevelComboBox->setCurrentIndex(securityLevelIndex);
    }
    m_enableEncryptionCheckBox->setChecked(m_enableEncryption);
    int encryptionIndex = m_encryptionAlgorithmComboBox->findData(m_encryptionAlgorithm);
    if (encryptionIndex >= 0) {
        m_encryptionAlgorithmComboBox->setCurrentIndex(encryptionIndex);
    }
    m_enableAuditLogCheckBox->setChecked(m_enableAuditLog);
    m_auditLogRetentionSpinBox->setValue(m_auditLogRetention);
    m_enableAutoBackupCheckBox->setChecked(m_enableAutoBackup);
    m_backupIntervalSpinBox->setValue(m_backupInterval);
    m_backupPathLineEdit->setText(m_backupPath);

    // 加载性能设置
    m_performanceMode = settings->value("performanceMode", "balanced").toString();
    m_memoryLimit = settings->value("memoryLimit", 2048).toInt();
    m_enableMemoryOptimization = settings->value("enableMemoryOptimization", true).toBool();
    m_threadCount = settings->value("threadCount", 4).toInt();
    m_enableMultithreading = settings->value("enableMultithreading", true).toBool();
    m_priorityLevel = settings->value("priorityLevel", "normal").toString();
    m_cacheSize = settings->value("cacheSize", 256).toInt();
    m_enableCache = settings->value("enableCache", true).toBool();

    int performanceModeIndex = m_performanceModeComboBox->findData(m_performanceMode);
    if (performanceModeIndex >= 0) {
        m_performanceModeComboBox->setCurrentIndex(performanceModeIndex);
    }
    m_memoryLimitSpinBox->setValue(m_memoryLimit);
    m_enableMemoryOptimizationCheckBox->setChecked(m_enableMemoryOptimization);
    m_threadCountSpinBox->setValue(m_threadCount);
    m_enableMultithreadingCheckBox->setChecked(m_enableMultithreading);
    int priorityIndex = m_priorityLevelComboBox->findData(m_priorityLevel);
    if (priorityIndex >= 0) {
        m_priorityLevelComboBox->setCurrentIndex(priorityIndex);
    }
    m_cacheSizeSpinBox->setValue(m_cacheSize);
    m_enableCacheCheckBox->setChecked(m_enableCache);

    updatePermissionUI();
}

void SystemSettingsPanel::saveSpecificSettings() {
    auto settings = getSettings();
    if (!settings) return;

    // 保存用户账号设置
    settings->setValue("username", m_currentUsername);
    settings->setValue("permissionLevel", m_currentPermissionLevel);

    // 保存密码策略设置
    settings->setValue("minPasswordLength", m_minPasswordLength);
    settings->setValue("requireSpecialChars", m_requireSpecialChars);
    settings->setValue("requireNumbers", m_requireNumbers);
    settings->setValue("requireUppercase", m_requireUppercase);
    settings->setValue("maxLoginAttempts", m_maxLoginAttempts);
    settings->setValue("sessionTimeout", m_sessionTimeout);

    // 保存硬件设备设置
    settings->setValue("deviceType", m_deviceType);
    settings->setValue("communicationPort", m_communicationPort);
    settings->setValue("baudRate", m_baudRate);
    settings->setValue("connectionTimeout", m_connectionTimeout);
    settings->setValue("autoReconnect", m_autoReconnect);

    // 保存安全设置
    settings->setValue("securityLevel", m_securityLevel);
    settings->setValue("enableEncryption", m_enableEncryption);
    settings->setValue("encryptionAlgorithm", m_encryptionAlgorithm);
    settings->setValue("enableAuditLog", m_enableAuditLog);
    settings->setValue("auditLogRetention", m_auditLogRetention);
    settings->setValue("enableAutoBackup", m_enableAutoBackup);
    settings->setValue("backupInterval", m_backupInterval);
    settings->setValue("backupPath", m_backupPath);

    // 保存性能设置
    settings->setValue("performanceMode", m_performanceMode);
    settings->setValue("memoryLimit", m_memoryLimit);
    settings->setValue("enableMemoryOptimization", m_enableMemoryOptimization);
    settings->setValue("threadCount", m_threadCount);
    settings->setValue("enableMultithreading", m_enableMultithreading);
    settings->setValue("priorityLevel", m_priorityLevel);
    settings->setValue("cacheSize", m_cacheSize);
    settings->setValue("enableCache", m_enableCache);
}

void SystemSettingsPanel::resetSpecificSettings() {
    // 重置为默认值
    m_currentUsername = "admin";
    m_currentPermissionLevel = "administrator";
    m_minPasswordLength = 8;
    m_requireSpecialChars = true;
    m_requireNumbers = true;
    m_requireUppercase = true;
    m_maxLoginAttempts = 3;
    m_sessionTimeout = 30;

    m_deviceType = "Serial";
    m_communicationPort = "COM1";
    m_baudRate = 9600;
    m_connectionTimeout = 5000;
    m_autoReconnect = true;

    m_securityLevel = "medium";
    m_enableEncryption = true;
    m_encryptionAlgorithm = "AES-256";
    m_enableAuditLog = true;
    m_auditLogRetention = 30;
    m_enableAutoBackup = true;
    m_backupInterval = 24;
    m_backupPath = QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation) + "/LA_Backups";

    m_performanceMode = "balanced";
    m_memoryLimit = 2048;
    m_enableMemoryOptimization = true;
    m_threadCount = 4;
    m_enableMultithreading = true;
    m_priorityLevel = "normal";
    m_cacheSize = 256;
    m_enableCache = true;

    // 重新加载设置到UI
    loadSpecificSettings();
}

bool SystemSettingsPanel::validateSpecificSettings() {
    // 验证密码长度
    if (m_minPasswordLength < 4 || m_minPasswordLength > 20) {
        QMessageBox::warning(this, tr("设置错误"), tr("密码最小长度必须在4-20之间"));
        return false;
    }

    // 验证备份路径
    if (m_enableAutoBackup && m_backupPath.isEmpty()) {
        QMessageBox::warning(this, tr("设置错误"), tr("启用自动备份时必须指定备份路径"));
        return false;
    }

    return true;
}

void SystemSettingsPanel::applySpecificSettings() {
    qDebug() << "SystemSettingsPanel: 应用系统设置";

    // 应用性能设置
    if (m_performanceMode == "high_performance") {
        QMessageBox::information(this, tr("性能设置"), tr("高性能模式已启用，系统将优先考虑处理速度"));
    }

    // 应用安全设置
    if (m_enableEncryption) {
        qDebug() << "SystemSettingsPanel: 数据加密已启用，算法:" << m_encryptionAlgorithm;
    }

    // 应用硬件设置
    qDebug() << "SystemSettingsPanel: 设备配置 - 类型:" << m_deviceType << "端口:" << m_communicationPort;

    QMessageBox::information(this, tr("设置应用"), tr("系统设置已成功应用"));
}

// 槽函数实现
void SystemSettingsPanel::onUserAccountChanged() { markAsModified(); }
void SystemSettingsPanel::onPermissionLevelChanged() {
    m_currentPermissionLevel = m_permissionLevelComboBox->currentData().toString();
    updatePermissionUI();
    markAsModified();
}
void SystemSettingsPanel::onPasswordPolicyChanged() { markAsModified(); }
void SystemSettingsPanel::onLoginAttemptsChanged() { markAsModified(); }
void SystemSettingsPanel::onSessionTimeoutChanged() { markAsModified(); }
void SystemSettingsPanel::onChangePasswordClicked() {
    QMessageBox::information(this, tr("修改密码"), tr("密码修改功能尚未实现"));
}
void SystemSettingsPanel::onManageUsersClicked() {
    QMessageBox::information(this, tr("用户管理"), tr("用户管理功能尚未实现"));
}

void SystemSettingsPanel::onDeviceConfigChanged() { markAsModified(); }
void SystemSettingsPanel::onCommunicationPortChanged() { markAsModified(); }
void SystemSettingsPanel::onBaudRateChanged() { markAsModified(); }
void SystemSettingsPanel::onTimeoutChanged() { markAsModified(); }
void SystemSettingsPanel::onAutoDetectDevicesClicked() {
    QMessageBox::information(this, tr("设备检测"), tr("正在检测可用设备..."));
}
void SystemSettingsPanel::onTestConnectionClicked() {
    QMessageBox::information(this, tr("连接测试"), tr("设备连接测试成功"));
}

void SystemSettingsPanel::onSecurityLevelChanged() { markAsModified(); }
void SystemSettingsPanel::onEncryptionChanged() { markAsModified(); }
void SystemSettingsPanel::onAuditLogChanged() { markAsModified(); }
void SystemSettingsPanel::onBackupSettingsChanged() { markAsModified(); }

void SystemSettingsPanel::onPerformanceModeChanged() {
    m_performanceMode = m_performanceModeComboBox->currentData().toString();
    QString description;
    if (m_performanceMode == "power_save") {
        description = tr("节能模式优先降低功耗，可能影响处理速度");
    } else if (m_performanceMode == "balanced") {
        description = tr("平衡模式在性能和功耗之间取得最佳平衡");
    } else if (m_performanceMode == "high_performance") {
        description = tr("高性能模式优先保证处理速度，功耗较高");
    }
    m_performanceModeDescription->setText(description);
    markAsModified();
}
void SystemSettingsPanel::onMemoryLimitChanged() { markAsModified(); }
void SystemSettingsPanel::onThreadCountChanged() { markAsModified(); }
void SystemSettingsPanel::onCacheSettingsChanged() { markAsModified(); }

void SystemSettingsPanel::onResetToDefaults() {
    int ret = QMessageBox::question(this, tr("恢复默认设置"), 
                                   tr("确定要恢复所有系统设置为默认值吗？"), 
                                   QMessageBox::Yes | QMessageBox::No);
    if (ret == QMessageBox::Yes) {
        resetSpecificSettings();
    }
}

void SystemSettingsPanel::updatePermissionUI() {
    QString description;
    if (m_currentPermissionLevel == "administrator") {
        description = tr("管理员拥有系统的完全访问权限，可以修改所有设置");
    } else if (m_currentPermissionLevel == "operator") {
        description = tr("操作员可以执行日常操作，但无法修改系统配置");
    } else if (m_currentPermissionLevel == "observer") {
        description = tr("观察员只能查看数据和状态，无法执行操作");
    }
    m_permissionDescriptionLabel->setText(description);
}

void SystemSettingsPanel::validateUserCredentials() {
    // TODO: 实现用户凭据验证
}

void SystemSettingsPanel::loadHardwareDevices() {
    // TODO: 实现硬件设备加载
}

void SystemSettingsPanel::testDeviceConnection() {
    // TODO: 实现设备连接测试
}

void SystemSettingsPanel::exportSystemConfig() {
    // TODO: 实现系统配置导出
}

void SystemSettingsPanel::importSystemConfig() {
    // TODO: 实现系统配置导入
}

} // namespace Settings
} // namespace LA