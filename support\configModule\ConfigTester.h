#pragma once

#include <QJsonObject>
#include <QString>
#include <QStringList>

namespace Config {

/**
 * @brief 配置测试工具
 *
 * 用于测试配置文件的加载、验证和功能性测试
 */
class ConfigTester {
  public:
    /**
     * @brief 测试结果结构
     */
    struct TestResult {
        bool        success = false;
        QString     message;
        QStringList passedTests;
        QStringList failedTests;
        QStringList warnings;
        double      totalTime = 0.0;  // 测试总耗时（毫秒）
    };

    /**
     * @brief 测试类型枚举
     */
    enum class TestType {
        LoadTest,           // 配置加载测试
        ValidationTest,     // 配置验证测试
        CompatibilityTest,  // 兼容性测试
        PerformanceTest,    // 性能测试
        IntegrationTest,    // 集成测试
        AllTests            // 所有测试
    };

  public:
    ConfigTester();
    ~ConfigTester() = default;

    /**
     * @brief 运行指定类型的测试
     * @param testType 测试类型
     * @return 测试结果
     */
    TestResult runTest(TestType testType);

    /**
     * @brief 运行所有测试
     * @return 测试结果
     */
    TestResult runAllTests();

    /**
     * @brief 测试配置文件加载
     * @return 测试结果
     */
    TestResult testConfigLoading();

    /**
     * @brief 测试配置文件验证
     * @return 测试结果
     */
    TestResult testConfigValidation();

    /**
     * @brief 测试向后兼容性
     * @return 测试结果
     */
    TestResult testBackwardCompatibility();

    /**
     * @brief 测试配置性能
     * @return 测试结果
     */
    TestResult testConfigPerformance();

    /**
     * @brief 测试配置集成
     * @return 测试结果
     */
    TestResult testConfigIntegration();

    /**
     * @brief 生成测试报告
     * @param result 测试结果
     * @param outputPath 输出路径
     * @return 是否成功生成报告
     */
    bool generateTestReport(const TestResult &result, const QString &outputPath);

    /**
     * @brief 设置详细输出模式
     * @param verbose 是否详细输出
     */
    void setVerbose(bool verbose) {
        m_verbose = verbose;
    }

  private:
    /**
     * @brief 测试单个配置文件加载
     */
    bool testSingleConfigLoad(const QString &configPath, QStringList &errors);

    /**
     * @brief 测试配置参数有效性
     */
    bool testConfigParameters(const QJsonObject &config, QStringList &errors);

    /**
     * @brief 测试配置文件格式
     */
    bool testConfigFormat(const QString &configPath, QStringList &errors);

    /**
     * @brief 测试配置迁移功能
     */
    bool testConfigMigration(QStringList &errors);

    /**
     * @brief 记录测试日志
     */
    void logTest(const QString &testName, bool passed, const QString &message = QString());

    /**
     * @brief 获取测试配置文件列表
     */
    QStringList getTestConfigFiles() const;

  private:
    bool        m_verbose = false;
    QStringList m_testLog;
};

}  // namespace Config
