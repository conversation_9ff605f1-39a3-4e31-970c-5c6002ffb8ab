#include "IImageFilter.h"
#include "../common/ValidationUtils.h"
#include <QDebug>
#include <QtMath>

namespace ImageProcessing {

// FilterParams 基类实现
void FilterParams::validate() const {
    ValidationUtils::validateRange(strength, 0.0f, 2.0f, "strength");
}

void FilterParams::reset() {
    strength = 1.0f;
    enabled  = true;
}

std::unique_ptr<FilterParams> FilterParams::clone() const {
    return std::make_unique<FilterParams>(*this);
}

QString FilterParams::toString() const {
    return QString("FilterParams{strength=%1, enabled=%2}").arg(strength).arg(enabled ? "true" : "false");
}

// ConvolutionParams 实现
void ConvolutionParams::validate() const {
    FilterParams::validate();
    ValidationUtils::validateKernelSize(kernelSize, "kernelSize");

    if (kernel.isEmpty() || kernel.size() != kernelSize) {
        throw InvalidParameterException("kernel", "size mismatch with kernelSize");
    }

    for (const auto &row : kernel) {
        if (row.size() != kernelSize) {
            throw InvalidParameterException("kernel", "must be square matrix");
        }
    }
}

void ConvolutionParams::reset() {
    FilterParams::reset();
    kernelSize = 3;
    normalize  = true;
    kernel.clear();
    kernel.resize(3);
    for (auto &row : kernel) {
        row.resize(3);
        row.fill(0.0f);
    }
    // 设置默认的3x3单位核
    kernel[1][1] = 1.0f;
}

std::unique_ptr<FilterParams> ConvolutionParams::clone() const {
    return std::make_unique<ConvolutionParams>(*this);
}

QString ConvolutionParams::toString() const {
    return QString("ConvolutionParams{strength=%1, enabled=%2, kernelSize=%3, normalize=%4}")
        .arg(strength)
        .arg(enabled ? "true" : "false")
        .arg(kernelSize)
        .arg(normalize ? "true" : "false");
}

// GaussianParams 实现
void GaussianParams::validate() const {
    FilterParams::validate();
    ValidationUtils::validatePositive(sigma, "sigma");
    ValidationUtils::validateKernelSize(kernelSize, "kernelSize");
}

void GaussianParams::reset() {
    FilterParams::reset();
    sigma      = 1.0f;
    kernelSize = 5;
}

std::unique_ptr<FilterParams> GaussianParams::clone() const {
    return std::make_unique<GaussianParams>(*this);
}

QString GaussianParams::toString() const {
    return QString("GaussianParams{strength=%1, enabled=%2, sigma=%3, kernelSize=%4}").arg(strength).arg(enabled ? "true" : "false").arg(sigma).arg(kernelSize);
}

// MedianParams 实现
void MedianParams::validate() const {
    FilterParams::validate();
    ValidationUtils::validateKernelSize(kernelSize, "kernelSize");
}

void MedianParams::reset() {
    FilterParams::reset();
    kernelSize = 3;
}

std::unique_ptr<FilterParams> MedianParams::clone() const {
    return std::make_unique<MedianParams>(*this);
}

QString MedianParams::toString() const {
    return QString("MedianParams{strength=%1, enabled=%2, kernelSize=%3}").arg(strength).arg(enabled ? "true" : "false").arg(kernelSize);
}

// KalmanParams 实现
void KalmanParams::validate() const {
    FilterParams::validate();
    ValidationUtils::validatePositive(processNoise, "processNoise");
    ValidationUtils::validatePositive(measurementNoise, "measurementNoise");
}

void KalmanParams::reset() {
    FilterParams::reset();
    processNoise     = 0.1f;
    measurementNoise = 0.1f;
    initialEstimate  = 0.0f;
}

std::unique_ptr<FilterParams> KalmanParams::clone() const {
    return std::make_unique<KalmanParams>(*this);
}

QString KalmanParams::toString() const {
    return QString("KalmanParams{strength=%1, enabled=%2, processNoise=%3, measurementNoise=%4, initialEstimate=%5}")
        .arg(strength)
        .arg(enabled ? "true" : "false")
        .arg(processNoise)
        .arg(measurementNoise)
        .arg(initialEstimate);
}

// BilateralParams 实现
void BilateralParams::validate() const {
    FilterParams::validate();
    ValidationUtils::validatePositive(sigmaColor, "sigmaColor");
    ValidationUtils::validatePositive(sigmaSpace, "sigmaSpace");
    ValidationUtils::validateKernelSize(kernelSize, "kernelSize");
}

void BilateralParams::reset() {
    FilterParams::reset();
    sigmaColor = 75.0f;
    sigmaSpace = 75.0f;
    kernelSize = 5;
}

std::unique_ptr<FilterParams> BilateralParams::clone() const {
    return std::make_unique<BilateralParams>(*this);
}

QString BilateralParams::toString() const {
    return QString("BilateralParams{strength=%1, enabled=%2, sigmaColor=%3, sigmaSpace=%4, kernelSize=%5}")
        .arg(strength)
        .arg(enabled ? "true" : "false")
        .arg(sigmaColor)
        .arg(sigmaSpace)
        .arg(kernelSize);
}

// WeightedAverageParams 实现
void WeightedAverageParams::validate() const {
    FilterParams::validate();
    ValidationUtils::validateKernelSize(kernelSize, "kernelSize");

    // 允许weights为空，稍后通过setPredefinedWeights设置
    if (!weights.isEmpty()) {
        if (weights.size() != kernelSize) {
            throw InvalidParameterException("weights", "size mismatch with kernelSize");
        }

        for (const auto &row : weights) {
            if (row.size() != kernelSize) {
                throw InvalidParameterException("weights", "must be square matrix");
            }
        }
    }
}

void WeightedAverageParams::reset() {
    FilterParams::reset();
    kernelSize = 3;
    normalize  = true;
    weights.clear();
    weights.resize(3);
    for (auto &row : weights) {
        row.resize(3);
        row.fill(1.0f / 9.0f);  // 默认均匀权重
    }
}

std::unique_ptr<FilterParams> WeightedAverageParams::clone() const {
    return std::make_unique<WeightedAverageParams>(*this);
}

QString WeightedAverageParams::toString() const {
    return QString("WeightedAverageParams{strength=%1, enabled=%2, kernelSize=%3, normalize=%4}")
        .arg(strength)
        .arg(enabled ? "true" : "false")
        .arg(kernelSize)
        .arg(normalize ? "true" : "false");
}

// IImageFilter 基类实现
void IImageFilter::validateInput(const ImageDataU32 &data) const {
    ValidationUtils::validateImageData(data, "input image");
}

uint32_t IImageFilter::clampValue(uint32_t value, uint32_t minVal, uint32_t maxVal) const {
    if (value < minVal)
        return minVal;
    if (value > maxVal)
        return maxVal;
    return value;
}

uint32_t IImageFilter::safeFloatToUint32(float value) const {
    if (value < 0.0f)
        return 0;
    if (value > static_cast<float>(UINT32_MAX))
        return UINT32_MAX;
    return static_cast<uint32_t>(value + 0.5f);  // 四舍五入
}

}  // namespace ImageProcessing
