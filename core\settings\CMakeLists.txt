# LA设置系统模块 CMakeLists.txt

cmake_minimum_required(VERSION 3.16)
project(LA_settings VERSION 1.0.0 LANGUAGES CXX)

# 项目信息
set(TARGET_NAME LA_settings_lib)
set(MODULE_NAME "Settings")

# 包含LA库工具函数
include(${CMAKE_CURRENT_SOURCE_DIR}/../../cmake/LALibraryUtils.cmake)

# 查找Qt5组件
find_package(Qt5 REQUIRED COMPONENTS Core Widgets Gui)

# Settings库保持独立，不依赖sidebar系统

# 设置Qt5相关属性
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)

# 头文件目录
set(INCLUDE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/include)
set(PRIVATE_INCLUDE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/src)

# 公共头文件
set(PUBLIC_HEADERS
    ${INCLUDE_DIR}/LA/Settings/Settings.h
    ${INCLUDE_DIR}/LA/Settings/SettingsManager.h
    ${INCLUDE_DIR}/LA/Settings/SettingsPanel.h
    ${INCLUDE_DIR}/LA/Settings/interfaces/ISettingsManager.h
    ${INCLUDE_DIR}/LA/Settings/interfaces/ISettingsPanel.h
    ${INCLUDE_DIR}/LA/Settings/panels/GeneralSettingsPanel.h
    ${INCLUDE_DIR}/LA/Settings/panels/ThemeSettingsPanel.h
    ${INCLUDE_DIR}/LA/Settings/panels/SystemSettingsPanel.h
    # 移除sidebar相关头文件，Settings库应保持独立
    # ${INCLUDE_DIR}/LA/Settings/panels/SettingsSidebarPanel.h  # 已移除，避免循环依赖
    # ${INCLUDE_DIR}/LA/Settings/SettingsPanelFactory.h  # 已移除，避免循环依赖
)

# 源文件
set(SOURCES
    src/SettingsManager.cpp
    src/SettingsPanel.cpp
    src/panels/GeneralSettingsPanel.cpp
    src/panels/ThemeSettingsPanel.cpp
    src/panels/SystemSettingsPanel.cpp
    # 移除sidebar相关文件，Settings库应保持独立
    # src/panels/SettingsSidebarPanel.cpp  # 已移除，避免循环依赖
    # src/SettingsPanelFactory.cpp  # 已移除，避免循环依赖
    src/Settings.cpp
)

# 资源文件
set(RESOURCES
    resources/settings.qrc
)

# 创建共享库
add_library(${TARGET_NAME} SHARED
    ${PUBLIC_HEADERS}
    ${SOURCES}
    ${RESOURCES}
)

# 设置目标属性
set_target_properties(${TARGET_NAME} PROPERTIES
    VERSION 1.0.0
    SOVERSION 1
    OUTPUT_NAME "LA_settings"
    EXPORT_NAME "Settings"
)

# 包含目录
target_include_directories(${TARGET_NAME}
    PUBLIC
        $<BUILD_INTERFACE:${INCLUDE_DIR}>
        $<BUILD_INTERFACE:${SIDEBAR_INCLUDE_DIR}>
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/../../ui/themes/include>
        $<INSTALL_INTERFACE:include>
    PRIVATE
        ${PRIVATE_INCLUDE_DIR}
        ${CMAKE_CURRENT_BINARY_DIR}
)

# 链接库
target_link_libraries(${TARGET_NAME}
    PUBLIC
        Qt5::Core
        Qt5::Widgets
        Qt5::Gui
    PRIVATE
        # 链接主题系统以支持主题设置面板
        LA_themes_lib
)

# 编译定义
target_compile_definitions(${TARGET_NAME}
    PRIVATE
        LA_SETTINGS_LIBRARY
        # 暂时注释掉严格的字符串转换限制，等代码稳定后再启用
        # QT_NO_CAST_FROM_ASCII
        # QT_NO_CAST_TO_ASCII
        # QT_NO_URL_CAST_FROM_STRING
        # QT_NO_CAST_FROM_BYTEARRAY
)

# 编译选项
if(MSVC)
    target_compile_options(${TARGET_NAME} PRIVATE /W4)
else()
    target_compile_options(${TARGET_NAME} PRIVATE -Wall -Wextra -Wpedantic)
endif()

# C++标准
set_property(TARGET ${TARGET_NAME} PROPERTY CXX_STANDARD 17)
set_property(TARGET ${TARGET_NAME} PROPERTY CXX_STANDARD_REQUIRED ON)

# 安装规则
install(TARGETS ${TARGET_NAME}
    EXPORT LA_SettingsTargets
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
    INCLUDES DESTINATION include
)

# 安装头文件
install(DIRECTORY ${INCLUDE_DIR}/LA
    DESTINATION include
    FILES_MATCHING PATTERN "*.h"
)

# 安装资源文件
install(DIRECTORY resources/
    DESTINATION share/LA/settings
    FILES_MATCHING 
    PATTERN "*.png"
    PATTERN "*.svg"
    PATTERN "*.json"
    PATTERN "*.ini"
)

# 导出目标
install(EXPORT LA_SettingsTargets
    FILE LA_SettingsTargets.cmake
    NAMESPACE LA::
    DESTINATION lib/cmake/LA
)

# 创建配置文件
include(CMakePackageConfigHelpers)
write_basic_package_version_file(
    "${CMAKE_CURRENT_BINARY_DIR}/LA_SettingsConfigVersion.cmake"
    VERSION 1.0.0
    COMPATIBILITY AnyNewerVersion
)

configure_package_config_file(
    "${CMAKE_CURRENT_SOURCE_DIR}/LA_SettingsConfig.cmake.in"
    "${CMAKE_CURRENT_BINARY_DIR}/LA_SettingsConfig.cmake"
    INSTALL_DESTINATION lib/cmake/LA
)

install(FILES
    "${CMAKE_CURRENT_BINARY_DIR}/LA_SettingsConfig.cmake"
    "${CMAKE_CURRENT_BINARY_DIR}/LA_SettingsConfigVersion.cmake"
    DESTINATION lib/cmake/LA
)

# 调试信息
message(STATUS "LA Settings Library Configuration:")
message(STATUS "  Target Name: ${TARGET_NAME}")
message(STATUS "  Include Directory: ${INCLUDE_DIR}")
message(STATUS "  Source Files: ${SOURCES}")
message(STATUS "  Public Headers: ${PUBLIC_HEADERS}")

# 添加到父项目的库列表（如果存在）
if(DEFINED LA_CORE_LIBRARIES)
    list(APPEND LA_CORE_LIBRARIES ${TARGET_NAME})
    set(LA_CORE_LIBRARIES ${LA_CORE_LIBRARIES} PARENT_SCOPE)
endif()

# 注册为LA共享库，自动复制到运行时目录
register_la_shared_library(${TARGET_NAME})
