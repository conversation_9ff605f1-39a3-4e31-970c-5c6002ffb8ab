#include "LA/DeviceManagement/Discovery/SimpleDeviceDiscoveryService.h"
#include <QDebug>
#include <QDateTime>
#include <QSerialPortInfo>
#include <QNetworkInterface>
#include <QUuid>

namespace LA {
namespace DeviceManagement {
namespace Discovery {

// ==================== SimpleDeviceDiscoveryService ====================

SimpleDeviceDiscoveryService::SimpleDeviceDiscoveryService(QObject* parent)
    : IDeviceDiscoveryService(parent)
    , m_state(ServiceState::Stopped)
    , m_initialized(false)
    , m_discoveryTimer(nullptr)
    , m_totalScans(0)
    , m_totalDevicesFound(0)
    , m_currentDeviceCount(0)
{
    qDebug() << "SimpleDeviceDiscoveryService: 构造函数";
    
    // 创建发现定时器
    m_discoveryTimer = new QTimer(this);
    connect(m_discoveryTimer, &QTimer::timeout, this, &SimpleDeviceDiscoveryService::onDiscoveryTimer);
    
    // 初始化统计信息
    updateStatistics();
}

SimpleDeviceDiscoveryService::~SimpleDeviceDiscoveryService()
{
    qDebug() << "SimpleDeviceDiscoveryService: 析构函数";
    shutdown();
}

bool SimpleDeviceDiscoveryService::initialize(const DiscoveryConfig& config)
{
    QMutexLocker locker(&m_mutex);
    
    qDebug() << "SimpleDeviceDiscoveryService: 初始化开始";
    
    if (m_initialized) {
        qWarning() << "SimpleDeviceDiscoveryService: 已经初始化";
        return true;
    }
    
    m_config = config;
    m_state = ServiceState::Starting;
    
    try {
        // 添加默认发现策略
        if (config.enabledDiscoverers.isEmpty() || config.enabledDiscoverers.contains("serial")) {
            auto serialStrategy = std::make_shared<SimpleSerialDiscoveryStrategy>(this);
            if (serialStrategy->initialize()) {
                addDiscoveryStrategy(serialStrategy);
                qDebug() << "SimpleDeviceDiscoveryService: 串口发现策略已添加";
            }
        }
        
        if (config.enabledDiscoverers.isEmpty() || config.enabledDiscoverers.contains("virtual")) {
            auto virtualStrategy = std::make_shared<VirtualDeviceDiscoveryStrategy>(this);
            if (virtualStrategy->initialize()) {
                addDiscoveryStrategy(virtualStrategy);
                qDebug() << "SimpleDeviceDiscoveryService: 虚拟设备发现策略已添加";
            }
        }
        
        m_serviceStartTime = QDateTime::currentDateTime();
        m_initialized = true;
        m_state = ServiceState::Stopped;
        
        emit serviceStateChanged(m_state);
        qDebug() << "SimpleDeviceDiscoveryService: 初始化完成";
        return true;
        
    } catch (const std::exception& e) {
        qWarning() << "SimpleDeviceDiscoveryService: 初始化异常:" << e.what();
        m_state = ServiceState::Error;
        emit serviceStateChanged(m_state);
        return false;
    }
}

void SimpleDeviceDiscoveryService::shutdown()
{
    QMutexLocker locker(&m_mutex);
    
    qDebug() << "SimpleDeviceDiscoveryService: 关闭";
    
    if (!m_initialized) {
        return;
    }
    
    m_state = ServiceState::Stopping;
    emit serviceStateChanged(m_state);
    
    // 停止发现
    if (m_discoveryTimer) {
        m_discoveryTimer->stop();
    }
    
    // 清理策略
    m_strategies.clear();
    m_discoveredDevices.clear();
    
    m_initialized = false;
    m_state = ServiceState::Stopped;
    emit serviceStateChanged(m_state);
    
    qDebug() << "SimpleDeviceDiscoveryService: 关闭完成";
}

bool SimpleDeviceDiscoveryService::startDiscovery()
{
    QMutexLocker locker(&m_mutex);
    
    qDebug() << "SimpleDeviceDiscoveryService: 启动发现";
    
    if (!m_initialized) {
        qWarning() << "SimpleDeviceDiscoveryService: 服务未初始化";
        return false;
    }
    
    if (m_state == ServiceState::Running) {
        qDebug() << "SimpleDeviceDiscoveryService: 发现已在运行";
        return true;
    }
    
    m_state = ServiceState::Starting;
    emit serviceStateChanged(m_state);
    
    try {
        // 执行初始扫描
        performScan();
        
        // 启动周期性发现
        if (m_config.autoDiscovery && m_config.discoveryInterval > 0) {
            m_discoveryTimer->start(m_config.discoveryInterval);
            qDebug() << "SimpleDeviceDiscoveryService: 周期性发现已启动，间隔:" << m_config.discoveryInterval << "ms";
        }
        
        m_state = ServiceState::Running;
        emit serviceStateChanged(m_state);
        
        qDebug() << "SimpleDeviceDiscoveryService: 发现启动成功";
        return true;
        
    } catch (const std::exception& e) {
        qWarning() << "SimpleDeviceDiscoveryService: 启动发现异常:" << e.what();
        m_state = ServiceState::Error;
        emit serviceStateChanged(m_state);
        return false;
    }
}

void SimpleDeviceDiscoveryService::stopDiscovery()
{
    QMutexLocker locker(&m_mutex);
    
    qDebug() << "SimpleDeviceDiscoveryService: 停止发现";
    
    m_state = ServiceState::Stopping;
    emit serviceStateChanged(m_state);
    
    if (m_discoveryTimer) {
        m_discoveryTimer->stop();
    }
    
    m_state = ServiceState::Stopped;
    emit serviceStateChanged(m_state);
    
    qDebug() << "SimpleDeviceDiscoveryService: 发现已停止";
}

int SimpleDeviceDiscoveryService::performScan()
{
    QMutexLocker locker(&m_mutex);
    
    qDebug() << "SimpleDeviceDiscoveryService: 执行设备扫描";
    
    if (!m_initialized) {
        qWarning() << "SimpleDeviceDiscoveryService: 服务未初始化";
        return 0;
    }
    
    int totalFound = 0;
    m_totalScans++;
    m_lastScanTime = QDateTime::currentDateTime();
    
    // 使用所有策略进行发现
    for (auto& strategy : m_strategies) {
        try {
            QList<DeviceInfo> devices = strategy->discoverDevices();
            for (const DeviceInfo& device : devices) {
                addDiscoveredDevice(device);
                totalFound++;
            }
        } catch (const std::exception& e) {
            QString error = QString("策略 %1 发现异常: %2").arg(strategy->getStrategyId()).arg(e.what());
            qWarning() << "SimpleDeviceDiscoveryService:" << error;
            emit discoveryError(error);
        }
    }
    
    m_totalDevicesFound += totalFound;
    updateStatistics();
    
    emit discoveryCompleted(totalFound);
    qDebug() << "SimpleDeviceDiscoveryService: 扫描完成，发现设备数:" << totalFound;
    
    return totalFound;
}

bool SimpleDeviceDiscoveryService::addDiscoveryStrategy(std::shared_ptr<IDiscoveryStrategy> strategy)
{
    if (!strategy) {
        qWarning() << "SimpleDeviceDiscoveryService: 策略为空";
        return false;
    }
    
    QMutexLocker locker(&m_mutex);
    
    QString strategyId = strategy->getStrategyId();
    
    if (m_strategies.contains(strategyId)) {
        qWarning() << "SimpleDeviceDiscoveryService: 策略已存在:" << strategyId;
        return false;
    }
    
    // 连接策略信号
    connect(strategy.get(), &IDiscoveryStrategy::deviceFound, 
            this, &SimpleDeviceDiscoveryService::onStrategyDeviceFound);
    connect(strategy.get(), &IDiscoveryStrategy::strategyError, 
            this, &SimpleDeviceDiscoveryService::onStrategyError);
    
    m_strategies[strategyId] = strategy;
    
    qDebug() << "SimpleDeviceDiscoveryService: 策略已添加:" << strategyId;
    return true;
}

bool SimpleDeviceDiscoveryService::removeDiscoveryStrategy(const QString& strategyId)
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_strategies.contains(strategyId)) {
        qWarning() << "SimpleDeviceDiscoveryService: 策略不存在:" << strategyId;
        return false;
    }
    
    m_strategies.remove(strategyId);
    qDebug() << "SimpleDeviceDiscoveryService: 策略已移除:" << strategyId;
    return true;
}

QList<DeviceInfo> SimpleDeviceDiscoveryService::getDiscoveredDevices() const
{
    QMutexLocker locker(&m_mutex);
    return m_discoveredDevices.values();
}

QList<DeviceInfo> SimpleDeviceDiscoveryService::getDiscoveredDevicesByType(const QString& deviceType) const
{
    QMutexLocker locker(&m_mutex);
    
    QList<DeviceInfo> result;
    for (const DeviceInfo& device : m_discoveredDevices.values()) {
        if (QString::number(static_cast<int>(device.deviceType)) == deviceType || 
            device.parameters.value("deviceTypeName").toString() == deviceType) {
            result.append(device);
        }
    }
    return result;
}

bool SimpleDeviceDiscoveryService::isDeviceDiscovered(const QString& deviceId) const
{
    QMutexLocker locker(&m_mutex);
    return m_discoveredDevices.contains(deviceId);
}

QVariantMap SimpleDeviceDiscoveryService::getDiscoveryStatistics() const
{
    QMutexLocker locker(&m_mutex);
    return m_statistics;
}

void SimpleDeviceDiscoveryService::setDiscoveryConfig(const DiscoveryConfig& config)
{
    QMutexLocker locker(&m_mutex);
    
    m_config = config;
    
    // 如果正在运行，重新启动定时器
    if (m_state == ServiceState::Running && m_discoveryTimer) {
        m_discoveryTimer->stop();
        if (config.autoDiscovery && config.discoveryInterval > 0) {
            m_discoveryTimer->start(config.discoveryInterval);
        }
    }
    
    qDebug() << "SimpleDeviceDiscoveryService: 配置已更新";
}

DiscoveryConfig SimpleDeviceDiscoveryService::getDiscoveryConfig() const
{
    QMutexLocker locker(&m_mutex);
    return m_config;
}

ServiceState SimpleDeviceDiscoveryService::getServiceState() const
{
    QMutexLocker locker(&m_mutex);
    return m_state;
}

void SimpleDeviceDiscoveryService::onDiscoveryTimer()
{
    qDebug() << "SimpleDeviceDiscoveryService: 周期性发现触发";
    performScan();
}

void SimpleDeviceDiscoveryService::onStrategyDeviceFound(const DeviceInfo& device)
{
    qDebug() << "SimpleDeviceDiscoveryService: 策略发现设备:" << device.deviceName;
    addDiscoveredDevice(device);
}

void SimpleDeviceDiscoveryService::onStrategyError(const QString& error)
{
    qWarning() << "SimpleDeviceDiscoveryService: 策略错误:" << error;
    emit discoveryError(error);
}

void SimpleDeviceDiscoveryService::addDiscoveredDevice(const DeviceInfo& device)
{
    if (device.deviceId.isEmpty()) {
        qWarning() << "SimpleDeviceDiscoveryService: 设备ID为空，忽略";
        return;
    }
    
    bool isNewDevice = !m_discoveredDevices.contains(device.deviceId);
    m_discoveredDevices[device.deviceId] = device;
    m_currentDeviceCount = m_discoveredDevices.size();
    
    if (isNewDevice) {
        qDebug() << "SimpleDeviceDiscoveryService: 新设备发现:" << device.deviceName;
        emit deviceDiscovered(device);
    }
    
    updateStatistics();
}

void SimpleDeviceDiscoveryService::removeDiscoveredDevice(const QString& deviceId)
{
    if (m_discoveredDevices.contains(deviceId)) {
        m_discoveredDevices.remove(deviceId);
        m_currentDeviceCount = m_discoveredDevices.size();
        emit deviceLost(deviceId);
        updateStatistics();
        qDebug() << "SimpleDeviceDiscoveryService: 设备丢失:" << deviceId;
    }
}

bool SimpleDeviceDiscoveryService::verifyDiscoveredDevice(const QString& deviceId)
{
    if (!m_discoveredDevices.contains(deviceId)) {
        return false;
    }
    
    const DeviceInfo& device = m_discoveredDevices[deviceId];
    
    // 使用策略验证设备
    for (auto& strategy : m_strategies) {
        if (strategy->getSupportedDeviceTypes().contains(QString::number(static_cast<int>(device.deviceType)))) {
            if (!strategy->verifyDevice(device)) {
                return false;
            }
        }
    }
    
    return true;
}

void SimpleDeviceDiscoveryService::updateStatistics()
{
    m_statistics.clear();
    m_statistics["totalScans"] = m_totalScans;
    m_statistics["totalDevicesFound"] = m_totalDevicesFound;
    m_statistics["currentDeviceCount"] = m_currentDeviceCount;
    m_statistics["strategiesCount"] = m_strategies.size();
    m_statistics["serviceState"] = static_cast<int>(m_state);
    m_statistics["lastScanTime"] = m_lastScanTime.toString(Qt::ISODate);
    m_statistics["serviceStartTime"] = m_serviceStartTime.toString(Qt::ISODate);
    
    if (m_serviceStartTime.isValid()) {
        qint64 uptime = m_serviceStartTime.secsTo(QDateTime::currentDateTime());
        m_statistics["uptimeSeconds"] = uptime;
    }
}

QString SimpleDeviceDiscoveryService::generateDeviceId(const QString& deviceName, const QString& deviceType)
{
    return QString("%1_%2_%3").arg(deviceType).arg(deviceName).arg(QUuid::createUuid().toString().mid(1, 8));
}

// ==================== SimpleSerialDiscoveryStrategy ====================

SimpleSerialDiscoveryStrategy::SimpleSerialDiscoveryStrategy(QObject* parent)
    : IDiscoveryStrategy(parent)
    , m_scanCount(0)
    , m_devicesFound(0)
{
}

QString SimpleSerialDiscoveryStrategy::getStrategyId() const
{
    return "simple_serial_discovery";
}

QString SimpleSerialDiscoveryStrategy::getStrategyName() const
{
    return "Simple Serial Port Discovery";
}

QStringList SimpleSerialDiscoveryStrategy::getSupportedDeviceTypes() const
{
    return QStringList() << QString::number(static_cast<int>(LA::Foundation::Core::DeviceType::Serial));
}

bool SimpleSerialDiscoveryStrategy::initialize(const QVariantMap& config)
{
    Q_UNUSED(config)
    qDebug() << "SimpleSerialDiscoveryStrategy: 初始化";
    return true;
}

QList<DeviceInfo> SimpleSerialDiscoveryStrategy::discoverDevices()
{
    qDebug() << "SimpleSerialDiscoveryStrategy: 发现串口设备";
    
    m_scanCount++;
    QList<DeviceInfo> devices;
    
    // 扫描串口
    QList<QSerialPortInfo> ports = QSerialPortInfo::availablePorts();
    
    for (const QSerialPortInfo& portInfo : ports) {
        DeviceInfo device;
        device.deviceId = QString("serial_%1").arg(portInfo.portName());
        device.deviceName = QString("Serial Device (%1)").arg(portInfo.portName());
        device.deviceType = LA::Foundation::Core::DeviceType::Serial;
        device.manufacturer = portInfo.manufacturer().isEmpty() ? "Unknown" : portInfo.manufacturer();
        device.model = portInfo.description().isEmpty() ? "Serial Device" : portInfo.description();
        device.description = QString("Serial device on port %1").arg(portInfo.portName());
        
        // 添加端口特定参数
        device.parameters["portName"] = portInfo.portName();
        device.parameters["systemLocation"] = portInfo.systemLocation();
        device.parameters["description"] = portInfo.description();
        device.parameters["manufacturer"] = portInfo.manufacturer();
        device.parameters["serialNumber"] = portInfo.serialNumber();
        device.parameters["vendorId"] = portInfo.vendorIdentifier();
        device.parameters["productId"] = portInfo.productIdentifier();
        device.parameters["deviceTypeName"] = "Serial";
        
        device.supportedPorts << portInfo.portName();
        
        devices.append(device);
        m_devicesFound++;
        
        qDebug() << "SimpleSerialDiscoveryStrategy: 发现串口设备:" << device.deviceName;
    }
    
    // 更新统计信息
    m_statistics["scanCount"] = m_scanCount;
    m_statistics["devicesFound"] = m_devicesFound;
    m_statistics["lastScanDevices"] = devices.size();
    m_statistics["lastScanTime"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    
    return devices;
}

bool SimpleSerialDiscoveryStrategy::verifyDevice(const DeviceInfo& device)
{
    if (device.deviceType != LA::Foundation::Core::DeviceType::Serial) {
        return false;
    }
    
    QString portName = device.parameters.value("portName").toString();
    if (portName.isEmpty()) {
        return false;
    }
    
    // 检查端口是否仍然存在
    QList<QSerialPortInfo> ports = QSerialPortInfo::availablePorts();
    for (const QSerialPortInfo& portInfo : ports) {
        if (portInfo.portName() == portName) {
            return true;
        }
    }
    
    return false;
}

QVariantMap SimpleSerialDiscoveryStrategy::getStatistics() const
{
    return m_statistics;
}

// ==================== VirtualDeviceDiscoveryStrategy ====================

VirtualDeviceDiscoveryStrategy::VirtualDeviceDiscoveryStrategy(QObject* parent)
    : IDiscoveryStrategy(parent)
    , m_scanCount(0)
    , m_virtualDeviceCount(3)
{
}

QString VirtualDeviceDiscoveryStrategy::getStrategyId() const
{
    return "virtual_device_discovery";
}

QString VirtualDeviceDiscoveryStrategy::getStrategyName() const
{
    return "Virtual Device Discovery";
}

QStringList VirtualDeviceDiscoveryStrategy::getSupportedDeviceTypes() const
{
    return QStringList() << QString::number(static_cast<int>(LA::Foundation::Core::DeviceType::Virtual));
}

bool VirtualDeviceDiscoveryStrategy::initialize(const QVariantMap& config)
{
    m_virtualDeviceCount = config.value("virtualDeviceCount", 3).toInt();
    qDebug() << "VirtualDeviceDiscoveryStrategy: 初始化，虚拟设备数量:" << m_virtualDeviceCount;
    return true;
}

QList<DeviceInfo> VirtualDeviceDiscoveryStrategy::discoverDevices()
{
    qDebug() << "VirtualDeviceDiscoveryStrategy: 发现虚拟设备";
    
    m_scanCount++;
    QList<DeviceInfo> devices;
    
    // 创建虚拟设备
    for (int i = 1; i <= m_virtualDeviceCount; ++i) {
        DeviceInfo device;
        device.deviceId = QString("virtual_device_%1").arg(i);
        device.deviceName = QString("Virtual Device %1").arg(i);
        device.deviceType = LA::Foundation::Core::DeviceType::Virtual;
        device.manufacturer = "LA Virtual";
        device.model = QString("VirtualDevice-Model-%1").arg(i);
        device.description = QString("Virtual test device #%1").arg(i);
        device.firmwareVersion = LA::Foundation::Core::VersionInfo(1, 0, 0, "virtual");
        
        // 添加虚拟设备参数
        device.parameters["virtualId"] = i;
        device.parameters["simulatedData"] = true;
        device.parameters["deviceTypeName"] = "Virtual";
        device.parameters["status"] = "Available";
        
        device.supportedPorts << QString("virtual_port_%1").arg(i);
        
        devices.append(device);
        
        qDebug() << "VirtualDeviceDiscoveryStrategy: 发现虚拟设备:" << device.deviceName;
    }
    
    // 更新统计信息
    m_statistics["scanCount"] = m_scanCount;
    m_statistics["virtualDeviceCount"] = m_virtualDeviceCount;
    m_statistics["lastScanDevices"] = devices.size();
    m_statistics["lastScanTime"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    
    return devices;
}

bool VirtualDeviceDiscoveryStrategy::verifyDevice(const DeviceInfo& device)
{
    // 虚拟设备总是可用的
    return device.deviceType == LA::Foundation::Core::DeviceType::Virtual;
}

QVariantMap VirtualDeviceDiscoveryStrategy::getStatistics() const
{
    return m_statistics;
}

// ==================== 工厂函数 ====================

std::shared_ptr<IDeviceDiscoveryService> createDeviceDiscoveryService(QObject* parent)
{
    qDebug() << "创建设备发现服务实例";
    return std::make_shared<SimpleDeviceDiscoveryService>(parent);
}

} // namespace Discovery
} // namespace DeviceManagement
} // namespace LA

#include "SimpleDeviceDiscoveryService.moc"