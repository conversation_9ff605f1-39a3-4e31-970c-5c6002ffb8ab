# Parameter Management Module
cmake_minimum_required(VERSION 3.16)

# 设置项目信息
set(MODULE_NAME "LA_Support_Config_Parameter")
set(MODULE_VERSION "1.0.0")

# 查找依赖
find_package(Qt5 REQUIRED COMPONENTS Core)

# 设置源文件
set(HEADERS
    IParameterManager.h
    ParameterManager.h
)

set(SOURCES
    ParameterManager.cpp
)

# 创建库
add_library(${MODULE_NAME} STATIC ${SOURCES} ${HEADERS})

# 设置目标属性
set_target_properties(${MODULE_NAME} PROPERTIES
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
    VERSION ${MODULE_VERSION}
    EXPORT_NAME Config::Parameter
)

# 链接依赖
target_link_libraries(${MODULE_NAME}
    PUBLIC
        Qt5::Core
        LA_Support_Foundation
)

# 设置包含目录
target_include_directories(${MODULE_NAME}
    PUBLIC
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/..>
        $<INSTALL_INTERFACE:include>
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}
)

# 编译定义
target_compile_definitions(${MODULE_NAME}
    PUBLIC
        LA_SUPPORT_CONFIG_PARAMETER_LIBRARY
    PRIVATE
        QT_NO_KEYWORDS
        QT_USE_QSTRINGBUILDER
)

# 安装规则
install(TARGETS ${MODULE_NAME}
    EXPORT LA_Support_Config_ParameterTargets
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
    INCLUDES DESTINATION include
)

# 安装头文件
install(FILES ${HEADERS}
    DESTINATION include/LA/Support/Config/Parameter
)

# 导出目标
install(EXPORT LA_Support_Config_ParameterTargets
    FILE LA_Support_Config_ParameterTargets.cmake
    NAMESPACE LA::Support::Config::
    DESTINATION lib/cmake/LA_Support_Config_Parameter
)

# 创建配置文件
include(CMakePackageConfigHelpers)

configure_package_config_file(
    "${CMAKE_CURRENT_SOURCE_DIR}/Config.cmake.in"
    "${CMAKE_CURRENT_BINARY_DIR}/LA_Support_Config_ParameterConfig.cmake"
    INSTALL_DESTINATION lib/cmake/LA_Support_Config_Parameter
)

write_basic_package_version_file(
    "${CMAKE_CURRENT_BINARY_DIR}/LA_Support_Config_ParameterConfigVersion.cmake"
    VERSION ${MODULE_VERSION}
    COMPATIBILITY SameMajorVersion
)

install(FILES
    "${CMAKE_CURRENT_BINARY_DIR}/LA_Support_Config_ParameterConfig.cmake"
    "${CMAKE_CURRENT_BINARY_DIR}/LA_Support_Config_ParameterConfigVersion.cmake"
    DESTINATION lib/cmake/LA_Support_Config_Parameter
)

# 测试
if(BUILD_TESTING)
    add_subdirectory(tests)
endif()

# 文档
if(BUILD_DOCUMENTATION)
    # 添加Doxygen文档生成
endif()

# 打印构建信息
message(STATUS "Configuring ${MODULE_NAME} v${MODULE_VERSION}")
message(STATUS "  - Source files: ${SOURCES}")
message(STATUS "  - Header files: ${HEADERS}")
message(STATUS "  - Install prefix: ${CMAKE_INSTALL_PREFIX}")