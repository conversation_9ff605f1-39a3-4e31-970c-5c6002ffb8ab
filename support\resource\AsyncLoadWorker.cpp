#include "AsyncLoadWorker.h"

namespace LA {
namespace Support {
namespace Resource {

AsyncLoadWorker::AsyncLoadWorker(QObject* parent) 
    : QObject(parent), m_processing(false) {
}

AsyncLoadWorker::~AsyncLoadWorker() = default;

void AsyncLoadWorker::addTask(const LoadTask& task) {
    m_taskQueue.append(task);
    if (!m_processing) {
        processQueue();
    }
}

void AsyncLoadWorker::processQueue() {
    if (m_taskQueue.isEmpty()) {
        m_processing = false;
        return;
    }
    
    m_processing = true;
    // 处理第一个任务
    LoadTask task = m_taskQueue.takeFirst();
    
    // 这里应该有实际的资源加载逻辑
    // 现在只是一个占位符实现
    emit taskCompleted(task.id, ResourceData());
}

void AsyncLoadWorker::handleTaskCompletion() {
    // 继续处理队列中的下一个任务
    if (!m_taskQueue.isEmpty()) {
        processQueue();
    } else {
        m_processing = false;
    }
}

}  // namespace Resource
}  // namespace Support
}  // namespace LA