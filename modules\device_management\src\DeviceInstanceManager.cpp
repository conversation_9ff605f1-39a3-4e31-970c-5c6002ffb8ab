#include "LA/DeviceManagement/DeviceInstanceManager.h"
// #include "LA/Device/BaseDevice.h"  // 暂时移除，文件不存在
#include "LA/Device/Core/IDevice.h"  // 添加IDevice接口
#include <QDebug>
#include <QMutexLocker>
#include <QUuid>

namespace LA::DeviceManagement {

DeviceInstanceManager::DeviceInstanceManager(QObject *parent) : QObject(parent), m_nextInstanceNumber(1) {
    // 注册元类型以支持信号槽
    qRegisterMetaType<InstanceLifecycle>("InstanceLifecycle");
    qRegisterMetaType<DeviceInstanceInfo>("DeviceInstanceInfo");

    qDebug() << "DeviceInstanceManager initialized";
}

DeviceInstanceManager::~DeviceInstanceManager() {
    QMutexLocker locker(&m_mutex);

    // 销毁所有活动实例
    for (auto it = m_activeInstances.begin(); it != m_activeInstances.end(); ++it) {
        if (it->deviceObject) {
            destroyDeviceObject(it->deviceObject);
        }
    }
    m_activeInstances.clear();

    qDebug() << "DeviceInstanceManager destroyed";
}

QString DeviceInstanceManager::createDeviceInstance(const QString &deviceType, const QVariantMap &config) {
    QMutexLocker locker(&m_mutex);
    m_lastError.clear();

    // 验证设备类型
    if (!validateDeviceType(deviceType)) {
        m_lastError = QString("Invalid device type: %1").arg(deviceType);
        qWarning() << m_lastError;
        return QString();
    }

    // 生成实例ID
    QString instanceId = generateInstanceId(deviceType);

    // 创建实例数据
    DeviceInstanceData instanceData;
    instanceData.instanceId     = instanceId;
    instanceData.deviceType     = deviceType;
    instanceData.instanceConfig = config;
    instanceData.lifecycle      = InstanceLifecycle::Created;

    // 创建设备对象
    instanceData.deviceObject = createDeviceObject(deviceType, instanceId);
    if (!instanceData.deviceObject) {
        m_lastError = QString("Failed to create device object for type: %1").arg(deviceType);
        qWarning() << m_lastError;
        return QString();
    }

    // 连接设备信号
    connectDeviceSignals(instanceData.deviceObject, instanceId);

    // 存储实例
    m_activeInstances[instanceId] = instanceData;

    emit instanceCreated(instanceId);
    qDebug() << QString("Device instance created: %1 (type: %2)").arg(instanceId, deviceType);

    return instanceId;
}

bool DeviceInstanceManager::destroyDeviceInstance(const QString &instanceId) {
    QMutexLocker locker(&m_mutex);
    m_lastError.clear();

    if (!validateInstanceId(instanceId)) {
        m_lastError = QString("Invalid instance ID: %1").arg(instanceId);
        qWarning() << m_lastError;
        return false;
    }

    DeviceInstanceData &instanceData = m_activeInstances[instanceId];

    // 停止实例（如果正在运行）
    if (instanceData.lifecycle == InstanceLifecycle::Started || instanceData.lifecycle == InstanceLifecycle::Connected) {
        stopInstance(instanceId);
    }

    // 销毁设备对象
    if (instanceData.deviceObject) {
        disconnectDeviceSignals(instanceData.deviceObject);
        destroyDeviceObject(instanceData.deviceObject);
        instanceData.deviceObject = nullptr;
    }

    // 更新生命周期
    setInstanceLifecycle(instanceId, InstanceLifecycle::Destroyed);

    // 移除实例
    m_activeInstances.remove(instanceId);

    emit instanceDestroyed(instanceId);
    qDebug() << QString("Device instance destroyed: %1").arg(instanceId);

    return true;
}

QStringList DeviceInstanceManager::getActiveInstanceIds() const {
    QMutexLocker locker(&m_mutex);
    return m_activeInstances.keys();
}

QStringList DeviceInstanceManager::getInstancesByType(const QString &deviceType) const {
    QMutexLocker locker(&m_mutex);
    QStringList  result;

    for (auto it = m_activeInstances.constBegin(); it != m_activeInstances.constEnd(); ++it) {
        if (it->deviceType == deviceType) {
            result.append(it->instanceId);
        }
    }

    return result;
}

DeviceInstanceInfo DeviceInstanceManager::getInstanceInfo(const QString &instanceId) const {
    QMutexLocker locker(&m_mutex);

    DeviceInstanceInfo info;
    if (!m_activeInstances.contains(instanceId)) {
        return info;
    }

    const DeviceInstanceData &data = m_activeInstances[instanceId];
    info.instanceId                = data.instanceId;
    info.deviceType                = data.deviceType;
    info.instanceConfig            = data.instanceConfig;
    info.lifecycle                 = data.lifecycle;
    info.createTime                = data.createTime;
    info.lastActiveTime            = data.lastActiveTime;
    info.lastError                 = data.lastError;
    info.runtimeData               = data.runtimeData;

    // 从设备类型注册表获取友好名称 - 遵循双层架构设计
    // TODO: 实现DeviceTypeRegistry::getDeviceTypeMetadata
    // auto typeMetadata = LA::Device::Types::DeviceTypeRegistry::getDeviceTypeMetadata(data.deviceType);
    // info.friendlyName = typeMetadata.displayName;

    // 临时实现：直接使用设备类型作为友好名称
    info.friendlyName = data.deviceType;

    return info;
}

LA::Device::Core::IDevice *DeviceInstanceManager::getDeviceObject(const QString &instanceId) const {
    QMutexLocker locker(&m_mutex);

    if (!m_activeInstances.contains(instanceId)) {
        return nullptr;
    }

    return m_activeInstances[instanceId].deviceObject;
}

bool DeviceInstanceManager::connectInstance(const QString &instanceId) {
    QMutexLocker locker(&m_mutex);
    m_lastError.clear();

    if (!validateInstanceId(instanceId)) {
        m_lastError = QString("Invalid instance ID: %1").arg(instanceId);
        return false;
    }

    DeviceInstanceData &instanceData = m_activeInstances[instanceId];

    if (!instanceData.deviceObject) {
        m_lastError = QString("Device object not found for instance: %1").arg(instanceId);
        return false;
    }

    // 初始化设备（如果尚未初始化）
    if (instanceData.lifecycle == InstanceLifecycle::Created) {
        if (!instanceData.deviceObject->initialize({})) {  // 传入空配置
            m_lastError = QString("Failed to initialize device instance: %1").arg(instanceId);
            setInstanceError(instanceId, m_lastError);
            return false;
        }
        setInstanceLifecycle(instanceId, InstanceLifecycle::Initialized);
    }

    // 启动设备（如果尚未启动）
    if (instanceData.lifecycle == InstanceLifecycle::Initialized) {
        if (!instanceData.deviceObject->connect()) {
            m_lastError = QString("Failed to start device instance: %1").arg(instanceId);
            setInstanceError(instanceId, m_lastError);
            return false;
        }
        setInstanceLifecycle(instanceId, InstanceLifecycle::Started);
    }

    // 设置为已连接状态
    setInstanceLifecycle(instanceId, InstanceLifecycle::Connected);
    updateInstanceActivity(instanceId);

    emit instanceConnected(instanceId);
    qDebug() << QString("Device instance connected: %1").arg(instanceId);

    return true;
}

bool DeviceInstanceManager::disconnectInstance(const QString &instanceId) {
    QMutexLocker locker(&m_mutex);
    m_lastError.clear();

    if (!validateInstanceId(instanceId)) {
        m_lastError = QString("Invalid instance ID: %1").arg(instanceId);
        return false;
    }

    DeviceInstanceData &instanceData = m_activeInstances[instanceId];

    if (instanceData.lifecycle == InstanceLifecycle::Connected) {
        // 停止设备但不销毁
        if (instanceData.deviceObject) {
            instanceData.deviceObject->disconnect();
        }
        setInstanceLifecycle(instanceId, InstanceLifecycle::Started);
    }

    emit instanceDisconnected(instanceId);
    qDebug() << QString("Device instance disconnected: %1").arg(instanceId);

    return true;
}

bool DeviceInstanceManager::isInstanceConnected(const QString &instanceId) const {
    QMutexLocker locker(&m_mutex);

    if (!m_activeInstances.contains(instanceId)) {
        return false;
    }

    return m_activeInstances[instanceId].lifecycle == InstanceLifecycle::Connected;
}

InstanceLifecycle DeviceInstanceManager::getInstanceLifecycle(const QString &instanceId) const {
    QMutexLocker locker(&m_mutex);

    if (!m_activeInstances.contains(instanceId)) {
        return InstanceLifecycle::Destroyed;
    }

    return m_activeInstances[instanceId].lifecycle;
}

bool DeviceInstanceManager::startInstance(const QString &instanceId) {
    return connectInstance(instanceId);
}

bool DeviceInstanceManager::stopInstance(const QString &instanceId) {
    QMutexLocker locker(&m_mutex);
    m_lastError.clear();

    if (!validateInstanceId(instanceId)) {
        m_lastError = QString("Invalid instance ID: %1").arg(instanceId);
        return false;
    }

    DeviceInstanceData &instanceData = m_activeInstances[instanceId];

    if (instanceData.deviceObject) {
        instanceData.deviceObject->disconnect();
    }

    setInstanceLifecycle(instanceId, InstanceLifecycle::Stopped);

    qDebug() << QString("Device instance stopped: %1").arg(instanceId);
    return true;
}

bool DeviceInstanceManager::initializeInstance(const QString &instanceId) {
    QMutexLocker locker(&m_mutex);
    m_lastError.clear();

    if (!validateInstanceId(instanceId)) {
        m_lastError = QString("Invalid instance ID: %1").arg(instanceId);
        return false;
    }

    DeviceInstanceData &instanceData = m_activeInstances[instanceId];

    if (!instanceData.deviceObject) {
        m_lastError = QString("Device object not found for instance: %1").arg(instanceId);
        return false;
    }

    if (instanceData.deviceObject->initialize({})) {  // 传入空配置
        setInstanceLifecycle(instanceId, InstanceLifecycle::Initialized);
        qDebug() << QString("Device instance initialized: %1").arg(instanceId);
        return true;
    } else {
        m_lastError = QString("Failed to initialize device instance: %1").arg(instanceId);
        setInstanceError(instanceId, m_lastError);
        return false;
    }
}

int DeviceInstanceManager::getTotalInstanceCount() const {
    QMutexLocker locker(&m_mutex);
    return m_activeInstances.size();
}

int DeviceInstanceManager::getInstanceCountByType(const QString &deviceType) const {
    return getInstancesByType(deviceType).size();
}

QStringList DeviceInstanceManager::getSupportedDeviceTypes() const {
    // 从统一设备注册表获取支持的设备类型
    // TODO: 实现设备类型查询
    return {"SPRM", "Motor", "Sensor"};  // 临时返回
}

QString DeviceInstanceManager::getLastError() const {
    QMutexLocker locker(&m_mutex);
    return m_lastError;
}

bool DeviceInstanceManager::setInstanceConfig(const QString &instanceId, const QVariantMap &config) {
    QMutexLocker locker(&m_mutex);
    m_lastError.clear();

    if (!validateInstanceId(instanceId)) {
        m_lastError = QString("Invalid instance ID: %1").arg(instanceId);
        return false;
    }

    m_activeInstances[instanceId].instanceConfig = config;
    updateInstanceActivity(instanceId);

    qDebug() << QString("Instance config updated: %1").arg(instanceId);
    return true;
}

QVariantMap DeviceInstanceManager::getInstanceConfig(const QString &instanceId) const {
    QMutexLocker locker(&m_mutex);

    if (!m_activeInstances.contains(instanceId)) {
        return QVariantMap();
    }

    return m_activeInstances[instanceId].instanceConfig;
}

bool DeviceInstanceManager::setInstanceRuntimeData(const QString &instanceId, const QVariantMap &data) {
    QMutexLocker locker(&m_mutex);
    m_lastError.clear();

    if (!validateInstanceId(instanceId)) {
        m_lastError = QString("Invalid instance ID: %1").arg(instanceId);
        return false;
    }

    m_activeInstances[instanceId].runtimeData = data;
    updateInstanceActivity(instanceId);

    return true;
}

QVariantMap DeviceInstanceManager::getInstanceRuntimeData(const QString &instanceId) const {
    QMutexLocker locker(&m_mutex);

    if (!m_activeInstances.contains(instanceId)) {
        return QVariantMap();
    }

    return m_activeInstances[instanceId].runtimeData;
}

QString DeviceInstanceManager::generateInstanceId(const QString &deviceType) {
    return QString("%1_Instance_%2").arg(deviceType).arg(m_nextInstanceNumber++);
}

bool DeviceInstanceManager::validateInstanceId(const QString &instanceId) const {
    return !instanceId.isEmpty() && m_activeInstances.contains(instanceId);
}

bool DeviceInstanceManager::validateDeviceType(const QString &deviceType) const {
    // TODO: 实现设备类型验证
    QStringList validTypes = {"SPRM", "Motor", "Sensor"};
    return validTypes.contains(deviceType);
}

void DeviceInstanceManager::updateInstanceActivity(const QString &instanceId) {
    if (m_activeInstances.contains(instanceId)) {
        m_activeInstances[instanceId].lastActiveTime = QDateTime::currentDateTime();
    }
}

void DeviceInstanceManager::setInstanceLifecycle(const QString &instanceId, InstanceLifecycle lifecycle) {
    if (m_activeInstances.contains(instanceId)) {
        m_activeInstances[instanceId].lifecycle = lifecycle;
        updateInstanceActivity(instanceId);
        emit instanceLifecycleChanged(instanceId, lifecycle);
    }
}

void DeviceInstanceManager::setInstanceError(const QString &instanceId, const QString &error) {
    if (m_activeInstances.contains(instanceId)) {
        m_activeInstances[instanceId].lastError = error;
        setInstanceLifecycle(instanceId, InstanceLifecycle::Error);
        emit instanceError(instanceId, error);
    }
}

LA::Device::Core::IDevice *DeviceInstanceManager::createDeviceObject(const QString &deviceType, const QString &instanceId) {
    // 使用ModernBaseDevice创建设备对象
    // 这里可以根据设备类型创建不同的设备对象
    // 暂时使用ModernBaseDevice作为基础实现

    try {
        // TODO: 实现设备工厂创建
        LA::Device::Core::IDevice *device = nullptr;  // 临时返回nullptr
        return device;
    } catch (const std::exception &e) {
        qWarning() << "Failed to create device object:" << e.what();
        return nullptr;
    }
}

void DeviceInstanceManager::destroyDeviceObject(LA::Device::Core::IDevice *device) {
    if (device) {
        device->disconnect();  // 使用IDevice的disconnect方法
        device->deleteLater();
    }
}

void DeviceInstanceManager::connectDeviceSignals(LA::Device::Core::IDevice *device, const QString &instanceId) {
    if (!device)
        return;

    connect(device, &LA::Device::Core::IDevice::deviceError, this, &DeviceInstanceManager::onDeviceError);
    connect(device, &LA::Device::Core::IDevice::deviceDataReady, this, &DeviceInstanceManager::onDeviceDataReceived);
}

void DeviceInstanceManager::disconnectDeviceSignals(LA::Device::Core::IDevice *device) {
    if (!device)
        return;

    disconnect(device, nullptr, this, nullptr);
}

void DeviceInstanceManager::onDeviceError(const QString &error) {
    // 暂时注释掉BaseDevice相关代码，因为头文件不存在
    /*
    LA::Device::Core::IDevice *device = qobject_cast<LA::Device::Core::IDevice *>(sender());
    if (device) {
        QString instanceId = device->getDeviceId();
        setInstanceError(instanceId, error);
    }
    */
    qDebug() << "Device error received:" << error;
}

void DeviceInstanceManager::onDeviceDataReceived(const QVariantMap &data) {
    // 暂时注释掉BaseDevice相关代码，因为头文件不存在
    /*
    LA::Device::Core::IDevice *device = qobject_cast<LA::Device::Core::IDevice *>(sender());
    if (device) {
        QString instanceId = device->getDeviceId();
        updateInstanceActivity(instanceId);

        // 更新运行时数据
        if (m_activeInstances.contains(instanceId)) {
            m_activeInstances[instanceId].runtimeData = data;
        }
    }
    */
    qDebug() << "Device data received:" << data;
}

}  // namespace LA::DeviceManagement
