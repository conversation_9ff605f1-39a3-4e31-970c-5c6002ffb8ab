# 嵌入式功能管理插件 CMakeLists.txt
cmake_minimum_required(VERSION 3.16)

# 项目名称和版本
project(EmbeddedFeatureManagerPlugin VERSION 1.0.0)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 查找Qt5组件
find_package(Qt5 REQUIRED COMPONENTS Core Widgets)

# 设置Qt MOC自动处理
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

# 包含目录
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_SOURCE_DIR}/core/plugins/include
    ${CMAKE_SOURCE_DIR}/support/foundation/core
    ${CMAKE_SOURCE_DIR}/support/logging
    ${CMAKE_SOURCE_DIR}/support/config/file
)

# 源文件列表
set(PLUGIN_SOURCES
    EmbeddedFeatureManagerPlugin.cpp
    FeatureConfigWidget.cpp
    FileSync.cpp
    PermissionManager.cpp
)

# 头文件列表
set(PLUGIN_HEADERS
    EmbeddedFeatureManagerPlugin.h
    FeatureConfigWidget.h
    FileSync.h
    PermissionManager.h
)

# UI文件列表（如果有的话）
set(PLUGIN_UI_FILES
    # FeatureConfigWidget.ui  # 如果需要.ui文件可以添加
)

# 资源文件列表（如果有的话）
set(PLUGIN_RESOURCES
    # resources.qrc  # 如果需要资源文件可以添加
)

# 创建静态库目标
add_library(${PROJECT_NAME} STATIC
    ${PLUGIN_SOURCES}
    ${PLUGIN_HEADERS}
    ${PLUGIN_UI_FILES}
    ${PLUGIN_RESOURCES}
)

# 链接Qt库
target_link_libraries(${PROJECT_NAME}
    Qt5::Core
    Qt5::Widgets
)

# 设置目标属性
set_target_properties(${PROJECT_NAME} PROPERTIES
    VERSION ${PROJECT_VERSION}
    SOVERSION 1
    OUTPUT_NAME "embedded_feature_manager_plugin"
    ARCHIVE_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/lib"
)

# 安装规则
install(TARGETS ${PROJECT_NAME}
    ARCHIVE DESTINATION lib/plugins
    LIBRARY DESTINATION lib/plugins
    RUNTIME DESTINATION bin/plugins
)

# 安装头文件
install(FILES ${PLUGIN_HEADERS}
    DESTINATION include/plugins/embeddedFeatureManager
)

# 编译定义
target_compile_definitions(${PROJECT_NAME} PRIVATE
    QT_NO_KEYWORDS  # 避免Qt关键字冲突
    EMBEDDED_FEATURE_MANAGER_VERSION="${PROJECT_VERSION}"
    $<$<CONFIG:Debug>:DEBUG_BUILD>
    $<$<CONFIG:Release>:RELEASE_BUILD>
)

# 编译选项
if(MSVC)
    target_compile_options(${PROJECT_NAME} PRIVATE
        /W4                     # 高警告级别
        /permissive-           # 严格标准遵循
        /utf-8                 # UTF-8源文件编码
    )
else()
    target_compile_options(${PROJECT_NAME} PRIVATE
        -Wall                   # 所有警告
        -Wextra                # 额外警告
        -Wpedantic             # 严格标准警告
        -Wno-unused-parameter  # 忽略未使用参数警告
    )
endif()

# 添加预处理器定义
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_definitions(${PROJECT_NAME} PRIVATE DEBUG)
endif()

# 包含测试（如果启用）
option(BUILD_PLUGIN_TESTS "Build plugin tests" OFF)
if(BUILD_PLUGIN_TESTS)
    enable_testing()
    add_subdirectory(tests)
endif()

# 添加示例（如果启用）
option(BUILD_PLUGIN_EXAMPLES "Build plugin examples" OFF)
if(BUILD_PLUGIN_EXAMPLES)
    add_subdirectory(examples)
endif()

# 生成配置文件
configure_file(
    "${CMAKE_CURRENT_SOURCE_DIR}/EmbeddedFeatureManagerConfig.h.in"
    "${CMAKE_CURRENT_BINARY_DIR}/EmbeddedFeatureManagerConfig.h"
    @ONLY
)

# 添加配置头文件到包含目录
target_include_directories(${PROJECT_NAME} PRIVATE
    "${CMAKE_CURRENT_BINARY_DIR}"
)

# 设置RPATH (Linux/macOS)
if(UNIX AND NOT APPLE)
    set_target_properties(${PROJECT_NAME} PROPERTIES
        INSTALL_RPATH "$ORIGIN/../lib"
        BUILD_WITH_INSTALL_RPATH FALSE
        INSTALL_RPATH_USE_LINK_PATH TRUE
    )
endif()

# macOS特定设置
if(APPLE)
    set_target_properties(${PROJECT_NAME} PROPERTIES
        INSTALL_RPATH "@loader_path/../lib"
        BUILD_WITH_INSTALL_RPATH FALSE
        INSTALL_RPATH_USE_LINK_PATH TRUE
    )
endif()

# 打印构建信息
message(STATUS "Embedded Feature Manager Plugin Configuration:")
message(STATUS "  Version: ${PROJECT_VERSION}")
message(STATUS "  Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  Install Prefix: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "  Qt5 Version: ${Qt5_VERSION}")

# 添加自定义目标用于代码格式化（如果有clang-format）
find_program(CLANG_FORMAT_EXECUTABLE NAMES clang-format)
if(CLANG_FORMAT_EXECUTABLE)
    add_custom_target(format-plugin
        COMMAND ${CLANG_FORMAT_EXECUTABLE} -style=file -i ${PLUGIN_SOURCES} ${PLUGIN_HEADERS}
        WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
        COMMENT "Formatting plugin source code"
    )
endif()

# 添加自定义目标用于静态分析（如果有cppcheck）
find_program(CPPCHECK_EXECUTABLE NAMES cppcheck)
if(CPPCHECK_EXECUTABLE)
    add_custom_target(cppcheck-plugin
        COMMAND ${CPPCHECK_EXECUTABLE} 
            --enable=warning,performance,portability
            --std=c++17
            --template=gcc
            --quiet
            ${CMAKE_CURRENT_SOURCE_DIR}
        WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
        COMMENT "Running static analysis on plugin"
    )
endif()

# 导出编译命令数据库（用于IDE和工具）
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

# 添加到父项目的插件列表（如果父项目定义了相关变量）
if(DEFINED STATIC_PLUGINS_LIST)
    list(APPEND STATIC_PLUGINS_LIST ${PROJECT_NAME})
    set(STATIC_PLUGINS_LIST ${STATIC_PLUGINS_LIST} PARENT_SCOPE)
endif()

# 将插件头文件添加到父项目包含路径
if(DEFINED PARENT_INCLUDE_DIRECTORIES)
    list(APPEND PARENT_INCLUDE_DIRECTORIES ${CMAKE_CURRENT_SOURCE_DIR})
    set(PARENT_INCLUDE_DIRECTORIES ${PARENT_INCLUDE_DIRECTORIES} PARENT_SCOPE)
endif()