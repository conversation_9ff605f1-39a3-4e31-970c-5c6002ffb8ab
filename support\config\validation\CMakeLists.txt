# Configuration Validation Module
cmake_minimum_required(VERSION 3.16)

# 设置项目信息
set(MODULE_NAME "LA_Support_Config_Validation")
set(MODULE_VERSION "1.0.0")

# 查找依赖
find_package(Qt5 REQUIRED COMPONENTS Core)

# 设置源文件
set(HEADERS
    IConfigValidator.h
    ConfigValidator.h
)

set(SOURCES
    ConfigValidator.cpp
)

# 创建库
add_library(${MODULE_NAME} STATIC ${SOURCES} ${HEADERS})

# 设置目标属性
set_target_properties(${MODULE_NAME} PROPERTIES
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
    VERSION ${MODULE_VERSION}
    EXPORT_NAME Config::Validation
)

# 链接依赖
target_link_libraries(${MODULE_NAME}
    PUBLIC
        Qt5::Core
        LA_Support_Foundation
)

# 设置包含目录
target_include_directories(${MODULE_NAME}
    PUBLIC
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/..>
        $<INSTALL_INTERFACE:include>
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}
)

# 编译定义
target_compile_definitions(${MODULE_NAME}
    PUBLIC
        LA_SUPPORT_CONFIG_VALIDATION_LIBRARY
    PRIVATE
        QT_NO_KEYWORDS
        QT_USE_QSTRINGBUILDER
)

# 条件编译 - 并行验证支持
option(ENABLE_PARALLEL_VALIDATION "Enable parallel validation support" ON)
if(ENABLE_PARALLEL_VALIDATION)
    target_compile_definitions(${MODULE_NAME} PRIVATE ENABLE_PARALLEL_VALIDATION)
    message(STATUS "Parallel validation support enabled")
endif()

# 条件编译 - 自定义验证器支持
option(ENABLE_CUSTOM_VALIDATORS "Enable custom validator support" ON)
if(ENABLE_CUSTOM_VALIDATORS)
    target_compile_definitions(${MODULE_NAME} PRIVATE ENABLE_CUSTOM_VALIDATORS)
    message(STATUS "Custom validator support enabled")
endif()

# 条件编译 - 统计信息收集
option(ENABLE_VALIDATION_STATISTICS "Enable validation statistics collection" ON)
if(ENABLE_VALIDATION_STATISTICS)
    target_compile_definitions(${MODULE_NAME} PRIVATE ENABLE_VALIDATION_STATISTICS)
    message(STATUS "Validation statistics collection enabled")
endif()

# 条件编译 - Schema验证支持
option(ENABLE_SCHEMA_VALIDATION "Enable JSON Schema validation support" ON)
if(ENABLE_SCHEMA_VALIDATION)
    target_compile_definitions(${MODULE_NAME} PRIVATE ENABLE_SCHEMA_VALIDATION)
    message(STATUS "JSON Schema validation support enabled")
endif()

# 条件编译 - 缓存支持
option(ENABLE_VALIDATION_CACHE "Enable validation result caching" OFF)
if(ENABLE_VALIDATION_CACHE)
    target_compile_definitions(${MODULE_NAME} PRIVATE ENABLE_VALIDATION_CACHE)
    message(STATUS "Validation result caching enabled")
endif()

# 安装规则
install(TARGETS ${MODULE_NAME}
    EXPORT LA_Support_Config_ValidationTargets
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
    INCLUDES DESTINATION include
)

# 安装头文件
install(FILES ${HEADERS}
    DESTINATION include/LA/Support/Config/Validation
)

# 导出目标
install(EXPORT LA_Support_Config_ValidationTargets
    FILE LA_Support_Config_ValidationTargets.cmake
    NAMESPACE LA::Support::Config::
    DESTINATION lib/cmake/LA_Support_Config_Validation
)

# 创建配置文件
include(CMakePackageConfigHelpers)

configure_package_config_file(
    "${CMAKE_CURRENT_SOURCE_DIR}/Config.cmake.in"
    "${CMAKE_CURRENT_BINARY_DIR}/LA_Support_Config_ValidationConfig.cmake"
    INSTALL_DESTINATION lib/cmake/LA_Support_Config_Validation
)

write_basic_package_version_file(
    "${CMAKE_CURRENT_BINARY_DIR}/LA_Support_Config_ValidationConfigVersion.cmake"
    VERSION ${MODULE_VERSION}
    COMPATIBILITY SameMajorVersion
)

install(FILES
    "${CMAKE_CURRENT_BINARY_DIR}/LA_Support_Config_ValidationConfig.cmake"
    "${CMAKE_CURRENT_BINARY_DIR}/LA_Support_Config_ValidationConfigVersion.cmake"
    DESTINATION lib/cmake/LA_Support_Config_Validation
)

# 预定义验证规则集
option(INSTALL_STANDARD_RULESETS "Install standard validation rule sets" ON)
if(INSTALL_STANDARD_RULESETS)
    # 安装标准规则集文件
    install(DIRECTORY rulesets/
        DESTINATION share/LA/config/validation/rulesets
        PATTERN "*.json"
    )
    message(STATUS "Standard validation rule sets will be installed")
endif()

# 测试
if(BUILD_TESTING)
    enable_testing()
    
    # 单元测试
    add_subdirectory(tests)
    
    # 性能测试
    if(BUILD_PERFORMANCE_TESTS)
        add_subdirectory(performance_tests)
    endif()
endif()

# 示例程序
option(BUILD_VALIDATION_EXAMPLES "Build validation examples" OFF)
if(BUILD_VALIDATION_EXAMPLES)
    add_subdirectory(examples)
endif()

# 基准测试
option(BUILD_VALIDATION_BENCHMARKS "Build validation benchmarks" OFF)
if(BUILD_VALIDATION_BENCHMARKS)
    find_package(benchmark QUIET)
    if(benchmark_FOUND)
        add_subdirectory(benchmarks)
        message(STATUS "Validation benchmarks enabled")
    else()
        message(WARNING "Google Benchmark not found, benchmarks will be skipped")
    endif()
endif()

# 文档生成
if(BUILD_DOCUMENTATION)
    find_package(Doxygen)
    if(Doxygen_FOUND)
        set(DOXYGEN_IN ${CMAKE_CURRENT_SOURCE_DIR}/Doxyfile.in)
        set(DOXYGEN_OUT ${CMAKE_CURRENT_BINARY_DIR}/Doxyfile)
        configure_file(${DOXYGEN_IN} ${DOXYGEN_OUT} @ONLY)
        
        add_custom_target(doc_config_validation ALL
            COMMAND ${DOXYGEN_EXECUTABLE} ${DOXYGEN_OUT}
            WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
            COMMENT "Generating API documentation for Config Validation module"
            VERBATIM
        )
        
        install(DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/html/
            DESTINATION share/doc/LA/config/validation
            OPTIONAL
        )
    endif()
endif()

# 代码质量检查
if(ENABLE_STATIC_ANALYSIS)
    # Clang-Tidy
    find_program(CLANG_TIDY_EXE NAMES "clang-tidy")
    if(CLANG_TIDY_EXE)
        set_target_properties(${MODULE_NAME} PROPERTIES
            CXX_CLANG_TIDY "${CLANG_TIDY_EXE};-checks=-*,readability-*,performance-*,modernize-*,bugprone-*"
        )
        message(STATUS "Clang-Tidy static analysis enabled")
    endif()
    
    # Cppcheck
    find_program(CPPCHECK_EXE NAMES "cppcheck")
    if(CPPCHECK_EXE)
        add_custom_target(cppcheck_config_validation
            COMMAND ${CPPCHECK_EXE} --enable=all --std=c++17 --quiet --error-exitcode=1
                    ${CMAKE_CURRENT_SOURCE_DIR}
            COMMENT "Running Cppcheck on Config Validation module"
        )
        message(STATUS "Cppcheck static analysis target added")
    endif()
endif()

# 代码覆盖率
if(ENABLE_CODE_COVERAGE AND CMAKE_COMPILER_IS_GNUCXX)
    target_compile_options(${MODULE_NAME} PRIVATE --coverage)
    target_link_libraries(${MODULE_NAME} PRIVATE gcov)
    
    # 添加覆盖率报告目标
    add_custom_target(coverage_config_validation
        COMMAND lcov --capture --directory . --output-file coverage_validation.info
        COMMAND lcov --remove coverage_validation.info '/usr/*' --output-file coverage_validation_clean.info
        COMMAND genhtml coverage_validation_clean.info --output-directory coverage_validation_report
        WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
        COMMENT "Generating code coverage report for Config Validation module"
    )
    
    message(STATUS "Code coverage support enabled")
endif()

# 内存检查
if(ENABLE_MEMORY_CHECK)
    find_program(VALGRIND_EXE NAMES "valgrind")
    if(VALGRIND_EXE)
        add_custom_target(memcheck_config_validation
            COMMAND ${VALGRIND_EXE} --tool=memcheck --leak-check=full --show-reachable=yes 
                    --error-exitcode=1 $<TARGET_FILE:${MODULE_NAME}>
            DEPENDS ${MODULE_NAME}
            COMMENT "Running memory check on Config Validation module"
        )
        message(STATUS "Memory check target added")
    endif()
endif()

# 线程安全检查
if(ENABLE_THREAD_SANITIZER AND CMAKE_CXX_COMPILER_ID MATCHES "Clang")
    target_compile_options(${MODULE_NAME} PRIVATE -fsanitize=thread -g)
    target_link_options(${MODULE_NAME} PRIVATE -fsanitize=thread)
    message(STATUS "Thread sanitizer enabled")
endif()

# 地址检查
if(ENABLE_ADDRESS_SANITIZER AND CMAKE_CXX_COMPILER_ID MATCHES "Clang")
    target_compile_options(${MODULE_NAME} PRIVATE -fsanitize=address -g)
    target_link_options(${MODULE_NAME} PRIVATE -fsanitize=address)
    message(STATUS "Address sanitizer enabled")
endif()

# 性能分析
if(ENABLE_PROFILING)
    if(CMAKE_COMPILER_IS_GNUCXX)
        target_compile_options(${MODULE_NAME} PRIVATE -pg)
        target_link_options(${MODULE_NAME} PRIVATE -pg)
        message(STATUS "Profiling support enabled (gprof)")
    endif()
endif()

# 打印构建信息
message(STATUS "Configuring ${MODULE_NAME} v${MODULE_VERSION}")
message(STATUS "  - Source files: ${SOURCES}")
message(STATUS "  - Header files: ${HEADERS}")
message(STATUS "  - Qt5 Core: ${Qt5Core_VERSION}")
message(STATUS "  - Install prefix: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "  - Build type: ${CMAKE_BUILD_TYPE}")

# 特性总结
message(STATUS "  - Features:")
message(STATUS "    * Parallel validation: ${ENABLE_PARALLEL_VALIDATION}")
message(STATUS "    * Custom validators: ${ENABLE_CUSTOM_VALIDATORS}")
message(STATUS "    * Statistics collection: ${ENABLE_VALIDATION_STATISTICS}")
message(STATUS "    * Schema validation: ${ENABLE_SCHEMA_VALIDATION}")
message(STATUS "    * Result caching: ${ENABLE_VALIDATION_CACHE}")
message(STATUS "    * Standard rule sets: ${INSTALL_STANDARD_RULESETS}")
message(STATUS "    * Testing: ${BUILD_TESTING}")
message(STATUS "    * Examples: ${BUILD_VALIDATION_EXAMPLES}")
message(STATUS "    * Benchmarks: ${BUILD_VALIDATION_BENCHMARKS}")
message(STATUS "    * Documentation: ${BUILD_DOCUMENTATION}")
message(STATUS "    * Static analysis: ${ENABLE_STATIC_ANALYSIS}")
message(STATUS "    * Code coverage: ${ENABLE_CODE_COVERAGE}")
message(STATUS "    * Memory check: ${ENABLE_MEMORY_CHECK}")
message(STATUS "    * Thread sanitizer: ${ENABLE_THREAD_SANITIZER}")
message(STATUS "    * Address sanitizer: ${ENABLE_ADDRESS_SANITIZER}")
message(STATUS "    * Profiling: ${ENABLE_PROFILING}")

# 依赖检查
message(STATUS "  - Dependencies:")
message(STATUS "    * Qt5::Core: REQUIRED")
message(STATUS "    * LA_Support_Foundation: REQUIRED")
if(benchmark_FOUND)
    message(STATUS "    * Google Benchmark: FOUND (for benchmarks)")
endif()
if(Doxygen_FOUND)
    message(STATUS "    * Doxygen: FOUND (for documentation)")
endif()