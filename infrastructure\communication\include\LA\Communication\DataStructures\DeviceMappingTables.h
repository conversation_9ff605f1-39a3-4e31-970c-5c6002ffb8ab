#pragma once

/**
 * @file DeviceMappingTables.h
 * @brief 设备静态映射表定义
 * 
 * 基于静态表格关系原则，定义设备与协议、端口、指令的映射关系
 * 这些映射表在编译期确定，提供快速查找和验证功能
 */

#include "TypeHash.h"
#include "DeviceAttributes.h"
#include "CommunicationAttributes.h"
#include "PortAttributes.h"
#include <QMap>
#include <QString>
#include <QStringList>

namespace LA {
namespace Communication {
namespace DataStructures {

/**
 * @brief 指令定义结构
 */
struct CommandDefinition {
    QString commandId;                  // 指令ID
    QString commandName;                // 指令名称
    QString description;                // 指令描述
    
    // 指令格式
    QString requestFormat;              // 请求格式模板
    QString responseFormat;             // 响应格式模板
    QStringList requiredParameters;     // 必需参数
    QStringList optionalParameters;     // 可选参数
    
    // 执行属性
    int timeout = 1000;                // 超时时间(ms)
    int maxRetries = 3;                // 最大重试次数
    bool needsResponse = true;          // 是否需要响应
    bool isReadOnly = true;             // 是否只读指令
    
    // 验证规则
    QVariantMap parameterValidation;    // 参数验证规则
    QStringList preconditions;          // 前置条件
    
    CommandDefinition() = default;
    CommandDefinition(const QString& id, const QString& name, const QString& desc = "") 
        : commandId(id), commandName(name), description(desc) {}
};

/**
 * @brief 设备类型映射表
 * 
 * 静态映射表：设备类型 -> 支持的协议列表
 */
class DeviceProtocolMappingTable {
public:
    static const QMap<DeviceType, QList<ProtocolType>>& getMapping() {
        static const QMap<DeviceType, QList<ProtocolType>> mapping = {
            // 传感器设备
            {DeviceType::YJSensor, {ProtocolType::Custom_YJSensor, ProtocolType::Raw}},
            {DeviceType::CoinSensor, {ProtocolType::Custom_CoinSensor, ProtocolType::SimpleChecksum}},
            {DeviceType::TemperatureSensor, {ProtocolType::Modbus_RTU, ProtocolType::Raw}},
            {DeviceType::PressureSensor, {ProtocolType::Modbus_RTU, ProtocolType::Raw}},
            
            // 测试设备
            {DeviceType::TestBoard, {ProtocolType::Custom_TestBoard, ProtocolType::Raw, ProtocolType::CRC16}},
            {DeviceType::BottomBoard, {ProtocolType::Custom_BottomBoard, ProtocolType::SimpleChecksum}},
            
            // 扫描设备
            {DeviceType::NovaScanner, {ProtocolType::Custom_Nova, ProtocolType::JSON}},
            {DeviceType::Scanner, {ProtocolType::Raw, ProtocolType::JSON}},
            {DeviceType::Barcode, {ProtocolType::Raw, ProtocolType::SimpleChecksum}},
            
            // 工业控制设备
            {DeviceType::ModbusDevice, {ProtocolType::Modbus_RTU, ProtocolType::Modbus_TCP}},
            {DeviceType::CanDevice, {ProtocolType::CAN}},
            {DeviceType::PLC, {ProtocolType::Modbus_RTU, ProtocolType::Modbus_TCP, ProtocolType::Ethernet_IP}},
            
            // 客户定制设备
            {DeviceType::BesterDevice, {ProtocolType::Custom_Bester, ProtocolType::Raw}},
            {DeviceType::HeliDevice, {ProtocolType::Custom_Heli, ProtocolType::Modbus_RTU}},
            {DeviceType::HuayuanDevice, {ProtocolType::Custom_Huayuan, ProtocolType::Raw}},
            {DeviceType::YoushengDevice, {ProtocolType::Custom_Yousheng, ProtocolType::SimpleChecksum}},
            
            // 通用设备
            {DeviceType::Generic, {ProtocolType::Raw, ProtocolType::SimpleChecksum, ProtocolType::JSON}},
            {DeviceType::Unknown, {ProtocolType::Raw}}
        };
        return mapping;
    }
    
    /**
     * @brief 获取设备支持的协议列表
     */
    static QList<ProtocolType> getSupportedProtocols(DeviceType deviceType) {
        const auto& mapping = getMapping();
        return mapping.value(deviceType, {ProtocolType::Raw});
    }
    
    /**
     * @brief 检查设备是否支持指定协议
     */
    static bool supportsProtocol(DeviceType deviceType, ProtocolType protocolType) {
        const auto protocols = getSupportedProtocols(deviceType);
        return protocols.contains(protocolType);
    }
    
    /**
     * @brief 获取设备的默认协议
     */
    static ProtocolType getDefaultProtocol(DeviceType deviceType) {
        const auto protocols = getSupportedProtocols(deviceType);
        return protocols.isEmpty() ? ProtocolType::Raw : protocols.first();
    }
};

/**
 * @brief 设备端口类型映射表
 * 
 * 静态映射表：设备类型 -> 支持的端口类型列表
 */
class DevicePortMappingTable {
public:
    static const QMap<DeviceType, QList<PortType>>& getMapping() {
        static const QMap<DeviceType, QList<PortType>> mapping = {
            // 传感器设备 - 通常使用串口或网络
            {DeviceType::YJSensor, {PortType::Serial, PortType::USB}},
            {DeviceType::CoinSensor, {PortType::Serial, PortType::USB}},
            {DeviceType::TemperatureSensor, {PortType::Serial, PortType::TCP, PortType::I2C}},
            {DeviceType::PressureSensor, {PortType::Serial, PortType::TCP, PortType::I2C}},
            
            // 测试设备 - 主要使用串口
            {DeviceType::TestBoard, {PortType::Serial, PortType::USB, PortType::Ethernet}},
            {DeviceType::BottomBoard, {PortType::Serial, PortType::USB}},
            
            // 扫描设备 - 支持多种接口
            {DeviceType::NovaScanner, {PortType::Serial, PortType::TCP, PortType::USB}},
            {DeviceType::Scanner, {PortType::Serial, PortType::TCP, PortType::USB}},
            {DeviceType::Barcode, {PortType::Serial, PortType::USB}},
            
            // 工业控制设备 - 支持工业总线
            {DeviceType::ModbusDevice, {PortType::Serial, PortType::TCP, PortType::Ethernet}},
            {DeviceType::CanDevice, {PortType::CAN}},
            {DeviceType::PLC, {PortType::Serial, PortType::TCP, PortType::Ethernet, PortType::CAN}},
            
            // 客户定制设备
            {DeviceType::BesterDevice, {PortType::Serial, PortType::TCP}},
            {DeviceType::HeliDevice, {PortType::Serial, PortType::TCP}},
            {DeviceType::HuayuanDevice, {PortType::Serial, PortType::USB}},
            {DeviceType::YoushengDevice, {PortType::Serial, PortType::USB}},
            
            // 通用设备 - 支持所有常见接口
            {DeviceType::Generic, {PortType::Serial, PortType::TCP, PortType::UDP, PortType::USB}},
            {DeviceType::Unknown, {PortType::Serial, PortType::TCP}}
        };
        return mapping;
    }
    
    /**
     * @brief 获取设备支持的端口类型列表
     */
    static QList<PortType> getSupportedPorts(DeviceType deviceType) {
        const auto& mapping = getMapping();
        return mapping.value(deviceType, {PortType::Serial});
    }
    
    /**
     * @brief 检查设备是否支持指定端口类型
     */
    static bool supportsPort(DeviceType deviceType, PortType portType) {
        const auto ports = getSupportedPorts(deviceType);
        return ports.contains(portType);
    }
    
    /**
     * @brief 获取设备的默认端口类型
     */
    static PortType getDefaultPort(DeviceType deviceType) {
        const auto ports = getSupportedPorts(deviceType);
        return ports.isEmpty() ? PortType::Serial : ports.first();
    }
};

/**
 * @brief 设备指令映射表
 * 
 * 静态映射表：设备类型 -> 支持的指令列表
 */
class DeviceCommandMappingTable {
public:
    static const QMap<DeviceType, QList<CommandDefinition>>& getMapping() {
        static const QMap<DeviceType, QList<CommandDefinition>> mapping = {
            // YJSensor设备指令
            {DeviceType::YJSensor, {
                CommandDefinition("READ_STATUS", "读取状态", "获取传感器当前状态"),
                CommandDefinition("READ_DATA", "读取数据", "获取传感器测量数据"),
                CommandDefinition("SET_PARAM", "设置参数", "设置传感器参数"),
                CommandDefinition("CALIBRATE", "校准", "执行传感器校准"),
                CommandDefinition("RESET", "复位", "复位传感器")
            }},
            
            // 硬币传感器指令
            {DeviceType::CoinSensor, {
                CommandDefinition("READ_COUNT", "读取计数", "获取硬币计数"),
                CommandDefinition("CLEAR_COUNT", "清除计数", "清零计数器"),
                CommandDefinition("SET_THRESHOLD", "设置阈值", "设置检测阈值"),
                CommandDefinition("GET_CONFIG", "获取配置", "获取当前配置")
            }},
            
            // 测试板指令
            {DeviceType::TestBoard, {
                CommandDefinition("START_TEST", "开始测试", "启动测试程序"),
                CommandDefinition("STOP_TEST", "停止测试", "停止测试程序"),
                CommandDefinition("GET_RESULT", "获取结果", "获取测试结果"),
                CommandDefinition("SET_TEST_PARAM", "设置测试参数", "配置测试参数"),
                CommandDefinition("SELF_CHECK", "自检", "执行设备自检")
            }},
            
            // Nova扫描仪指令
            {DeviceType::NovaScanner, {
                CommandDefinition("START_SCAN", "开始扫描", "启动扫描功能"),
                CommandDefinition("STOP_SCAN", "停止扫描", "停止扫描功能"),
                CommandDefinition("GET_SCAN_DATA", "获取扫描数据", "获取扫描结果"),
                CommandDefinition("SET_SCAN_MODE", "设置扫描模式", "配置扫描模式"),
                CommandDefinition("CALIBRATE_SCANNER", "校准扫描仪", "执行扫描仪校准")
            }},
            
            // Modbus设备通用指令
            {DeviceType::ModbusDevice, {
                CommandDefinition("READ_COILS", "读取线圈", "读取线圈状态"),
                CommandDefinition("READ_DISCRETE", "读取离散输入", "读取离散输入状态"),
                CommandDefinition("READ_HOLDING", "读取保持寄存器", "读取保持寄存器值"),
                CommandDefinition("READ_INPUT", "读取输入寄存器", "读取输入寄存器值"),
                CommandDefinition("WRITE_SINGLE_COIL", "写单个线圈", "写入单个线圈"),
                CommandDefinition("WRITE_SINGLE_REGISTER", "写单个寄存器", "写入单个寄存器"),
                CommandDefinition("WRITE_MULTIPLE_COILS", "写多个线圈", "写入多个线圈"),
                CommandDefinition("WRITE_MULTIPLE_REGISTERS", "写多个寄存器", "写入多个寄存器")
            }},
            
            // 通用设备指令
            {DeviceType::Generic, {
                CommandDefinition("PING", "Ping", "检查设备连通性"),
                CommandDefinition("GET_INFO", "获取信息", "获取设备基本信息"),
                CommandDefinition("GET_STATUS", "获取状态", "获取设备状态"),
                CommandDefinition("RESET", "复位", "复位设备"),
                CommandDefinition("READ_DATA", "读取数据", "读取设备数据"),
                CommandDefinition("WRITE_DATA", "写入数据", "写入设备数据")
            }}
        };
        return mapping;
    }
    
    /**
     * @brief 获取设备支持的指令列表
     */
    static QList<CommandDefinition> getSupportedCommands(DeviceType deviceType) {
        const auto& mapping = getMapping();
        auto commands = mapping.value(deviceType);
        
        // 如果没有找到特定设备的指令，返回通用设备指令
        if (commands.isEmpty() && deviceType != DeviceType::Generic) {
            commands = mapping.value(DeviceType::Generic);
        }
        
        return commands;
    }
    
    /**
     * @brief 查找指定指令定义
     */
    static CommandDefinition findCommand(DeviceType deviceType, const QString& commandId) {
        const auto commands = getSupportedCommands(deviceType);
        for (const auto& cmd : commands) {
            if (cmd.commandId == commandId) {
                return cmd;
            }
        }
        return CommandDefinition(); // 返回空的指令定义
    }
    
    /**
     * @brief 检查设备是否支持指定指令
     */
    static bool supportsCommand(DeviceType deviceType, const QString& commandId) {
        const auto cmd = findCommand(deviceType, commandId);
        return !cmd.commandId.isEmpty();
    }
    
    /**
     * @brief 获取设备支持的指令ID列表
     */
    static QStringList getCommandIds(DeviceType deviceType) {
        const auto commands = getSupportedCommands(deviceType);
        QStringList ids;
        for (const auto& cmd : commands) {
            ids.append(cmd.commandId);
        }
        return ids;
    }
};

/**
 * @brief 协议端口兼容性映射表
 * 
 * 静态映射表：协议类型 -> 兼容的端口类型列表
 */
class ProtocolPortCompatibilityTable {
public:
    static const QMap<ProtocolType, QList<PortType>>& getMapping() {
        static const QMap<ProtocolType, QList<PortType>> mapping = {
            // 标准工业协议
            {ProtocolType::Modbus_RTU, {PortType::Serial}},
            {ProtocolType::Modbus_TCP, {PortType::TCP, PortType::Ethernet}},
            {ProtocolType::CAN, {PortType::CAN}},
            {ProtocolType::Profibus, {PortType::Serial, PortType::Ethernet}},
            {ProtocolType::Ethernet_IP, {PortType::Ethernet, PortType::TCP}},
            
            // 通用协议 - 支持多种端口
            {ProtocolType::Raw, {PortType::Serial, PortType::TCP, PortType::UDP, PortType::USB, PortType::CAN, PortType::I2C, PortType::SPI}},
            {ProtocolType::SimpleChecksum, {PortType::Serial, PortType::TCP, PortType::UDP, PortType::USB}},
            {ProtocolType::CRC16, {PortType::Serial, PortType::TCP, PortType::UDP, PortType::USB}},
            {ProtocolType::CRC32, {PortType::Serial, PortType::TCP, PortType::UDP, PortType::USB}},
            {ProtocolType::XOR, {PortType::Serial, PortType::TCP, PortType::UDP, PortType::USB}},
            {ProtocolType::JSON, {PortType::TCP, PortType::UDP, PortType::Serial, PortType::USB}},
            {ProtocolType::XML, {PortType::TCP, PortType::UDP, PortType::Serial}},
            
            // 客户定制协议 - 根据实际硬件接口
            {ProtocolType::Custom_YJSensor, {PortType::Serial, PortType::USB}},
            {ProtocolType::Custom_CoinSensor, {PortType::Serial, PortType::USB}},
            {ProtocolType::Custom_BottomBoard, {PortType::Serial, PortType::USB}},
            {ProtocolType::Custom_TestBoard, {PortType::Serial, PortType::USB, PortType::Ethernet}},
            {ProtocolType::Custom_Nova, {PortType::Serial, PortType::TCP, PortType::USB}},
            {ProtocolType::Custom_Bester, {PortType::Serial, PortType::TCP}},
            {ProtocolType::Custom_Huayuan, {PortType::Serial, PortType::USB}},
            {ProtocolType::Custom_Heli, {PortType::Serial, PortType::TCP}},
            {ProtocolType::Custom_Yousheng, {PortType::Serial, PortType::USB}},
            
            // 自定义协议
            {ProtocolType::Custom, {PortType::Serial, PortType::TCP, PortType::UDP, PortType::USB}}
        };
        return mapping;
    }
    
    /**
     * @brief 检查协议是否兼容指定端口
     */
    static bool isCompatible(ProtocolType protocolType, PortType portType) {
        const auto& mapping = getMapping();
        const auto compatiblePorts = mapping.value(protocolType);
        return compatiblePorts.contains(portType);
    }
    
    /**
     * @brief 获取协议兼容的端口类型列表
     */
    static QList<PortType> getCompatiblePorts(ProtocolType protocolType) {
        const auto& mapping = getMapping();
        return mapping.value(protocolType);
    }
};

/**
 * @brief 推荐配置结构体
 */
struct RecommendedConfig {
    ProtocolType protocol;
    PortType port;
    bool isValid = false;
};

/**
 * @brief 设备配置结构体
 */
struct DeviceConfig {
    DeviceType device;
    ProtocolType protocol; 
    PortType port;
};

/**
 * @brief 设备映射表工具类
 */
class DeviceMappingUtils {
public:
    /**
     * @brief 验证设备配置的兼容性
     */
    static bool validateDeviceConfiguration(DeviceType deviceType, 
                                          ProtocolType protocolType, 
                                          PortType portType) {
        // 检查设备是否支持该协议
        if (!DeviceProtocolMappingTable::supportsProtocol(deviceType, protocolType)) {
            return false;
        }
        
        // 检查设备是否支持该端口
        if (!DevicePortMappingTable::supportsPort(deviceType, portType)) {
            return false;
        }
        
        // 检查协议是否兼容该端口
        if (!ProtocolPortCompatibilityTable::isCompatible(protocolType, portType)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * @brief 获取设备的推荐配置
     */
    static RecommendedConfig getRecommendedConfiguration(DeviceType deviceType) {
        RecommendedConfig config;
        config.isValid = false;
        
        const auto supportedProtocols = DeviceProtocolMappingTable::getSupportedProtocols(deviceType);
        const auto supportedPorts = DevicePortMappingTable::getSupportedPorts(deviceType);
        
        if (supportedProtocols.isEmpty() || supportedPorts.isEmpty()) {
            return config;
        }
        
        // 找到第一个兼容的协议-端口组合
        for (const auto& protocol : supportedProtocols) {
            for (const auto& port : supportedPorts) {
                if (ProtocolPortCompatibilityTable::isCompatible(protocol, port)) {
                    config.protocol = protocol;
                    config.port = port;
                    config.isValid = true;
                    return config;
                }
            }
        }
        
        return config;
    }
    
    /**
     * @brief 获取所有有效的设备配置组合
     */
    static QList<DeviceConfig> getAllValidConfigurations(DeviceType deviceType) {
        QList<DeviceConfig> configurations;
        
        const auto supportedProtocols = DeviceProtocolMappingTable::getSupportedProtocols(deviceType);
        const auto supportedPorts = DevicePortMappingTable::getSupportedPorts(deviceType);
        
        for (const auto& protocol : supportedProtocols) {
            for (const auto& port : supportedPorts) {
                if (ProtocolPortCompatibilityTable::isCompatible(protocol, port)) {
                    DeviceConfig config;
                    config.device = deviceType;
                    config.protocol = protocol;
                    config.port = port;
                    configurations.append(config);
                }
            }
        }
        
        return configurations;
    }
};

/**
 * @brief 设备依赖注入配置表
 * 
 * 静态映射表，定义每种设备类型的通讯组件依赖配置
 * 支持条件依赖注入：根据设备类型动态注入所需的通讯组件
 */
class DeviceDependencyMappingTable {
public:
    static const QMap<DeviceType, DeviceCommunicationDependency>& getMapping() {
        static const QMap<DeviceType, DeviceCommunicationDependency> mapping = []() {
            QMap<DeviceType, DeviceCommunicationDependency> map;
            
            // === 有完整通讯接口的设备（Direct） ===
            auto createDirectDependency = [](const QString& connSrv, const QString& protSrv, const QString& cmdSrv) {
                DeviceCommunicationDependency dep;
                dep.dependencyType = CommunicationDependencyType::Direct;
                dep.needsConnection = true;
                dep.needsProtocol = true;
                dep.needsCommandSystem = true;
                dep.connectionServiceId = connSrv;
                dep.protocolServiceId = protSrv;
                dep.commandServiceId = cmdSrv;
                return dep;
            };
            
            // 传感器设备 - 需要完整通讯组件
            map[DeviceType::YJSensor] = createDirectDependency("serial-connection", "yjsensor-protocol", "yjsensor-commands");
            map[DeviceType::CoinSensor] = createDirectDependency("serial-connection", "coinsensor-protocol", "coinsensor-commands");
            map[DeviceType::TemperatureSensor] = createDirectDependency("serial-connection", "modbus-protocol", "standard-commands");
            map[DeviceType::PressureSensor] = createDirectDependency("serial-connection", "modbus-protocol", "standard-commands");
            
            // 测试设备 - 需要完整通讯组件
            map[DeviceType::TestBoard] = createDirectDependency("serial-connection", "testboard-protocol", "testboard-commands");
            map[DeviceType::BottomBoard] = createDirectDependency("serial-connection", "bottomboard-protocol", "bottomboard-commands");
            
            // 扫描设备 - 需要完整通讯组件
            map[DeviceType::NovaScanner] = createDirectDependency("tcp-connection", "nova-protocol", "nova-commands");
            map[DeviceType::Scanner] = createDirectDependency("serial-connection", "standard-protocol", "scanner-commands");
            map[DeviceType::Barcode] = createDirectDependency("serial-connection", "standard-protocol", "barcode-commands");
            
            // 工业控制设备 - 需要完整通讯组件
            map[DeviceType::ModbusDevice] = createDirectDependency("serial-connection", "modbus-protocol", "modbus-commands");
            map[DeviceType::CanDevice] = createDirectDependency("can-connection", "can-protocol", "can-commands");
            map[DeviceType::PLC] = createDirectDependency("ethernet-connection", "modbus-protocol", "plc-commands");
            
            // 客户定制设备 - 需要完整通讯组件
            map[DeviceType::BesterDevice] = createDirectDependency("tcp-connection", "bester-protocol", "bester-commands");
            map[DeviceType::HeliDevice] = createDirectDependency("serial-connection", "heli-protocol", "heli-commands");
            map[DeviceType::HuayuanDevice] = createDirectDependency("serial-connection", "huayuan-protocol", "huayuan-commands");
            map[DeviceType::YoushengDevice] = createDirectDependency("serial-connection", "yousheng-protocol", "yousheng-commands");
            
            // === 无通讯接口的设备（None） ===
            auto createNoneDependency = []() {
                DeviceCommunicationDependency dep;
                dep.dependencyType = CommunicationDependencyType::None;
                dep.needsConnection = false;
                dep.needsProtocol = false;
                dep.needsCommandSystem = false;
                return dep;
            };
            
            // 纯软件设备或本地设备（无需通讯组件）
            map[DeviceType::Fixture] = createNoneDependency();  // 夹具设备 - 纯机械，无通讯
            
            // === 间接通讯设备（Indirect） ===
            auto createIndirectDependency = [](const QString& parentId) {
                DeviceCommunicationDependency dep;
                dep.dependencyType = CommunicationDependencyType::Indirect;
                dep.needsConnection = false;  // 不需要自己的连接
                dep.needsProtocol = false;    // 不需要自己的协议
                dep.needsCommandSystem = true; // 需要指令系统来构建指令
                dep.parentDeviceId = parentId;
                dep.commandServiceId = "proxy-commands";
                return dep;
            };
            
            // 通过其他设备进行通讯的设备
            map[DeviceType::Motor] = createIndirectDependency("plc-controller");      // 电机通过PLC控制
            map[DeviceType::Valve] = createIndirectDependency("plc-controller");      // 阀门通过PLC控制
            map[DeviceType::Pump] = createIndirectDependency("plc-controller");       // 泵通过PLC控制
            map[DeviceType::Actuator] = createIndirectDependency("main-controller");  // 执行器通过主控制器
            
            // === 混合模式设备（Hybrid） ===
            auto createHybridDependency = [](const QString& connSrv, const QString& protSrv, const QString& cmdSrv, const QString& parentId) {
                DeviceCommunicationDependency dep;
                dep.dependencyType = CommunicationDependencyType::Hybrid;
                dep.needsConnection = true;
                dep.needsProtocol = true;
                dep.needsCommandSystem = true;
                dep.connectionServiceId = connSrv;
                dep.protocolServiceId = protSrv;
                dep.commandServiceId = cmdSrv;
                dep.parentDeviceId = parentId; // 某些功能需要父设备
                return dep;
            };
            
            // 既有直接通讯又需要通过父设备的设备
            map[DeviceType::Sensor] = createHybridDependency("i2c-connection", "i2c-protocol", "sensor-commands", "sensor-hub");
            
            // === 通用设备配置 ===
            map[DeviceType::Generic] = createDirectDependency("generic-connection", "generic-protocol", "generic-commands");
            map[DeviceType::Unknown] = createNoneDependency();
            
            return map;
        }();
        return mapping;
    }
    
    /**
     * @brief 获取设备的依赖注入配置
     */
    static DeviceCommunicationDependency getDependencyConfig(DeviceType deviceType) {
        const auto& mapping = getMapping();
        return mapping.value(deviceType, DeviceCommunicationDependency());
    }
    
    /**
     * @brief 检查设备是否需要指定的服务组件
     */
    static bool requiresService(DeviceType deviceType, const QString& serviceId) {
        const auto config = getDependencyConfig(deviceType);
        const auto services = config.getRequiredServices();
        return services.contains(serviceId);
    }
    
    /**
     * @brief 获取需要连接组件的设备类型列表
     */
    static QList<DeviceType> getDevicesNeedingConnection() {
        QList<DeviceType> result;
        const auto& mapping = getMapping();
        for (auto it = mapping.begin(); it != mapping.end(); ++it) {
            if (it.value().needsConnection) {
                result << it.key();
            }
        }
        return result;
    }
    
    /**
     * @brief 获取需要协议组件的设备类型列表
     */
    static QList<DeviceType> getDevicesNeedingProtocol() {
        QList<DeviceType> result;
        const auto& mapping = getMapping();
        for (auto it = mapping.begin(); it != mapping.end(); ++it) {
            if (it.value().needsProtocol) {
                result << it.key();
            }
        }
        return result;
    }
    
    /**
     * @brief 获取需要指令系统的设备类型列表
     */
    static QList<DeviceType> getDevicesNeedingCommands() {
        QList<DeviceType> result;
        const auto& mapping = getMapping();
        for (auto it = mapping.begin(); it != mapping.end(); ++it) {
            if (it.value().needsCommandSystem) {
                result << it.key();
            }
        }
        return result;
    }
    
    /**
     * @brief 获取独立设备（无通讯依赖）列表
     */
    static QList<DeviceType> getStandaloneDevices() {
        QList<DeviceType> result;
        const auto& mapping = getMapping();
        for (auto it = mapping.begin(); it != mapping.end(); ++it) {
            if (it.value().isStandalone()) {
                result << it.key();
            }
        }
        return result;
    }
    
    /**
     * @brief 获取需要间接通讯的设备列表
     */
    static QList<DeviceType> getIndirectCommunicationDevices() {
        QList<DeviceType> result;
        const auto& mapping = getMapping();
        for (auto it = mapping.begin(); it != mapping.end(); ++it) {
            if (it.value().requiresParent()) {
                result << it.key();
            }
        }
        return result;
    }
    
    /**
     * @brief 获取设备的父设备ID
     */
    static QString getParentDeviceId(DeviceType deviceType) {
        const auto config = getDependencyConfig(deviceType);
        return config.parentDeviceId;
    }
};

} // namespace DataStructures
} // namespace Communication
} // namespace LA