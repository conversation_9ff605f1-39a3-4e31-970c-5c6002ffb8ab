{"permissions": {"allow": ["Bash(find:*)", "Bash(cmake:*)", "Bash(timeout 30 cmake --build build)", "mcp__ide__getDiagnostics", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(taskkill:*)", "Bash(ls:*)", "<PERSON><PERSON>(timeout:*)", "Bash(./CSPC_LA_function.exe)", "<PERSON><PERSON>(mkdir:*)", "Bash(ldd:*)", "<PERSON><PERSON>(objdump:*)", "Bash(./CSPC_LA_function.exe --version)", "Bash(rm:*)", "Bash(cp:*)", "<PERSON><PERSON>(echo:*)", "Bash(.scriptstest_app_simple.bat)", "Bash(scripts/test_app_simple.bat)", "<PERSON><PERSON>(dir)", "<PERSON><PERSON>(dir scripts)", "Bash(./scripts/test_app_simple.bat)", "Ba<PERSON>(cmd:*)", "Bash(start CSPC_LA_function.exe)", "<PERSON><PERSON>(ninja:*)", "<PERSON><PERSON>(make:*)", "Bash(grep:*)", "Bash(./CSPC_LA_function.exe --help)", "WebFetch(domain:github.com)", "Bash(\"ui\\themes\\tests\\baseline_theme_test.exe\")", "WebFetch(domain:raw.githubusercontent.com)", "Bash(start \"\" \"baseline_theme_test.exe\")", "<PERSON><PERSON>(dir:*)", "Bash(./baseline_theme_test.exe)", "Bash(\"./baseline_theme_test.exe\")", "<PERSON><PERSON>(cat:*)", "Bash(./simple_theme_test.exe)", "Bash(powershell.exe:*)", "Bash(g++:*)", "Bash(.connection_interface_test.exe)", "Bash(connection_interface_test.exe)", "Bash(./connection_interface_test.exe)", "Bash(ctest:*)", "<PERSON><PERSON>(touch:*)", "Bash(./bin/CSPC_LA_function.exe)", "Bash(\"./build/bin/CSPC_LA_function.exe\" --help)", "Bash(\"./build/bin/CSPC_LA_function.exe\")", "Bash(gdb:*)", "<PERSON><PERSON>(start:*)", "Bash(CSPC_LA_function.exe)", "<PERSON><PERSON>(mv:*)", "Bash(\"./build/bin/CSPC_LA_function.exe\")", "Bash(\".\\build\\bin\\CSPC_LA_function.exe\")", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(powershell:*)", "WebSearch", "Bash(./build/bin/test_settings_layout.exe)", "Bash(copy ..CMakeLists_container_test.txt CMakeLists.txt)", "Bash(copy CMakeContainerTest.txt CMakeLists.txt)", "Bash(copy CMakeLists_container_test.txt CMakeLists.txt)", "Bash(./build/bin/simple_stack_test.exe)", "<PERSON><PERSON>(true)", "Bash(./test_settings_layout.exe)", "Bash(./simple_settings_test.exe)", "Bash(./minimal_layout_test.exe)", "Bash(./minimal_layout_test.exe:*)", "Bash(.buildbinCSPC_LA_function.exe --version)", "Bash(for file in *Test.cpp)", "Bash(do if [[ \"$file\" == *Test.cpp ]])", "Bash(then newname=\"$file%Test.cpp.cpp\")", "Bash(fi)", "Bash(done)", "Bash(for:*)", "Bash(do if [[ \"$file\" != Test*)", "Bash(do if [[ \"$file\" != Test* ]])", "Bash(bash build_and_run_sprm_test.bat)", "Bash(./print_sprm_commands.exe)", "Bash(.toolsrun_device_command_tests.bat sprm)", "Bash(./print_sprm_commands_test.exe)", "Bash(./simple_sprm_test.exe)", "Bash(toolsvalidate_sprm_integration.bat)", "Bash(./functionality_test.exe)", "Bash(\"tools\\run_matching_flow_test.bat\")", "Bash(toolsvalidate_test_structure.bat)", "Bash(toolsbuild_device_module.bat)", "Bash(.testsprint_sprm_commands.exe)", "Bash(\"./tests/print_sprm_commands.exe\")", "Bash(qmake:*)", "Bash(toolsrun_unit_tests.bat)", "Bash(\"tools\\run_unit_tests.bat\")", "Bash(copy ..CMakeLists_quick_test.txt CMakeLists.txt)", "Bash(./tests/print_sprm_commands.exe)", "Bash(toolsconfigure_cmake.bat)", "Bash(.toolsconfigure_cmake.bat)"], "deny": [], "defaultMode": "acceptEdits", "additionalDirectories": ["F:\\f\\02_qt-code\\13_LA-Function", "F:\\d F:\\02_qt-code\\13_LA-Function", "F:\\02_qt-code\\13_LA-Function"]}}