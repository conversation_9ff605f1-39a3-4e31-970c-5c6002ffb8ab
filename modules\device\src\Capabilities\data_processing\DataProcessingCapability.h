/**
 * @file DataProcessingCapability.h
 * @brief 数据处理能力适配器 - 设备层与data sharing模块的桥接
 * 
 * Linus式设计原则：
 * - "数据结构决定算法" - 基于标准数据格式进行处理
 * - "最小接口" - 只负责数据处理能力，不涉及其他逻辑
 * - "可组合性" - 可与其他能力模块配合使用
 */

#pragma once

#include "../communication/CommunicationCapability.h"
#include "support/data_sharing/IDataSharingSystem.h"
#include <QObject>
#include <QVariantMap>
#include <QQueue>
#include <QTimer>
#include <memory>

namespace LA::Device::Capability {

/**
 * @brief 数据处理规则
 */
struct DataProcessingRule {
    QString ruleId;               // 规则ID
    QString ruleName;             // 规则名称
    QString dataField;            // 处理的数据字段
    QString operation;            // 处理操作
    QVariantMap parameters;       // 操作参数
    bool enabled;                 // 是否启用
};

/**
 * @brief 数据验证规则
 */
struct DataValidationRule {
    QString fieldName;            // 字段名
    QString validationType;       // 验证类型 (range, enum, regex等)
    QVariantMap constraints;      // 约束条件
    QString errorMessage;         // 错误消息
    bool required;                // 是否必需
};

/**
 * @brief 数据处理结果
 */
struct DataProcessingResult {
    bool success;                 // 是否成功
    QVariantMap originalData;     // 原始数据
    QVariantMap processedData;    // 处理后数据
    QVariantList appliedRules;    // 应用的规则
    QString error;                // 错误信息
    QDateTime timestamp;          // 处理时间
};

/**
 * @brief 数据处理能力适配器 - 与数据共享模块桥接
 * 
 * 职责：
 * - 提供设备数据处理能力接口
 * - 适配support/data_sharing模块的数据服务
 * - 管理数据验证、转换、缓存
 * - 支持实时数据流处理
 */
class DataProcessingCapability : public IDeviceCapability {
    Q_OBJECT

public:
    explicit DataProcessingCapability(QObject* parent = nullptr);
    virtual ~DataProcessingCapability();

    // === IDeviceCapability接口实现 ===
    QString getCapabilityId() const override { return "data_processing"; }
    QStringList getDependencies() const override { 
        return {"data_sharing_system", "algorithm_service"}; 
    }
    
    bool initialize(const QVariantMap& config) override;
    QVariantMap executeCapability(const QString& action, const QVariantMap& params) override;
    void shutdown() override;
    void setInfrastructureServices(const QVariantMap& services) override;

    // === 数据处理专用接口 ===
    
    /**
     * @brief 处理设备数据
     * @param deviceData 设备原始数据
     * @param processingRules 处理规则列表
     * @return 处理结果
     */
    QVariantMap processDeviceData(const QVariantMap& deviceData, const QStringList& processingRules = {});
    
    /**
     * @brief 验证设备数据
     * @param deviceData 设备数据
     * @param validationRules 验证规则列表
     * @return 验证结果
     */
    QVariantMap validateDeviceData(const QVariantMap& deviceData, const QStringList& validationRules = {});
    
    /**
     * @brief 缓存设备数据
     * @param deviceId 设备ID
     * @param data 数据
     * @param cacheKey 缓存键
     * @return 是否成功
     */
    bool cacheDeviceData(const QString& deviceId, const QVariantMap& data, const QString& cacheKey = "default");
    
    /**
     * @brief 获取缓存数据
     * @param deviceId 设备ID
     * @param cacheKey 缓存键
     * @return 缓存数据
     */
    QVariantMap getCachedData(const QString& deviceId, const QString& cacheKey = "default");
    
    /**
     * @brief 共享数据到数据共享系统
     * @param dataType 数据类型
     * @param data 数据内容
     * @param metadata 元数据
     * @return 是否成功
     */
    bool shareData(const QString& dataType, const QVariantMap& data, const QVariantMap& metadata = {});
    
    /**
     * @brief 从数据共享系统订阅数据
     * @param dataType 数据类型
     * @param filter 过滤条件
     * @return 是否成功
     */
    bool subscribeToSharedData(const QString& dataType, const QVariantMap& filter = {});

    // === 数据流处理 ===
    
    /**
     * @brief 启动实时数据流处理
     * @param streamConfig 流配置
     * @return 是否成功
     */
    bool startDataStreamProcessing(const QVariantMap& streamConfig);
    
    /**
     * @brief 停止实时数据流处理
     * @return 是否成功
     */
    bool stopDataStreamProcessing();
    
    /**
     * @brief 处理实时数据流
     * @param streamData 流数据
     * @return 处理结果
     */
    QVariantMap processDataStream(const QVariantMap& streamData);

    // === 规则管理 ===
    
    /**
     * @brief 添加数据处理规则
     * @param rule 处理规则
     * @return 是否成功
     */
    bool addProcessingRule(const DataProcessingRule& rule);
    
    /**
     * @brief 添加数据验证规则
     * @param rule 验证规则
     * @return 是否成功
     */
    bool addValidationRule(const DataValidationRule& rule);
    
    /**
     * @brief 移除处理规则
     * @param ruleId 规则ID
     * @return 是否成功
     */
    bool removeProcessingRule(const QString& ruleId);
    
    /**
     * @brief 获取当前活跃的处理规则
     * @return 规则列表
     */
    QVector<DataProcessingRule> getActiveProcessingRules() const;

Q_SIGNALS:
    // === 数据处理信号 ===
    void dataProcessed(const QString& deviceId, const QVariantMap& originalData, const QVariantMap& processedData);
    void dataValidated(const QString& deviceId, const QVariantMap& data, bool isValid);
    void dataValidationFailed(const QString& deviceId, const QVariantMap& data, const QString& error);
    
    // === 数据流信号 ===
    void dataStreamStarted(const QString& streamId);
    void dataStreamStopped(const QString& streamId);
    void dataStreamProcessed(const QString& streamId, const QVariantMap& processedData);
    
    // === 数据共享信号 ===
    void dataShared(const QString& dataType, const QVariantMap& data);
    void sharedDataReceived(const QString& dataType, const QVariantMap& data);

private slots:
    // === 数据共享系统事件处理 ===
    void onSharedDataReceived(const QString& dataType, const QVariantMap& data);
    void onDataSharingError(const QString& error);
    
    // === 数据流处理 ===
    void onStreamDataReceived();
    void processDataStreamBatch();

private:
    // === 基础设施服务注入 ===
    std::shared_ptr<LA::Support::DataSharing::IDataSharingSystem> m_dataSharingSystem;  // 数据共享系统
    QVariant m_algorithmService;                              // 算法服务（数据处理算法）
    
    // === 数据处理状态 ===
    bool m_initialized;
    bool m_streamProcessingActive;
    QString m_currentStreamId;
    
    // === 数据处理配置 ===
    QVariantMap m_processingConfig;
    QVector<DataProcessingRule> m_processingRules;
    QVector<DataValidationRule> m_validationRules;
    
    // === 数据缓存 ===
    QMap<QString, QMap<QString, QVariantMap>> m_dataCache;  // deviceId -> cacheKey -> data
    QQueue<QVariantMap> m_streamDataQueue;                  // 流数据队列
    
    // === 定时器 ===
    QTimer* m_streamTimer;
    QTimer* m_cacheCleanupTimer;
    
    // === 统计信息 ===
    quint64 m_dataProcessedCount;
    quint64 m_dataValidatedCount;
    quint64 m_dataSharedCount;
    quint64 m_dataReceivedCount;
    quint64 m_errorCount;

private:
    // === 内部工具方法 ===
    
    /**
     * @brief 验证基础设施服务
     * @return 是否所有必需服务都可用
     */
    bool validateInfrastructureServices() const;
    
    /**
     * @brief 初始化默认处理规则
     */
    void initializeDefaultProcessingRules();
    
    /**
     * @brief 初始化默认验证规则
     */
    void initializeDefaultValidationRules();
    
    /**
     * @brief 应用处理规则到数据
     * @param data 原始数据
     * @param rules 处理规则
     * @return 处理后数据
     */
    QVariantMap applyProcessingRules(const QVariantMap& data, const QVector<DataProcessingRule>& rules);
    
    /**
     * @brief 应用验证规则到数据
     * @param data 数据
     * @param rules 验证规则
     * @return 验证结果
     */
    QVariantMap applyValidationRules(const QVariantMap& data, const QVector<DataValidationRule>& rules);
    
    /**
     * @brief 清理过期缓存数据
     */
    void cleanupExpiredCache();
    
    /**
     * @brief 构建错误结果
     * @param error 错误描述
     * @return 错误结果映射
     */
    QVariantMap createErrorResult(const QString& error) const;
    
    /**
     * @brief 构建成功结果
     * @param data 结果数据
     * @return 成功结果映射
     */
    QVariantMap createSuccessResult(const QVariantMap& data = {}) const;
    
    /**
     * @brief 更新统计信息
     * @param operation 操作类型
     * @param success 是否成功
     */
    void updateStatistics(const QString& operation, bool success);
};

/**
 * @brief 数据处理能力工厂
 */
class DataProcessingCapabilityFactory {
public:
    /**
     * @brief 创建数据处理能力实例
     * @param deviceType 设备类型
     * @param config 配置参数
     * @return 数据处理能力实例
     */
    static std::unique_ptr<DataProcessingCapability> createCapability(const QString& deviceType, 
                                                                     const QVariantMap& config = {});
    
    /**
     * @brief 创建带基础设施服务的数据处理能力
     * @param deviceType 设备类型
     * @param dataSharingSystem 数据共享系统
     * @param algorithmService 算法服务
     * @return 数据处理能力实例
     */
    static std::unique_ptr<DataProcessingCapability> createWithServices(
        const QString& deviceType,
        std::shared_ptr<LA::Support::DataSharing::IDataSharingSystem> dataSharingSystem,
        QVariant algorithmService);
};

} // namespace LA::Device::Capability