#pragma once

/**
 * @file CommunicationAttributes.h
 * @brief 通信属性结构体定义
 * 
 * 定义设备通信相关的所有属性，建立设备与通信配置的映射关系
 */

#include "PortAttributes.h"
#include <QString>
#include <QVariantMap>
#include <QHostAddress>
#include <variant>

namespace LA {
namespace Communication {
namespace DataStructures {

/**
 * @brief 协议类型枚举
 */
enum class ProtocolType {
    // 标准工业协议
    Modbus_RTU,         // Modbus RTU协议
    Modbus_TCP,         // Modbus TCP协议  
    CAN,                // CAN总线协议
    Profibus,           // Profibus协议
    Ethernet_IP,        // Ethernet/IP协议
    
    // 通用协议
    Raw,                // 原始数据协议
    SimpleChecksum,     // 简单校验和协议
    CRC16,              // CRC16校验协议
    CRC32,              // CRC32校验协议
    XOR,                // 异或校验协议
    JSON,               // JSON协议
    XML,                // XML协议
    
    // 客户定制协议
    Custom_YJSensor,    // 云鲸传感器协议
    Custom_CoinSensor,  // 硬币传感器协议
    Custom_BottomBoard, // 底板协议
    Custom_TestBoard,   // 测试板协议
    Custom_Nova,        // Nova协议
    Custom_Bester,      // Bester协议
    Custom_Huayuan,     // 华远协议
    Custom_Heli,        // 合力协议
    Custom_Yousheng,    // 优胜协议
    
    // 自定义协议
    Custom              // 完全自定义协议
};

/**
 * @brief 协议配置参数
 */
struct ProtocolConfig {
    ProtocolType type;              // 协议类型
    QString name;                   // 协议名称
    QString version;                // 协议版本
    QVariantMap parameters;         // 协议参数
    
    // 数据格式配置
    int headerSize = 0;             // 帧头大小
    int footerSize = 0;             // 帧尾大小
    int maxDataSize = 1024;         // 最大数据大小
    bool needChecksum = false;      // 是否需要校验
    QString checksumType;           // 校验类型
    int checksumOffset = -1;        // 校验位置(-1表示末尾)
    
    // 通信配置
    int timeout = 1000;             // 超时时间（毫秒）
    int retryCount = 3;             // 重试次数
    bool autoRetry = true;          // 自动重试
    int responseDelay = 0;          // 响应延迟（毫秒）
    
    ProtocolConfig() : type(ProtocolType::Raw) {}
};

/**
 * @brief 串口通信配置
 */
struct SerialConfig {
    QString portName;               // 端口名称
    int baudRate = 9600;           // 波特率
    int dataBits = 8;              // 数据位
    int stopBits = 1;              // 停止位
    QString parity = "None";        // 校验位 (None/Even/Odd/Mark/Space)
    QString flowControl = "None";   // 流控 (None/Hardware/Software)
    
    // 高级配置
    int readTimeout = 1000;         // 读超时
    int writeTimeout = 1000;        // 写超时
    int readBufferSize = 4096;      // 读缓冲区大小
    int writeBufferSize = 4096;     // 写缓冲区大小
};

/**
 * @brief 网络通信配置
 */
struct NetworkConfig {
    QHostAddress hostAddress;       // 主机地址
    quint16 port = 502;            // 端口号
    QString protocol = "TCP";       // 协议类型 (TCP/UDP)
    
    // 连接配置
    int connectTimeout = 5000;      // 连接超时
    int readTimeout = 1000;         // 读超时
    int writeTimeout = 1000;        // 写超时
    bool keepAlive = true;          // 保持连接
    int keepAliveInterval = 30000;  // 保活间隔
    
    // 缓冲区配置
    int readBufferSize = 8192;      // 读缓冲区大小
    int writeBufferSize = 8192;     // 写缓冲区大小
};

/**
 * @brief CAN总线配置
 */
struct CanConfig {
    QString interface = "can0";     // CAN接口名称
    int bitrate = 500000;          // 比特率
    bool extendedFrame = false;     // 是否使用扩展帧
    quint32 canId = 0x123;         // CAN ID
    quint32 mask = 0x7FF;          // 过滤掩码
    
    // 高级配置
    int txTimeout = 1000;           // 发送超时
    int rxTimeout = 1000;           // 接收超时
    bool loopback = false;          // 是否回环
    bool receiveOwnMessages = false; // 是否接收自己发送的消息
};

/**
 * @brief 通用连接配置
 */
struct ConnectionConfig {
    PortType portType;              // 端口类型
    QString connectionString;       // 连接字符串
    QVariantMap parameters;         // 连接参数
    
    // 具体配置使用std::variant
    std::variant<SerialConfig, NetworkConfig, CanConfig> config;
    
    ConnectionConfig() : portType(PortType::Serial), config(SerialConfig{}) {
    }
    
    // 获取串口配置
    SerialConfig& getSerialConfig() {
        return std::get<SerialConfig>(config);
    }
    
    const SerialConfig& getSerialConfig() const {
        return std::get<SerialConfig>(config);
    }
    
    // 获取网络配置
    NetworkConfig& getNetworkConfig() {
        return std::get<NetworkConfig>(config);
    }
    
    const NetworkConfig& getNetworkConfig() const {
        return std::get<NetworkConfig>(config);
    }
    
    // 获取CAN配置
    CanConfig& getCanConfig() {
        return std::get<CanConfig>(config);
    }
    
    const CanConfig& getCanConfig() const {
        return std::get<CanConfig>(config);
    }
    
    // 设置配置
    void setSerialConfig(const SerialConfig& serialConfig) {
        portType = PortType::Serial;
        config = serialConfig;
    }
    
    void setNetworkConfig(const NetworkConfig& networkConfig) {
        portType = (networkConfig.protocol == "TCP") ? PortType::TCP : PortType::UDP;
        config = networkConfig;
    }
    
    void setCanConfig(const CanConfig& canConfig) {
        portType = PortType::CAN;
        config = canConfig;
    }
};

/**
 * @brief 通信统计信息
 */
struct CommunicationStats {
    // 数据统计
    quint64 bytesSent = 0;          // 发送字节数
    quint64 bytesReceived = 0;      // 接收字节数
    quint64 packetsSent = 0;        // 发送包数
    quint64 packetsReceived = 0;    // 接收包数
    
    // 错误统计
    quint64 sendErrors = 0;         // 发送错误数
    quint64 receiveErrors = 0;      // 接收错误数
    quint64 timeoutErrors = 0;      // 超时错误数
    quint64 checksumErrors = 0;     // 校验错误数
    
    // 性能统计
    double averageResponseTime = 0.0;   // 平均响应时间
    double maxResponseTime = 0.0;       // 最大响应时间
    double minResponseTime = 0.0;       // 最小响应时间
    quint64 totalTransactions = 0;      // 总交易数
    
    // 时间信息
    QDateTime firstActivity;            // 首次活动时间
    QDateTime lastActivity;             // 最后活动时间
    QDateTime statsResetTime;           // 统计重置时间
    
    /**
     * @brief 重置统计信息
     */
    void reset() {
        *this = CommunicationStats();
        statsResetTime = QDateTime::currentDateTime();
    }
    
    /**
     * @brief 计算成功率
     */
    double getSuccessRate() const {
        if (totalTransactions == 0) return 0.0;
        quint64 errors = sendErrors + receiveErrors + timeoutErrors + checksumErrors;
        return (double)(totalTransactions - errors) / totalTransactions * 100.0;
    }
};

/**
 * @brief 设备通信属性
 * 
 * 包含设备通信相关的所有配置和状态信息
 */
struct CommunicationAttributes {
    // === 协议配置 ===
    ProtocolConfig protocol;            // 协议配置
    
    // === 连接配置 ===  
    ConnectionConfig connection;        // 连接配置
    QString primaryPortId;              // 主端口ID
    QStringList secondaryPortIds;       // 备用端口ID列表
    
    // === 通信状态 ===
    bool isConnected = false;           // 是否已连接
    QDateTime connectedTime;            // 连接建立时间
    QDateTime lastDataTime;             // 最后数据时间
    QString lastErrorMessage;           // 最后错误信息
    
    // === 统计信息 ===
    CommunicationStats statistics;      // 通信统计
    
    // === 高级配置 ===
    bool enableAutoReconnect = true;    // 启用自动重连
    int reconnectInterval = 5000;       // 重连间隔(ms)
    int maxReconnectAttempts = 3;       // 最大重连次数
    
    bool enableHeartbeat = false;       // 启用心跳
    int heartbeatInterval = 30000;      // 心跳间隔(ms)
    QString heartbeatCommand;           // 心跳指令
    
    bool enableDataLogging = false;     // 启用数据记录
    QString logLevel = "Info";          // 日志级别
    
    /**
     * @brief 检查是否健康
     */
    bool isHealthy() const {
        if (!isConnected) return false;
        
        // 检查最后数据时间
        if (lastDataTime.isValid()) {
            qint64 elapsed = lastDataTime.msecsTo(QDateTime::currentDateTime());
            return elapsed < heartbeatInterval * 2;  // 2倍心跳间隔内有数据认为健康
        }
        
        return true;
    }
    
    /**
     * @brief 获取连接持续时间(秒)
     */
    qint64 getConnectionDuration() const {
        if (!isConnected || !connectedTime.isValid()) return 0;
        return connectedTime.secsTo(QDateTime::currentDateTime());
    }
};

/**
 * @brief 通信属性工具类
 */
class CommunicationAttributesUtils {
public:
    /**
     * @brief 协议类型转字符串
     */
    static QString protocolTypeToString(ProtocolType type);
    
    /**
     * @brief 字符串转协议类型
     */
    static ProtocolType stringToProtocolType(const QString& typeStr);
    
    /**
     * @brief 验证协议配置
     */
    static bool validateProtocolConfig(const ProtocolConfig& config);
    
    /**
     * @brief 验证连接配置
     */
    static bool validateConnectionConfig(const ConnectionConfig& config);
    
    /**
     * @brief 创建默认协议配置
     */
    static ProtocolConfig createDefaultProtocolConfig(ProtocolType type);
    
    /**
     * @brief 创建默认连接配置
     */
    static ConnectionConfig createDefaultConnectionConfig(PortType portType);
};

} // namespace DataStructures
} // namespace Communication
} // namespace LA