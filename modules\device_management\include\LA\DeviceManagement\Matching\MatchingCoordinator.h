#pragma once

#include "IDeviceIdentifier.h"
#include "IDeviceProber.h"
#include "IMatchingAlgorithm.h"
#include "IPortScanner.h"
#include "LA/DeviceManagement/Registration/IRegistrationManager.h"
#include "../Core/SimpleTypes.h"  // 使用简化类型
#include <QObject>
#include <memory>


// 移除危险的命名空间导入 - 防止Qt MOC命名空间污染
// using namespace LA::DeviceManagement::Registration;

// 前向声明 - 明确指定完整命名空间
namespace LA {
namespace DeviceManagement {
namespace Registration {
class IRegistrationManager;
}
}  // namespace DeviceManagement
}  // namespace LA

namespace LA {
namespace DeviceManagement {
namespace Matching {

// 使用简化类型 - 确保类型一致性
using LA::DeviceManagement::Core::DeviceInfo;
using LA::DeviceManagement::Core::PortInfo;

/**
 * @brief 匹配协调器 - 组合各个最小模块（完整4阶段流程）
 *
 * Linus: "通过组合而非继承实现复杂功能"
 * ✅ 负责: 统一协调各个最小模块，执行完整的4阶段匹配流程
 * ❌ 不涉及: 具体的扫描、探测、识别、算法、注册实现
 *
 * 完整流程（基于data_flow_architecture.md）:
 * 阶段1: 扫描发现（PortScanner + DeviceRegistry）
 * 阶段2: 探测识别（DeviceProber + DeviceIdentifier）
 * 阶段3: 匹配计算（MatchingAlgorithm）
 * 阶段4: 注册管理（RegistrationManager） <- 新增，完成后匹配流程结束
 */
class MatchingCoordinator : public QObject {
    Q_OBJECT

  public:
    explicit MatchingCoordinator(QObject *parent = nullptr);
    virtual ~MatchingCoordinator() = default;

    // ====== 依赖注入 - 组合各个最小模块（完整5个模块） ======
    void setPortScanner(std::unique_ptr<IPortScanner> portScanner);
    void setDeviceProber(std::unique_ptr<IDeviceProber> deviceProber);
    void setDeviceIdentifier(std::unique_ptr<IDeviceIdentifier> deviceIdentifier);
    void setMatchingAlgorithm(std::unique_ptr<IMatchingAlgorithm> matchingAlgorithm);
    void setRegistrationManager(std::shared_ptr<Registration::IRegistrationManager> registrationManager);

    // ====== 核心协调功能 ======
    /**
     * @brief 执行完整的自动匹配流程
     * @param probeConfig 探测配置
     * @param matchingConfig 匹配配置
     * @return 匹配结果
     */
    MatchingResult performAutoMatching(const DeviceProbeConfig &probeConfig = DeviceProbeConfig(), const MatchingConfig &matchingConfig = MatchingConfig());

    /**
     * @brief 异步执行自动匹配
     * @param probeConfig 探测配置
     * @param matchingConfig 匹配配置
     */
    void performAutoMatchingAsync(const DeviceProbeConfig &probeConfig = DeviceProbeConfig(), const MatchingConfig &matchingConfig = MatchingConfig());

    // ====== 分步执行 - 可独立调用（完整4阶段） ======
    QList<PortInfo>   discoverPorts();                                                                                                 // 阶段1
    QList<DeviceInfo> probeAndIdentifyDevices(const QList<PortInfo> &ports, const DeviceProbeConfig &config);                          // 阶段2
    MatchingResult    calculateMatches(const QList<PortInfo> &ports, const QList<DeviceInfo> &devices, const MatchingConfig &config);  // 阶段3
    bool              registerMatchedDevices(const MatchingResult &matchingResult);                                                    // 阶段4 - 新增

    // ====== 状态查询 ======
    bool    isReady() const;
    QString getLastError() const;

  signals:
    void matchingStarted();
    void matchingProgress(int current, int total, const QString &status);
    void matchingCompleted(const MatchingResult &result);
    void matchingError(const QString &error);

  private slots:
    void onAsyncMatchingFinished();

  private:
    // ====== 内部实现 ======
    void validateDependencies();
    void emitProgress(int current, int total, const QString &status);

    // ====== 依赖的最小模块（完整5个模块） ======
    std::unique_ptr<IPortScanner>                       m_portScanner;
    std::unique_ptr<IDeviceProber>                      m_deviceProber;
    std::unique_ptr<IDeviceIdentifier>                  m_deviceIdentifier;
    std::unique_ptr<IMatchingAlgorithm>                 m_matchingAlgorithm;
    std::shared_ptr<Registration::IRegistrationManager> m_registrationManager;  // 注册管理器

    // ====== 状态管理 ======
    QString m_lastError;
    bool    m_isRunning = false;
};

}  // namespace Matching
}  // namespace DeviceManagement
}  // namespace LA