/**
 * @file KalmanFilter.cpp
 * @brief 卡尔曼滤波策略实现
 */

#include "KalmanFilter.h"
#include <QDebug>
#include <QDateTime>
#include <cmath>
#include <algorithm>

namespace LA::Device::Strategy {

KalmanFilter::Ka<PERSON>Filter(QObject* parent) 
    : QObject(parent) 
{
    // 初始化状态
    reset();
}

QVariantMap KalmanFilter::executeStrategy(const QVariantMap& input) 
{
    if (!input.contains("measurement")) {
        return formatResult(0.0, 0.0, 0.0, 0.0);
    }
    
    double measurement = input["measurement"].toDouble();
    
    if (!m_state.initialized) {
        initializeFilter(measurement);
        return formatResult(measurement, measurement, 0.0, 0.0);
    }
    
    // 预测步骤
    predict();
    
    // 更新步骤
    double filteredValue = update(measurement);
    double residual = measurement - m_state.x;
    
    // 性能指标更新
    updateHistory(residual, m_state.lastGain);
    
    // 自适应噪声调整
    if (m_config.adaptiveNoise) {
        adaptNoiseParameters(residual);
    }
    
    emit filterUpdated(filteredValue, m_state.lastGain);
    
    return formatResult(filteredValue, measurement, m_state.lastGain, residual);
}

QVariantMap KalmanFilter::getStrategyConfig() const 
{
    QVariantMap config;
    config["process_noise"] = m_config.processNoise;
    config["measurement_noise"] = m_config.measurementNoise;
    config["initial_covariance"] = m_config.initialCovariance;
    config["time_interval"] = m_config.timeInterval;
    config["adaptive_noise"] = m_config.adaptiveNoise;
    config["adaptive_threshold"] = m_config.adaptiveThreshold;
    config["window_size"] = m_config.windowSize;
    return config;
}

bool KalmanFilter::setStrategyConfig(const QVariantMap& config) 
{
    if (config.contains("process_noise")) {
        m_config.processNoise = config["process_noise"].toDouble();
    }
    if (config.contains("measurement_noise")) {
        m_config.measurementNoise = config["measurement_noise"].toDouble();
    }
    if (config.contains("initial_covariance")) {
        m_config.initialCovariance = config["initial_covariance"].toDouble();
    }
    if (config.contains("time_interval")) {
        m_config.timeInterval = config["time_interval"].toDouble();
        setStateTransition(m_config.timeInterval);
    }
    if (config.contains("adaptive_noise")) {
        m_config.adaptiveNoise = config["adaptive_noise"].toBool();
    }
    if (config.contains("adaptive_threshold")) {
        m_config.adaptiveThreshold = config["adaptive_threshold"].toDouble();
    }
    if (config.contains("window_size")) {
        m_config.windowSize = config["window_size"].toInt();
    }
    
    // 重新初始化滤波器状态矩阵
    reset();
    return true;
}

void KalmanFilter::setProcessNoise(double Q) 
{
    m_config.processNoise = Q;
    m_state.Q[0][0] = Q;
    m_state.Q[1][1] = Q * 0.1; // 速度噪声通常较小
}

void KalmanFilter::setMeasurementNoise(double R) 
{
    m_config.measurementNoise = R;
    m_state.R = R;
}

void KalmanFilter::setInitialCovariance(double P) 
{
    m_config.initialCovariance = P;
    m_state.P[0][0] = P;
    m_state.P[1][1] = P;
}

void KalmanFilter::setStateTransition(double dt) 
{
    m_config.timeInterval = dt;
    // 状态转移矩阵 F = [1, dt; 0, 1]
    m_state.F[0][0] = 1.0;
    m_state.F[0][1] = dt;
    m_state.F[1][0] = 0.0;
    m_state.F[1][1] = 1.0;
}

void KalmanFilter::reset() 
{
    // 重置状态
    m_state.x = 0.0;
    m_state.v = 0.0;
    m_state.initialized = false;
    m_state.updateCount = 0;
    m_state.lastGain = 0.0;
    
    // 初始化矩阵
    setProcessNoise(m_config.processNoise);
    setMeasurementNoise(m_config.measurementNoise);
    setInitialCovariance(m_config.initialCovariance);
    setStateTransition(m_config.timeInterval);
    
    // 观测矩阵 H = [1, 0]
    m_state.H[0][0] = 1.0;
    m_state.H[0][1] = 0.0;
    
    // 清空历史数据
    m_residualHistory.clear();
    m_gainHistory.clear();
}

QVariantMap KalmanFilter::getFilterState() const 
{
    QVariantMap state;
    state["position"] = m_state.x;
    state["velocity"] = m_state.v;
    state["covariance_position"] = m_state.P[0][0];
    state["covariance_velocity"] = m_state.P[1][1];
    state["last_gain"] = m_state.lastGain;
    state["update_count"] = m_state.updateCount;
    state["initialized"] = m_state.initialized;
    return state;
}

void KalmanFilter::initializeFilter(double initialValue) 
{
    m_state.x = initialValue;
    m_state.v = 0.0;
    m_state.initialized = true;
    m_state.updateCount = 1;
    
    qDebug() << "[KalmanFilter] Initialized with value:" << initialValue;
}

void KalmanFilter::predict() 
{
    // 状态预测: x_pred = F * x
    double x_pred = m_state.F[0][0] * m_state.x + m_state.F[0][1] * m_state.v;
    double v_pred = m_state.F[1][0] * m_state.x + m_state.F[1][1] * m_state.v;
    
    // 协方差预测: P_pred = F * P * F^T + Q
    double FP[2][2], FPFT[2][2];
    
    // FP = F * P
    FP[0][0] = m_state.F[0][0] * m_state.P[0][0] + m_state.F[0][1] * m_state.P[1][0];
    FP[0][1] = m_state.F[0][0] * m_state.P[0][1] + m_state.F[0][1] * m_state.P[1][1];
    FP[1][0] = m_state.F[1][0] * m_state.P[0][0] + m_state.F[1][1] * m_state.P[1][0];
    FP[1][1] = m_state.F[1][0] * m_state.P[0][1] + m_state.F[1][1] * m_state.P[1][1];
    
    // FPFT = FP * F^T
    FPFT[0][0] = FP[0][0] * m_state.F[0][0] + FP[0][1] * m_state.F[0][1];
    FPFT[0][1] = FP[0][0] * m_state.F[1][0] + FP[0][1] * m_state.F[1][1];
    FPFT[1][0] = FP[1][0] * m_state.F[0][0] + FP[1][1] * m_state.F[0][1];
    FPFT[1][1] = FP[1][0] * m_state.F[1][0] + FP[1][1] * m_state.F[1][1];
    
    // 更新状态
    m_state.x = x_pred;
    m_state.v = v_pred;
    
    // 更新协方差: P = FPFT + Q
    m_state.P[0][0] = FPFT[0][0] + m_state.Q[0][0];
    m_state.P[0][1] = FPFT[0][1];
    m_state.P[1][0] = FPFT[1][0];
    m_state.P[1][1] = FPFT[1][1] + m_state.Q[1][1];
}

double KalmanFilter::update(double measurement) 
{
    // 计算卡尔曼增益
    double S = m_state.H[0][0] * m_state.P[0][0] * m_state.H[0][0] + 
               m_state.H[0][1] * m_state.P[1][0] * m_state.H[0][0] + m_state.R;
    
    double K[2];
    K[0] = (m_state.P[0][0] * m_state.H[0][0] + m_state.P[0][1] * m_state.H[0][1]) / S;
    K[1] = (m_state.P[1][0] * m_state.H[0][0] + m_state.P[1][1] * m_state.H[0][1]) / S;
    
    // 计算创新(残差)
    double y = measurement - (m_state.H[0][0] * m_state.x + m_state.H[0][1] * m_state.v);
    
    // 状态更新
    m_state.x += K[0] * y;
    m_state.v += K[1] * y;
    
    // 协方差更新: P = (I - K*H) * P
    double KH[2][2];
    KH[0][0] = K[0] * m_state.H[0][0];
    KH[0][1] = K[0] * m_state.H[0][1];
    KH[1][0] = K[1] * m_state.H[0][0];
    KH[1][1] = K[1] * m_state.H[0][1];
    
    double I_KH[2][2];
    I_KH[0][0] = 1.0 - KH[0][0];
    I_KH[0][1] = -KH[0][1];
    I_KH[1][0] = -KH[1][0];
    I_KH[1][1] = 1.0 - KH[1][1];
    
    double newP[2][2];
    newP[0][0] = I_KH[0][0] * m_state.P[0][0] + I_KH[0][1] * m_state.P[1][0];
    newP[0][1] = I_KH[0][0] * m_state.P[0][1] + I_KH[0][1] * m_state.P[1][1];
    newP[1][0] = I_KH[1][0] * m_state.P[0][0] + I_KH[1][1] * m_state.P[1][0];
    newP[1][1] = I_KH[1][0] * m_state.P[0][1] + I_KH[1][1] * m_state.P[1][1];
    
    m_state.P[0][0] = newP[0][0];
    m_state.P[0][1] = newP[0][1];
    m_state.P[1][0] = newP[1][0];
    m_state.P[1][1] = newP[1][1];
    
    m_state.lastGain = K[0];
    m_state.updateCount++;
    
    return m_state.x;
}

void KalmanFilter::adaptNoiseParameters(double residual) 
{
    if (std::abs(residual) > m_config.adaptiveThreshold) {
        // 增加过程噪声以适应快速变化
        m_config.measurementNoise *= 1.1;
        m_state.R = m_config.measurementNoise;
        
        qDebug() << "[KalmanFilter] Adapted measurement noise to:" << m_config.measurementNoise;
    } else {
        // 减少测量噪声以提高精度
        m_config.measurementNoise *= 0.99;
        m_config.measurementNoise = std::max(m_config.measurementNoise, 0.01);
        m_state.R = m_config.measurementNoise;
    }
}

bool KalmanFilter::isOutlier(double measurement, double prediction) 
{
    double residual = std::abs(measurement - prediction);
    double threshold = 3.0 * std::sqrt(m_state.P[0][0] + m_state.R);
    return residual > threshold;
}

QVariantMap KalmanFilter::formatResult(double filteredValue, double originalValue, 
                                      double gain, double residual) 
{
    QVariantMap result;
    result["success"] = true;
    result["strategy_type"] = getStrategyType();
    result["strategy_name"] = getStrategyName();
    result["filtered_value"] = filteredValue;
    result["original_value"] = originalValue;
    result["improvement"] = std::abs(residual);
    result["kalman_gain"] = gain;
    result["residual"] = residual;
    result["update_count"] = m_state.updateCount;
    result["timestamp"] = QDateTime::currentDateTime();
    
    // 性能指标
    if (!m_residualHistory.empty()) {
        auto metrics = calculatePerformanceMetrics();
        result["performance"] = metrics;
    }
    
    return result;
}

void KalmanFilter::updateHistory(double residual, double gain) 
{
    m_residualHistory.push_back(residual);
    m_gainHistory.push_back(gain);
    
    // 维护窗口大小
    while (static_cast<int>(m_residualHistory.size()) > m_config.windowSize) {
        m_residualHistory.erase(m_residualHistory.begin());
    }
    while (static_cast<int>(m_gainHistory.size()) > m_config.windowSize) {
        m_gainHistory.erase(m_gainHistory.begin());
    }
}

QVariantMap KalmanFilter::calculatePerformanceMetrics() 
{
    QVariantMap metrics;
    
    if (m_residualHistory.empty()) {
        return metrics;
    }
    
    // 计算残差统计
    double sumResidual = 0.0, sumSquaredResidual = 0.0;
    for (double r : m_residualHistory) {
        sumResidual += r;
        sumSquaredResidual += r * r;
    }
    
    double meanResidual = sumResidual / m_residualHistory.size();
    double varianceResidual = sumSquaredResidual / m_residualHistory.size() - meanResidual * meanResidual;
    
    metrics["mean_residual"] = meanResidual;
    metrics["residual_variance"] = varianceResidual;
    metrics["residual_std"] = std::sqrt(varianceResidual);
    
    // 计算增益统计
    if (!m_gainHistory.empty()) {
        double sumGain = 0.0;
        for (double g : m_gainHistory) {
            sumGain += g;
        }
        metrics["mean_gain"] = sumGain / m_gainHistory.size();
    }
    
    return metrics;
}

// ===== ExtendedKalmanFilter Implementation =====

ExtendedKalmanFilter::ExtendedKalmanFilter(QObject* parent)
    : KalmanFilter(parent)
{
}

QVariantMap ExtendedKalmanFilter::executeStrategy(const QVariantMap& input) 
{
    // For now, delegate to base KalmanFilter
    // TODO: Implement actual Extended Kalman Filter algorithm
    return KalmanFilter::executeStrategy(input);
}

void ExtendedKalmanFilter::setNonlinearFunction(const QString& function) 
{
    m_nonlinearFunction = function;
}

void ExtendedKalmanFilter::calculateJacobian(double x[2], double jacobian[2][2]) 
{
    // Simplified Jacobian for linear case
    jacobian[0][0] = 1.0;
    jacobian[0][1] = 0.0;
    jacobian[1][0] = 0.0;
    jacobian[1][1] = 1.0;
}

void ExtendedKalmanFilter::nonlinearStateFunction(const double x[2], double result[2]) 
{
    if (m_nonlinearFunction == "linear") {
        result[0] = x[0];
        result[1] = x[1];
    } else {
        // TODO: Implement other nonlinear functions
        result[0] = x[0];
        result[1] = x[1];
    }
}

// ===== UnscentedKalmanFilter Implementation =====

UnscentedKalmanFilter::UnscentedKalmanFilter(QObject* parent)
    : KalmanFilter(parent)
{
}

QVariantMap UnscentedKalmanFilter::executeStrategy(const QVariantMap& input) 
{
    // For now, delegate to base KalmanFilter  
    // TODO: Implement actual Unscented Kalman Filter algorithm
    return KalmanFilter::executeStrategy(input);
}

void UnscentedKalmanFilter::setSigmaPointParams(double alpha, double beta, double kappa) 
{
    m_alpha = alpha;
    m_beta = beta;
    m_kappa = kappa;
}

void UnscentedKalmanFilter::generateSigmaPoints(const double mean[2], const double cov[2][2], 
                                              double sigmaPoints[5][2]) 
{
    // Simplified sigma point generation
    // TODO: Implement proper Unscented Transform sigma point generation
    for (int i = 0; i < 5; i++) {
        sigmaPoints[i][0] = mean[0];
        sigmaPoints[i][1] = mean[1];
    }
}

void UnscentedKalmanFilter::unscentedTransform(const double sigmaPoints[5][2], 
                                             double mean[2], double cov[2][2]) 
{
    // Simplified unscented transform
    // TODO: Implement proper Unscented Transform
    mean[0] = sigmaPoints[0][0];
    mean[1] = sigmaPoints[0][1];
    cov[0][0] = 1.0;
    cov[0][1] = 0.0;
    cov[1][0] = 0.0;
    cov[1][1] = 1.0;
}

} // namespace LA::Device::Strategy