/**
 * @file LaserRangingCapability.cpp
 * @brief 激光测距能力组件实现 - 四层架构第2层
 */

#include "LaserRangingCapability.h"
#include "LA/Device/Core/Device.h"
#include <QDebug>
#include <QDateTime>
#include <QThread>
#include <algorithm>
#include <numeric>
#include <cmath>

namespace LA::Device::Capability {

LaserRangingCapability::LaserRangingCapability(QObject* parent)
    : QObject(parent)
{
    // 初始化连续测量定时器
    m_continuousTimer = new QTimer(this);
    connect(m_continuousTimer, &QTimer::timeout, 
            this, &LaserRangingCapability::onContinuousMeasurementTimer);
}

QStringList LaserRangingCapability::getProvidedCommands() const {
    return {
        "single_measurement",      // 单次测距
        "continuous_measurement",  // 连续测距
        "stop_measurement",       // 停止测距
        "average_measurement",    // 平均测距
        "get_distance",          // 获取当前距离
        "multipoint_measurement" // 多点测距
    };
}

QVariantMap LaserRangingCapability::executeCapability(const QString& command, const QVariantMap& params, Driver::IDriver* driver) {
    if (!driver || !driver->isConnected()) {
        return handleDriverError("Driver not available or not connected");
    }

    m_currentDriver = driver;
    
    qDebug() << "[LaserRangingCapability] Executing command:" << command << "with params:" << params;

    if (command == "single_measurement") {
        return executeSingleMeasurement(params, driver);
    } else if (command == "continuous_measurement") {
        return startContinuousMeasurement(params, driver);
    } else if (command == "stop_measurement") {
        return stopContinuousMeasurement();
    } else if (command == "average_measurement") {
        return executeAverageMeasurement(params, driver);
    } else if (command == "get_distance") {
        return getCurrentDistance(driver);
    } else if (command == "multipoint_measurement") {
        return executeMultiPointMeasurement(params, driver);
    } else {
        return handleDriverError(QString("Unknown command: %1").arg(command));
    }
}

bool LaserRangingCapability::isAvailable() const {
    return m_currentDriver && m_currentDriver->isConnected();
}

void LaserRangingCapability::setRangingConfig(const QVariantMap& config) {
    m_config.mode = config.value("mode", m_config.mode).toString();
    m_config.averageSamples = config.value("average_samples", m_config.averageSamples).toInt();
    m_config.accuracyThreshold = config.value("accuracy_threshold", m_config.accuracyThreshold).toDouble();
    m_config.timeoutMs = config.value("timeout_ms", m_config.timeoutMs).toInt();
    m_config.continuousIntervalMs = config.value("continuous_interval_ms", m_config.continuousIntervalMs).toInt();
    m_config.minRange = config.value("min_range", m_config.minRange).toDouble();
    m_config.maxRange = config.value("max_range", m_config.maxRange).toDouble();
    m_config.enableFiltering = config.value("enable_filtering", m_config.enableFiltering).toBool();
    m_config.enableValidation = config.value("enable_validation", m_config.enableValidation).toBool();

    qDebug() << "[LaserRangingCapability] Configuration updated:" << config;
}

QVariantMap LaserRangingCapability::getCapabilityInfo() const {
    QVariantMap info;
    info["name"] = getCapabilityName();
    info["available"] = isAvailable();
    info["provided_commands"] = getProvidedCommands();
    
    QVariantMap currentConfig;
    currentConfig["mode"] = m_config.mode;
    currentConfig["average_samples"] = m_config.averageSamples;
    currentConfig["accuracy_threshold"] = m_config.accuracyThreshold;
    currentConfig["timeout_ms"] = m_config.timeoutMs;
    currentConfig["continuous_interval_ms"] = m_config.continuousIntervalMs;
    currentConfig["min_range"] = m_config.minRange;
    currentConfig["max_range"] = m_config.maxRange;
    currentConfig["enable_filtering"] = m_config.enableFiltering;
    currentConfig["enable_validation"] = m_config.enableValidation;
    
    info["configuration"] = currentConfig;
    info["measuring"] = m_measuring;
    info["continuous_measuring"] = m_continuousMeasuring;
    info["history_count"] = static_cast<int>(m_historyData.size());
    
    return info;
}

void LaserRangingCapability::setRangingMode(const QString& mode) {
    if (mode != m_config.mode) {
        m_config.mode = mode;
        qDebug() << "[LaserRangingCapability] Ranging mode changed to:" << mode;
        
        // 如果正在连续测量，重新配置定时器
        if (m_continuousMeasuring) {
            m_continuousTimer->setInterval(m_config.continuousIntervalMs);
        }
    }
}

QVariantList LaserRangingCapability::getHistoryData(int count) const {
    QVariantList history;
    
    int start = 0;
    if (count > 0 && count < static_cast<int>(m_historyData.size())) {
        start = static_cast<int>(m_historyData.size()) - count;
    }
    
    for (size_t i = start; i < m_historyData.size(); ++i) {
        const MeasurementData& data = m_historyData[i];
        QVariantMap item;
        item["distance"] = data.distance;
        item["accuracy"] = data.accuracy;
        item["timestamp"] = data.timestamp.toString();
        item["mode"] = data.mode;
        item["sample_count"] = data.sampleCount;
        item["raw_data"] = data.rawData;
        history.append(item);
    }
    
    return history;
}

void LaserRangingCapability::onContinuousMeasurementTimer() {
    if (!m_continuousMeasuring || !m_currentDriver) {
        return;
    }
    
    // 执行单次测量
    QVariantMap params;
    params["timeout"] = m_config.timeoutMs;
    
    auto result = executeSingleMeasurement(params, m_currentDriver);
    
    if (result["success"].toBool()) {
        emit continuousMeasurementData(result);
    } else {
        qWarning() << "[LaserRangingCapability] Continuous measurement failed:" << result["error"];
        // 错误超过阈值时停止连续测量
        // TODO: 添加错误计数和处理逻辑
    }
}

QVariantMap LaserRangingCapability::executeSingleMeasurement(const QVariantMap& params, Driver::IDriver* driver) {
    if (m_measuring) {
        return handleDriverError("Another measurement is in progress");
    }
    
    m_measuring = true;
    emit measurementStarted();
    
    QVariantMap commandParams;
    commandParams["timeout"] = params.value("timeout", m_config.timeoutMs);
    commandParams["precision_mode"] = params.value("precision_mode", "normal");
    
    // 发送开始测量命令
    auto startResult = driver->sendCommand("START_MEASURE", commandParams);
    if (!startResult["success"].toBool()) {
        m_measuring = false;
        emit measurementFailed(startResult["error"].toString());
        return startResult;
    }
    
    // 等待一段时间让设备准备
    QThread::msleep(50);
    
    // 获取距离值
    QVariantMap getParams;
    getParams["sample_count"] = params.value("sample_count", 1);
    
    auto distanceResult = driver->sendCommand("GET_DISTANCE", getParams);
    m_measuring = false;
    
    if (!distanceResult["success"].toBool()) {
        emit measurementFailed(distanceResult["error"].toString());
        return distanceResult;
    }
    
    double distance = distanceResult["distance"].toDouble();
    double accuracy = m_config.accuracyThreshold; // 默认精度
    
    // 验证测量结果
    if (m_config.enableValidation) {
        if (!validateMeasurement(distance, distanceResult)) {
            auto errorResult = handleDriverError("Measurement validation failed");
            emit measurementFailed(errorResult["error"].toString());
            return errorResult;
        }
    }
    
    // 创建测量数据
    MeasurementData measurementData;
    measurementData.distance = distance;
    measurementData.accuracy = accuracy;
    measurementData.timestamp = QDateTime::currentDateTime();
    measurementData.mode = "single";
    measurementData.sampleCount = 1;
    measurementData.rawData = distanceResult;
    
    // 添加到历史数据
    addHistoryData(measurementData);
    
    // 格式化结果
    auto result = formatMeasurementResult(distance, accuracy, "single", 1, distanceResult);
    emit measurementCompleted(result);
    
    return result;
}

QVariantMap LaserRangingCapability::startContinuousMeasurement(const QVariantMap& params, Driver::IDriver* driver) {
    if (m_continuousMeasuring) {
        return handleDriverError("Continuous measurement already running");
    }
    
    int interval = params.value("interval", m_config.continuousIntervalMs).toInt();
    if (interval < 100) {
        interval = 100; // 最小间隔100ms
    }
    
    m_continuousMeasuring = true;
    m_continuousTimer->setInterval(interval);
    m_continuousTimer->start();
    
    qDebug() << "[LaserRangingCapability] Started continuous measurement with interval:" << interval << "ms";
    
    QVariantMap result;
    result["success"] = true;
    result["mode"] = "continuous";
    result["interval_ms"] = interval;
    result["message"] = "Continuous measurement started";
    result["timestamp"] = QDateTime::currentDateTime().toString();
    
    return result;
}

QVariantMap LaserRangingCapability::stopContinuousMeasurement() {
    if (!m_continuousMeasuring) {
        return handleDriverError("Continuous measurement not running");
    }
    
    m_continuousMeasuring = false;
    m_continuousTimer->stop();
    
    qDebug() << "[LaserRangingCapability] Stopped continuous measurement";
    
    QVariantMap result;
    result["success"] = true;
    result["message"] = "Continuous measurement stopped";
    result["total_measurements"] = static_cast<int>(m_historyData.size());
    result["timestamp"] = QDateTime::currentDateTime().toString();
    
    return result;
}

QVariantMap LaserRangingCapability::executeAverageMeasurement(const QVariantMap& params, Driver::IDriver* driver) {
    int sampleCount = params.value("sample_count", m_config.averageSamples).toInt();
    if (sampleCount <= 0) {
        sampleCount = m_config.averageSamples;
    }
    
    qDebug() << "[LaserRangingCapability] Starting average measurement with" << sampleCount << "samples";
    
    std::vector<double> distances;
    distances.reserve(sampleCount);
    
    for (int i = 0; i < sampleCount; ++i) {
        QVariantMap singleParams;
        singleParams["timeout"] = m_config.timeoutMs;
        
        auto result = executeSingleMeasurement(singleParams, driver);
        if (!result["success"].toBool()) {
            return result;
        }
        
        distances.push_back(result["distance"].toDouble());
        
        // 样本间小延时
        if (i < sampleCount - 1) {
            QThread::msleep(20);
        }
    }
    
    // 数据滤波
    double filteredDistance;
    if (m_config.enableFiltering) {
        filteredDistance = filterDistances(distances);
    } else {
        filteredDistance = std::accumulate(distances.begin(), distances.end(), 0.0) / distances.size();
    }
    
    // 计算精度
    double accuracy = calculateAccuracy(distances);
    
    // 创建测量数据
    MeasurementData measurementData;
    measurementData.distance = filteredDistance;
    measurementData.accuracy = accuracy;
    measurementData.timestamp = QDateTime::currentDateTime();
    measurementData.mode = "average";
    measurementData.sampleCount = sampleCount;
    
    QVariantMap rawData;
    QVariantList rawDistances;
    for (double d : distances) {
        rawDistances.append(d);
    }
    rawData["raw_distances"] = rawDistances;
    rawData["filter_applied"] = m_config.enableFiltering;
    measurementData.rawData = rawData;
    
    addHistoryData(measurementData);
    
    auto result = formatMeasurementResult(filteredDistance, accuracy, "average", sampleCount, rawData);
    emit measurementCompleted(result);
    
    return result;
}

QVariantMap LaserRangingCapability::getCurrentDistance(Driver::IDriver* driver) {
    QVariantMap params;
    params["sample_count"] = 1;
    
    auto result = driver->sendCommand("GET_DISTANCE", params);
    if (!result["success"].toBool()) {
        return result;
    }
    
    double distance = result["distance"].toDouble();
    
    // 简单验证
    if (m_config.enableValidation) {
        if (distance < m_config.minRange || distance > m_config.maxRange) {
            return handleDriverError(QString("Distance %1mm out of range [%2, %3]")
                                   .arg(distance).arg(m_config.minRange).arg(m_config.maxRange));
        }
    }
    
    return formatMeasurementResult(distance, m_config.accuracyThreshold, "current", 1, result);
}

QVariantMap LaserRangingCapability::executeMultiPointMeasurement(const QVariantMap& params, Driver::IDriver* driver) {
    QVariantList points = params.value("points", QVariantList()).toList();
    if (points.isEmpty()) {
        return handleDriverError("No measurement points specified");
    }
    
    QVariantMap result;
    result["success"] = true;
    result["mode"] = "multipoint";
    result["total_points"] = points.size();
    
    QVariantList measurements;
    
    for (int i = 0; i < points.size(); ++i) {
        QVariantMap pointParams = points[i].toMap();
        
        qDebug() << "[LaserRangingCapability] Measuring point" << (i+1) << "of" << points.size();
        
        auto measurement = executeSingleMeasurement(pointParams, driver);
        
        QVariantMap pointResult;
        pointResult["point_index"] = i;
        pointResult["measurement"] = measurement;
        
        measurements.append(pointResult);
        
        // 点间延时
        if (i < points.size() - 1) {
            int delay = pointParams.value("delay", 500).toInt();
            QThread::msleep(delay);
        }
    }
    
    result["measurements"] = measurements;
    result["timestamp"] = QDateTime::currentDateTime().toString();
    
    return result;
}

bool LaserRangingCapability::validateMeasurement(double distance, const QVariantMap& rawData) {
    Q_UNUSED(rawData)
    
    // 范围检查
    if (distance < m_config.minRange || distance > m_config.maxRange) {
        qWarning() << "[LaserRangingCapability] Distance validation failed: out of range" << distance;
        return false;
    }
    
    // TODO: 添加更多验证逻辑
    // - 噪声检测
    // - 信号质量检查
    // - 历史数据一致性检查
    
    return true;
}

double LaserRangingCapability::filterDistances(const std::vector<double>& distances) {
    if (distances.empty()) {
        return 0.0;
    }
    
    if (distances.size() == 1) {
        return distances[0];
    }
    
    // 简单的中位数滤波
    std::vector<double> sortedDistances = distances;
    std::sort(sortedDistances.begin(), sortedDistances.end());
    
    size_t size = sortedDistances.size();
    if (size % 2 == 0) {
        return (sortedDistances[size/2-1] + sortedDistances[size/2]) / 2.0;
    } else {
        return sortedDistances[size/2];
    }
}

double LaserRangingCapability::calculateAccuracy(const std::vector<double>& distances) {
    if (distances.size() < 2) {
        return m_config.accuracyThreshold;
    }
    
    // 计算标准差作为精度指标
    double mean = std::accumulate(distances.begin(), distances.end(), 0.0) / distances.size();
    
    double variance = 0.0;
    for (double distance : distances) {
        variance += std::pow(distance - mean, 2);
    }
    variance /= distances.size();
    
    return std::sqrt(variance);
}

void LaserRangingCapability::addHistoryData(const MeasurementData& data) {
    m_historyData.push_back(data);
    
    // 保持历史数据大小限制
    if (m_historyData.size() > MAX_HISTORY_SIZE) {
        cleanupHistoryData();
    }
}

void LaserRangingCapability::cleanupHistoryData() {
    if (m_historyData.size() <= MAX_HISTORY_SIZE) {
        return;
    }
    
    // 删除最旧的数据，保留最新的 MAX_HISTORY_SIZE 条
    size_t removeCount = m_historyData.size() - MAX_HISTORY_SIZE;
    m_historyData.erase(m_historyData.begin(), m_historyData.begin() + removeCount);
    
    qDebug() << "[LaserRangingCapability] Cleaned up" << removeCount << "old history records";
}

QVariantMap LaserRangingCapability::formatMeasurementResult(double distance, double accuracy, 
                                                          const QString& mode, int sampleCount,
                                                          const QVariantMap& rawData) {
    QVariantMap result;
    result["success"] = true;
    result["distance"] = distance;
    result["accuracy"] = accuracy;
    result["unit"] = "mm";
    result["mode"] = mode;
    result["sample_count"] = sampleCount;
    result["timestamp"] = QDateTime::currentDateTime().toString();
    
    if (!rawData.isEmpty()) {
        result["raw_data"] = rawData;
    }
    
    return result;
}

QVariantMap LaserRangingCapability::handleDriverError(const QString& error) {
    QVariantMap result;
    result["success"] = false;
    result["error"] = error;
    result["capability"] = getCapabilityName();
    result["timestamp"] = QDateTime::currentDateTime().toString();
    
    qCritical() << "[LaserRangingCapability] Error:" << error;
    return result;
}

// RangingDataAnalyzer 实现

RangingDataAnalyzer::AnalysisResult RangingDataAnalyzer::analyzeDistances(const std::vector<double>& distances) {
    AnalysisResult result = {};
    
    if (distances.empty()) {
        return result;
    }
    
    // 计算基本统计量
    result.min = *std::min_element(distances.begin(), distances.end());
    result.max = *std::max_element(distances.begin(), distances.end());
    result.mean = std::accumulate(distances.begin(), distances.end(), 0.0) / distances.size();
    
    // 计算中位数
    std::vector<double> sorted = distances;
    std::sort(sorted.begin(), sorted.end());
    size_t size = sorted.size();
    if (size % 2 == 0) {
        result.median = (sorted[size/2-1] + sorted[size/2]) / 2.0;
    } else {
        result.median = sorted[size/2];
    }
    
    // 计算标准差
    if (distances.size() > 1) {
        double variance = 0.0;
        for (double distance : distances) {
            variance += std::pow(distance - result.mean, 2);
        }
        variance /= (distances.size() - 1); // 样本标准差
        result.stdDev = std::sqrt(variance);
    }
    
    // 检测异常值
    std::vector<int> outlierIndices = detectOutliers(distances, 2.0);
    result.outlierCount = static_cast<int>(outlierIndices.size());
    
    for (int index : outlierIndices) {
        result.outliers.push_back(distances[index]);
    }
    
    return result;
}

std::vector<int> RangingDataAnalyzer::detectOutliers(const std::vector<double>& distances, double threshold) {
    std::vector<int> outliers;
    
    if (distances.size() < 3) {
        return outliers; // 数据太少，无法检测异常值
    }
    
    // 计算平均值和标准差
    double mean = std::accumulate(distances.begin(), distances.end(), 0.0) / distances.size();
    
    double variance = 0.0;
    for (double distance : distances) {
        variance += std::pow(distance - mean, 2);
    }
    variance /= distances.size();
    double stdDev = std::sqrt(variance);
    
    // 使用Z-score方法检测异常值
    for (size_t i = 0; i < distances.size(); ++i) {
        double zScore = std::abs(distances[i] - mean) / stdDev;
        if (zScore > threshold) {
            outliers.push_back(static_cast<int>(i));
        }
    }
    
    return outliers;
}

double RangingDataAnalyzer::calculateStability(const std::vector<double>& distances) {
    if (distances.size() < 2) {
        return 100.0; // 单个数据点认为是100%稳定
    }
    
    double mean = std::accumulate(distances.begin(), distances.end(), 0.0) / distances.size();
    
    double variance = 0.0;
    for (double distance : distances) {
        variance += std::pow(distance - mean, 2);
    }
    variance /= distances.size();
    double stdDev = std::sqrt(variance);
    
    // 相对稳定性 = (1 - CV) * 100%，其中CV是变异系数
    double cv = (mean != 0.0) ? (stdDev / mean) : 1.0;
    double stability = (1.0 - std::min(cv, 1.0)) * 100.0;
    
    return std::max(0.0, stability);
}

} // namespace LA::Device::Capability