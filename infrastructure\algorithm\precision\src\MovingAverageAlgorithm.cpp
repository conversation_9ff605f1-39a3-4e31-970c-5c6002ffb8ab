#include "LA/Algorithm/Precision/Precision.h"
#include <QQueue>
#include <QDebug>

namespace LA {
namespace Algorithm {
namespace Precision {

/**
 * @brief MovingAverageAlgorithm实现  
 * 移动平均精度控制算法
 */
class MovingAverageAlgorithm : public IPrecisionAlgorithm {
private:
    QQueue<double> m_dataBuffer;
    int m_windowSize;
    double m_threshold;
    
public:
    MovingAverageAlgorithm(int windowSize = 5, double threshold = 0.01) 
        : m_windowSize(windowSize), m_threshold(threshold) {
        if (m_windowSize <= 0) {
            m_windowSize = 5;
        }
        if (m_threshold <= 0) {
            m_threshold = 0.01;
        }
    }
    
    ~MovingAverageAlgorithm() override = default;
    
    PrecisionResult process(double value) override {
        PrecisionResult result;
        result.originalValue = value;
        result.success = true;
        
        // 添加新值到缓冲区
        m_dataBuffer.enqueue(value);
        
        // 保持窗口大小
        if (m_dataBuffer.size() > m_windowSize) {
            m_dataBuffer.dequeue();
        }
        
        // 计算移动平均
        double sum = 0;
        for (double val : m_dataBuffer) {
            sum += val;
        }
        result.processedValue = sum / m_dataBuffer.size();
        
        // 计算方差来评估精度
        double variance = 0;
        for (double val : m_dataBuffer) {
            variance += (val - result.processedValue) * (val - result.processedValue);
        }
        variance /= m_dataBuffer.size();
        result.confidence = 1.0 / (1.0 + variance);
        
        // 判断是否达到精度要求
        result.meetsRequirement = (variance < m_threshold);
        
        result.algorithm = getName();
        result.parameters["windowSize"] = m_windowSize;
        result.parameters["threshold"] = m_threshold;
        result.parameters["variance"] = variance;
        result.parameters["bufferSize"] = m_dataBuffer.size();
        
        return result;
    }
    
    void reset() override {
        m_dataBuffer.clear();
    }
    
    bool configure(const QVariantMap& config) override {
        if (config.contains("windowSize")) {
            int newWindowSize = config["windowSize"].toInt();
            if (newWindowSize > 0) {
                m_windowSize = newWindowSize;
                // 调整缓冲区大小
                while (m_dataBuffer.size() > m_windowSize) {
                    m_dataBuffer.dequeue();
                }
            }
        }
        
        if (config.contains("threshold")) {
            double newThreshold = config["threshold"].toDouble();
            if (newThreshold > 0) {
                m_threshold = newThreshold;
            }
        }
        
        return true;
    }
    
    QVariantMap getConfiguration() const override {
        QVariantMap config;
        config["windowSize"] = m_windowSize;
        config["threshold"] = m_threshold;
        config["bufferSize"] = m_dataBuffer.size();
        return config;
    }
    
    QString getName() const override {
        return "MovingAverage";
    }
    
    QString getDescription() const override {
        return QString("移动平均精度控制算法 (窗口大小: %1, 阈值: %2)")
               .arg(m_windowSize).arg(m_threshold);
    }
    
    double getPrecision() const override {
        if (m_dataBuffer.isEmpty()) {
            return 0.0;
        }
        
        // 计算当前缓冲区的标准差作为精度指标
        double mean = 0;
        for (double val : m_dataBuffer) {
            mean += val;
        }
        mean /= m_dataBuffer.size();
        
        double variance = 0;
        for (double val : m_dataBuffer) {
            variance += (val - mean) * (val - mean);
        }
        variance /= m_dataBuffer.size();
        
        return qSqrt(variance);
    }
};

} // namespace Precision
} // namespace Algorithm
} // namespace LA