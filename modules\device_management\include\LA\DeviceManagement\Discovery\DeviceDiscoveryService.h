#pragma once

#include "../Core/DeviceManagementTypes.h"
#include "../Matching/IDeviceIdentifier.h"
#include "../Matching/IDeviceProber.h"
#include "../Matching/IPortScanner.h"
#include "../Core/SimpleTypes.h"  // 简化类型包含
#include <QDateTime>
#include <QList>
#include <QObject>
#include <QStringList>
#include <QTimer>
#include <QVariantMap>
#include <memory>

namespace LA {
namespace DeviceManagement {
namespace Discovery {

// 使用简化类型 - 通过DeviceManagementTypes.h间接引用
using LA::DeviceManagement::Core::DeviceInfo;
using LA::DeviceManagement::Core::PortInfo;
using LA::DeviceManagement::Core::PortType;

// 前向声明 - 避免命名空间污染，使用完全限定名称
namespace Matching {
class IPortScanner;
class IDeviceProber;
class IDeviceIdentifier;
}  // namespace Matching

/**
 * @brief 设备发现结果
 */
struct DeviceDiscoveryResult {
    QString     deviceId;
    QString     deviceName;
    QString     deviceType;
    QString     manufacturer;
    QString     model;
    QString     version;
    QStringList requiredPortTypes;  // 设备需要的端口类型
    QVariantMap properties;
    QDateTime   discoveredTime;
};

/**
 * @brief 设备发现服务 - 协调层单一职责
 *
 * 🎯 协调层职责：组合基础设施服务完成业务目标
 * ✅ 负责: 发现流程协调、业务规则应用、结果整合
 * ❌ 不涉及: 具体端口扫描、底层设备探测、通信实现
 *
 * 🔧 依赖倒置实现：
 * - 依赖Foundation::Core抽象接口
 * - 通过依赖注入使用Communication服务
 * - 业务逻辑独立于基础设施实现
 */
class DeviceDiscoveryService : public QObject {
    Q_OBJECT

  public:
    explicit DeviceDiscoveryService(QObject *parent = nullptr);
    virtual ~DeviceDiscoveryService() = default;

    // ====== 依赖注入 - Linus式组合 ======
    void setPortScanner(std::shared_ptr<Matching::IPortScanner> portScanner);
    void setDeviceProber(std::shared_ptr<Matching::IDeviceProber> deviceProber);
    void setDeviceIdentifier(std::shared_ptr<Matching::IDeviceIdentifier> deviceIdentifier);

    // ====== 设备发现协调 - 单一职责 ======
    QList<DeviceDiscoveryResult> discoverDevices();
    QList<DeviceDiscoveryResult> discoverDevicesByType(const QString &deviceType);

    // ====== 设备类型识别（业务逻辑） ======
    QString     identifyDeviceType(const QVariantMap &deviceInfo);
    QStringList getSupportedPortTypes(const QString &deviceType);

    // ====== 发现配置 ======
    void setDiscoveryTimeout(int timeoutMs);
    void setDiscoveryFilters(const QStringList &filters);

    // ====== 状态查询 ======
    bool isDiscovering() const;
    int  getDiscoveredDeviceCount() const;

  signals:
    void deviceDiscovered(const DeviceDiscoveryResult &result);
    void discoveryFinished();
    void discoveryError(const QString &error);

  private slots:
    void onDiscoveryTimeout();

  private:
    // ====== Linus式发现协调 - 不包含具体扫描逻辑 ======
    QList<DeviceDiscoveryResult> discoverDevicesFromPorts();
    DeviceDiscoveryResult        createDiscoveryResult(const PortInfo &portInfo, const DeviceInfo &deviceInfo);

    // ====== 设备类型识别（保留业务逻辑） ======
    QString identifyDeviceFromPortType(PortType portType);
    QString identifySerialDevice(const QVariantMap &portInfo);
    QString identifyNetworkDevice(const QVariantMap &networkInfo);

    // ====== 依赖注入的最小模块 ======
    std::shared_ptr<Matching::IPortScanner>      m_portScanner;
    std::shared_ptr<Matching::IDeviceProber>     m_deviceProber;
    std::shared_ptr<Matching::IDeviceIdentifier> m_deviceIdentifier;

    // ====== 内部状态 ======
    bool                         m_isDiscovering;
    int                          m_discoveryTimeout;
    QStringList                  m_discoveryFilters;
    QList<DeviceDiscoveryResult> m_discoveredDevices;
    QTimer *                     m_discoveryTimer;
};

}  // namespace Discovery
}  // namespace DeviceManagement
}  // namespace LA
