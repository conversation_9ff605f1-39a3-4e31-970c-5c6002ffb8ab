#include <LA/Communication/Protocol/IProtocol.h>
#include <QDebug>
#include <QMutexLocker>
#include <QDateTime>
#include <QDataStream>
#include <QBuffer>

namespace LA {
namespace Communication {
namespace Protocol {

/**
 * @brief 简单二进制协议实现
 * 
 * 格式：[Header:2] [Length:2] [Command:1] [Data:Length-3] [Checksum:2]
 * Header: 0xAA55
 * Length: 整个帧长度（不包括Header和Length字段）
 * Command: 命令字节
 * Data: 数据部分（可选）
 * Checksum: CRC16校验
 */
class BinaryProtocol : public IProtocol {
    Q_OBJECT

public:
    explicit BinaryProtocol(QObject* parent = nullptr);
    virtual ~BinaryProtocol() = default;

    // === 协议信息 ===
    ProtocolType type() const override;
    QString name() const override;

    // === 核心编解码 ===
    QByteArray encode(const QVariantMap& data) override;
    QVariantMap decode(const QByteArray& frame) override;
    bool validateFrame(const QByteArray& frame) override;
    bool isCompleteFrame(const QByteArray& data) override;

    // === 状态查询 ===
    QString errorString() const override;
    DeviceStatistics getStatistics() const override;
    void resetStatistics() override;

private:
    static const quint16 FRAME_HEADER = 0xAA55;
    static const int MIN_FRAME_SIZE = 7; // Header(2) + Length(2) + Command(1) + Checksum(2)
    
    mutable QMutex m_mutex;
    DeviceStatistics m_statistics;
    QString m_lastError;
    
    quint16 calculateCRC16(const QByteArray& data) const;
    bool verifyCRC16(const QByteArray& frame) const;
    void updateStatistics(bool success);
};

BinaryProtocol::BinaryProtocol(QObject* parent)
    : IProtocol(parent)
{
    resetStatistics();
}

// === 协议信息 ===

ProtocolType BinaryProtocol::type() const {
    return ProtocolType::Binary;
}

QString BinaryProtocol::name() const {
    return "Simple Binary Protocol";
}

// === 核心编解码 ===

QByteArray BinaryProtocol::encode(const QVariantMap& data) {
    QMutexLocker locker(&m_mutex);
    
    m_lastError.clear();
    
    // 检查必需的命令字段
    if (!data.contains("command")) {
        m_lastError = "Missing 'command' field";
        updateStatistics(false);
        return QByteArray();
    }
    
    bool ok;
    quint8 command = data["command"].toUInt(&ok);
    if (!ok) {
        m_lastError = "Invalid command value";
        updateStatistics(false);
        return QByteArray();
    }
    
    // 构建数据部分
    QByteArray payload;
    if (data.contains("data")) {
        QByteArray rawData = data["data"].toByteArray();
        payload.append(rawData);
    }
    
    // 添加其他参数（作为键值对）
    for (auto it = data.begin(); it != data.end(); ++it) {
        if (it.key() == "command" || it.key() == "data") continue;
        
        QString param = QString("%1=%2;").arg(it.key()).arg(it.value().toString());
        payload.append(param.toUtf8());
    }
    
    // 计算总长度（Command + Data + Checksum）
    quint16 length = 1 + payload.size() + 2;
    
    // 构建帧
    QByteArray frame;
    QDataStream stream(&frame, QIODevice::WriteOnly);
    stream.setByteOrder(QDataStream::BigEndian);
    
    // Header
    stream << FRAME_HEADER;
    
    // Length
    stream << length;
    
    // Command
    stream << command;
    
    // Data
    if (!payload.isEmpty()) {
        frame.append(payload);
    }
    
    // 计算并添加CRC16（不包括Header和自身）
    QByteArray dataForCRC = frame.mid(4); // 跳过Header和Length
    quint16 crc = calculateCRC16(dataForCRC);
    stream << crc;
    
    updateStatistics(true);
    return frame;
}

QVariantMap BinaryProtocol::decode(const QByteArray& frame) {
    QMutexLocker locker(&m_mutex);
    
    m_lastError.clear();
    QVariantMap result;
    
    // 验证帧格式
    if (!validateFrame(frame)) {
        updateStatistics(false);
        return result;
    }
    
    QDataStream stream(frame);
    stream.setByteOrder(QDataStream::BigEndian);
    
    // 读取Header（已验证）
    quint16 header;
    stream >> header;
    
    // 读取Length
    quint16 length;
    stream >> length;
    
    // 读取Command
    quint8 command;
    stream >> command;
    result["command"] = command;
    
    // 读取数据部分（如果有）
    int dataLength = length - 3; // 减去Command(1) + Checksum(2)
    if (dataLength > 0) {
        QByteArray payload(dataLength, 0);
        stream.readRawData(payload.data(), dataLength);
        
        // 尝试解析为参数或原始数据
        QString payloadStr = QString::fromUtf8(payload);
        if (payloadStr.contains("=") && payloadStr.contains(";")) {
            // 解析为参数
            QStringList params = payloadStr.split(";", Qt::SkipEmptyParts);
            for (const QString& param : params) {
                QStringList keyValue = param.split("=");
                if (keyValue.size() == 2) {
                    result[keyValue[0]] = keyValue[1];
                }
            }
        } else {
            // 作为原始数据
            result["data"] = payload;
        }
    }
    
    updateStatistics(true);
    emit frameDecoded(result);
    
    return result;
}

bool BinaryProtocol::validateFrame(const QByteArray& frame) {
    if (frame.size() < MIN_FRAME_SIZE) {
        m_lastError = QString("Frame too short: %1 bytes").arg(frame.size());
        return false;
    }
    
    QDataStream stream(frame);
    stream.setByteOrder(QDataStream::BigEndian);
    
    // 检查Header
    quint16 header;
    stream >> header;
    if (header != FRAME_HEADER) {
        m_lastError = QString("Invalid header: 0x%1").arg(header, 4, 16, QChar('0'));
        return false;
    }
    
    // 检查Length
    quint16 length;
    stream >> length;
    if (frame.size() != length + 4) { // +4 for Header and Length fields
        m_lastError = QString("Frame size mismatch: expected %1, got %2")
                      .arg(length + 4).arg(frame.size());
        return false;
    }
    
    // 验证CRC
    if (!verifyCRC16(frame)) {
        m_lastError = "CRC verification failed";
        return false;
    }
    
    return true;
}

bool BinaryProtocol::isCompleteFrame(const QByteArray& data) {
    if (data.size() < 4) return false; // 至少需要Header和Length
    
    QDataStream stream(data);
    stream.setByteOrder(QDataStream::BigEndian);
    
    quint16 header;
    stream >> header;
    if (header != FRAME_HEADER) return false;
    
    quint16 length;
    stream >> length;
    
    return data.size() >= (length + 4);
}

// === 状态查询 ===

QString BinaryProtocol::errorString() const {
    QMutexLocker locker(&m_mutex);
    return m_lastError;
}

DeviceStatistics BinaryProtocol::getStatistics() const {
    QMutexLocker locker(&m_mutex);
    return m_statistics;
}

void BinaryProtocol::resetStatistics() {
    QMutexLocker locker(&m_mutex);
    m_statistics = DeviceStatistics();
    m_statistics.lastActivity = QDateTime::currentMSecsSinceEpoch();
    m_lastError.clear();
}

// === 私有辅助方法 ===

quint16 BinaryProtocol::calculateCRC16(const QByteArray& data) const {
    // 简单的CRC16算法（CRC-16-CCITT）
    quint16 crc = 0xFFFF;
    
    for (char byte : data) {
        crc ^= (quint8)byte << 8;
        for (int i = 0; i < 8; i++) {
            if (crc & 0x8000) {
                crc = (crc << 1) ^ 0x1021;
            } else {
                crc <<= 1;
            }
        }
    }
    
    return crc;
}

bool BinaryProtocol::verifyCRC16(const QByteArray& frame) const {
    if (frame.size() < MIN_FRAME_SIZE) return false;
    
    // 提取数据部分（不包括Header、Length和最后的CRC）
    QByteArray dataForCRC = frame.mid(4, frame.size() - 6);
    
    // 计算期望的CRC
    quint16 expectedCRC = calculateCRC16(dataForCRC);
    
    // 提取帧中的CRC
    QDataStream stream(frame.right(2));
    stream.setByteOrder(QDataStream::BigEndian);
    quint16 frameCRC;
    stream >> frameCRC;
    
    return expectedCRC == frameCRC;
}

void BinaryProtocol::updateStatistics(bool success) {
    qint64 currentTime = QDateTime::currentMSecsSinceEpoch();
    m_statistics.lastActivity = currentTime;
    
    if (success) {
        m_statistics.packetsReceived++;
    } else {
        m_statistics.errorsCount++;
        emit errorOccurred(m_lastError);
    }
}

} // namespace Protocol
} // namespace Communication
} // namespace LA

#include "BinaryProtocol.moc"