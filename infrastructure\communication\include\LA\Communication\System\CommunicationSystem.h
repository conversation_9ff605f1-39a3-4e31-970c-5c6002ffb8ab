#pragma once

/**
 * @file CommunicationSystem.h
 * @brief Linus式通信系统 - 最高层系统集成
 * 
 * 遵循Linus Torvalds的设计哲学：
 * - "Good programmers worry about data structures" - 基于稳定的Layer 1-3基础
 * - 最小接口原则 - 只提供必需的系统级功能
 * - 组合设计 - 组合所有底层组件
 * - Layer 4接口 - 为应用层提供完整的通信解决方案
 */

#include <QObject>
#include <QString>
#include <QStringList>
#include <memory>
#include "support/foundation/core/CommonTypes.h"

// 前向声明所有Layer组件
namespace LA {
namespace Communication {
namespace Manager { class ICommunicationManager; }
namespace PortManagement { class IPortManager; }
namespace Protocol { class IProtocolFactory; }
namespace Connection { class IConnection; }
namespace Command { class ICommandHandlerFactory; }
namespace Session { class ICommunicationSessionFactory; }
}
}

namespace LA {
namespace Communication {
namespace System {

// 使用Foundation层的标准类型
using namespace LA::Foundation::Core;

/**
 * @brief Linus式通信系统
 * 
 * 系统级设计原则:
 * - 组合优于继承: 组合所有底层组件
 * - 单一入口点: 为应用层提供统一接口
 * - 最小复杂性: 只暴露必要的系统功能
 * - 完整抽象: 隐藏底层实现细节
 */
class CommunicationSystem : public QObject {
    Q_OBJECT

public:
    explicit CommunicationSystem(QObject* parent = nullptr);
    virtual ~CommunicationSystem();

    // === 系统生命周期 ===
    
    /**
     * @brief 初始化通信系统
     * @param config 系统配置参数
     * @return 操作结果
     */
    SimpleResult initialize(const ConfigParameters& config = {});
    
    /**
     * @brief 关闭通信系统
     * @return 操作结果
     */
    SimpleResult shutdown();
    
    /**
     * @brief 检查系统是否已初始化
     * @return 是否已初始化
     */
    bool isInitialized() const;

    // === 高级通信接口 ===
    
    /**
     * @brief 创建通信连接
     * @param connectionId 连接ID
     * @param portName 端口名称
     * @param protocolType 协议类型
     * @param config 连接配置
     * @return 操作结果
     */
    SimpleResult createConnection(const QString& connectionId,
                                const QString& portName,
                                ProtocolType protocolType,
                                const ConfigParameters& config = {});
    
    /**
     * @brief 发送数据
     * @param connectionId 连接ID
     * @param data 要发送的数据
     * @return 发送的字节数，-1表示失败
     */
    qint64 sendData(const QString& connectionId, const QByteArray& data);
    
    /**
     * @brief 发送消息
     * @param connectionId 连接ID
     * @param message 消息内容
     * @return 操作结果
     */
    SimpleResult sendMessage(const QString& connectionId, const QVariantMap& message);
    
    /**
     * @brief 执行命令
     * @param connectionId 连接ID
     * @param command 命令内容
     * @return 命令执行结果
     */
    CommandResult executeCommand(const QString& connectionId, const QVariantMap& command);
    
    /**
     * @brief 关闭连接
     * @param connectionId 连接ID
     * @return 操作结果
     */
    SimpleResult closeConnection(const QString& connectionId);

    // === 系统管理接口 ===
    
    /**
     * @brief 获取可用端口列表
     * @return 端口信息列表
     */
    PortInfoList getAvailablePorts();
    
    /**
     * @brief 获取支持的协议类型
     * @return 协议类型列表
     */
    QStringList getSupportedProtocols();
    
    /**
     * @brief 获取活动连接列表
     * @return 连接ID列表
     */
    QStringList getActiveConnections();
    
    /**
     * @brief 获取连接状态
     * @param connectionId 连接ID
     * @return 连接状态
     */
    ConnectionStatus getConnectionStatus(const QString& connectionId);

    // === 监控和诊断 ===
    
    /**
     * @brief 获取系统统计信息
     * @return 统计信息
     */
    DeviceStatistics getSystemStatistics();
    
    /**
     * @brief 获取连接统计信息
     * @param connectionId 连接ID
     * @return 统计信息
     */
    DeviceStatistics getConnectionStatistics(const QString& connectionId);
    
    /**
     * @brief 执行系统健康检查
     * @return 操作结果
     */
    SimpleResult performSystemHealthCheck();
    
    /**
     * @brief 获取系统状态报告
     * @return 状态报告字符串
     */
    QString getSystemStatusReport();
    
    /**
     * @brief 获取最后错误信息
     * @return 错误描述
     */
    QString errorString() const;

    // === 配置管理 ===
    
    /**
     * @brief 设置系统配置
     * @param config 配置参数
     * @return 操作结果
     */
    SimpleResult setSystemConfig(const ConfigParameters& config);
    
    /**
     * @brief 获取系统配置
     * @return 配置参数
     */
    ConfigParameters getSystemConfig() const;
    
    /**
     * @brief 重置系统统计
     */
    void resetSystemStatistics();

signals:
    /**
     * @brief 系统状态变化信号
     * @param initialized 是否已初始化
     */
    void systemStatusChanged(bool initialized);
    
    /**
     * @brief 连接创建信号
     * @param connectionId 连接ID
     */
    void connectionCreated(const QString& connectionId);
    
    /**
     * @brief 连接状态变化信号
     * @param connectionId 连接ID
     * @param status 新状态
     */
    void connectionStatusChanged(const QString& connectionId, ConnectionStatus status);
    
    /**
     * @brief 数据接收信号
     * @param connectionId 连接ID
     * @param data 接收到的数据
     */
    void dataReceived(const QString& connectionId, const QByteArray& data);
    
    /**
     * @brief 消息接收信号
     * @param connectionId 连接ID
     * @param message 接收到的消息
     */
    void messageReceived(const QString& connectionId, const QVariantMap& message);
    
    /**
     * @brief 系统错误信号
     * @param error 错误描述
     */
    void systemError(const QString& error);

private:
    mutable QMutex m_mutex;
    
    // 系统状态
    bool m_initialized;
    ConfigParameters m_systemConfig;
    DeviceStatistics m_systemStats;
    QString m_lastError;
    
    // 核心组件 (Layer 1-3)
    std::shared_ptr<Manager::ICommunicationManager> m_communicationManager;
    std::shared_ptr<PortManagement::IPortManager> m_portManager;
    std::shared_ptr<Protocol::IProtocolFactory> m_protocolFactory;
    std::shared_ptr<Command::ICommandHandlerFactory> m_commandHandlerFactory;
    std::shared_ptr<Session::ICommunicationSessionFactory> m_sessionFactory;
    
    // 内部方法
    void initializeComponents();
    void shutdownComponents();
    void connectManagerSignals();
    void disconnectManagerSignals();
    void updateSystemStatistics();
    
private slots:
    void onConnectionStatusChanged(const QString& connectionId, ConnectionStatus status);
    void onManagerError(const QString& error);
};

/**
 * @brief 通信系统单例访问
 * 
 * 提供全局通信系统实例访问
 */
class CommunicationSystemInstance {
public:
    /**
     * @brief 获取通信系统实例
     * @return 系统实例引用
     */
    static CommunicationSystem& instance();
    
    /**
     * @brief 销毁系统实例
     */
    static void destroyInstance();

private:
    static CommunicationSystem* s_instance;
    static QMutex s_mutex;
};

} // namespace System
} // namespace Communication
} // namespace LA