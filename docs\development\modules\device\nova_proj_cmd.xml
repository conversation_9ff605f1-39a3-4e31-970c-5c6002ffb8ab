<?xml version="1.0" encoding="UTF-8"?>
<project_cmd>
    <nova-A1>
        <eBAUD_IO_eB19200>55 5a 81 05 01 04 8b </eBAUD_IO_eB19200>
        <eBAUD_IO_eB2400>55 5a 82 05 01 01 89 </eBAUD_IO_eB2400>
        <eBAUD_IO_eB4800>55 5a 85 05 01 02 8d </eBAUD_IO_eB4800>
        <eBAUD_IO_eB9600>55 5a 85 05 01 03 8e </eBAUD_IO_eB9600>
        <eCALIB1>55 5a 85 01 00 86 </eCALIB1>
        <eCALIB2>55 5a 85 02 00 87 </eCALIB2>
        <eCHIP_ID>55 5a 85 0d 00 92 </eCHIP_ID>
        <eDATA_IO_eCURVE_SHOW_OUTPUT_MODE>55 5a 85 08 01 01 8f </eDATA_IO_eCURVE_SHOW_OUTPUT_MODE>
        <eDATA_IO_eDISTANCE_OUTPUT_MODE>55 5a 85 08 01 02 90 </eDATA_IO_eDISTANCE_OUTPUT_MODE>
        <eDATA_IO_eNOT_OUTPUT_MODE>55 5a 85 08 01 00 8e </eDATA_IO_eNOT_OUTPUT_MODE>
        <eDDS_LOG>55 5a 85 0e 00 93 </eDDS_LOG>
        <eLED_IO_kBREATH>55 5a 85 06 01 03 8f </eLED_IO_kBREATH>
        <eLED_IO_kCLOSE>55 5a 85 06 01 01 8d </eLED_IO_kCLOSE>
        <eLED_IO_kFLICKER>55 5a 85 06 01 04 90 </eLED_IO_kFLICKER>
        <eLED_IO_kOPEN>55 5a 85 06 01 02 8e </eLED_IO_kOPEN>
        <eLOG_IO>55 5a 85 09 01 01 90 </eLOG_IO>
        <eMODE_MODIFY_kMEASURE_MODE>55 5a 85 07 01 01 8e </eMODE_MODIFY_kMEASURE_MODE>
        <eMODE_MODIFY_kSTANDBY_MODE>55 5a 85 07 01 04 91 </eMODE_MODIFY_kSTANDBY_MODE>
        <eMODE_MODIFY_kWORK_MODE>55 5a 85 07 01 03 90 </eMODE_MODIFY_kWORK_MODE>
        <eQUERY_DIST>55 5a 85 0a 01 00 90 </eQUERY_DIST>
        <eRARAM_READ>55 5a 85 0c 00 91 </eRARAM_READ>
        <eSET_RANGE>55 5a 85 04 01 32 bc </eSET_RANGE>
        <eVERSION>55 5a 85 0b 00 90 </eVERSION>
        <eDATA_MODE_eCURVE_SHOW_OUTPUT_MODE>55 5a 85 08 01 01 8f </eDATA_MODE_eCURVE_SHOW_OUTPUT_MODE>
        <eDATA_MODE_eDISTANCE_OUTPUT_MODE>55 5a 85 08 01 02 90 </eDATA_MODE_eDISTANCE_OUTPUT_MODE>
        <eDATA_MODE_eNOT_OUTPUT_MODE>55 5a 85 08 01 00 8e </eDATA_MODE_eNOT_OUTPUT_MODE>
        <eMODE_MODIFY_KOPTIMIZATION_MODE>55 5a 85 07 01 05 92 </eMODE_MODIFY_KOPTIMIZATION_MODE>
        <eMODE_MODIFY_KTEST_MODE>55 5a 85 07 01 00 8d </eMODE_MODIFY_KTEST_MODE>
        <eMODE_MODIFY_kCALIB_MODE>55 5a 85 07 01 02 8f </eMODE_MODIFY_kCALIB_MODE>
        <eMODE_MODIFY_kTRIGGER_MODE>55 5a 85 07 01 03 90 </eMODE_MODIFY_kTRIGGER_MODE>
        <ePROC_MODE_eCALIB_MODE>55 5a 85 07 01 02 8f </ePROC_MODE_eCALIB_MODE>
        <ePROC_MODE_eMEASURE_MODE>55 5a 85 07 01 01 8e </ePROC_MODE_eMEASURE_MODE>
        <ePROC_MODE_eOPTIMIZATION_MODE>55 5a 85 07 01 05 92 </ePROC_MODE_eOPTIMIZATION_MODE>
        <ePROC_MODE_eSTANDBY_MODE>55 5a 85 07 01 04 91 </ePROC_MODE_eSTANDBY_MODE>
        <ePROC_MODE_eTEST_MODE>55 5a 85 07 01 00 8d </ePROC_MODE_eTEST_MODE>
        <ePROC_MODE_eTRIGGER_MODE>55 5a 85 07 01 03 90 </ePROC_MODE_eTRIGGER_MODE>
    </nova-A1>
    <nova-A1B>
        <eCALIB1>55 5a 85 03 02 00 8a </eCALIB1>
        <eCALIB2>55 5a 85 03 03 00 8b </eCALIB2>
        <eCHIP_ID>55 5a 85 03 0d 00 95 </eCHIP_ID>
        <eDATA_IO>55 5a 85 03 08 01 91 </eDATA_IO>
        <eDDS_LOG>55 5a 85 03 0e 00 96 </eDDS_LOG>
        <eLOG_IO>55 5a 85 03 09 01 92 </eLOG_IO>
        <eMODE_MODIFY_kMEASURE_MODE>55 5a 85 03 07 01 90 </eMODE_MODIFY_kMEASURE_MODE>
        <eMODE_MODIFY_kSTANDBY_MODE>55 5a 85 03 07 04 93 </eMODE_MODIFY_kSTANDBY_MODE>
        <eMODE_MODIFY_kWORK_MODE>55 5a 85 03 07 03 92 </eMODE_MODIFY_kWORK_MODE>
        <eQUERY_DIST>55 5a 85 03 00 00 88 </eQUERY_DIST>
        <eRARAM_READ>55 5a 85 03 0c 00 94 </eRARAM_READ>
        <eSET_RANGE>55 5a 85 03 01 00 89 </eSET_RANGE>
        <eVERSION>55 5a 85 03 0b 00 93 </eVERSION>
        <eDATA_MODE>55 5a 85 03 08 01 91 </eDATA_MODE>
        <eEXPAND_DIST>55 5a 85 03 0a 00 92 </eEXPAND_DIST>
    </nova-A1B>
    <nova-A1D>
        <eCALIB1>55 5a 85 02 01 88 </eCALIB1>
        <eCALIB2>55 5a 85 02 02 89 </eCALIB2>
        <eCHIP_ID>55 5a 85 02 0d 94 </eCHIP_ID>
        <eDATA_MODE_eCURVE_SHOW_OUTPUT_MODE>55 5a 85 03 08 01 91 </eDATA_MODE_eCURVE_SHOW_OUTPUT_MODE>
        <eDATA_MODE_eDISTANCE_OUTPUT_MODE>55 5a 85 03 08 02 92 </eDATA_MODE_eDISTANCE_OUTPUT_MODE>
        <eDATA_MODE_eNOT_OUTPUT_MODE>55 5a 85 03 08 00 90 </eDATA_MODE_eNOT_OUTPUT_MODE>
        <eDDS_LOG>55 5a 85 02 0e 95 </eDDS_LOG>
        <eLED_IO_kBREATH>55 5a 85 03 06 03 91 </eLED_IO_kBREATH>
        <eLED_IO_kCLOSE>55 5a 85 03 06 01 8f </eLED_IO_kCLOSE>
        <eLED_IO_kFLICKER>55 5a 85 03 06 04 92 </eLED_IO_kFLICKER>
        <eLED_IO_kOPEN>55 5a 85 03 06 02 90 </eLED_IO_kOPEN>
        <eLOG_IO>55 5a 85 03 09 01 92 </eLOG_IO>
        <eMODE_MODIFY_kCALIB_MODE>55 5a 85 03 07 02 91 </eMODE_MODIFY_kCALIB_MODE>
        <eMODE_MODIFY_kMEASURE_MODE>55 5a 85 03 07 01 90 </eMODE_MODIFY_kMEASURE_MODE>
        <eMODE_MODIFY_kSTANDBY_MODE>55 5a 85 03 07 04 93 </eMODE_MODIFY_kSTANDBY_MODE>
        <eMODE_MODIFY_kWORK_MODE>55 5a 85 03 07 03 92 </eMODE_MODIFY_kWORK_MODE>
        <eQUERY_DIST>55 5a 85 03 0a 00 92 </eQUERY_DIST>
        <eRARAM_READ>55 5a 85 02 0c 93 </eRARAM_READ>
        <eSELF_ADAPT>55 5a 85 02 05 8c </eSELF_ADAPT>
        <eVERSION>55 5a 85 02 0b 92 </eVERSION>
        <eFUNC_CTRL>32 15 ea 08 01 00 00 00 00 00 00 f6 34 </eFUNC_CTRL>
        <eRARAM_RW>32 15 ea 0c 00 00 00 00 00 00 00 f3 34 </eRARAM_RW>
        <eCUSTOM_VERSION>55 5a 85 02 fa 81 </eCUSTOM_VERSION>
        <eMODE_MODIFY_kTRIGGER_MODE>55 5a 85 03 07 03 92 </eMODE_MODIFY_kTRIGGER_MODE>
    </nova-A1D>
    <nova-A1H>
        <eCALIB1>55 5a 03 01 00 00 </eCALIB1>
        <eCALIB2>55 5a 03 02 00 00 </eCALIB2>
        <eCHIP_ID>55 5a 03 0d 00 00 </eCHIP_ID>
        <eDATA_MODE_eCURVE_SHOW_OUTPUT_MODE>55 5a 03 08 01 00 </eDATA_MODE_eCURVE_SHOW_OUTPUT_MODE>
        <eDATA_MODE_eDISTANCE_OUTPUT_MODE>55 5a 03 08 02 00 </eDATA_MODE_eDISTANCE_OUTPUT_MODE>
        <eDATA_MODE_eNOT_OUTPUT_MODE>55 5a 03 08 00 00 </eDATA_MODE_eNOT_OUTPUT_MODE>
        <eDDS_LOG>55 5a 03 0e 00 00 </eDDS_LOG>
        <eLOG_IO>55 5a 03 09 01 00 </eLOG_IO>
        <eMODIFY_PARAM>55 5a 0c 0a 00 01 00 00 00 01 00 01 00 01 00 </eMODIFY_PARAM>
        <eQUERY_DIST>55 5a 05 b5 00 2b 3c 00 </eQUERY_DIST>
        <eRARAM_READ>55 5a 03 0c 00 00 </eRARAM_READ>
        <eVERSION>55 5a 03 0b 00 00 </eVERSION>
        <eBAUD_IO_eB19200>55 5a 81 05 01 04 8b </eBAUD_IO_eB19200>
        <eBAUD_IO_eB2400>55 5a 81 05 01 01 88 </eBAUD_IO_eB2400>
        <eBAUD_IO_eB4800>55 5a 85 05 01 02 8d </eBAUD_IO_eB4800>
        <eBAUD_IO_eB9600>55 5a 85 05 01 03 8e </eBAUD_IO_eB9600>
        <eDATA_IO_eCURVE_SHOW_OUTPUT_MODE>55 5a 85 08 01 01 8f </eDATA_IO_eCURVE_SHOW_OUTPUT_MODE>
        <eDATA_IO_eDISTANCE_OUTPUT_MODE>55 5a 85 08 01 02 90 </eDATA_IO_eDISTANCE_OUTPUT_MODE>
        <eDATA_IO_eNOT_OUTPUT_MODE>55 5a 85 08 01 00 8e </eDATA_IO_eNOT_OUTPUT_MODE>
        <eLED_IO_kBREATH>55 5a 85 06 01 03 8f </eLED_IO_kBREATH>
        <eLED_IO_kCLOSE>55 5a 85 06 01 01 8d </eLED_IO_kCLOSE>
        <eLED_IO_kFLICKER>55 5a 85 06 01 04 90 </eLED_IO_kFLICKER>
        <eLED_IO_kOPEN>55 5a 85 06 01 02 8e </eLED_IO_kOPEN>
        <eMODE_MODIFY_kMEASURE_MODE>55 5a 85 07 01 01 8e </eMODE_MODIFY_kMEASURE_MODE>
        <eMODE_MODIFY_kSTANDBY_MODE>55 5a 85 07 01 04 91 </eMODE_MODIFY_kSTANDBY_MODE>
        <eMODE_MODIFY_kWORK_MODE>55 5a 85 07 01 03 90 </eMODE_MODIFY_kWORK_MODE>
        <eSET_RANGE>55 5a 85 04 01 32 bc </eSET_RANGE>
    </nova-A1H>
    <nova-A1L>
        <eCALIB1>55 5a 85 01 00 86 </eCALIB1>
        <eCALIB2>55 5a 85 02 00 87 </eCALIB2>
        <eCHIP_ID>55 5a 85 0d 00 92 </eCHIP_ID>
        <eCOM_MODE_eI2C>55 5a 85 09 01 02 91 </eCOM_MODE_eI2C>
        <eCOM_MODE_eUART>55 5a 85 09 01 01 90 </eCOM_MODE_eUART>
        <eDATA_IO_eCURVE_SHOW_OUTPUT_MODE>55 5a 85 08 01 01 8f </eDATA_IO_eCURVE_SHOW_OUTPUT_MODE>
        <eDATA_IO_eDISTANCE_OUTPUT_MODE>55 5a 85 08 01 02 90 </eDATA_IO_eDISTANCE_OUTPUT_MODE>
        <eDATA_IO_eNOT_OUTPUT_MODE>55 5a 85 08 01 00 8e </eDATA_IO_eNOT_OUTPUT_MODE>
        <eDDS_LOG>55 5a 85 0e 00 93 </eDDS_LOG>
        <eDEVICE_ADDR>55 5a 85 05 01 35 c0 </eDEVICE_ADDR>
        <eLED_IO_kBREATH>55 5a 85 06 01 03 8f </eLED_IO_kBREATH>
        <eLED_IO_kCLOSE>55 5a 85 06 01 01 8d </eLED_IO_kCLOSE>
        <eLED_IO_kFLICKER>55 5a 85 06 01 04 90 </eLED_IO_kFLICKER>
        <eLED_IO_kOPEN>55 5a 85 06 01 02 8e </eLED_IO_kOPEN>
        <eMODE_MODIFY_kMEASURE_MODE>55 5a 85 07 01 01 8e </eMODE_MODIFY_kMEASURE_MODE>
        <eMODE_MODIFY_kSTANDBY_MODE>55 5a 85 07 01 04 91 </eMODE_MODIFY_kSTANDBY_MODE>
        <eMODE_MODIFY_kWORK_MODE>55 5a 85 07 01 03 90 </eMODE_MODIFY_kWORK_MODE>
        <eQUERY_DIST>55 5a 85 0a 01 00 90 </eQUERY_DIST>
        <eRARAM_READ>55 5a 85 0c 00 91 </eRARAM_READ>
        <eSELF_ADAPT>55 5a 85 03 00 88 </eSELF_ADAPT>
        <eVERSION>55 5a 85 0b 00 90 </eVERSION>
        <eDATA_MODE_eCURVE_SHOW_OUTPUT_MODE>55 5a 85 08 01 01 8f </eDATA_MODE_eCURVE_SHOW_OUTPUT_MODE>
        <eDATA_MODE_eDISTANCE_OUTPUT_MODE>55 5a 85 08 01 02 90 </eDATA_MODE_eDISTANCE_OUTPUT_MODE>
        <eDATA_MODE_eNOT_OUTPUT_MODE>55 5a 85 08 01 00 8e </eDATA_MODE_eNOT_OUTPUT_MODE>
    </nova-A1L>
</project_cmd>
