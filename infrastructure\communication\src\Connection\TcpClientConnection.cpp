#include <LA/Communication/Connection/NetworkConnection.h>
#include <QDateTime>
#include <QDebug>
#include <QHostAddress>
#include <QMutexLocker>
#include <QRegExp>
#include <QTcpSocket>
#include <QThread>
#include <QTimer>
#include <QUuid>


namespace LA {
namespace Communication {
namespace Connection {

TcpClientConnection::TcpClientConnection(QObject *parent)
    : IConnection(parent), m_socket(nullptr), m_tcpSocket(nullptr), m_reconnectTimer(nullptr), 
      m_state(ConnectionState::Disconnected), m_stats(m_statistics), m_reconnectAttempts(0) {
    initializeTcpSocket();
}

TcpClientConnection::TcpClientConnection(const ConnectionConfig &config, QObject *parent)
    : IConnection(parent), m_socket(nullptr), m_tcpSocket(nullptr), m_reconnectTimer(nullptr), 
      m_state(ConnectionState::Disconnected), m_stats(m_statistics), m_reconnectAttempts(0) {
    initializeTcpSocket();
}

TcpClientConnection::~TcpClientConnection() {
    if (isConnected()) {
        close();
    }

    if (m_reconnectTimer) {
        m_reconnectTimer->stop();
        delete m_reconnectTimer;
    }

    if (m_tcpSocket) {
        delete m_tcpSocket;
    }
}

void TcpClientConnection::initializeTcpSocket() {
    m_tcpSocket = new QTcpSocket(this);
    m_socket = m_tcpSocket;  // 设置别名指针

    // 连接信号槽
    QObject::connect(m_tcpSocket, &QTcpSocket::connected, this, &TcpClientConnection::onSocketConnected);
    QObject::connect(m_tcpSocket, &QTcpSocket::disconnected, this, &TcpClientConnection::onSocketDisconnected);
    QObject::connect(m_tcpSocket, &QTcpSocket::readyRead, this, &TcpClientConnection::onSocketReadyRead);
    QObject::connect(m_tcpSocket, QOverload<QAbstractSocket::SocketError>::of(&QTcpSocket::error), this, &TcpClientConnection::onSocketError);

    // ????????
    m_reconnectTimer = new QTimer(this);
    m_reconnectTimer->setSingleShot(true);
    QObject::connect(m_reconnectTimer, &QTimer::timeout, this, &TcpClientConnection::handleReconnectTimer);
}

Result<void> TcpClientConnection::open() {
    QMutexLocker locker(&m_mutex);

    if (m_state == ConnectionState::Connected) {
        return Result<void>();
    }

    setState(ConnectionState::Connecting);

    // ??????
    auto configResult = applyNetworkConfig();
    if (configResult.isFailed()) {
        setState(ConnectionState::Error);
        return configResult;
    }

    // ??????
    QHostAddress address(m_config.networkConfig.hostAddress);
    m_tcpSocket->connectToHost(address, m_config.networkConfig.port);

    // ??????
    if (!m_tcpSocket->waitForConnected(m_config.networkConfig.connectTimeout)) {
        QString error =
            QString("Failed to connect to %1:%2 - %3").arg(m_config.networkConfig.hostAddress).arg(m_config.networkConfig.port).arg(m_tcpSocket->errorString());
        setError(error);
        setState(ConnectionState::Error);
        return Result<void>();
    }

    return Result<void>();
}

Result<void> TcpClientConnection::close() {
    QMutexLocker locker(&m_mutex);

    if (m_state == ConnectionState::Disconnected) {
        return Result<void>();
    }

    setState(ConnectionState::Disconnecting);
    stopReconnectTimer();

    if (m_tcpSocket && m_tcpSocket->state() != QAbstractSocket::UnconnectedState) {
        m_tcpSocket->disconnectFromHost();
        if (m_tcpSocket->state() != QAbstractSocket::UnconnectedState) {
            m_tcpSocket->waitForDisconnected(3000);
        }
    }

    setState(ConnectionState::Disconnected);
    emitEvent(ConnectionEvent::Disconnected, "TCP connection closed");

    return Result<void>();
}

bool TcpClientConnection::isConnected() const {
    QMutexLocker locker(&m_mutex);
    return m_state == ConnectionState::Connected && m_tcpSocket && m_tcpSocket->state() == QAbstractSocket::ConnectedState;
}

ConnectionState TcpClientConnection::getState() const {
    QMutexLocker locker(&m_mutex);
    return m_state;
}

Result<void> TcpClientConnection::reconnect() {
    auto closeResult = close();
    if (closeResult.isFailed()) {
        return closeResult;
    }

    QThread::msleep(100);  // ????
    return open();
}

Result<QByteArray> TcpClientConnection::read(qint32 timeoutMs) {
    QMutexLocker locker(&m_mutex);

    if (!isConnected()) {
        return Result<QByteArray>::failure("TCP socket not connected");
    }

    if (!waitForReadyRead(timeoutMs)) {
        return Result<QByteArray>::failure("Read timeout");
    }

    QByteArray data = m_tcpSocket->readAll();
    if (!data.isEmpty()) {
        updateStatistics(DataDirection::Input, data.size());
        emitEvent(ConnectionEvent::DataReceived, QString("Received %1 bytes").arg(data.size()));
    }

    return Result<QByteArray>(data);
}

Result<void> TcpClientConnection::write(const QByteArray &data) {
    QMutexLocker locker(&m_mutex);

    if (!isConnected()) {
        return Result<void>();
    }

    qint64 bytesWritten = m_tcpSocket->write(data);
    if (bytesWritten == -1) {
        QString error = QString("Failed to write data: %1").arg(m_tcpSocket->errorString());
        setError(error);
        return Result<void>();
    }

    if (bytesWritten != data.size()) {
        QString error = QString("Partial write: %1 of %2 bytes written").arg(bytesWritten).arg(data.size());
        setError(error);
        return Result<void>();
    }

    updateStatistics(DataDirection::Output, bytesWritten);
    emitEvent(ConnectionEvent::DataSent, QString("Sent %1 bytes").arg(bytesWritten));

    return Result<void>();
}

qint64 TcpClientConnection::bytesAvailable() const {
    QMutexLocker locker(&m_mutex);

    if (!isConnected()) {
        return 0;
    }

    return m_tcpSocket->bytesAvailable();
}

bool TcpClientConnection::waitForReadyRead(qint32 timeoutMs) {
    if (!isConnected()) {
        return false;
    }

    return m_tcpSocket->waitForReadyRead(timeoutMs);
}

bool TcpClientConnection::waitForBytesWritten(qint32 timeoutMs) {
    if (!isConnected()) {
        return false;
    }

    return m_tcpSocket->waitForBytesWritten(timeoutMs);
}

Result<void> TcpClientConnection::configure(const ConnectionConfig &config) {
    QMutexLocker locker(&m_mutex);

    m_config = config;

    if (isConnected()) {
        // ???????????????????        locker.unlock();
        return reconnect();
    }

    return Result<void>();
}

ConnectionConfig TcpClientConnection::getConfiguration() const {
    QMutexLocker locker(&m_mutex);
    return m_config;
}

Result<void> TcpClientConnection::setParameter(const QString &key, const QVariant &value) {
    QMutexLocker locker(&m_mutex);

    m_config.parameters[key] = value;

    // ????????
    if (key == "hostAddress") {
        m_config.networkConfig.hostAddress = value.toString();
    } else if (key == "port") {
        m_config.networkConfig.port = value.toUInt();
    } else if (key == "connectTimeout") {
        m_config.networkConfig.connectTimeout = value.toInt();
    } else if (key == "keepAlive") {
        m_config.networkConfig.keepAlive = value.toBool();
    }

    return Result<void>();
}

QVariant TcpClientConnection::getParameter(const QString &key) const {
    QMutexLocker locker(&m_mutex);
    return m_config.parameters.value(key);
}

QString TcpClientConnection::getName() const {
    QMutexLocker locker(&m_mutex);
    return m_config.name.isEmpty() ? QString("TcpClient_%1:%2").arg(m_config.networkConfig.hostAddress).arg(m_config.networkConfig.port) : m_config.name;
}

QString TcpClientConnection::getDescription() const {
    QMutexLocker locker(&m_mutex);
    return m_config.description.isEmpty() ? QString("TCP client connection to %1:%2").arg(m_config.networkConfig.hostAddress).arg(m_config.networkConfig.port)
                                          : m_config.description;
}

ConnectionType TcpClientConnection::getType() const {
    return ConnectionType::TcpClient;
}

ConnectionStatistics TcpClientConnection::getStatistics() const {
    QMutexLocker locker(&m_mutex);
    return m_stats;
}

QString TcpClientConnection::getLastError() const {
    QMutexLocker locker(&m_mutex);
    return m_lastError;
}

void TcpClientConnection::resetStatistics() {
    QMutexLocker locker(&m_mutex);
    m_stats = ConnectionStatistics();
}

bool TcpClientConnection::isHealthy() const {
    return isConnected() && m_tcpSocket && m_tcpSocket->error() == QAbstractSocket::UnknownSocketError;
}

Result<bool> TcpClientConnection::testConnection() {
    if (!isConnected()) {
        return Result<bool>("Not connected");
    }

    // ??????????socket??
    if (m_tcpSocket->error() != QAbstractSocket::UnknownSocketError) {
        return Result<bool>(QString("Socket error: %1").arg(socketErrorToString(m_tcpSocket->error())));
    }

    return Result<bool>(true);
}

QHostAddress TcpClientConnection::getRemoteAddress() const {
    if (m_tcpSocket) {
        return m_tcpSocket->peerAddress();
    }
    return QHostAddress();
}

quint16 TcpClientConnection::getRemotePort() const {
    if (m_tcpSocket) {
        return m_tcpSocket->peerPort();
    }
    return 0;
}

QHostAddress TcpClientConnection::getLocalAddress() const {
    if (m_tcpSocket) {
        return m_tcpSocket->localAddress();
    }
    return QHostAddress();
}

quint16 TcpClientConnection::getLocalPort() const {
    if (m_tcpSocket) {
        return m_tcpSocket->localPort();
    }
    return 0;
}

void TcpClientConnection::updateStatistics(DataDirection direction, qint64 bytes) {
    m_stats.lastActivity = QDateTime::currentDateTime();

    if (direction == DataDirection::Input) {
        m_stats.bytesReceived += bytes;
        m_stats.packetsReceived++;
    } else if (direction == DataDirection::Output) {
        m_stats.bytesSent += bytes;
        m_stats.packetsSent++;
    }
}

void TcpClientConnection::emitEvent(ConnectionEvent event, const QString &message, const QVariantMap &data) {
    ConnectionEventInfo eventInfo(event, message);
    eventInfo.data = data;
    emit eventOccurred(eventInfo);
}

void TcpClientConnection::handleConnected() {
    QMutexLocker locker(&m_mutex);
    setState(ConnectionState::Connected);
    m_reconnectAttempts = 0;
    m_stats.connectTime = QDateTime::currentDateTime();

    emitEvent(ConnectionEvent::Connected, QString("Connected to %1:%2").arg(getRemoteAddress().toString()).arg(getRemotePort()));
}

void TcpClientConnection::handleDisconnected() {
    QMutexLocker locker(&m_mutex);
    setState(ConnectionState::Disconnected);
    emitEvent(ConnectionEvent::Disconnected, "TCP connection disconnected");
}

void TcpClientConnection::handleReadyRead() {
    QByteArray data = m_tcpSocket->readAll();
    if (!data.isEmpty()) {
        updateStatistics(DataDirection::Input, data.size());
        emit dataReceived(data);
        emitEvent(ConnectionEvent::DataReceived, QString("Received %1 bytes").arg(data.size()));
    }
}

void TcpClientConnection::handleError(QAbstractSocket::SocketError error) {
    if (error == QAbstractSocket::UnknownSocketError) {
        return;
    }

    QString errorString = socketErrorToString(error);
    setError(errorString);
    setState(ConnectionState::Error);

    emit errorOccurred(errorString);
    emitEvent(ConnectionEvent::Error, errorString);

    // ????????????????
    if (m_config.autoReconnect && m_reconnectAttempts < m_config.maxReconnectAttempts) {
        startReconnectTimer();
    }
}

void TcpClientConnection::handleReconnectTimer() {
    if (m_reconnectAttempts < m_config.maxReconnectAttempts) {
        m_reconnectAttempts++;
        qDebug() << "Attempting to reconnect TCP client, attempt" << m_reconnectAttempts;

        auto result = reconnect();
        if (result.isFailed()) {
            if (m_reconnectAttempts < m_config.maxReconnectAttempts) {
                startReconnectTimer();
            } else {
                qDebug() << "Max reconnect attempts reached for TCP client";
            }
        }
    }
}

Result<void> TcpClientConnection::applyNetworkConfig() {
    if (!m_tcpSocket) {
        return Result<void>();
    }

    // ??????
    if (!ConnectionUtils::isValidNetworkConfig(m_config.networkConfig)) {
        return Result<void>();
    }

    // ??socket??
    if (m_config.networkConfig.keepAlive) {
        m_tcpSocket->setSocketOption(QAbstractSocket::KeepAliveOption, 1);
    }

    return Result<void>();
}

void TcpClientConnection::setState(ConnectionState state) {
    if (m_state != state) {
        ConnectionState previousState = m_state;
        m_state = state;
        emit stateChanged(getConnectionId(), state, previousState);
    }
}

void TcpClientConnection::setError(const QString &error) {
    m_lastError = error;
    m_stats.errorCount++;
}

void TcpClientConnection::startReconnectTimer() {
    if (m_reconnectTimer && !m_reconnectTimer->isActive()) {
        m_reconnectTimer->start(m_config.reconnectInterval);
    }
}

void TcpClientConnection::stopReconnectTimer() {
    if (m_reconnectTimer && m_reconnectTimer->isActive()) {
        m_reconnectTimer->stop();
    }
}

QString TcpClientConnection::socketErrorToString(QAbstractSocket::SocketError error) const {
    switch (error) {
    case QAbstractSocket::ConnectionRefusedError:
        return "Connection refused";
    case QAbstractSocket::RemoteHostClosedError:
        return "Remote host closed connection";
    case QAbstractSocket::HostNotFoundError:
        return "Host not found";
    case QAbstractSocket::SocketAccessError:
        return "Socket access error";
    case QAbstractSocket::SocketResourceError:
        return "Socket resource error";
    case QAbstractSocket::SocketTimeoutError:
        return "Socket timeout";
    case QAbstractSocket::DatagramTooLargeError:
        return "Datagram too large";
    case QAbstractSocket::NetworkError:
        return "Network error";
    case QAbstractSocket::AddressInUseError:
        return "Address already in use";
    case QAbstractSocket::SocketAddressNotAvailableError:
        return "Socket address not available";
    case QAbstractSocket::UnsupportedSocketOperationError:
        return "Unsupported socket operation";
    case QAbstractSocket::ProxyAuthenticationRequiredError:
        return "Proxy authentication required";
    case QAbstractSocket::SslHandshakeFailedError:
        return "SSL handshake failed";
    case QAbstractSocket::UnfinishedSocketOperationError:
        return "Unfinished socket operation";
    case QAbstractSocket::ProxyConnectionRefusedError:
        return "Proxy connection refused";
    case QAbstractSocket::ProxyConnectionClosedError:
        return "Proxy connection closed";
    case QAbstractSocket::ProxyConnectionTimeoutError:
        return "Proxy connection timeout";
    case QAbstractSocket::ProxyNotFoundError:
        return "Proxy not found";
    case QAbstractSocket::ProxyProtocolError:
        return "Proxy protocol error";
    case QAbstractSocket::OperationError:
        return "Operation error";
    case QAbstractSocket::SslInternalError:
        return "SSL internal error";
    case QAbstractSocket::SslInvalidUserDataError:
        return "SSL invalid user data";
    case QAbstractSocket::TemporaryError:
        return "Temporary error";
    default:
        return "Unknown socket error";
    }
}

}  // namespace Connection
}  // namespace Communication
}  // namespace LA
