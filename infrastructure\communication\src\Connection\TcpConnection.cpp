#include <LA/Communication/Connection/NetworkConnection.h>
#include <QDebug>
#include <QMutexLocker>
#include <QDateTime>

namespace LA {
namespace Communication {
namespace Connection {

TcpConnection::TcpConnection(QObject* parent)
    : IConnection(parent)
    , m_socket(new QTcpSocket(this))
    , m_status(ConnectionStatus::Disconnected)
{
    // 连接Qt TCP Socket信号到我们的槽
    connect(m_socket, &QTcpSocket::connected, 
            this, &TcpConnection::onConnected);
    connect(m_socket, &QTcpSocket::disconnected, 
            this, &TcpConnection::onDisconnected);
    connect(m_socket, &QTcpSocket::readyRead, 
            this, &TcpConnection::onReadyRead);
    connect(m_socket, QOverload<QAbstractSocket::SocketError>::of(&QTcpSocket::error),
            this, &TcpConnection::onErrorOccurred);
    
    resetStatistics();
}

TcpConnection::~TcpConnection() {
    if (isOpen()) {
        close();
    }
}

// === IConnection接口实现 ===

SimpleResult TcpConnection::open(const ConnectionConfig& config) {
    QMutexLocker locker(&m_mutex);
    
    if (m_status == ConnectionStatus::Connected) {
        return SimpleResult::success(true);
    }
    
    m_config = config;
    
    // 获取主机地址和端口
    QString hostAddress = config.hostAddress;
    if (hostAddress.isEmpty()) {
        hostAddress = config.parameters.value("hostAddress").toString();
    }
    
    int port = config.port;
    if (port == 0) {
        port = config.parameters.value("port", 8080).toInt();
    }
    
    if (hostAddress.isEmpty() || port <= 0) {
        m_lastError = QString("Invalid host address (%1) or port (%2)").arg(hostAddress).arg(port);
        return SimpleResult::failure(m_lastError);
    }
    
    setStatus(ConnectionStatus::Connecting);
    
    // 连接到TCP服务器
    m_socket->connectToHost(hostAddress, port);
    
    // 等待连接完成
    int timeout = config.parameters.value("timeout", 5000).toInt();
    if (!m_socket->waitForConnected(timeout)) {
        m_lastError = QString("Failed to connect to %1:%2 - %3")
                      .arg(hostAddress)
                      .arg(port)
                      .arg(m_socket->errorString());
        setStatus(ConnectionStatus::Error);
        return SimpleResult::failure(m_lastError);
    }
    
    setStatus(ConnectionStatus::Connected);
    m_lastError.clear();
    
    return SimpleResult::success(true);
}

SimpleResult TcpConnection::close() {
    QMutexLocker locker(&m_mutex);
    
    if (m_status == ConnectionStatus::Disconnected) {
        return SimpleResult::success(true);
    }
    
    if (m_socket->state() == QAbstractSocket::ConnectedState) {
        m_socket->disconnectFromHost();
        // 等待断开连接
        if (m_socket->state() != QAbstractSocket::UnconnectedState) {
            m_socket->waitForDisconnected(3000);
        }
    }
    
    setStatus(ConnectionStatus::Disconnected);
    return SimpleResult::success(true);
}

ConnectionStatus TcpConnection::status() const {
    QMutexLocker locker(&m_mutex);
    return m_status;
}

bool TcpConnection::isOpen() const {
    QMutexLocker locker(&m_mutex);
    return m_status == ConnectionStatus::Connected && 
           m_socket && m_socket->state() == QAbstractSocket::ConnectedState;
}

qint64 TcpConnection::write(const QByteArray& data) {
    QMutexLocker locker(&m_mutex);
    
    if (!isOpen()) {
        m_lastError = "TCP socket not connected";
        return -1;
    }
    
    qint64 bytesWritten = m_socket->write(data);
    if (bytesWritten == -1) {
        m_lastError = QString("Write failed: %1").arg(m_socket->errorString());
        return -1;
    }
    
    // 更新统计信息
    updateStatistics(0, bytesWritten);
    
    return bytesWritten;
}

QByteArray TcpConnection::read(qint64 maxBytes) {
    QMutexLocker locker(&m_mutex);
    
    if (!isOpen()) {
        return QByteArray();
    }
    
    QByteArray data;
    if (maxBytes > 0) {
        data = m_socket->read(maxBytes);
    } else {
        data = m_socket->readAll();
    }
    
    if (!data.isEmpty()) {
        updateStatistics(data.size(), 0);
    }
    
    return data;
}

qint64 TcpConnection::bytesAvailable() const {
    QMutexLocker locker(&m_mutex);
    return m_socket ? m_socket->bytesAvailable() : 0;
}

bool TcpConnection::waitForReadyRead(int timeout) {
    QMutexLocker locker(&m_mutex);
    return m_socket ? m_socket->waitForReadyRead(timeout) : false;
}

bool TcpConnection::waitForBytesWritten(int timeout) {
    QMutexLocker locker(&m_mutex);
    return m_socket ? m_socket->waitForBytesWritten(timeout) : false;
}

DeviceStatistics TcpConnection::getStatistics() const {
    QMutexLocker locker(&m_mutex);
    return m_statistics;
}

QString TcpConnection::errorString() const {
    QMutexLocker locker(&m_mutex);
    return m_lastError;
}

void TcpConnection::resetStatistics() {
    QMutexLocker locker(&m_mutex);
    m_statistics = DeviceStatistics();
    m_statistics.lastActivity = QDateTime::currentMSecsSinceEpoch();
}

// === TCP特定功能 ===

QHostAddress TcpConnection::getRemoteAddress() const {
    QMutexLocker locker(&m_mutex);
    return m_socket ? m_socket->peerAddress() : QHostAddress();
}

quint16 TcpConnection::getRemotePort() const {
    QMutexLocker locker(&m_mutex);
    return m_socket ? m_socket->peerPort() : 0;
}

// === 私有槽函数 ===

void TcpConnection::onConnected() {
    QMutexLocker locker(&m_mutex);
    setStatus(ConnectionStatus::Connected);
    emit connected();
}

void TcpConnection::onDisconnected() {
    QMutexLocker locker(&m_mutex);
    setStatus(ConnectionStatus::Disconnected);
    emit disconnected();
}

void TcpConnection::onReadyRead() {
    // 发出信号通知有数据可读
    emit readyRead();
}

void TcpConnection::onErrorOccurred(QAbstractSocket::SocketError error) {
    if (error != QAbstractSocket::SocketTimeoutError) {
        QMutexLocker locker(&m_mutex);
        
        m_lastError = m_socket->errorString();
        m_statistics.errorsCount++;
        
        // 对于严重错误，改变连接状态
        if (error != QAbstractSocket::SocketTimeoutError && 
            error != QAbstractSocket::TemporaryError) {
            setStatus(ConnectionStatus::Error);
        }
        
        emit errorOccurred(m_lastError);
    }
}

// === 私有辅助函数 ===

void TcpConnection::updateStatistics(qint64 bytesRead, qint64 bytesWritten) {
    qint64 currentTime = QDateTime::currentMSecsSinceEpoch();
    
    if (bytesRead > 0) {
        m_statistics.bytesReceived += bytesRead;
        m_statistics.packetsReceived++;
        m_statistics.lastActivity = currentTime;
    }
    
    if (bytesWritten > 0) {
        m_statistics.bytesSent += bytesWritten;
        m_statistics.packetsSent++;
        m_statistics.lastActivity = currentTime;
    }
}

void TcpConnection::setStatus(ConnectionStatus newStatus) {
    if (m_status != newStatus) {
        ConnectionStatus previousStatus = m_status;
        m_status = newStatus;
        
        // 发出状态变化信号
        emit statusChanged(newStatus);
        
        // 根据状态发出特定信号
        if (newStatus == ConnectionStatus::Connected && 
            previousStatus != ConnectionStatus::Connected) {
            emit readyRead(); // 可能有数据等待读取
        }
    }
}

} // namespace Connection
} // namespace Communication
} // namespace LA