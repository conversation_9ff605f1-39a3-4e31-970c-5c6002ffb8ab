#pragma once

#include <LA/Foundation/Core/CommonTypes.h>
#include <QObject>
#include <QString>
#include <QVariant>
#include <QVariantMap>
#include <QStringList>
#include <QDateTime>
#include <QRegularExpression>
#include <memory>

namespace LA {
namespace Support {
namespace Config {

using namespace Foundation::Core;

/**
 * @brief 验证规则类型枚举
 */
enum class ValidationRuleType {
    UNKNOWN = 0,        // 未知类型
    TYPE_CHECK,         // 类型检查
    RANGE_CHECK,        // 范围检查
    LENGTH_CHECK,       // 长度检查
    PATTERN_CHECK,      // 模式匹配检查
    ENUM_CHECK,         // 枚举值检查
    REQUIRED_CHECK,     // 必需字段检查
    DEPENDENCY_CHECK,   // 依赖关系检查
    CUSTOM_CHECK,       // 自定义检查
    SCHEMA_CHECK        // Schema检查
};

/**
 * @brief 验证严重级别枚举
 */
enum class ValidationSeverity {
    INFO = 0,           // 信息
    WARNING,            // 警告
    ERROR,              // 错误
    CRITICAL            // 严重错误
};

/**
 * @brief 验证结果状态枚举
 */
enum class ValidationStatus {
    UNKNOWN = 0,        // 未知状态
    PASSED,             // 通过
    FAILED,             // 失败
    SKIPPED,            // 跳过
    WARNING,            // 警告
    ERROR               // 错误
};

/**
 * @brief 验证规则定义
 */
struct ValidationRule {
    QString                 id;             // 规则ID
    QString                 name;           // 规则名称
    QString                 description;    // 规则描述
    ValidationRuleType      type;           // 规则类型
    ValidationSeverity      severity;       // 严重级别
    QString                 fieldPath;      // 字段路径
    QVariant               expectedValue;   // 期望值
    QVariant               minValue;        // 最小值
    QVariant               maxValue;        // 最大值
    QStringList            allowedValues;   // 允许的值列表
    QString                pattern;         // 正则表达式模式
    QString                customFunction;  // 自定义验证函数名
    QVariantMap            parameters;      // 规则参数
    QStringList            dependencies;    // 依赖字段列表
    bool                   enabled;         // 是否启用
    int                    priority;        // 优先级
    QString                category;        // 分类
    QString                group;           // 分组
    QDateTime              created;         // 创建时间
    QDateTime              modified;        // 修改时间
    QString                version;         // 版本
    QVariantMap            metadata;        // 元数据

    ValidationRule() 
        : type(ValidationRuleType::UNKNOWN)
        , severity(ValidationSeverity::ERROR)
        , enabled(true)
        , priority(0)
        , created(QDateTime::currentDateTime())
        , modified(QDateTime::currentDateTime()) {}
};

/**
 * @brief 验证结果
 */
struct ValidationResult {
    QString                 ruleId;         // 规则ID
    QString                 ruleName;       // 规则名称
    ValidationStatus        status;         // 验证状态
    ValidationSeverity      severity;       // 严重级别
    QString                 fieldPath;      // 字段路径
    QVariant               actualValue;     // 实际值
    QVariant               expectedValue;   // 期望值
    QString                 message;        // 验证消息
    QString                 suggestion;     // 修复建议
    QDateTime               timestamp;      // 验证时间
    double                  executionTime;  // 执行时间（毫秒）
    QVariantMap            context;         // 上下文信息

    ValidationResult() 
        : status(ValidationStatus::UNKNOWN)
        , severity(ValidationSeverity::ERROR)
        , timestamp(QDateTime::currentDateTime())
        , executionTime(0.0) {}
    
    ValidationResult(const QString& ruleId, ValidationStatus status, const QString& message)
        : ruleId(ruleId)
        , status(status)
        , message(message)
        , timestamp(QDateTime::currentDateTime())
        , executionTime(0.0) {}
};

/**
 * @brief 验证报告
 */
struct ValidationReport {
    QString                         reportId;       // 报告ID
    QString                         configName;     // 配置名称
    QString                         configPath;     // 配置路径
    QDateTime                       timestamp;      // 验证时间
    double                          totalTime;      // 总执行时间
    int                            totalRules;     // 总规则数
    int                            passedRules;    // 通过规则数
    int                            failedRules;    // 失败规则数
    int                            warningCount;   // 警告数量
    int                            errorCount;     // 错误数量
    bool                           isValid;        // 整体是否有效
    QList<ValidationResult>        results;        // 验证结果列表
    QVariantMap                    summary;        // 摘要信息
    QVariantMap                    statistics;     // 统计信息
    QVariantMap                    metadata;       // 元数据

    ValidationReport() 
        : timestamp(QDateTime::currentDateTime())
        , totalTime(0.0)
        , totalRules(0)
        , passedRules(0)
        , failedRules(0)
        , warningCount(0)
        , errorCount(0)
        , isValid(false) {}
};

/**
 * @brief 验证上下文
 */
struct ValidationContext {
    QString                 configName;     // 配置名称
    QString                 configPath;     // 配置路径
    QVariantMap            fullData;        // 完整数据
    QVariantMap            currentScope;    // 当前作用域
    QString                currentPath;     // 当前路径
    QStringList            pathStack;       // 路径栈
    QVariantMap            variables;       // 变量
    QVariantMap            options;         // 验证选项
    QDateTime              timestamp;       // 验证时间

    ValidationContext() : timestamp(QDateTime::currentDateTime()) {}
};

/**
 * @brief 验证选项
 */
struct ValidationOptions {
    bool                    enabled;            // 是否启用验证
    bool                    stopOnFirstError;   // 遇到第一个错误时停止
    bool                    includeWarnings;    // 包含警告
    bool                    includeInfo;        // 包含信息
    ValidationSeverity      minSeverity;        // 最小严重级别
    QStringList            enabledRules;        // 启用的规则列表
    QStringList            disabledRules;       // 禁用的规则列表
    QStringList            enabledCategories;   // 启用的分类列表
    QStringList            disabledCategories;  // 禁用的分类列表
    int                    maxErrors;           // 最大错误数
    int                    maxWarnings;         // 最大警告数
    double                 timeoutMs;           // 超时时间（毫秒）
    bool                   parallel;            // 并行验证
    int                    threadCount;         // 线程数
    QVariantMap            customOptions;       // 自定义选项

    ValidationOptions() 
        : enabled(true)
        , stopOnFirstError(false)
        , includeWarnings(true)
        , includeInfo(false)
        , minSeverity(ValidationSeverity::WARNING)
        , maxErrors(100)
        , maxWarnings(100)
        , timeoutMs(30000.0)
        , parallel(false)
        , threadCount(1) {}
};

/**
 * @brief 配置验证器接口
 */
class IConfigValidator : public QObject, public IManager {
    Q_OBJECT

public:
    explicit IConfigValidator(QObject* parent = nullptr) : QObject(parent) {}
    virtual ~IConfigValidator() = default;

    /**
     * @brief 添加验证规则
     */
    virtual SimpleResult addRule(const ValidationRule& rule) = 0;

    /**
     * @brief 批量添加验证规则
     */
    virtual SimpleResult addRules(const QList<ValidationRule>& rules) = 0;

    /**
     * @brief 删除验证规则
     */
    virtual SimpleResult removeRule(const QString& ruleId) = 0;

    /**
     * @brief 更新验证规则
     */
    virtual SimpleResult updateRule(const ValidationRule& rule) = 0;

    /**
     * @brief 获取验证规则
     */
    virtual Result<ValidationRule> getRule(const QString& ruleId) const = 0;

    /**
     * @brief 获取所有验证规则
     */
    virtual QList<ValidationRule> getAllRules() const = 0;

    /**
     * @brief 查询验证规则
     */
    virtual QList<ValidationRule> queryRules(const QString& category = QString(),
                                            const QString& group = QString(),
                                            ValidationRuleType type = ValidationRuleType::UNKNOWN) const = 0;

    /**
     * @brief 启用验证规则
     */
    virtual SimpleResult enableRule(const QString& ruleId) = 0;

    /**
     * @brief 禁用验证规则
     */
    virtual SimpleResult disableRule(const QString& ruleId) = 0;

    /**
     * @brief 验证配置数据
     */
    virtual Result<ValidationReport> validate(const QVariantMap& data, 
                                             const ValidationOptions& options = ValidationOptions()) = 0;

    /**
     * @brief 验证单个字段
     */
    virtual Result<QList<ValidationResult>> validateField(const QString& fieldPath, 
                                                         const QVariant& value,
                                                         const QVariantMap& context = QVariantMap()) = 0;

    /**
     * @brief 验证配置文件
     */
    virtual Result<ValidationReport> validateFile(const QString& filePath,
                                                 const ValidationOptions& options = ValidationOptions()) = 0;

    /**
     * @brief 验证配置Schema
     */
    virtual Result<ValidationReport> validateSchema(const QVariantMap& data,
                                                   const QVariantMap& schema,
                                                   const ValidationOptions& options = ValidationOptions()) = 0;

    /**
     * @brief 创建验证规则集
     */
    virtual SimpleResult createRuleSet(const QString& name, 
                                      const QList<ValidationRule>& rules) = 0;

    /**
     * @brief 加载验证规则集
     */
    virtual SimpleResult loadRuleSet(const QString& name) = 0;

    /**
     * @brief 保存验证规则集
     */
    virtual SimpleResult saveRuleSet(const QString& name, 
                                    const QString& filePath = QString()) = 0;

    /**
     * @brief 导出验证规则
     */
    virtual ByteArrayResult exportRules(const QString& format = "json") = 0;

    /**
     * @brief 导入验证规则
     */
    virtual SimpleResult importRules(const QByteArray& data, 
                                    const QString& format = "json",
                                    bool overwrite = false) = 0;

    /**
     * @brief 清空所有规则
     */
    virtual SimpleResult clearRules() = 0;

    /**
     * @brief 获取验证统计信息
     */
    virtual StatusInfoList getStatistics() const = 0;

    /**
     * @brief 获取支持的规则类型  
     */
    virtual QList<ValidationRuleType> getSupportedRuleTypes() const = 0;

    /**
     * @brief 注册自定义验证函数
     */
    virtual SimpleResult registerCustomValidator(const QString& name,
                                                std::function<ValidationResult(const QVariant&, const ValidationRule&, const ValidationContext&)> validator) = 0;

    /**
     * @brief 注销自定义验证函数
     */
    virtual SimpleResult unregisterCustomValidator(const QString& name) = 0;

signals:
    /**
     * @brief 规则添加信号
     */
    void ruleAdded(const ValidationRule& rule);

    /**
     * @brief 规则删除信号
     */
    void ruleRemoved(const QString& ruleId);

    /**
     * @brief 规则更新信号
     */
    void ruleUpdated(const ValidationRule& rule);

    /**
     * @brief 验证开始信号
     */
    void validationStarted(const QString& configName);

    /**
     * @brief 验证完成信号
     */
    void validationCompleted(const ValidationReport& report);

    /**
     * @brief 验证错误信号
     */
    void validationError(const QString& error);

    /**
     * @brief 规则验证完成信号
     */
    void ruleValidated(const ValidationResult& result);
};

/**
 * @brief 验证器工厂接口
 */
class IConfigValidatorFactory {
public:
    virtual ~IConfigValidatorFactory() = default;

    /**
     * @brief 创建配置验证器
     */
    virtual std::shared_ptr<IConfigValidator> createConfigValidator(const ConfigParameters& config = {}) = 0;
};

// 工具函数
QString validationRuleTypeToString(ValidationRuleType type);
ValidationRuleType stringToValidationRuleType(const QString& typeStr);
QString validationSeverityToString(ValidationSeverity severity);
ValidationSeverity stringToValidationSeverity(const QString& severityStr);
QString validationStatusToString(ValidationStatus status);
ValidationStatus stringToValidationStatus(const QString& statusStr);

// 内置验证函数
ValidationResult validateType(const QVariant& value, const ValidationRule& rule, const ValidationContext& context);
ValidationResult validateRange(const QVariant& value, const ValidationRule& rule, const ValidationContext& context);
ValidationResult validateLength(const QVariant& value, const ValidationRule& rule, const ValidationContext& context);
ValidationResult validatePattern(const QVariant& value, const ValidationRule& rule, const ValidationContext& context);
ValidationResult validateEnum(const QVariant& value, const ValidationRule& rule, const ValidationContext& context);
ValidationResult validateRequired(const QVariant& value, const ValidationRule& rule, const ValidationContext& context);
ValidationResult validateDependency(const QVariant& value, const ValidationRule& rule, const ValidationContext& context);

}  // namespace Config
}  // namespace Support
}  // namespace LA