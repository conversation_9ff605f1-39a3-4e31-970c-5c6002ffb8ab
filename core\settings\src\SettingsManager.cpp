#include "LA/Settings/SettingsManager.h"
#include "LA/Settings/interfaces/ISettingsPanel.h"
#include <QApplication>
#include <QDebug>
#include <QDir>
#include <QFile>
#include <QJsonDocument>
#include <QJsonObject>
#include <QStandardPaths>

namespace LA {
namespace Settings {

SettingsManager *SettingsManager::s_instance = nullptr;

SettingsManager::SettingsManager(QObject *parent) : QObject(parent), m_initialized(false) {
    initialize();
}

SettingsManager *SettingsManager::instance() {
    if (!s_instance) {
        s_instance = new SettingsManager();
    }
    return s_instance;
}

void SettingsManager::initialize() {
    if (m_initialized) {
        return;
    }

    // 创建配置目录
    createConfigDirectory();

    // 设置默认配置文件路径
    QString configDir = QStandardPaths::writableLocation(QStandardPaths::AppConfigLocation);
    m_configFilePath  = configDir + "/settings.ini";

    // 创建设置对象
    m_settings = std::make_shared<QSettings>(m_configFilePath, QSettings::IniFormat);

    // 设置默认值
    setupDefaultSettings();

    m_initialized = true;
    qDebug() << "SettingsManager initialized with config file:" << m_configFilePath;
}

void SettingsManager::createConfigDirectory() {
    QString configDir = QStandardPaths::writableLocation(QStandardPaths::AppConfigLocation);
    QDir    dir;
    if (!dir.exists(configDir)) {
        if (dir.mkpath(configDir)) {
            qDebug() << "Created config directory:" << configDir;
        } else {
            qWarning() << "Failed to create config directory:" << configDir;
        }
    }
}

void SettingsManager::setupDefaultSettings() {
    if (!m_settings) {
        return;
    }

    // 设置默认值（如果不存在）
    if (!m_settings->contains("general/language")) {
        m_settings->setValue("general/language", "zh_CN");
    }
    if (!m_settings->contains("general/theme")) {
        m_settings->setValue("general/theme", "default");
    }
    if (!m_settings->contains("general/autoSave")) {
        m_settings->setValue("general/autoSave", true);
    }
    if (!m_settings->contains("general/autoSaveInterval")) {
        m_settings->setValue("general/autoSaveInterval", 5);
    }

    m_settings->sync();
}

bool SettingsManager::registerPanel(std::shared_ptr<ISettingsPanel> panel) {
    if (!panel) {
        qWarning() << "Cannot register null panel";
        return false;
    }

    QString panelId = panel->getPanelId();
    if (panelId.isEmpty()) {
        qWarning() << "Cannot register panel with empty ID";
        return false;
    }

    if (m_panels.contains(panelId)) {
        qWarning() << "Panel with ID" << panelId << "is already registered";
        return false;
    }

    m_panels[panelId] = panel;

    // 连接面板的设置更改信号
    QObject *panelObject = dynamic_cast<QObject *>(panel.get());
    if (panelObject) {
        connect(panelObject, SIGNAL(settingsChanged()), this, SLOT(onPanelSettingsChanged()));
    }

    emit panelRegistered(panelId);
    qDebug() << "Panel registered:" << panelId << "(" << panel->getDisplayName() << ")";
    return true;
}

bool SettingsManager::unregisterPanel(const QString &panelId) {
    if (!m_panels.contains(panelId)) {
        qWarning() << "Panel with ID" << panelId << "is not registered";
        return false;
    }

    // 断开信号连接
    auto     panel       = m_panels[panelId];
    QObject *panelObject = dynamic_cast<QObject *>(panel.get());
    if (panelObject) {
        disconnect(panelObject, SIGNAL(settingsChanged()), this, SLOT(onPanelSettingsChanged()));
    }

    m_panels.remove(panelId);
    emit panelUnregistered(panelId);
    qDebug() << "Panel unregistered:" << panelId;
    return true;
}

std::shared_ptr<ISettingsPanel> SettingsManager::getPanel(const QString &panelId) {
    return m_panels.value(panelId, nullptr);
}

QStringList SettingsManager::getAllPanelIds() const {
    return m_panels.keys();
}

QStringList SettingsManager::getPanelsByCategory(const QString &category) const {
    QStringList result;
    for (auto it = m_panels.begin(); it != m_panels.end(); ++it) {
        if (it.value()->getCategory() == category) {
            result.append(it.key());
        }
    }
    return result;
}

QStringList SettingsManager::getAllCategories() const {
    QStringList categories;
    for (auto it = m_panels.begin(); it != m_panels.end(); ++it) {
        QString category = it.value()->getCategory();
        if (!categories.contains(category)) {
            categories.append(category);
        }
    }
    categories.sort();
    return categories;
}

void SettingsManager::loadAllSettings() {
    for (auto it = m_panels.begin(); it != m_panels.end(); ++it) {
        try {
            it.value()->loadSettings();
        } catch (const std::exception &e) {
            qWarning() << "Failed to load settings for panel" << it.key() << ":" << e.what();
        }
    }
    emit allSettingsLoaded();
    qDebug() << "All settings loaded for" << m_panels.size() << "panels";
}

void SettingsManager::saveAllSettings() {
    for (auto it = m_panels.begin(); it != m_panels.end(); ++it) {
        try {
            it.value()->saveSettings();
        } catch (const std::exception &e) {
            qWarning() << "Failed to save settings for panel" << it.key() << ":" << e.what();
        }
    }

    // 同步全局设置
    if (m_settings) {
        m_settings->sync();
    }

    emit allSettingsSaved();
    qDebug() << "All settings saved for" << m_panels.size() << "panels";
}

void SettingsManager::resetAllSettings() {
    for (auto it = m_panels.begin(); it != m_panels.end(); ++it) {
        try {
            it.value()->resetSettings();
        } catch (const std::exception &e) {
            qWarning() << "Failed to reset settings for panel" << it.key() << ":" << e.what();
        }
    }

    // 重置全局设置
    if (m_settings) {
        m_settings->clear();
        setupDefaultSettings();
    }

    emit allSettingsReset();
    qDebug() << "All settings reset for" << m_panels.size() << "panels";
}

void SettingsManager::applyAllSettings() {
    for (auto it = m_panels.begin(); it != m_panels.end(); ++it) {
        try {
            it.value()->applySettings();
        } catch (const std::exception &e) {
            qWarning() << "Failed to apply settings for panel" << it.key() << ":" << e.what();
        }
    }
    qDebug() << "All settings applied for" << m_panels.size() << "panels";
}

bool SettingsManager::hasUnsavedChanges() const {
    for (auto it = m_panels.begin(); it != m_panels.end(); ++it) {
        if (it.value()->hasUnsavedChanges()) {
            return true;
        }
    }
    return false;
}

QVariant SettingsManager::getSetting(const QString &key, const QVariant &defaultValue) const {
    if (!m_settings) {
        return defaultValue;
    }
    return m_settings->value(key, defaultValue);
}

void SettingsManager::setSetting(const QString &key, const QVariant &value) {
    if (!m_settings) {
        return;
    }

    QVariant oldValue = m_settings->value(key);
    m_settings->setValue(key, value);

    if (oldValue != value) {
        emit settingChanged(key, value);
    }
}

void SettingsManager::removeSetting(const QString &key) {
    if (!m_settings) {
        return;
    }
    m_settings->remove(key);
}

bool SettingsManager::containsSetting(const QString &key) const {
    if (!m_settings) {
        return false;
    }
    return m_settings->contains(key);
}

QStringList SettingsManager::getAllKeys() const {
    if (!m_settings) {
        return QStringList();
    }
    return m_settings->allKeys();
}

void SettingsManager::clearAllSettings() {
    if (!m_settings) {
        return;
    }
    m_settings->clear();
    m_settings->sync();
}

QString SettingsManager::getConfigFilePath() const {
    return m_configFilePath;
}

void SettingsManager::setConfigFilePath(const QString &filePath) {
    if (m_configFilePath != filePath) {
        m_configFilePath = filePath;
        m_settings       = std::make_shared<QSettings>(m_configFilePath, QSettings::IniFormat);
        setupDefaultSettings();
    }
}

bool SettingsManager::isPanelRegistered(const QString &panelId) const {
    return m_panels.contains(panelId);
}

bool SettingsManager::exportSettings(const QString &filePath) {
    QJsonObject allSettings = getAllSettingsAsJson();

    QJsonDocument doc(allSettings);
    QFile         file(filePath);

    if (!file.open(QIODevice::WriteOnly)) {
        qWarning() << "Failed to open file for writing:" << filePath;
        return false;
    }

    file.write(doc.toJson());
    file.close();

    qDebug() << "Settings exported to:" << filePath;
    return true;
}

bool SettingsManager::importSettings(const QString &filePath) {
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        qWarning() << "Failed to open file for reading:" << filePath;
        return false;
    }

    QByteArray data = file.readAll();
    file.close();

    QJsonParseError error;
    QJsonDocument   doc = QJsonDocument::fromJson(data, &error);

    if (error.error != QJsonParseError::NoError) {
        qWarning() << "Failed to parse JSON:" << error.errorString();
        return false;
    }

    setAllSettingsFromJson(doc.object());

    qDebug() << "Settings imported from:" << filePath;
    return true;
}

QJsonObject SettingsManager::getAllSettingsAsJson() const {
    QJsonObject result;

    // 导出全局设置
    if (m_settings) {
        QJsonObject globalSettings;
        QStringList keys = m_settings->allKeys();
        for (const QString &key : keys) {
            QVariant value      = m_settings->value(key);
            globalSettings[key] = QJsonValue::fromVariant(value);
        }
        result["global"] = globalSettings;
    }

    // 导出各面板设置
    QJsonObject panelSettings;
    for (auto it = m_panels.begin(); it != m_panels.end(); ++it) {
        panelSettings[it.key()] = it.value()->getSettingsData();
    }
    result["panels"] = panelSettings;

    return result;
}

void SettingsManager::setAllSettingsFromJson(const QJsonObject &json) {
    // 导入全局设置
    if (json.contains("global") && json["global"].isObject()) {
        QJsonObject globalSettings = json["global"].toObject();
        for (auto it = globalSettings.begin(); it != globalSettings.end(); ++it) {
            m_settings->setValue(it.key(), it.value().toVariant());
        }
        m_settings->sync();
    }

    // 导入各面板设置
    if (json.contains("panels") && json["panels"].isObject()) {
        QJsonObject panelSettings = json["panels"].toObject();
        for (auto it = panelSettings.begin(); it != panelSettings.end(); ++it) {
            QString panelId = it.key();
            if (m_panels.contains(panelId) && it.value().isObject()) {
                m_panels[panelId]->setSettingsData(it.value().toObject());
            }
        }
    }
}

void SettingsManager::onPanelSettingsChanged() {
    // 面板设置已更改，可以在这里执行一些全局操作
    qDebug() << "Panel settings changed";
}

}  // namespace Settings
}  // namespace LA

// MOC文件由CMake AutoMoc自动处理，无需手动包含
