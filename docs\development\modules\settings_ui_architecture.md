# 设置模块UI显示区域代码架构文档

## 概述

LA设置模块采用分层容器架构，提供灵活的设置面板注册和显示机制。本文档详细描述了设置模块的UI容器结构以及新面板的添加流程。

## 核心容器架构

### 1. 主容器层次结构

```
SettingsDialog (QDialog)
├── QVBoxLayout (主布局)
    ├── QSplitter (水平分割器)
    │   ├── QListWidget (左侧类别列表)
    │   └── QStackedWidget (右侧面板容器) ⭐ 核心显示容器
    └── QHBoxLayout (底部按钮区域)
        ├── QPushButton (重置)
        ├── QPushButton (应用)  
        └── QDialogButtonBox (确定/取消)
```

### 2. 关键容器说明

#### QStackedWidget - 核心面板容器
- **位置**: `core/settings/src/Settings.cpp:329`
- **变量名**: `m_panelStack`
- **作用**: 承载所有注册的设置面板，实现面板切换显示
- **特性**:
  - 同时只显示一个面板
  - 支持索引切换
  - 自动管理面板的显示/隐藏状态

#### QListWidget - 面板选择器
- **位置**: `core/settings/src/Settings.cpp:298`
- **变量名**: `m_categoryList` 
- **作用**: 显示已注册面板的分类列表，控制面板切换
- **交互**: 通过 `currentRowChanged` 信号驱动面板切换

## 面板注册与显示流程

### 1. 面板注册机制

#### 步骤1: 面板类定义
```cpp
// 继承SettingsPanel基类
class MyCustomPanel : public LA::Settings::SettingsPanel {
    Q_OBJECT
public:
    explicit MyCustomPanel(QWidget *parent = nullptr)
        : SettingsPanel(
            "my_panel",                    // 面板ID
            tr("我的面板"),                 // 显示名称
            tr("面板描述"),                 // 描述信息
            ":/icons/my_panel.png",        // 图标路径
            tr("自定义"),                   // 分类名称
            100,                           // 显示优先级
            parent
        ) {
        createUI();
        connectSignals();
        loadSettings();
    }
};
```

#### 步骤2: 面板注册到管理器
```cpp
// 在registerDefaultPanels()函数中添加
auto myPanel = std::make_shared<MyCustomPanel>();
if (!manager->registerPanel(myPanel)) {
    qWarning() << "Failed to register MyCustomPanel";
    success = false;
}
```

### 2. 自动加载到容器流程

#### 加载时序图
```
SettingsDialog::loadPanels() 
├── 1. 获取SettingsManager实例
├── 2. 获取所有分类: getAllCategories()
├── 3. 遍历每个分类
│   ├── 获取分类下的面板: getPanelsByCategory()
│   └── 遍历每个面板ID
│       ├── 获取面板实例: getPanel(panelId)
│       ├── 创建类别列表项: QListWidgetItem
│       └── ⭐ 添加到堆栈容器: m_panelStack->addWidget(panelWidget)
└── 4. 选择第一个面板显示
```

#### 关键代码位置
```cpp
// core/settings/src/Settings.cpp:480-487
QWidget *panelWidget = panel->getWidget();
if (panelWidget) {
    m_panelStack->addWidget(panelWidget);  // ⭐ 核心添加逻辑
    totalPanels++;
    qDebug() << "面板控件添加到堆栈，当前堆栈数量:" << m_panelStack->count();
}
```

## 面板切换机制

### 1. 切换触发
```cpp
// 类别列表选择变化触发
connect(m_categoryList, &QListWidget::currentRowChanged, 
        this, &SettingsDialog::onCategoryChanged);
```

### 2. 切换实现
```cpp
void SettingsDialog::onCategoryChanged(int row) {
    if (row >= 0 && row < m_panelStack->count()) {
        m_panelStack->setCurrentIndex(row);  // ⭐ 切换显示的面板
        
        // 确保新面板正确显示
        QWidget *currentWidget = m_panelStack->currentWidget();
        if (currentWidget) {
            currentWidget->setVisible(true);
            currentWidget->show();
            currentWidget->updateGeometry();
        }
    }
}
```

## 自定义面板添加指南

### 1. 创建新面板类
```cpp
// include/LA/Settings/panels/MyCustomPanel.h
class MyCustomPanel : public SettingsPanel {
    Q_OBJECT
public:
    explicit MyCustomPanel(QWidget *parent = nullptr);
    
protected:
    void createUI() override;
    void connectSignals() override;
    void loadSpecificSettings() override;
    void saveSpecificSettings() override;
    void resetSpecificSettings() override;
    bool validateSpecificSettings() override;
    void applySpecificSettings() override;
};
```

### 2. 实现面板逻辑
```cpp
// src/panels/MyCustomPanel.cpp
void MyCustomPanel::createUI() {
    QVBoxLayout *contentLayout = getContentLayout();
    
    // 添加自定义UI组件
    auto myWidget = new QLabel("自定义面板内容");
    contentLayout->addWidget(myWidget);
}
```

### 3. 注册到系统
```cpp
// 在Settings.cpp的registerDefaultPanels()中添加
auto myCustomPanel = std::make_shared<MyCustomPanel>();
if (!manager->registerPanel(myCustomPanel)) {
    qWarning() << "Failed to register MyCustomPanel";
    success = false;
}
```

### 4. 自动生效
面板注册后，系统会自动：
- 在类别列表中显示面板名称
- 将面板控件添加到`m_panelStack`容器
- 支持通过点击类别切换到该面板
- 处理面板的设置保存/加载

## 容器样式与布局

### 容器样式配置
```cpp
// QStackedWidget样式 - 去除边框的简洁设计
m_panelStack->setStyleSheet(
    "QStackedWidget {"
    "    background-color: transparent;"
    "    border: none;"
    "}"
);

// 设置容器边距
m_panelStack->setContentsMargins(5, 5, 5, 5);
```

### 布局参数
- **分割器比例**: 170px(类别列表) : 780px(面板容器)
- **最小宽度**: 面板容器600px，类别列表150px
- **容器边距**: 5px四周边距，确保内容不贴边

## 总结

LA设置模块通过`QStackedWidget`作为核心面板容器，配合`SettingsManager`的注册机制，实现了：

1. **自动化加载**: 注册的面板自动添加到显示容器
2. **灵活扩展**: 新面板只需继承基类并注册即可
3. **统一管理**: 所有面板通过统一的容器进行管理和切换
4. **简洁UI**: 去除边框的现代化界面设计

新增面板只需要继承`SettingsPanel`基类，实现必要方法，然后在`registerDefaultPanels()`中注册即可自动集成到设置界面中。