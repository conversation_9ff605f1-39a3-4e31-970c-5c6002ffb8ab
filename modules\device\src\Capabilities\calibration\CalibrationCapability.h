/**
 * @file CalibrationCapability.h
 * @brief 校准能力适配器 - 设备层与infrastructure/algorithm的桥接
 * 
 * Linus式设计原则：
 * - "最小模块原则" - 只负责校准能力，不涉及通信或UI
 * - "配置驱动" - 校准参数和流程通过配置定义
 * - "算法抽象" - 使用基础设施算法模块，而非内置算法
 */

#pragma once

#include "../communication/CommunicationCapability.h"
#include "infrastructure/algorithm/include/LA/Algorithm/IAlgorithmService.h"
#include <QObject>
#include <QVariantMap>
#include <QTimer>
#include <QVector>
#include <memory>

namespace LA::Device::Capability {

/**
 * @brief 校准步骤信息
 */
struct CalibrationStep {
    QString stepId;              // 步骤ID
    QString stepName;            // 步骤名称
    QString description;         // 步骤描述
    QVariantMap parameters;      // 步骤参数
    QVariantMap expectedResult;  // 期望结果
    int timeoutMs;              // 超时时间
    bool required;              // 是否必需
};

/**
 * @brief 校准流程配置
 */
struct CalibrationFlow {
    QString flowId;                    // 流程ID
    QString flowName;                  // 流程名称
    QVector<CalibrationStep> steps;    // 校准步骤列表
    QVariantMap globalParameters;      // 全局参数
    int totalTimeoutMs;               // 总超时时间
};

/**
 * @brief 校准结果
 */
struct CalibrationResult {
    bool success;                     // 是否成功
    QString flowId;                   // 流程ID
    QVariantMap calibrationData;      // 校准数据
    QVariantMap stepResults;          // 各步骤结果
    QString error;                    // 错误信息
    QDateTime timestamp;              // 时间戳
    double totalTime;                 // 总耗时(秒)
};

/**
 * @brief 校准能力适配器 - 与基础设施算法模块桥接
 * 
 * 职责：
 * - 提供设备校准能力接口
 * - 适配infrastructure/algorithm模块的算法服务
 * - 管理校准流程和步骤执行
 * - 处理校准数据的算法处理
 */
class CalibrationCapability : public IDeviceCapability {
    Q_OBJECT

public:
    explicit CalibrationCapability(QObject* parent = nullptr);
    virtual ~CalibrationCapability();

    // === IDeviceCapability接口实现 ===
    QString getCapabilityId() const override { return "device_calibration"; }
    QStringList getDependencies() const override { 
        return {"algorithm_service", "communication_capability", "data_sharing"}; 
    }
    
    bool initialize(const QVariantMap& config) override;
    QVariantMap executeCapability(const QString& action, const QVariantMap& params) override;
    void shutdown() override;
    void setInfrastructureServices(const QVariantMap& services) override;

    // === 校准专用接口 ===
    
    /**
     * @brief 执行校准流程
     * @param flowId 校准流程ID
     * @param parameters 校准参数
     * @return 校准结果
     */
    QVariantMap executeCalibrationFlow(const QString& flowId, const QVariantMap& parameters = {});
    
    /**
     * @brief 执行单个校准步骤
     * @param stepId 步骤ID
     * @param parameters 步骤参数
     * @return 步骤执行结果
     */
    QVariantMap executeCalibrationStep(const QString& stepId, const QVariantMap& parameters = {});
    
    /**
     * @brief 中断校准流程
     * @return 中断结果
     */
    QVariantMap abortCalibration();
    
    /**
     * @brief 验证校准结果
     * @param calibrationData 校准数据
     * @param referenceData 参考数据
     * @return 验证结果
     */
    QVariantMap validateCalibrationResult(const QVariantMap& calibrationData, const QVariantMap& referenceData = {});

    // === 校准流程管理 ===
    
    /**
     * @brief 加载校准流程配置
     * @param flowConfig 流程配置
     * @return 是否成功
     */
    bool loadCalibrationFlow(const CalibrationFlow& flowConfig);
    
    /**
     * @brief 获取支持的校准流程
     * @return 流程ID列表
     */
    QStringList getSupportedCalibrationFlows() const;
    
    /**
     * @brief 获取校准流程信息
     * @param flowId 流程ID
     * @return 流程信息
     */
    QVariantMap getCalibrationFlowInfo(const QString& flowId) const;
    
    /**
     * @brief 获取当前校准状态
     * @return 校准状态信息
     */
    QVariantMap getCurrentCalibrationStatus() const;

Q_SIGNALS:
    // === 校准流程信号 ===
    void calibrationFlowStarted(const QString& flowId);
    void calibrationFlowCompleted(const QString& flowId, const CalibrationResult& result);
    void calibrationFlowAborted(const QString& flowId, const QString& reason);
    
    // === 校准步骤信号 ===
    void calibrationStepStarted(const QString& stepId, const QString& stepName);
    void calibrationStepCompleted(const QString& stepId, const QVariantMap& result);
    void calibrationStepFailed(const QString& stepId, const QString& error);
    
    // === 校准进度信号 ===
    void calibrationProgress(const QString& flowId, int currentStep, int totalSteps, int percentage);
    void calibrationDataUpdated(const QVariantMap& calibrationData);

private slots:
    // === 算法服务事件处理 ===
    void onAlgorithmServiceResult(const QVariantMap& result);
    void onAlgorithmServiceError(const QString& error);
    
    // === 超时处理 ===
    void onCalibrationTimeout();

private:
    // === 基础设施服务注入 ===
    std::shared_ptr<LA::Algorithm::IAlgorithmService> m_algorithmService;           // 算法服务
    std::shared_ptr<CommunicationCapability> m_communicationCap;    // 通信能力
    QVariant m_dataSharingService;                                  // 数据共享服务
    
    // === 校准状态管理 ===
    bool m_initialized;
    bool m_calibrationInProgress;
    QString m_currentFlowId;
    int m_currentStepIndex;
    QDateTime m_calibrationStartTime;
    
    // === 校准配置 ===
    QVariantMap m_calibrationConfig;
    QMap<QString, CalibrationFlow> m_calibrationFlows;
    QVariantMap m_currentCalibrationData;
    
    // === 超时管理 ===
    QTimer* m_calibrationTimer;
    
    // === 结果缓存 ===
    QMap<QString, QVariantMap> m_stepResultCache;
    QVector<CalibrationResult> m_calibrationHistory;

private:
    // === 内部工具方法 ===
    
    /**
     * @brief 验证基础设施服务
     * @return 是否所有必需服务都可用
     */
    bool validateInfrastructureServices() const;
    
    /**
     * @brief 初始化默认校准流程
     */
    void initializeDefaultCalibrationFlows();
    
    /**
     * @brief 执行校准步骤的算法处理
     * @param step 校准步骤
     * @param stepData 步骤数据
     * @return 算法处理结果
     */
    QVariantMap processCalibrationAlgorithm(const CalibrationStep& step, const QVariantMap& stepData);
    
    /**
     * @brief 验证校准步骤结果
     * @param step 校准步骤
     * @param stepResult 步骤结果
     * @return 是否有效
     */
    bool validateStepResult(const CalibrationStep& step, const QVariantMap& stepResult);
    
    /**
     * @brief 构建错误结果
     * @param error 错误描述
     * @return 错误结果映射
     */
    QVariantMap createErrorResult(const QString& error) const;
    
    /**
     * @brief 构建成功结果
     * @param data 结果数据
     * @return 成功结果映射
     */
    QVariantMap createSuccessResult(const QVariantMap& data = {}) const;
    
    /**
     * @brief 保存校准数据到数据共享模块
     * @param calibrationData 校准数据
     * @return 是否成功
     */
    bool saveCalibrationData(const QVariantMap& calibrationData);
    
    /**
     * @brief 从数据共享模块加载校准数据
     * @param deviceId 设备ID
     * @return 校准数据
     */
    QVariantMap loadCalibrationData(const QString& deviceId);
};

/**
 * @brief SPRM特定校准流程工厂
 */
class SprmCalibrationFlowFactory {
public:
    /**
     * @brief 创建SPRM单点校准流程
     * @param referenceDistance 参考距离
     * @return 校准流程配置
     */
    static CalibrationFlow createSinglePointCalibration(double referenceDistance = 1000.0);
    
    /**
     * @brief 创建SPRM多点校准流程
     * @param referenceDistances 参考距离列表
     * @return 校准流程配置
     */
    static CalibrationFlow createMultiPointCalibration(const QVector<double>& referenceDistances);
    
    /**
     * @brief 创建SPRM交叉标定校准流程
     * @return 校准流程配置
     */
    static CalibrationFlow createCrossTalkCalibration();
    
    /**
     * @brief 创建SPRM偏移校准流程
     * @return 校准流程配置
     */
    static CalibrationFlow createOffsetCalibration();
    
    /**
     * @brief 获取所有SPRM支持的校准流程
     * @return 校准流程列表
     */
    static QVector<CalibrationFlow> getAllSprmCalibrationFlows();
};

/**
 * @brief 校准能力工厂
 */
class CalibrationCapabilityFactory {
public:
    /**
     * @brief 创建校准能力实例
     * @param deviceType 设备类型
     * @param config 配置参数
     * @return 校准能力实例
     */
    static std::unique_ptr<CalibrationCapability> createCapability(const QString& deviceType, 
                                                                  const QVariantMap& config = {});
    
    /**
     * @brief 创建带基础设施服务的校准能力
     * @param deviceType 设备类型
     * @param algorithmService 算法服务
     * @param communicationCap 通信能力
     * @return 校准能力实例
     */
    static std::unique_ptr<CalibrationCapability> createWithServices(
        const QString& deviceType,
        std::shared_ptr<LA::Algorithm::IAlgorithmService> algorithmService,
        std::shared_ptr<CommunicationCapability> communicationCap);
};

} // namespace LA::Device::Capability