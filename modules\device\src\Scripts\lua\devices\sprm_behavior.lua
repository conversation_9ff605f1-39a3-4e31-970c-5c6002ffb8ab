-- sprm_behavior.lua
-- SPRM设备行为脚本 - 业务逻辑可由非工程师调整

-- ============================================
-- SPRM设备行为配置 (可随时调整)
-- ============================================

-- 精度模式配置
local PRECISION_MODES = {
    high = {
        calibration_strategy = "multi_point",
        filter_strategy = "kalman", 
        measurement_count = 10,
        accuracy_threshold = 0.5
    },
    normal = {
        calibration_strategy = "single_point",
        filter_strategy = "moving_average",
        measurement_count = 3,
        accuracy_threshold = 1.0
    },
    fast = {
        calibration_strategy = "factory_default",
        filter_strategy = "none",
        measurement_count = 1,
        accuracy_threshold = 2.0
    }
}

-- 温度补偿配置 (可由现场工程师调整)
local TEMPERATURE_COMPENSATION = {
    enabled = true,
    ranges = {
        {min = -10, max = 10, laser_power_factor = 1.05},
        {min = 10, max = 40, laser_power_factor = 1.0},
        {min = 40, max = 60, laser_power_factor = 0.95},
        {min = 60, max = 80, laser_power_factor = 0.9}
    }
}

-- 校准点配置 (不同应用场景可调整)
local CALIBRATION_POINTS = {
    sprm_a1 = {50, 200, 500, 1000, 2000},
    sprm_a2 = {30, 100, 300, 800, 1500, 3000},
    sprm_a3 = {20, 50, 150, 400, 1000, 2500, 5000}
}

-- ============================================
-- 核心设备行为函数 (脚本化业务逻辑)  
-- ============================================

-- 测量开始事件处理
function onMeasurementStart(device, params)
    log("SPRM测量开始: " .. device:getId())
    
    -- 1. 根据精度要求选择模式
    local precision_mode = "normal"
    if params.accuracy_required and params.accuracy_required < 0.5 then
        precision_mode = "high"
    elseif params.speed_priority then
        precision_mode = "fast"  
    end
    
    local mode_config = PRECISION_MODES[precision_mode]
    log("选择精度模式: " .. precision_mode)
    
    -- 2. 设置对应的策略组合
    device:setStrategy("calibration", mode_config.calibration_strategy)
    device:setStrategy("filtering", mode_config.filter_strategy)
    
    -- 3. 应用温度补偿
    applyTemperatureCompensation(device)
    
    -- 4. 设置测量参数
    local measurement_params = {
        count = mode_config.measurement_count,
        accuracy_threshold = mode_config.accuracy_threshold,
        timeout = params.timeout or 5000
    }
    
    -- 5. 执行实际测量
    local result = device:execute("START_MEASURE", measurement_params)
    
    -- 6. 记录测量模式 (用于数据分析)
    if result.success then
        result.precision_mode = precision_mode
        result.temperature_compensated = TEMPERATURE_COMPENSATION.enabled
    end
    
    return result
end

-- 校准事件处理 
function onCalibration(device, step)
    local device_type = string.lower(device:getSpec("model"))
    local points = CALIBRATION_POINTS[device_type] or CALIBRATION_POINTS.sprm_a1
    
    log("SPRM校准步骤 " .. step .. "/" .. #points)
    
    if step <= #points then
        local distance = points[step]
        log("校准距离: " .. distance .. "mm")
        
        -- 可配置的校准逻辑
        local calibration_params = {
            target_distance = distance,
            measurement_count = 5,
            stability_threshold = 0.1,
            timeout = 10000
        }
        
        return device:measureAtDistance(distance, calibration_params)
    else
        log("校准完成，计算校准参数")
        return device:finishCalibration()
    end
end

-- 错误处理和恢复
function onDeviceError(device, error_type, error_data)
    log("SPRM设备错误: " .. error_type)
    
    -- 可配置的错误恢复策略
    local recovery_actions = {
        communication_timeout = function()
            log("通信超时，尝试重连")
            device:execute("RECONNECT")
            return {action = "reconnected", retry = true}
        end,
        
        measurement_unstable = function() 
            log("测量不稳定，切换到高精度模式")
            device:setStrategy("filtering", "kalman")
            device:setStrategy("calibration", "multi_point")
            return {action = "precision_mode_enabled", retry = true}
        end,
        
        laser_power_low = function()
            log("激光功率低，检查温度补偿")
            applyTemperatureCompensation(device)
            return {action = "temperature_compensated", retry = true}
        end,
        
        default = function()
            log("未知错误，执行设备重启")
            device:execute("RESTART")
            return {action = "device_restarted", retry = false}
        end
    }
    
    local recovery_func = recovery_actions[error_type] or recovery_actions.default
    return recovery_func()
end

-- 性能监控和优化
function onPerformanceCheck(device, metrics)
    log("SPRM性能检查")
    
    -- 可调整的性能优化规则
    local optimizations = {}
    
    -- 响应时间优化
    if metrics.avg_response_time > 1000 then
        table.insert(optimizations, {
            type = "speed_optimization",
            action = "reduce_measurement_count", 
            old_count = device:getStrategyParam("measurement_count"),
            new_count = math.max(1, device:getStrategyParam("measurement_count") - 1)
        })
    end
    
    -- 精度优化
    if metrics.accuracy_deviation > 2.0 then
        table.insert(optimizations, {
            type = "accuracy_optimization", 
            action = "enable_advanced_filtering",
            old_strategy = device:getStrategy("filtering"),
            new_strategy = "kalman"
        })
    end
    
    -- 应用优化
    for _, opt in ipairs(optimizations) do
        log("应用性能优化: " .. opt.action)
        applyOptimization(device, opt)
    end
    
    return {
        optimizations_applied = #optimizations,
        details = optimizations
    }
end

-- ============================================
-- 辅助函数 (业务逻辑支持)
-- ============================================

-- 应用温度补偿
function applyTemperatureCompensation(device)
    if not TEMPERATURE_COMPENSATION.enabled then
        return
    end
    
    local temperature = device:getEnvironment("temperature") or 25
    log("当前温度: " .. temperature .. "°C")
    
    -- 查找对应的温度范围
    for _, range in ipairs(TEMPERATURE_COMPENSATION.ranges) do
        if temperature >= range.min and temperature < range.max then
            local factor = range.laser_power_factor
            log("应用温度补偿因子: " .. factor)
            device:adjustLaserPower(factor)
            break
        end
    end
end

-- 应用性能优化
function applyOptimization(device, optimization)
    if optimization.type == "speed_optimization" then
        device:setStrategyParam("measurement_count", optimization.new_count)
    elseif optimization.type == "accuracy_optimization" then
        device:setStrategy("filtering", optimization.new_strategy)
    end
end

-- 日志输出 (可配置日志级别)
function log(message)
    -- 这里会调用C++的日志系统
    logMessage("SPRM_SCRIPT", message)
end

-- ============================================
-- 设备特定参数 (不同SPRM型号可定制)
-- ============================================

-- 获取设备相关配置
function getDeviceSpecificConfig(device)
    local model = device:getSpec("model")
    
    local configs = {
        ["SPRM-A1"] = {
            max_range = 2000,
            min_range = 50, 
            default_laser_power = 5.0,
            baudrate = 19200
        },
        ["SPRM-A2"] = {
            max_range = 5000,
            min_range = 30,
            default_laser_power = 8.0, 
            baudrate = 38400
        },
        ["SPRM-A3"] = {
            max_range = 10000,
            min_range = 20,
            default_laser_power = 12.0,
            baudrate = 115200
        }
    }
    
    return configs[model] or configs["SPRM-A1"]
end

-- 脚本版本和更新信息
SCRIPT_VERSION = "1.0.0"
SCRIPT_AUTHOR = "Device Team"
LAST_UPDATED = "2025-01-22"

log("SPRM行为脚本已加载 - 版本: " .. SCRIPT_VERSION)