#pragma once

/**
 * @file ICommunicationThreadLinus.h
 * @brief Linus式通信线程接口定义 - 最小接口设计
 * 
 * 遵循Linus Torvalds的设计哲学：
 * - "Good programmers worry about data structures" - 基于CommonTypes.h的数据结构
 * - 最小接口原则 - 只负责线程化的数据传输，不涉及协议、设备管理
 * - 单一职责 - 纯粹的异步通信线程
 * - Layer 1接口 - 基础的线程化通信抽象
 */

#include <QThread>
#include <QByteArray>
#include <QString>
#include "support/foundation/core/CommonTypes.h"

namespace LA {
namespace Communication {
namespace Thread {

// 使用Foundation层的标准类型
using namespace LA::Foundation::Core;

/**
 * @brief 纯粹的通信线程接口
 * 
 * Linus式设计原则:
 * - 最小接口: 只提供必需的线程化通信功能
 * - 无业务逻辑: 不涉及协议解析、设备管理、连接管理
 * - 可测试性: 每个方法都可以独立测试
 * - 可组合性: 为上层提供基础的异步通信能力
 */
class ICommunicationThreadLinus : public QThread {
    Q_OBJECT

public:
    explicit ICommunicationThreadLinus(QObject* parent = nullptr) : QThread(parent) {}
    virtual ~ICommunicationThreadLinus() = default;

    // === 线程控制 ===
    
    /**
     * @brief 启动通信线程
     * @return 操作结果
     */
    virtual SimpleResult startThread() = 0;
    
    /**
     * @brief 停止通信线程
     * @return 操作结果
     */
    virtual SimpleResult stopThread() = 0;
    
    /**
     * @brief 检查线程是否正在运行
     * @return 是否运行中
     */
    virtual bool isThreadRunning() const = 0;

    // === 数据传输 ===
    
    /**
     * @brief 发送数据
     * @param data 要发送的数据
     * @return 操作结果
     */
    virtual SimpleResult sendData(const QByteArray& data) = 0;
    
    /**
     * @brief 获取接收到的数据
     * @param maxBytes 最大字节数，-1表示读取所有
     * @return 接收到的数据
     */
    virtual QByteArray receiveData(qint64 maxBytes = -1) = 0;
    
    /**
     * @brief 检查是否有数据可读
     * @return 可读数据的字节数
     */
    virtual qint64 bytesAvailable() const = 0;
    
    /**
     * @brief 清空发送队列
     * @return 操作结果
     */
    virtual SimpleResult clearSendQueue() = 0;
    
    /**
     * @brief 清空接收缓冲区
     * @return 操作结果
     */
    virtual SimpleResult clearReceiveBuffer() = 0;

    // === 状态查询 ===
    
    /**
     * @brief 获取统计信息
     * @return 设备统计信息
     */
    virtual DeviceStatistics getStatistics() const = 0;
    
    /**
     * @brief 获取最后错误信息
     * @return 错误描述
     */
    virtual QString errorString() const = 0;
    
    /**
     * @brief 重置统计信息
     */
    virtual void resetStatistics() = 0;
    
    /**
     * @brief 获取发送队列大小
     * @return 队列中的数据包数量
     */
    virtual int getSendQueueSize() const = 0;
    
    /**
     * @brief 获取接收缓冲区大小
     * @return 缓冲区中的字节数
     */
    virtual int getReceiveBufferSize() const = 0;

    // === 配置管理 ===
    
    /**
     * @brief 设置线程优先级
     * @param priority Qt线程优先级
     * @return 操作结果
     */
    virtual SimpleResult setThreadPriority(QThread::Priority priority) = 0;
    
    /**
     * @brief 设置数据处理间隔
     * @param intervalMs 间隔毫秒数
     * @return 操作结果
     */
    virtual SimpleResult setProcessingInterval(int intervalMs) = 0;
    
    /**
     * @brief 设置最大队列大小
     * @param maxSize 最大队列大小
     * @return 操作结果
     */
    virtual SimpleResult setMaxQueueSize(int maxSize) = 0;

signals:
    /**
     * @brief 数据接收信号
     * @param data 接收到的数据
     */
    void dataReceived(const QByteArray& data);
    
    /**
     * @brief 数据发送完成信号
     * @param data 已发送的数据
     */
    void dataSent(const QByteArray& data);
    
    /**
     * @brief 线程状态变化信号
     * @param running 是否正在运行
     */
    void threadStateChanged(bool running);
    
    /**
     * @brief 错误发生信号
     * @param error 错误描述
     */
    void errorOccurred(const QString& error);
    
    /**
     * @brief 统计信息更新信号
     * @param statistics 统计信息
     */
    void statisticsUpdated(const DeviceStatistics& statistics);
};

/**
 * @brief 通信线程工厂接口
 * 
 * 用于创建不同类型的通信线程
 */
class ICommunicationThreadLinusFactory {
public:
    virtual ~ICommunicationThreadLinusFactory() = default;
    
    /**
     * @brief 创建通信线程实例
     * @return 通信线程实例的智能指针
     */
    virtual std::shared_ptr<ICommunicationThreadLinus> createCommunicationThread() = 0;
    
    /**
     * @brief 检查是否支持通信线程
     * @return 是否支持
     */
    virtual bool isSupported() const = 0;
};

} // namespace Thread
} // namespace Communication
} // namespace LA