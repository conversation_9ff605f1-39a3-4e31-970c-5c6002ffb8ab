- @docs\development\guidelines\development_guideline.md  @docs\development\development.md \
遵循项目原则，并查看当前项目 @docs\development\architecture\data_flow_architecture.md  @docs\development\architecture\device_port_registration.md 端口设备数据流架构相关模块完成情况。包括 @docs\development\modules\deviceSystem.md  @docs\development\modules\deviceManagement.md\
1. 先重构 @modules\device\src\Devices\Sensors\ProximitySensors\Sprm\Sprm.cpp设备，重构后设备 @modules\device\src\Devices\Sensors\ProximitySensors\Sprm\ModernSprmDevice.cpp ?\
2. @docs\development\MASTER_DEVELOPMENT_PLAN.md 添加重构计划并执行\
3. 按照linus原则，检查现有代码和架构是否可以优化