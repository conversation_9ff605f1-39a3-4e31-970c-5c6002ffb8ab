#include "LA/Communication/PortManagement/PortDiscoveryService.h"
#include <QDebug>
#include <QSerialPortInfo>

namespace LA {
namespace Communication {
namespace PortManagement {

PortDiscoveryService::PortDiscoveryService(QObject *parent) : QObject(parent), m_isMonitoring(false), m_monitoringTimer(new QTimer(this)) {
    m_monitoringTimer->setInterval(5000);  // 5秒监控间隔
    connect(m_monitoringTimer, &QTimer::timeout, this, &PortDiscoveryService::onMonitoringTimer);

    qDebug() << "PortDiscoveryService: Linus式端口发现服务已初始化";
}

QList<PortDiscoveryResult> PortDiscoveryService::discoverPorts() {
    qDebug() << "PortDiscoveryService: 开始发现端口";

    m_discoveredPorts.clear();

    // 扫描各种类型的端口
    scanSerialPorts();
    scanNetworkPorts();
    scanUSBPorts();

    qDebug() << "PortDiscoveryService: 发现完成，共找到" << m_discoveredPorts.size() << "个端口";

    return m_discoveredPorts;
}

QList<PortDiscoveryResult> PortDiscoveryService::discoverPortsByType(PortType type) {
    QList<PortDiscoveryResult> allPorts = discoverPorts();
    QList<PortDiscoveryResult> filteredPorts;

    for (const auto &port : allPorts) {
        if (port.portType == type) {
            filteredPorts.append(port);
        }
    }

    qDebug() << "PortDiscoveryService: 按类型过滤，找到" << filteredPorts.size() << "个端口";
    return filteredPorts;
}

PortStatus PortDiscoveryService::getPortStatus(const QString &portName) {
    return detectPortStatus(portName);
}

bool PortDiscoveryService::isPortAvailable(const QString &portName) {
    return getPortStatus(portName) == PortStatus::Available;
}

PortCapability PortDiscoveryService::getPortCapability(const QString &portName) {
    return detectPortCapability(portName);
}

QStringList PortDiscoveryService::getSupportedBaudRates(const QString &portName) {
    PortCapability capability = getPortCapability(portName);
    return capability.supportedBaudRates;
}

void PortDiscoveryService::startPortMonitoring() {
    if (!m_isMonitoring) {
        m_isMonitoring = true;
        m_monitoringTimer->start();
        qDebug() << "PortDiscoveryService: 开始端口监控";
    }
}

void PortDiscoveryService::stopPortMonitoring() {
    if (m_isMonitoring) {
        m_isMonitoring = false;
        m_monitoringTimer->stop();
        qDebug() << "PortDiscoveryService: 停止端口监控";
    }
}

void PortDiscoveryService::scanSerialPorts() {
    qDebug() << "PortDiscoveryService: 扫描串口端口";

    QList<QSerialPortInfo> availablePorts = QSerialPortInfo::availablePorts();

    for (const QSerialPortInfo &portInfo : availablePorts) {
        PortDiscoveryResult result;
        result.portName       = portInfo.portName();
        result.portType       = PortType::Serial;
        result.description    = portInfo.description();
        result.manufacturer   = portInfo.manufacturer();
        result.location       = portInfo.systemLocation();
        result.status         = detectPortStatus(portInfo.portName());
        result.capability     = detectPortCapability(portInfo.portName());
        result.discoveredTime = QDateTime::currentDateTime();

        // 设置端口属性
        result.properties["vendorId"]             = portInfo.vendorIdentifier();
        result.properties["productId"]            = portInfo.productIdentifier();
        result.properties["serialNumber"]         = portInfo.serialNumber();
        result.properties["hasVendorIdentifier"]  = portInfo.hasVendorIdentifier();
        result.properties["hasProductIdentifier"] = portInfo.hasProductIdentifier();

        m_discoveredPorts.append(result);
        emit portDiscovered(result);

        qDebug() << "PortDiscoveryService: 发现串口端口" << result.portName << "状态:" << (int)result.status;
    }
}

void PortDiscoveryService::scanNetworkPorts() {
    qDebug() << "PortDiscoveryService: 扫描网络端口 (暂未实现)";
    // TODO: 实现网络端口扫描
}

void PortDiscoveryService::scanUSBPorts() {
    qDebug() << "PortDiscoveryService: 扫描USB端口 (暂未实现)";
    // TODO: 实现USB端口扫描
}

PortStatus PortDiscoveryService::detectPortStatus(const QString &portName) {
    // Linus: "简单实用的端口状态检测"
    QList<QSerialPortInfo> availablePorts = QSerialPortInfo::availablePorts();

    for (const QSerialPortInfo &portInfo : availablePorts) {
        if (portInfo.portName() == portName) {
            // 简单检测：如果端口存在就认为可用
            return PortStatus::Available;
        }
    }

    return PortStatus::Disconnected;
}

PortCapability PortDiscoveryService::detectPortCapability(const QString &portName) {
    Q_UNUSED(portName)

    // Linus: "提供合理的默认能力"
    PortCapability capability;
    capability.supportedBaudRates = QStringList() << "9600"
                                                  << "19200"
                                                  << "38400"
                                                  << "57600"
                                                  << "115200";
    capability.supportedDataBits = QStringList() << "5"
                                                 << "6"
                                                 << "7"
                                                 << "8";
    capability.supportedStopBits = QStringList() << "1"
                                                 << "1.5"
                                                 << "2";
    capability.supportedParity = QStringList() << "None"
                                               << "Even"
                                               << "Odd"
                                               << "Space"
                                               << "Mark";
    capability.supportsFlowControl = true;
    capability.maxBufferSize       = 4096;

    return capability;
}

void PortDiscoveryService::onMonitoringTimer() {
    if (!m_isMonitoring) {
        return;
    }

    // 检查端口变化
    QList<PortDiscoveryResult> currentPorts = discoverPorts();
    QStringList                currentPortNames;

    for (const auto &port : currentPorts) {
        currentPortNames << port.portName;
    }

    // 检查之前发现的端口
    QStringList previousPortNames;
    for (const auto &port : m_discoveredPorts) {
        previousPortNames << port.portName;
    }

    // 检查新增的端口
    for (const QString &portName : currentPortNames) {
        if (!previousPortNames.contains(portName)) {
            qDebug() << "PortDiscoveryService: 检测到新端口" << portName;
            // 找到对应的端口结果并发送信号
            for (const auto &port : currentPorts) {
                if (port.portName == portName) {
                    emit portDiscovered(port);
                    break;
                }
            }
        }
    }

    // 检查移除的端口
    for (const QString &portName : previousPortNames) {
        if (!currentPortNames.contains(portName)) {
            qDebug() << "PortDiscoveryService: 检测到端口移除" << portName;
            emit portRemoved(portName);
        }
    }

    // 更新端口列表
    m_discoveredPorts = currentPorts;
}

}  // namespace PortManagement
}  // namespace Communication
}  // namespace LA
