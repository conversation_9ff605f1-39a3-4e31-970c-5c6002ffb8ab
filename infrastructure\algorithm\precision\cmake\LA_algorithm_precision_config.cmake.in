# LA Algorithm Precision Library Config File
# 为使用者提供库配置信息

@PACKAGE_INIT@

include("${CMAKE_CURRENT_LIST_DIR}/LA_algorithm_precision_targets.cmake")

# 检查所需组件是否可用
check_required_components(LA_algorithm_precision)

# 设置变量供使用者使用
set(LA_ALGORITHM_PRECISION_VERSION "@LA_ALGORITHM_PRECISION_VERSION@")
set(LA_ALGORITHM_PRECISION_INCLUDE_DIRS "@CMAKE_INSTALL_PREFIX@/include")
set(LA_ALGORITHM_PRECISION_LIBRARIES LA::Algorithm::Precision)

# 提供简便的包含路径
get_target_property(_LA_ALG_PRECISION_INCLUDE_DIRS LA::Algorithm::Precision INTERFACE_INCLUDE_DIRECTORIES)
set(LA_ALGORITHM_PRECISION_INCLUDE_DIRS "${_LA_ALG_PRECISION_INCLUDE_DIRS}")

# 兼容性设置
set(LA_Algorithm_Precision_FOUND TRUE)