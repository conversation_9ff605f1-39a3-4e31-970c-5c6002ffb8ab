@echo off
REM SPRM设备指令测试运行脚本
REM 遵循test_guideline.md规范

echo ========================================
echo    SPRM设备指令单元测试
echo ========================================
echo.

REM 设置工作目录
cd /d "%~dp0\.."

REM 编译测试
echo 正在编译SPRM测试程序...
cmake --build build --config Debug --target sprm_device_commands_test
if errorlevel 1 (
    echo 编译失败！
    pause
    exit /b 1
)

echo 编译成功！
echo.

REM 运行测试
echo 开始运行SPRM设备指令测试...
echo ----------------------------------------
build\tests\unit\sprm_device_commands_test.exe

REM 检查测试结果
if errorlevel 1 (
    echo.
    echo ❌ 测试失败！
) else (
    echo.
    echo ✅ 测试通过！
)

echo.
echo 测试完成。
pause