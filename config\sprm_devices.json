{"Nova-A1": {"category": "RangingSensor", "device_type": "SPRM", "manufacturer": "Nova", "model": "Nova-A1", "version": "1.0.0", "laser_wavelength": "650nm", "laser_power": "5mW", "receiver_type": "APD", "communication": "RS485", "baudrate": 19200, "data_bits": 8, "stop_bits": 1, "parity": "none", "timeout": 5000, "retry_count": 3, "protocol": "MODEL_SIMPLE_CHECKSUM_P", "calibration": "Quick", "measurement_range": [50, 2000], "accuracy": 1.0, "resolution": 0.1, "sampling_rate": 100, "enable_logging": true, "auto_calibration": false, "commands": ["CALIB1", "CALIB2", "SELF_ADAPT", "LED_IO", "PROC_MODE", "DATA_MODE", "QUERY_DIST", "VERSION", "PARAM", "CHIP_ID", "DDS_LOG"], "command_codes": {"CALIB1": 1, "CALIB2": 2, "SELF_ADAPT": 5, "LED_IO": 6, "PROC_MODE": 7, "DATA_MODE": 8, "QUERY_DIST": 10, "VERSION": 11, "PARAM": 12, "CHIP_ID": 13, "DDS_LOG": 14}, "modes": {"TestMode": 0, "CalibrationMode": 2, "OptimizationMode": 5, "SingleRangeMode": 1, "StandbyMode": 4, "WorkMode": 3}, "led_modes": {"Close": 1, "Open": 2, "Breath": 3, "Flicker": 4}, "data_output_modes": {"RAW": 0, "FILTERED": 1, "AVERAGED": 2}}, "Nova-A2": {"category": "RangingSensor", "device_type": "SPRM", "manufacturer": "Nova", "model": "Nova-A2", "version": "2.0.0", "laser_wavelength": "780nm", "laser_power": "8mW", "receiver_type": "APD", "communication": "RS485", "baudrate": 19200, "data_bits": 8, "stop_bits": 1, "parity": "none", "timeout": 3000, "retry_count": 3, "protocol": "SPRM_SIMPLE_XOR_P", "calibration": "Precision", "measurement_range": [30, 5000], "accuracy": 0.5, "resolution": 0.05, "sampling_rate": 200, "enable_logging": true, "auto_calibration": true, "commands": ["CALIB1", "CALIB2", "CALIB3", "CALIB4", "PROC_MODE", "LED_IO", "DATA_MODE", "QUERY_DIST", "VERSION", "PARAM", "CHIP_ID"], "command_codes": {"CALIB1": 1, "CALIB2": 2, "CALIB3": 3, "CALIB4": 4, "PROC_MODE": 7, "LED_IO": 6, "DATA_MODE": 8, "QUERY_DIST": 10, "VERSION": 11, "PARAM": 12, "CHIP_ID": 13}, "modes": {"NORMAL": "0xA5F1", "CONTINUOUS": "0xA5F5", "TRIGGER": "0xA5F6", "CALIBRATION": "0xA5F3", "HIGH_PRECISION": "0xA5F4", "WORK": "0xA5F8"}, "led_modes": {"OFF": 0, "ON": 1, "BLINK": 2, "AUTO": 3, "PULSE": 4}, "data_output_modes": {"RAW": 0, "FILTERED": 1, "AVERAGED": 2, "KALMAN_FILTERED": 3}}, "Nova-A1B": {"category": "RangingSensor", "device_type": "SPRM", "manufacturer": "Nova", "model": "Nova-A1B", "version": "1.2.0", "laser_wavelength": "650nm", "laser_power": "5mW", "receiver_type": "APD", "communication": "RS485", "baudrate": 19200, "data_bits": 8, "stop_bits": 1, "parity": "none", "timeout": 5000, "retry_count": 3, "protocol": "SPRM_BESTER_P", "calibration": "Quick", "measurement_range": [50, 2000], "accuracy": 1.0, "resolution": 0.1, "sampling_rate": 100, "enable_logging": true, "auto_calibration": false, "commands": ["CALIB1", "CALIB2", "PROC_MODE", "LED_IO", "DATA_MODE", "EXPAND_DIST", "VERSION", "PARAM", "CHIP_ID", "DDS_LOG"], "command_codes": {"CALIB1": 1, "CALIB2": 2, "PROC_MODE": 7, "LED_IO": 6, "DATA_MODE": 8, "EXPAND_DIST": 10, "VERSION": 11, "PARAM": 12, "CHIP_ID": 13, "DDS_LOG": 14}, "modes": {"TestMode": 0, "CalibrationMode": 2, "OptimizationMode": 5, "SingleRangeMode": 1, "StandbyMode": 4, "WorkMode": 3}, "led_modes": {"Close": 1, "Open": 2, "Breath": 3, "Flicker": 4}, "data_output_modes": {"RAW": 0, "FILTERED": 1, "AVERAGED": 2}}, "Nova-A1C": {"category": "RangingSensor", "device_type": "SPRM", "manufacturer": "Nova", "model": "Nova-A1C", "version": "1.3.0", "laser_wavelength": "650nm", "laser_power": "5mW", "receiver_type": "APD", "communication": "RS485", "baudrate": 19200, "data_bits": 8, "stop_bits": 1, "parity": "none", "timeout": 5000, "retry_count": 3, "protocol": "SPRM_BESTER_P", "calibration": "Quick", "measurement_range": [50, 2000], "accuracy": 1.0, "resolution": 0.1, "sampling_rate": 100, "enable_logging": true, "auto_calibration": false, "commands": ["CALIB1", "CALIB2", "PROC_MODE", "LED_IO", "DATA_MODE", "EXPAND_DIST", "VERSION", "PARAM", "CHIP_ID", "DDS_LOG"], "command_codes": {"CALIB1": 1, "CALIB2": 2, "PROC_MODE": 7, "LED_IO": 6, "DATA_MODE": 8, "EXPAND_DIST": 10, "VERSION": 11, "PARAM": 12, "CHIP_ID": 13, "DDS_LOG": 14}, "modes": {"TestMode": 0, "CalibrationMode": 2, "OptimizationMode": 5, "SingleRangeMode": 1, "StandbyMode": 4, "WorkMode": 3}, "led_modes": {"Close": 1, "Open": 2, "Breath": 3, "Flicker": 4}, "data_output_modes": {"RAW": 0, "FILTERED": 1, "AVERAGED": 2}}, "Nova-A1D": {"category": "RangingSensor", "device_type": "SPRM", "manufacturer": "Nova", "model": "Nova-A1D", "version": "1.4.0", "laser_wavelength": "650nm", "laser_power": "5mW", "receiver_type": "APD", "communication": "RS485", "baudrate": 19200, "data_bits": 8, "stop_bits": 1, "parity": "none", "timeout": 5000, "retry_count": 3, "protocol": "MODEL_SIMPLE_CHECKSUM_P", "calibration": "Quick", "measurement_range": [50, 2000], "accuracy": 1.0, "resolution": 0.1, "sampling_rate": 100, "enable_logging": true, "auto_calibration": false, "commands": ["CALIB1", "CALIB2", "SELF_ADAPT", "LED_IO", "PROC_MODE", "DATA_MODE", "QUERY_DIST", "VERSION", "PARAM", "CHIP_ID", "DDS_LOG"], "command_codes": {"CALIB1": 1, "CALIB2": 2, "SELF_ADAPT": 5, "LED_IO": 6, "PROC_MODE": 7, "DATA_MODE": 8, "QUERY_DIST": 10, "VERSION": 11, "PARAM": 12, "CHIP_ID": 13, "DDS_LOG": 14}, "modes": {"TestMode": 0, "CalibrationMode": 2, "OptimizationMode": 5, "SingleRangeMode": 1, "StandbyMode": 4, "WorkMode": 3}, "led_modes": {"Close": 1, "Open": 2, "Breath": 3, "Flicker": 4}, "data_output_modes": {"RAW": 0, "FILTERED": 1, "AVERAGED": 2}}, "Nova-A1H": {"category": "RangingSensor", "device_type": "SPRM", "manufacturer": "Nova", "model": "Nova-A1H", "version": "1.5.0", "laser_wavelength": "650nm", "laser_power": "5mW", "receiver_type": "APD", "communication": "RS485", "baudrate": 19200, "data_bits": 8, "stop_bits": 1, "parity": "none", "timeout": 5000, "retry_count": 3, "protocol": "MODEL_SIMPLE_CHECKSUM_P", "calibration": "High Precision", "measurement_range": [50, 2000], "accuracy": 0.5, "resolution": 0.05, "sampling_rate": 100, "enable_logging": true, "auto_calibration": false, "commands": ["CALIB1", "CALIB2", "CHIP_ID", "DATA_MODE", "DDS_LOG", "LOG_IO", "MODIFY_PARAM", "QUERY_DIST", "PARAM_READ", "VERSION"], "command_codes": {"CALIB1": 1, "CALIB2": 2, "CHIP_ID": 13, "DATA_MODE": 8, "DDS_LOG": 14, "LOG_IO": 9, "MODIFY_PARAM": 10, "QUERY_DIST": 181, "PARAM_READ": 12, "VERSION": 11}, "modes": {"TestMode": 0, "CalibrationMode": 2, "OptimizationMode": 5, "SingleRangeMode": 1, "StandbyMode": 4, "WorkMode": 3}, "led_modes": {"Close": 1, "Open": 2, "Breath": 3, "Flicker": 4}, "data_output_modes": {"RAW": 0, "FILTERED": 1, "AVERAGED": 2}}, "Nova-A3": {"category": "RangingSensor", "device_type": "SPRM", "manufacturer": "Nova", "model": "Nova-A3", "version": "3.0.0", "laser_wavelength": "940nm", "laser_power": "10mW", "receiver_type": "SPAD", "communication": "RS485", "baudrate": 19200, "data_bits": 8, "stop_bits": 1, "parity": "none", "timeout": 3000, "retry_count": 3, "protocol": "SPRM_SIMPLE_XOR_P", "calibration": "Advanced", "measurement_range": [30, 8000], "accuracy": 0.3, "resolution": 0.01, "sampling_rate": 500, "enable_logging": true, "auto_calibration": true, "commands": ["CALIB1", "FAST_MODE", "GET_STATUS", "VERSION"], "command_codes": {"CALIB1": 1, "FAST_MODE": 15, "GET_STATUS": 4, "VERSION": 11}, "modes": {"NORMAL": "0xA5F1", "FAST": "0xA5F9", "HIGH_PRECISION": "0xA5F4"}, "led_modes": {"OFF": 0, "ON": 1, "AUTO": 3}, "data_output_modes": {"RAW": 0, "FILTERED": 1, "HIGH_PRECISION": 3}}}