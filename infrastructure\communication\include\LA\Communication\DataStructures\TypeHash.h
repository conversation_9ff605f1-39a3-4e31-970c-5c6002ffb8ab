#pragma once

/**
 * @file TypeHash.h
 * @brief 数据结构类型的哈希函数声明
 * 
 * 为QMap和QHash提供枚举类型的哈希支持
 */

#include <QtCore/qglobal.h>
#include <QtCore/qhash.h>

namespace LA {
namespace Communication {
namespace DataStructures {

// 前向声明枚举类型
enum class DeviceType;
enum class DeviceState;
enum class CommunicationDependencyType;
enum class ProtocolType;
enum class PortType;
enum class ConnectionState;

} // namespace DataStructures
} // namespace Communication
} // namespace LA

// 为枚举类型提供qHash函数
inline uint qHash(LA::Communication::DataStructures::DeviceType key, uint seed = 0)
{
    return qHashBits(&key, sizeof(key), seed);
}

inline uint qHash(LA::Communication::DataStructures::DeviceState key, uint seed = 0)
{
    return qHashBits(&key, sizeof(key), seed);
}

inline uint qHash(LA::Communication::DataStructures::CommunicationDependencyType key, uint seed = 0)
{
    return qHashBits(&key, sizeof(key), seed);
}

inline uint qHash(LA::Communication::DataStructures::ProtocolType key, uint seed = 0)
{
    return qHashBits(&key, sizeof(key), seed);
}

inline uint qHash(LA::Communication::DataStructures::PortType key, uint seed = 0)
{
    return qHashBits(&key, sizeof(key), seed);
}

inline uint qHash(LA::Communication::DataStructures::ConnectionState key, uint seed = 0)
{
    return qHashBits(&key, sizeof(key), seed);
}