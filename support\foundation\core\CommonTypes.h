#pragma once

#include <QByteArray>
#include <QMap>
#include <QString>
#include <QVariant>
#include <QVector>
#include <functional>
#include <memory>

namespace LA {
namespace Foundation {
namespace Core {

// ===============================================
// Foundation层职责：提供纯抽象类型定义
// ✅ 只定义接口，不包含任何业务逻辑
// ✅ 作为各层模块间的契约
// ❌ 绝不依赖上层业务模块
// ===============================================

/**
 * @brief 通用结果类型
 */
enum class ResultCode {
    Success              = 0,   // 成功
    Failed               = 1,   // 失败
    Timeout              = 2,   // 超时
    InvalidParameter     = 3,   // 无效参数
    NotSupported         = 4,   // 不支持
    NotConnected         = 5,   // 未连接
    Busy                 = 6,   // 忙碌
    NotInitialized       = 7,   // 未初始化
    PermissionDenied     = 8,   // 权限拒绝
    ResourceNotAvailable = 9,   // 资源不可用
    UnknownError         = 999  // 未知错误
};

/**
 * @brief 操作结果模板类
 */
template <typename T> class Result {
  public:
    Result() : code_(ResultCode::Failed) {
    }
    Result(ResultCode code) : code_(code) {
    }
    Result(const T &value) : code_(ResultCode::Success), value_(value) {
    }
    Result(T &&value) : code_(ResultCode::Success), value_(std::move(value)) {
    }
    Result(ResultCode code, const QString &message) : code_(code), message_(message) {
    }

    bool isSuccess() const {
        return code_ == ResultCode::Success;
    }
    bool isFailed() const {
        return code_ != ResultCode::Success;
    }

    ResultCode code() const {
        return code_;
    }
    QString message() const {
        return message_;
    }

    const T &value() const {
        return value_;
    }
    T &value() {
        return value_;
    }

    void setCode(ResultCode code) {
        code_ = code;
    }
    void setMessage(const QString &message) {
        message_ = message;
    }
    void setValue(const T &value) {
        value_ = value;
        code_  = ResultCode::Success;
    }

    // 静态工厂方法
    static Result<T> success(const T &value) {
        return Result<T>(value);
    }

    static Result<T> failure(const QString &message, ResultCode code = ResultCode::Failed) {
        return Result<T>(code, message);
    }

    static Result<T> error(const QString &message, ResultCode code = ResultCode::Failed) {
        return Result<T>(code, message);
    }

  private:
    ResultCode code_;
    QString    message_;
    T          value_;
};

/**
 * @brief 简单结果类型（无返回值）
 */
using SimpleResult = Result<bool>;

/**
 * @brief 字符串结果类型
 */
using StringResult = Result<QString>;

/**
 * @brief 字节数组结果类型
 */
using ByteArrayResult = Result<QByteArray>;

/**
 * @brief 变体结果类型
 */
using VariantResult = Result<::QVariant>;

/**
 * @brief 通用配置参数类型
 */
using ConfigParameters = QMap<QString, ::QVariant>;

/**
 * @brief 通用属性映射类型
 */
using PropertyMap = QMap<QString, ::QVariant>;

/**
 * @brief 通用状态信息
 */
struct StatusInfo {
    QString    name;         // 状态名称
    ::QVariant value;        // 状态值
    QString    unit;         // 单位
    QString    description;  // 描述
    qint64     timestamp;    // 时间戳

    StatusInfo() : name(""), value(::QVariant()), unit(""), description(""), timestamp(0) {
    }
    StatusInfo(const QString &n, const ::QVariant &v, const QString &u = "", const QString &desc = "", qint64 ts = 0)
        : name(n), value(v), unit(u), description(desc), timestamp(ts) {
    }
};

/**
 * @brief 状态信息列表
 */
using StatusInfoList = QVector<StatusInfo>;

/**
 * @brief 通用事件信息
 */
struct EventInfo {
    QString    eventType;    // 事件类型
    QString    source;       // 事件源
    ::QVariant data;         // 事件数据
    qint64     timestamp;    // 时间戳
    QString    description;  // 描述

    EventInfo() : eventType(""), source(""), data(::QVariant()), timestamp(0), description("") {
    }
    EventInfo(const QString &type, const QString &src, const ::QVariant &d = ::QVariant(), qint64 ts = 0, const QString &desc = "")
        : eventType(type), source(src), data(d), timestamp(ts), description(desc) {
    }
};

/**
 * @brief 通用错误信息
 */
struct ErrorInfo {
    ResultCode code;       // 错误代码
    QString    message;    // 错误消息
    QString    source;     // 错误源
    qint64     timestamp;  // 时间戳
    ::QVariant context;    // 上下文信息

    ErrorInfo() : code(ResultCode::UnknownError), message(""), source(""), timestamp(0), context(::QVariant()) {
    }
    ErrorInfo(ResultCode c, const QString &msg, const QString &src = "", qint64 ts = 0, const ::QVariant &ctx = ::QVariant())
        : code(c), message(msg), source(src), timestamp(ts), context(ctx) {
    }
};

/**
 * @brief 版本信息
 */
struct VersionInfo {
    int     major;  // 主版本号
    int     minor;  // 次版本号
    int     patch;  // 补丁版本号
    QString build;  // 构建信息

    VersionInfo() : major(0), minor(0), patch(0), build("") {
    }
    VersionInfo(int maj, int min, int pat, const QString &b = "") : major(maj), minor(min), patch(pat), build(b) {
    }

    // 从字符串构造版本信息 - 兼容性构造函数
    VersionInfo(const QString &versionString) : major(0), minor(0), patch(0), build("") {
        parseVersionString(versionString);
    }

    // 从字符串赋值操作符 - 兼容性操作符
    VersionInfo &operator=(const QString &versionString) {
        parseVersionString(versionString);
        return *this;
    }

    // 解析版本字符串 - 私有辅助函数
    void parseVersionString(const QString &versionString) {
        // 简单的版本字符串解析，支持格式如 "1.2.3" 或 "1.2.3-build"
        QStringList parts = versionString.split('.');
        if (parts.size() >= 1)
            major = parts[0].toInt();
        if (parts.size() >= 2)
            minor = parts[1].toInt();
        if (parts.size() >= 3) {
            QString patchPart = parts[2];
            int     dashIndex = patchPart.indexOf('-');
            if (dashIndex >= 0) {
                patch = patchPart.left(dashIndex).toInt();
                build = patchPart.mid(dashIndex + 1);
            } else {
                patch = patchPart.toInt();
            }
        }
    }

    QString toString() const {
        QString version = QString("%1.%2.%3").arg(major).arg(minor).arg(patch);
        if (!build.isEmpty()) {
            version += QString("-%1").arg(build);
        }
        return version;
    }

    bool operator==(const VersionInfo &other) const {
        return major == other.major && minor == other.minor && patch == other.patch && build == other.build;
    }

    bool operator<(const VersionInfo &other) const {
        if (major != other.major)
            return major < other.major;
        if (minor != other.minor)
            return minor < other.minor;
        if (patch != other.patch)
            return patch < other.patch;
        return build < other.build;
    }
};

/**
 * @brief 通用回调函数类型
 */
template <typename... Args> using Callback = std::function<void(Args...)>;

/**
 * @brief 状态变化回调
 */
using StatusCallback = Callback<const StatusInfo &>;

/**
 * @brief 事件回调
 */
using EventCallback = Callback<const EventInfo &>;

/**
 * @brief 错误回调
 */
using ErrorCallback = Callback<const ErrorInfo &>;

/**
 * @brief 进度回调
 */
using ProgressCallback = Callback<int, const QString &>;  // 进度百分比, 描述

/**
 * @brief 数据回调
 */
using DataCallback = Callback<const QByteArray &>;

/**
 * @brief 通用工厂接口模板
 */
template <typename T> class IFactory {
  public:
    virtual ~IFactory()                                                                         = default;
    virtual std::shared_ptr<T> create(const QString &type, const ConfigParameters &params = {}) = 0;
    virtual QStringList        getSupportedTypes() const                                        = 0;
    virtual bool               isTypeSupported(const QString &type) const                       = 0;
};

/**
 * @brief 通用管理器接口
 */
class IManager {
  public:
    virtual ~IManager()                                                    = default;
    virtual SimpleResult   initialize(const ConfigParameters &params = {}) = 0;
    virtual SimpleResult   shutdown()                                      = 0;
    virtual bool           isInitialized() const                           = 0;
    virtual StatusInfoList getStatus() const                               = 0;
    virtual VersionInfo    getVersion() const                              = 0;
};

/**
 * @brief 通用组件接口
 */
class IComponent {
  public:
    virtual ~IComponent()                        = default;
    virtual QString     getName() const          = 0;
    virtual QString     getDescription() const   = 0;
    virtual VersionInfo getVersion() const       = 0;
    virtual bool        isEnabled() const        = 0;
    virtual void        setEnabled(bool enabled) = 0;
};

/**
 * @brief 通用服务接口
 */
class IService : public IManager, public IComponent {
  public:
    virtual ~IService()                  = default;
    virtual QString getServiceId() const = 0;
    virtual int     getPriority() const  = 0;
};

/**
 * @brief 设备类型枚举 - Foundation层统一定义
 *
 * 🎯 Linus原则：设备分类要基于功能而非实现
 * ✅ 职责：定义系统支持的所有设备类型
 * ❌ 不涉及：具体设备实现、通讯协议
 */
enum class DeviceType {
    Unknown = 0,  // 未知设备

    // === 传感器类设备 ===
    Sensor            = 100,  // 通用传感器
    TemperatureSensor = 101,  // 温度传感器
    PressureSensor    = 102,  // 压力传感器
    YJSensor          = 103,  // YJ传感器
    CoinSensor        = 104,  // 硬币传感器

    // === 执行器类设备 ===
    Actuator = 200,  // 通用执行器
    Motor    = 201,  // 电机
    Valve    = 202,  // 阀门
    Pump     = 203,  // 泵

    // === 测试设备 ===
    TestBoard   = 300,  // 测试板
    BottomBoard = 301,  // 底板
    Fixture     = 302,  // 夹具

    // === 扫描设备 ===
    Scanner     = 400,  // 通用扫描器
    NovaScanner = 401,  // Nova扫描器
    Barcode     = 402,  // 条码扫描器

    // === 工业控制设备 ===
    PLC          = 500,  // PLC控制器
    ModbusDevice = 501,  // Modbus设备
    CanDevice    = 502,  // CAN设备

    // === 客户定制设备 ===
    BesterDevice   = 600,  // Bester设备
    HeliDevice     = 601,  // Heli设备
    HuayuanDevice  = 602,  // 华源设备
    YoushengDevice = 603,  // 友声设备

    // === 通用设备 ===
    Generic = 900,  // 通用设备
    Virtual = 999   // 虚拟设备
};

/**
 * @brief 端口类型枚举 - Foundation层统一定义
 *
 * 🎯 Linus原则：单一来源，所有模块使用此定义
 * ✅ 职责：定义系统支持的所有端口类型
 * ❌ 不涉及：具体端口实现、设备业务逻辑
 */
enum class PortType {
    Unknown   = 0,   // 未知端口
    Serial    = 1,   // 串口（包含COM口）
    TCP       = 2,   // TCP网络端口
    UDP       = 3,   // UDP网络端口
    Network   = 4,   // 网络端口（通用）
    USB       = 5,   // USB端口
    CAN       = 6,   // CAN总线
    I2C       = 7,   // I2C总线
    SPI       = 8,   // SPI总线
    Bluetooth = 9,   // 蓝牙端口
    Ethernet  = 10,  // 以太网端口
    WiFi      = 11,  // WiFi端口
    Virtual   = 12   // 虚拟端口
};

/**
 * @brief 端口状态枚举 - Foundation层统一定义
 *
 * 🎯 Linus原则：状态定义要完整且互斥
 */
enum class PortStatus {
    Unknown      = 0,  // 未知状态
    Available    = 1,  // 可用
    Busy         = 2,  // 忙碌
    Connected    = 3,  // 已连接
    Disconnected = 4,  // 已断开
    Error        = 5,  // 错误状态
    Removed      = 6,  // 已移除
    Disabled     = 7   // 禁用
};

/**
 * @brief 端口事件枚举
 */
enum class PortEvent {
    Added        = 0,  // 端口添加
    Removed      = 1,  // 端口移除
    Opened       = 2,  // 端口打开
    Closed       = 3,  // 端口关闭
    Error        = 4,  // 端口错误
    DataReceived = 5   // 数据接收
};

/**
 * @brief 连接状态枚举
 */
enum class ConnectionStatus {
    Disconnected = 0,  // 未连接
    Connecting   = 1,  // 连接中
    Connected    = 2,  // 已连接
    Error        = 3,  // 连接错误
    Timeout      = 4   // 连接超时
};

/**
 * @brief 连接类型枚举
 */
enum class ConnectionType {
    Unknown   = 0,  // 未知连接
    Serial    = 1,  // 串口连接
    TCP       = 2,  // TCP连接
    UDP       = 3,  // UDP连接
    USB       = 4,  // USB连接
    Bluetooth = 5   // 蓝牙连接
};

/**
 * @brief 连接状态枚举
 */
enum class ConnectionState {
    Disconnected  = 0,  // 已断开
    Connecting    = 1,  // 连接中
    Connected     = 2,  // 已连接
    Disconnecting = 3,  // 断开中
    Error         = 4   // 错误状态
};

/**
 * @brief 协议类型枚举
 */
enum class ProtocolType {
    Raw    = 0,  // 原始数据（无协议）
    Modbus = 1,  // Modbus协议
    Custom = 2,  // 自定义协议
    JSON   = 3,  // JSON协议
    XML    = 4   // XML协议
};

/**
 * @brief 设备信息结构
 */
struct DeviceInfo {
    QString          deviceId;         // 设备ID
    QString          deviceName;       // 设备名称
    DeviceType       deviceType;       // 设备类型
    QString          deviceModel;      // 设备型号（兼容性字段）
    QStringList      supportedPorts;   // 支持的端口类型
    ConfigParameters parameters;       // 设备参数
    QVariantMap      properties;       // 设备属性（兼容性字段）
    QStringList      capabilities;     // 设备能力列表（兼容性字段）
    QString          manufacturer;     // 制造商
    QString          model;            // 型号
    VersionInfo      firmwareVersion;  // 固件版本
    QString          description;      // 描述

    DeviceInfo() : deviceType(DeviceType::Unknown) {
    }

    DeviceInfo(const QString &id, const QString &name, DeviceType type) : deviceId(id), deviceName(name), deviceType(type) {
    }

    // 兼容性方法 - 支持Matching模块的isValid()调用
    bool isValid() const {
        return !deviceId.isEmpty() && !deviceName.isEmpty() && deviceType != DeviceType::Unknown;
    }

    // operator== - 支持QList操作
    bool operator==(const DeviceInfo &other) const {
        return deviceId == other.deviceId && deviceName == other.deviceName && deviceType == other.deviceType;
    }

    // operator!= - 支持QList操作
    bool operator!=(const DeviceInfo &other) const {
        return !(*this == other);
    }
};

/**
 * @brief 端口配置结构
 */
struct PortConfig {
    QString          portName;       // 端口名称
    PortType         portType;       // 端口类型
    ConfigParameters parameters;     // 端口参数
    int              baudRate;       // 波特率（串口）
    QString          hostAddress;    // 主机地址（网络）
    int              port;           // 端口号（网络）
    int              timeout;        // 超时时间(ms)
    bool             autoReconnect;  // 自动重连

    PortConfig() : portType(PortType::Unknown), baudRate(9600), port(0), timeout(5000), autoReconnect(false) {
    }
};

/**
 * @brief 端口信息结构
 */
struct PortInfo {
    QString          portName;      // 端口名称
    QString          portId;        // 端口ID（别名）
    QString          displayName;   // 显示名称
    PortType         portType;      // 端口类型
    PortType         type;          // 端口类型（别名）
    PortStatus       status;        // 端口状态
    ConfigParameters config;        // 端口配置
    QVariantMap      properties;    // 端口属性（兼容性字段）
    QString          description;   // 描述
    PropertyMap      capabilities;  // 端口能力

    PortInfo() : portType(PortType::Unknown), type(PortType::Unknown), status(PortStatus::Unknown) {
        // 保持字段同步
        type        = portType;
        portId      = portName;
        displayName = portName;
    }

    PortInfo(const QString &name, PortType ptype, PortStatus stat = PortStatus::Available)
        : portName(name), portId(name), displayName(name), portType(ptype), type(ptype), status(stat) {
    }

    // 兼容性方法 - 支持Matching模块的isValid()调用
    bool isValid() const {
        return !portName.isEmpty() && portType != PortType::Unknown;
    }

    // 比较操作符 - 支持Matching模块的比较操作
    bool operator==(const PortInfo &other) const {
        return portName == other.portName && portType == other.portType;
    }

    bool operator!=(const PortInfo &other) const {
        return !(*this == other);
    }
};

/**
 * @brief 连接配置结构
 */
struct ConnectionConfig {
    QString          deviceId;       // 设备ID
    QString          portName;       // 端口名称 (用于串口)
    QString          hostAddress;    // 主机地址 (用于网络连接)
    int              port;           // 端口号 (用于网络连接)
    ProtocolType     protocolType;   // 协议类型
    ConfigParameters parameters;     // 连接参数
    int              timeout;        // 超时时间(ms)
    bool             autoReconnect;  // 自动重连
    int              retryCount;     // 重试次数

    ConnectionConfig() : port(0), protocolType(ProtocolType::Raw), timeout(5000), autoReconnect(false), retryCount(3) {
    }
};

/**
 * @brief 协议帧结构
 */
struct ProtocolFrame {
    QByteArray   header;        // 帧头
    QByteArray   payload;       // 载荷数据
    QByteArray   footer;        // 帧尾
    quint32      checksum;      // 校验和
    qint64       timestamp;     // 时间戳
    ProtocolType protocolType;  // 协议类型

    ProtocolFrame() : checksum(0), timestamp(0), protocolType(ProtocolType::Raw) {
    }
};

/**
 * @brief 设备命令结构
 */
struct DeviceCommand {
    QString          commandId;     // 命令ID
    QString          deviceId;      // 目标设备ID
    QByteArray       commandData;   // 命令数据
    ConfigParameters parameters;    // 命令参数
    int              timeout;       // 超时时间
    bool             needResponse;  // 是否需要响应

    DeviceCommand() : timeout(5000), needResponse(true) {
    }
};

/**
 * @brief 设备响应结构
 */
struct DeviceResponse {
    QString    commandId;     // 对应的命令ID
    QString    deviceId;      // 响应设备ID
    QByteArray responseData;  // 响应数据
    ResultCode resultCode;    // 结果代码
    QString    errorMessage;  // 错误消息
    qint64     timestamp;     // 时间戳

    DeviceResponse() : resultCode(ResultCode::Success), timestamp(0) {
    }
};

/**
 * @brief 命令执行结果结构
 */
struct CommandResult {
    QString       commandId;      // 命令ID
    QString       commandType;    // 命令类型
    bool          success;        // 执行是否成功
    QString       message;        // 结果消息
    QString       errorMessage;   // 错误消息
    ::QVariantMap data;           // 结果数据
    qint64        timestamp;      // 时间戳
    int           executionTime;  // 执行时间(ms)

    CommandResult() : success(false), timestamp(0), executionTime(0) {
    }
};

/**
 * @brief 设备统计信息
 */
struct DeviceStatistics {
    quint64 bytesReceived;     // 接收字节数
    quint64 bytesSent;         // 发送字节数
    quint64 packetsReceived;   // 接收包数
    quint64 packetsSent;       // 发送包数
    quint64 errorsCount;       // 错误计数
    qint64  lastActivity;      // 最后活动时间
    double  connectionUptime;  // 连接正常运行时间

    DeviceStatistics() : bytesReceived(0), bytesSent(0), packetsReceived(0), packetsSent(0), errorsCount(0), lastActivity(0), connectionUptime(0.0) {
    }
};

/**
 * @brief 连接统计信息
 */
struct ConnectionStatistics {
    quint64 bytesReceived;     // 接收字节数
    quint64 bytesSent;         // 发送字节数
    quint64 messagesReceived;  // 接收消息数
    quint64 messagesSent;      // 发送消息数
    quint64 errorsCount;       // 错误计数
    qint64  connectTime;       // 连接时间
    qint64  lastActivity;      // 最后活动时间
    double  uptime;            // 正常运行时间

    ConnectionStatistics()
        : bytesReceived(0), bytesSent(0), messagesReceived(0), messagesSent(0), errorsCount(0), connectTime(0), lastActivity(0), uptime(0.0) {
    }
};

/**
 * @brief 端口统计信息
 */
struct PortStatistics {
    quint64 bytesReceived;     // 接收字节数
    quint64 bytesSent;         // 发送字节数
    quint64 bytesTransmitted;  // 传输字节数（别名）
    quint64 operationsCount;   // 操作次数
    quint64 errorsCount;       // 错误计数
    quint64 errorCount;        // 错误计数（别名）
    quint64 totalConnections;  // 总连接数
    qint64  openTime;          // 开启时间
    qint64  lastActivity;      // 最后活动时间
    double  uptime;            // 正常运行时间

    PortStatistics()
        : bytesReceived(0),
          bytesSent(0),
          bytesTransmitted(0),
          operationsCount(0),
          errorsCount(0),
          errorCount(0),
          totalConnections(0),
          openTime(0),
          lastActivity(0),
          uptime(0.0) {
        // 保持字段同步
        bytesTransmitted = bytesSent;
        errorCount       = errorsCount;
    }
};

/**
 * @brief 设备信息列表类型定义
 */
using DeviceInfoList     = QVector<DeviceInfo>;
using PortInfoList       = QVector<PortInfo>;
using DeviceCommandList  = QVector<DeviceCommand>;
using DeviceResponseList = QVector<DeviceResponse>;

/**
 * @brief 设备相关回调函数类型
 */
using DeviceCallback     = Callback<const DeviceInfo &>;
using PortCallback       = Callback<const PortInfo &>;
using ConnectionCallback = Callback<ConnectionStatus, const QString &>;
using ProtocolCallback   = Callback<const ProtocolFrame &>;
using CommandCallback    = Callback<const DeviceCommand &>;
using ResponseCallback   = Callback<const DeviceResponse &>;

// 工具函数声明
QString          resultCodeToString(ResultCode code);
ResultCode       stringToResultCode(const QString &codeStr);
QString          deviceTypeToString(DeviceType type);
DeviceType       stringToDeviceType(const QString &typeStr);
QString          portTypeToString(PortType type);
PortType         stringToPortType(const QString &typeStr);
QString          portStatusToString(PortStatus status);
PortStatus       stringToPortStatus(const QString &statusStr);
QString          connectionStatusToString(ConnectionStatus status);
ConnectionStatus stringToConnectionStatus(const QString &statusStr);
QString          protocolTypeToString(ProtocolType type);
ProtocolType     stringToProtocolType(const QString &typeStr);

/**
 * @brief PortUtils 命名空间 - 端口工具函数
 */
namespace PortUtils {
QString    portTypeToString(PortType type);
QString    portStatusToString(PortStatus status);
PortType   stringToPortType(const QString &typeStr);
PortStatus stringToPortStatus(const QString &statusStr);
}  // namespace PortUtils

}  // namespace Core
}  // namespace Foundation
}  // namespace LA

// === QDebug输出操作符 - 暂时禁用以解决命名空间污染问题 ===
// #include <QDebug>  // 暂时禁用，避免命名空间污染

// 暂时禁用QDebug操作符以解决命名空间污染问题
/*
inline QDebug operator<<(QDebug debug, LA::Foundation::Core::DeviceType type) {
    debug.nospace() << "DeviceType(" << static_cast<int>(type) << ")";
    return debug.space();
}

inline QDebug operator<<(QDebug debug, LA::Foundation::Core::PortType type) {
    debug.nospace() << "PortType(" << static_cast<int>(type) << ")";
    return debug.space();
}

inline QDebug operator<<(QDebug debug, LA::Foundation::Core::PortStatus status) {
    debug.nospace() << "PortStatus(" << static_cast<int>(status) << ")";
    return debug.space();
}
*/

// === 枚举类型字符串转换函数 - 解决设备管理模块类型转换问题 ===
namespace LA {
namespace Foundation {
namespace Core {

// DeviceType转换函数
inline QString deviceTypeToString(DeviceType type) {
    switch (type) {
    case DeviceType::Unknown:
        return "UNKNOWN";
    case DeviceType::Generic:
        return "GENERIC";
    case DeviceType::Sensor:
        return "SENSOR";
    case DeviceType::Motor:
        return "MOTOR";
    case DeviceType::Actuator:
        return "ACTUATOR";
    case DeviceType::PLC:
        return "PLC";
    case DeviceType::Scanner:
        return "SCANNER";
    case DeviceType::TestBoard:
        return "TESTBOARD";
    case DeviceType::Virtual:
        return "VIRTUAL";
    default:
        return "UNKNOWN";
    }
}

// PortType转换函数
inline QString portTypeToString(PortType type) {
    switch (type) {
    case PortType::Unknown:
        return "UNKNOWN";
    case PortType::Serial:
        return "SERIAL";
    case PortType::TCP:
        return "TCP";
    case PortType::UDP:
        return "UDP";
    case PortType::USB:
        return "USB";
    case PortType::Bluetooth:
        return "BLUETOOTH";
    case PortType::WiFi:
        return "WIFI";
    default:
        return "UNKNOWN";
    }
}

// PortStatus转换函数
inline QString portStatusToString(PortStatus status) {
    switch (status) {
    case PortStatus::Unknown:
        return "UNKNOWN";
    case PortStatus::Available:
        return "AVAILABLE";
    case PortStatus::Busy:
        return "BUSY";
    case PortStatus::Error:
        return "ERROR";
    case PortStatus::Disconnected:
        return "DISCONNECTED";
    default:
        return "UNKNOWN";
    }
}

}  // namespace Core
}  // namespace Foundation
}  // namespace LA