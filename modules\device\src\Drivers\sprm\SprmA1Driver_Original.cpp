/**
 * @file SprmA1Driver.cpp
 * @brief SPRM-A1设备驱动适配器实现 - 四层架构第1层
 */

#include "SprmA1Driver.h"
#include <QDebug>
#include <QThread>
#include <QCoreApplication>

namespace LA::Device::Driver {

// SPRM-A1命令映射定义
const QMap<QString, SprmA1Driver::CommandCode> SprmA1Driver::s_commandCodes = {
    {"START_MEASURE", {SprmA1Protocol::CMD_START_MEASURE, "开始测量", 4}},
    {"STOP_MEASURE", {SprmA1Protocol::CMD_STOP_MEASURE, "停止测量", 4}},
    {"GET_DISTANCE", {SprmA1Protocol::CMD_GET_DISTANCE, "获取距离", 6}},
    {"CALIBRATE", {SprmA1Protocol::CMD_CALIBRATE, "校准传感器", 4}},
    {"SET_LASER_POWER", {SprmA1Protocol::CMD_SET_LASER_POWER, "设置激光功率", 4}},
    {"GET_STATUS", {SprmA1Protocol::CMD_GET_STATUS, "获取状态", 5}},
    {"RESET", {SprmA1Protocol::CMD_RESET, "复位设备", 4}},
    {"GET_VERSION", {SprmA1Protocol::CMD_GET_VERSION, "获取版本", 8}},
    {"SET_BAUDRATE", {SprmA1Protocol::CMD_SET_BAUDRATE, "设置波特率", 4}},
    {"SELF_TEST", {SprmA1Protocol::CMD_SELF_TEST, "自检", 4}}
};

SprmA1Driver::SprmA1Driver(QObject* parent)
    : QObject(parent)
    , m_serialPort(std::make_unique<QSerialPort>())
    , m_baudRate(19200)
    , m_connected(false)
    , m_laserPower(5.0)
    , m_timeoutMs(3000)
    , m_waitingForResponse(false)
{
    // 初始化超时定时器
    m_commandTimeout = new QTimer(this);
    m_commandTimeout->setSingleShot(true);
    connect(m_commandTimeout, &QTimer::timeout, this, &SprmA1Driver::onCommandTimeout);

    // 连接串口信号
    connect(m_serialPort.get(), &QSerialPort::readyRead, 
            this, &SprmA1Driver::onSerialDataReceived);
    connect(m_serialPort.get(), QOverload<QSerialPort::SerialPortError>::of(&QSerialPort::errorOccurred),
            this, &SprmA1Driver::onSerialError);
}

bool SprmA1Driver::initialize() {
    qDebug() << "[SprmA1Driver] Initializing driver...";
    
    // 设置串口参数
    m_serialPort->setBaudRate(m_baudRate);
    m_serialPort->setDataBits(QSerialPort::Data8);
    m_serialPort->setParity(QSerialPort::NoParity);
    m_serialPort->setStopBits(QSerialPort::OneStop);
    m_serialPort->setFlowControl(QSerialPort::NoFlowControl);

    return true;
}

bool SprmA1Driver::connect() {
    if (m_portName.isEmpty()) {
        emit errorOccurred("Port name not set");
        return false;
    }

    qDebug() << "[SprmA1Driver] Connecting to port:" << m_portName;

    m_serialPort->setPortName(m_portName);
    
    if (!m_serialPort->open(QIODevice::ReadWrite)) {
        QString error = QString("Failed to open port %1: %2")
                       .arg(m_portName)
                       .arg(m_serialPort->errorString());
        emit errorOccurred(error);
        return false;
    }

    m_connected = true;
    emit connectionStatusChanged(true);

    // 执行设备自检
    QTimer::singleShot(500, [this]() {
        auto result = performSelfTest();
        if (!result["success"].toBool()) {
            qWarning() << "[SprmA1Driver] Self test failed:" << result["error"].toString();
        } else {
            qDebug() << "[SprmA1Driver] Self test passed";
        }
    });

    return true;
}

bool SprmA1Driver::disconnect() {
    qDebug() << "[SprmA1Driver] Disconnecting...";

    if (m_serialPort->isOpen()) {
        m_serialPort->close();
    }

    m_connected = false;
    m_waitingForResponse = false;
    m_commandTimeout->stop();
    m_responseBuffer.clear();

    emit connectionStatusChanged(false);
    return true;
}

QVariantMap SprmA1Driver::sendCommand(const QString& command, const QVariantMap& params) {
    if (!m_connected) {
        return createErrorResult("Device not connected");
    }

    if (m_waitingForResponse) {
        return createErrorResult("Previous command still processing");
    }

    if (!s_commandCodes.contains(command)) {
        return createErrorResult(QString("Unknown command: %1").arg(command));
    }

    qDebug() << "[SprmA1Driver] Sending command:" << command << "with params:" << params;

    // 构建命令帧
    QByteArray frame = buildCommandFrame(command, params);
    if (frame.isEmpty()) {
        return createErrorResult("Failed to build command frame");
    }

    // 发送命令
    if (!sendRawData(frame)) {
        return createErrorResult("Failed to send command");
    }

    // 等待响应
    m_waitingForResponse = true;
    m_responseBuffer.clear();
    m_commandTimeout->start(m_timeoutMs);

    QByteArray response = waitForResponse(m_timeoutMs);
    m_waitingForResponse = false;
    m_commandTimeout->stop();

    if (response.isEmpty()) {
        return createErrorResult("Command timeout or no response");
    }

    // 解析响应
    return parseResponse(response);
}

bool SprmA1Driver::isConnected() const {
    return m_connected && m_serialPort->isOpen();
}

void SprmA1Driver::setSerialConfig(const QString& portName, int baudRate) {
    m_portName = portName;
    m_baudRate = baudRate;
    qDebug() << "[SprmA1Driver] Serial config set:" << portName << "at" << baudRate;
}

void SprmA1Driver::setLaserConfig(double power) {
    if (power < 1.0 || power > 10.0) {
        qWarning() << "[SprmA1Driver] Invalid laser power:" << power;
        return;
    }
    
    m_laserPower = power;
    qDebug() << "[SprmA1Driver] Laser power set to:" << power << "mW";
}

QVariantMap SprmA1Driver::getDeviceSpecs() const {
    QVariantMap specs;
    specs["model"] = "SPRM-A1";
    specs["manufacturer"] = "Nova";
    specs["laser_wavelength"] = "650nm";
    specs["laser_power"] = m_laserPower;
    specs["measurement_range"] = QVariantMap{{"min", 50}, {"max", 2000}, {"unit", "mm"}};
    specs["accuracy"] = QVariantMap{{"typical", 1.0}, {"unit", "mm"}};
    specs["resolution"] = QVariantMap{{"value", 0.1}, {"unit", "mm"}};
    specs["response_time"] = QVariantMap{{"typical", 10}, {"unit", "ms"}};
    specs["communication"] = QVariantMap{{"protocol", "RS485"}, {"baudrate", m_baudRate}};
    specs["protection_level"] = "IP65";
    return specs;
}

void SprmA1Driver::onSerialDataReceived() {
    if (!m_waitingForResponse) {
        return;
    }

    QByteArray data = m_serialPort->readAll();
    m_responseBuffer.append(data);

    emit dataReceived(data);
    
    // 检查是否接收到完整帧
    if (m_responseBuffer.size() >= SprmA1Protocol::MIN_FRAME_LENGTH) {
        if (m_responseBuffer[0] == SprmA1Protocol::FRAME_HEADER_1 &&
            m_responseBuffer[1] == SprmA1Protocol::FRAME_HEADER_2) {
            
            int expectedLength = m_responseBuffer.size() >= 5 ? m_responseBuffer[4] + 6 : SprmA1Protocol::MIN_FRAME_LENGTH;
            if (m_responseBuffer.size() >= expectedLength) {
                // 完整帧已接收，停止超时定时器
                m_commandTimeout->stop();
            }
        }
    }
}

void SprmA1Driver::onSerialError(QSerialPort::SerialPortError error) {
    if (error == QSerialPort::NoError) {
        return;
    }

    QString errorString = QString("Serial port error: %1").arg(m_serialPort->errorString());
    qCritical() << "[SprmA1Driver]" << errorString;
    emit errorOccurred(errorString);

    if (error == QSerialPort::ResourceError) {
        m_connected = false;
        emit connectionStatusChanged(false);
    }
}

void SprmA1Driver::onCommandTimeout() {
    qWarning() << "[SprmA1Driver] Command timeout";
    m_waitingForResponse = false;
    m_responseBuffer.clear();
}

QByteArray SprmA1Driver::buildCommandFrame(const QString& command, const QVariantMap& params) {
    if (!s_commandCodes.contains(command)) {
        return QByteArray();
    }

    const CommandCode& cmdInfo = s_commandCodes[command];
    QByteArray frame;

    // 帧头
    frame.append(SprmA1Protocol::FRAME_HEADER_1);
    frame.append(SprmA1Protocol::FRAME_HEADER_2);
    
    // 设备地址
    frame.append(SprmA1Protocol::DEVICE_ADDRESS);
    
    // 命令码
    frame.append(cmdInfo.code);

    // 数据长度和数据
    QByteArray data;
    
    // 根据命令类型添加参数数据
    if (command == "START_MEASURE") {
        int timeout = params.value("timeout", 5000).toInt();
        data.append((timeout >> 8) & 0xFF);
        data.append(timeout & 0xFF);
    } else if (command == "GET_DISTANCE") {
        int sampleCount = params.value("sample_count", 1).toInt();
        data.append(sampleCount & 0xFF);
    } else if (command == "CALIBRATE") {
        int refDistance = params.value("reference_distance", 1000).toInt();
        data.append((refDistance >> 8) & 0xFF);
        data.append(refDistance & 0xFF);
    } else if (command == "SET_LASER_POWER") {
        int powerLevel = static_cast<int>(params.value("power_level", 5.0).toDouble());
        data.append(powerLevel & 0xFF);
    }

    // 数据长度
    frame.append(static_cast<uint8_t>(data.size()));
    
    // 数据内容
    frame.append(data);
    
    // 计算校验和
    uint8_t checksum = calculateChecksum(frame.mid(2)); // 从地址字节开始
    frame.append(checksum);

    return frame;
}

QVariantMap SprmA1Driver::parseResponse(const QByteArray& data) {
    QVariantMap result;

    if (data.size() < SprmA1Protocol::MIN_FRAME_LENGTH) {
        return createErrorResult("Response too short");
    }

    // 验证帧头
    if (data[0] != SprmA1Protocol::FRAME_HEADER_1 || 
        data[1] != SprmA1Protocol::FRAME_HEADER_2) {
        return createErrorResult("Invalid frame header");
    }

    // 验证校验和
    if (!validateChecksum(data)) {
        return createErrorResult("Checksum validation failed");
    }

    uint8_t address = static_cast<uint8_t>(data[2]);
    uint8_t command = static_cast<uint8_t>(data[3]);
    uint8_t length = static_cast<uint8_t>(data[4]);
    
    result["success"] = true;
    result["address"] = address;
    result["command"] = command;
    result["data_length"] = length;

    // 解析响应数据
    if (length > 0 && data.size() >= 6 + length) {
        QByteArray responseData = data.mid(5, length);
        
        switch (command) {
            case SprmA1Protocol::CMD_GET_DISTANCE: {
                if (responseData.size() >= 2) {
                    uint16_t distance = (static_cast<uint8_t>(responseData[0]) << 8) | 
                                      static_cast<uint8_t>(responseData[1]);
                    result["distance"] = distance;
                    result["unit"] = "mm";
                }
                break;
            }
            case SprmA1Protocol::CMD_GET_STATUS: {
                if (responseData.size() >= 1) {
                    uint8_t status = static_cast<uint8_t>(responseData[0]);
                    result["status_code"] = status;
                    result["status_text"] = getStatusText(status);
                }
                break;
            }
            case SprmA1Protocol::CMD_GET_VERSION: {
                if (responseData.size() >= 4) {
                    QString version = QString("%1.%2.%3.%4")
                                    .arg(static_cast<uint8_t>(responseData[0]))
                                    .arg(static_cast<uint8_t>(responseData[1]))
                                    .arg(static_cast<uint8_t>(responseData[2]))
                                    .arg(static_cast<uint8_t>(responseData[3]));
                    result["firmware_version"] = version;
                    m_firmwareVersion = version;
                }
                break;
            }
            default:
                result["raw_data"] = responseData.toHex();
                break;
        }
    }

    result["timestamp"] = QDateTime::currentDateTime().toString();
    return result;
}

bool SprmA1Driver::validateChecksum(const QByteArray& frame) {
    if (frame.size() < SprmA1Protocol::MIN_FRAME_LENGTH) {
        return false;
    }

    uint8_t receivedChecksum = static_cast<uint8_t>(frame.back());
    QByteArray dataToCheck = frame.mid(2, frame.size() - 3); // 地址到数据结束，不包括校验和
    uint8_t calculatedChecksum = calculateChecksum(dataToCheck);

    return receivedChecksum == calculatedChecksum;
}

uint8_t SprmA1Driver::calculateChecksum(const QByteArray& data) {
    uint8_t sum = 0;
    for (char byte : data) {
        sum += static_cast<uint8_t>(byte);
    }
    return sum;
}

bool SprmA1Driver::sendRawData(const QByteArray& data) {
    if (!m_serialPort->isOpen()) {
        return false;
    }

    qint64 bytesWritten = m_serialPort->write(data);
    if (bytesWritten != data.size()) {
        qWarning() << "[SprmA1Driver] Write incomplete:" << bytesWritten << "of" << data.size();
        return false;
    }

    m_serialPort->flush();
    return true;
}

QByteArray SprmA1Driver::waitForResponse(int timeoutMs) {
    QElapsedTimer timer;
    timer.start();

    while (timer.elapsed() < timeoutMs && m_waitingForResponse) {
        QCoreApplication::processEvents(QEventLoop::AllEvents, 50);
        
        if (!m_responseBuffer.isEmpty() && 
            m_responseBuffer.size() >= SprmA1Protocol::MIN_FRAME_LENGTH) {
            
            // 检查是否为完整帧
            if (m_responseBuffer[0] == SprmA1Protocol::FRAME_HEADER_1 &&
                m_responseBuffer[1] == SprmA1Protocol::FRAME_HEADER_2) {
                
                if (m_responseBuffer.size() >= 5) {
                    int expectedLength = m_responseBuffer[4] + 6;
                    if (m_responseBuffer.size() >= expectedLength) {
                        return m_responseBuffer.left(expectedLength);
                    }
                }
            }
        }
        
        QThread::msleep(10);
    }

    return QByteArray();
}

QVariantMap SprmA1Driver::performSelfTest() {
    qDebug() << "[SprmA1Driver] Performing self test...";
    
    QVariantMap params;
    QVariantMap result = sendCommand("SELF_TEST", params);
    
    if (result["success"].toBool()) {
        // 获取设备信息
        auto versionResult = sendCommand("GET_VERSION", QVariantMap());
        if (versionResult["success"].toBool()) {
            result["firmware_version"] = versionResult["firmware_version"];
        }
    }
    
    return result;
}

QVariantMap SprmA1Driver::getDeviceInfo() {
    QVariantMap info = getDeviceSpecs();
    info["firmware_version"] = m_firmwareVersion;
    info["port_name"] = m_portName;
    info["connected"] = m_connected;
    info["laser_power"] = m_laserPower;
    return info;
}

QString SprmA1Driver::getStatusText(uint8_t statusCode) {
    switch (statusCode) {
        case SprmA1Protocol::STATUS_OK:
            return "正常";
        case SprmA1Protocol::STATUS_BUSY:
            return "忙碌";
        case SprmA1Protocol::STATUS_ERROR:
            return "错误";
        case SprmA1Protocol::STATUS_TIMEOUT:
            return "超时";
        case SprmA1Protocol::STATUS_INVALID_CMD:
            return "无效命令";
        case SprmA1Protocol::STATUS_CHECKSUM_ERROR:
            return "校验和错误";
        case SprmA1Protocol::STATUS_LASER_OFF:
            return "激光器关闭";
        case SprmA1Protocol::STATUS_OUT_OF_RANGE:
            return "超出量程";
        default:
            return QString("未知状态(%1)").arg(statusCode);
    }
}

QVariantMap SprmA1Driver::createErrorResult(const QString& error) {
    QVariantMap result;
    result["success"] = false;
    result["error"] = error;
    result["timestamp"] = QDateTime::currentDateTime().toString();
    return result;
}

} // namespace LA::Device::Driver