# 配置文件处理模块 (Configuration File Handler Module)

## 概述

配置文件处理模块提供了完整的配置文件读写、转换和管理功能，支持多种常见的配置文件格式，包括JSON、INI、XML、YAML等。

## 功能特性

### 核心功能
- **多格式支持** - 支持JSON、INI、XML、YAML、TOML等主流配置文件格式
- **格式检测** - 自动检测配置文件格式
- **格式转换** - 支持不同格式之间的相互转换
- **文件监控** - 实时监控配置文件变化并自动重载
- **文件验证** - 完整的配置文件格式和内容验证

### 高级功能
- **文件备份** - 自动备份和恢复配置文件
- **文件加密** - 支持敏感配置文件的加密存储
- **文件压缩** - 支持配置文件的压缩和解压
- **批量操作** - 支持多个配置文件的合并和分割
- **缓存机制** - 智能缓存提高文件访问性能

## 支持的文件格式

| 格式 | 扩展名 | 读取 | 写入 | 注释支持 | 验证 |
|------|--------|------|------|----------|------|
| JSON | .json | ✅ | ✅ | 特殊字段 | Schema |
| INI | .ini, .cfg | ✅ | ✅ | ✅ | 基础 |
| XML | .xml | ✅ | ✅ | ✅ | XSD |
| YAML | .yaml, .yml | ✅ | ✅ | ✅ | 基础 |
| TOML | .toml | ✅ | ✅ | ✅ | 基础 |
| Properties | .properties | ✅ | ✅ | ✅ | 基础 |

## 使用方法

### 基本文件操作

```cpp
#include <LA/Support/Config/File/ConfigFileHandler.h>

using namespace LA::Support::Config;

// 创建文件处理器
auto factory = std::make_unique<ConfigFileHandlerFactory>();
auto handler = factory->createConfigFileHandler();

// 加载配置文件
auto loadResult = handler->loadFile("config.json");
if (loadResult.success) {
    QVariantMap config = loadResult.data;
    qDebug() << "Loaded config:" << config;
}

// 保存配置文件
QVariantMap newConfig;
newConfig["app_name"] = "MyApp";
newConfig["version"] = "1.0.0";

ConfigFileOptions options;
options.prettyPrint = true;
options.createIfNotExists = true;

auto saveResult = handler->saveFile("config.json", newConfig, options);
if (saveResult.success) {
    qDebug() << "Config saved successfully";
}
```

### JSON 专用处理

```cpp
#include <LA/Support/Config/File/JsonConfigFileHandler.h>

// 直接使用JSON处理器
auto loadResult = JsonConfigFileHandler::loadJsonFile("config.json");
if (loadResult.success) {
    QVariantMap config = loadResult.data;
    
    // JSON路径操作
    QVariant value = JsonConfigFileHandler::getJsonPathValue(config, "database.host");
    JsonConfigFileHandler::setJsonPathValue(config, "database.port", 5432);
    
    // 保存回文件
    ConfigFileOptions options;
    options.prettyPrint = true;
    options.indentSize = 4;
    
    JsonConfigFileHandler::saveJsonFile("config.json", config, options);
}

// JSON合并
QVariantMap base = {{"a", 1}, {"b", QVariantMap{{"c", 2}}}};
QVariantMap overlay = {{"b", QVariantMap{{"d", 3}}}, {"e", 4}};
QVariantMap merged = JsonConfigFileHandler::mergeJsonObjects(base, overlay, true);
// 结果: {"a": 1, "b": {"c": 2, "d": 3}, "e": 4}
```

### INI 专用处理

```cpp
#include <LA/Support/Config/File/IniConfigFileHandler.h>

// 加载INI文件
auto loadResult = IniConfigFileHandler::loadIniFile("config.ini");
if (loadResult.success) {
    QVariantMap config = loadResult.data;
    
    // 获取节列表
    QStringList sections = IniConfigFileHandler::getIniSections(config);
    
    // 读取特定节的值
    QVariant dbHost = IniConfigFileHandler::getIniValue(config, "host", "database");
    
    // 设置值
    IniConfigFileHandler::setIniValue(config, "port", 3306, "database");
    
    // 保存回文件
    ConfigFileOptions options;
    options.prettyPrint = true;
    
    IniConfigFileHandler::saveIniFile("config.ini", config, options);
}
```

### 文件监控

```cpp
// 启用文件监控
handler->watchFile("config.json");

// 连接文件变化信号
connect(handler.get(), &IConfigFileHandler::fileChanged,
        [](const QString& filePath) {
            qDebug() << "Config file changed:" << filePath;
            // 重新加载配置
        });

// 停止监控
handler->unwatchFile("config.json");
```

### 格式转换

```cpp
// 转换JSON到INI
handler->convertFile("config.json", "config.ini", ConfigFileFormat::INI);

// 转换INI到XML
handler->convertFile("config.ini", "config.xml", ConfigFileFormat::XML);

// 批量转换
QStringList jsonFiles = {"app.json", "database.json", "network.json"};
for (const QString& file : jsonFiles) {
    QString iniFile = file;
    iniFile.replace(".json", ".ini");
    handler->convertFile(file, iniFile, ConfigFileFormat::INI);
}
```

### 文件验证

```cpp
// JSON Schema验证
QVariantMap schema;
schema["type"] = "object";
schema["required"] = QStringList{"name", "version"};

auto validateResult = handler->validateFile("config.json", schema);
if (!validateResult.success) {
    qDebug() << "Validation failed:" << validateResult.message;
}

// 基础格式验证
bool isValid = JsonConfigUtils::isValidJsonFile("config.json");
```

### 文件备份和恢复

```cpp
// 创建备份
auto backupResult = handler->backupFile("config.json");
if (backupResult.success) {
    QString backupPath = backupResult.data;
    qDebug() << "Backup created at:" << backupPath;
}

// 恢复备份
handler->restoreFile("config.json", backupPath);

// 自动备份（在保存时）
ConfigFileOptions options;
options.backupOnWrite = true;
options.maxBackups = 5;

handler->saveFile("config.json", newConfig, options);
```

### 文件加密

```cpp
// 加密配置文件
handler->encryptFile("config.json", "mypassword", "config.json.enc");

// 解密配置文件  
handler->decryptFile("config.json.enc", "mypassword", "config_decrypted.json");

// 直接加载加密文件
ConfigFileOptions options;
options.customOptions["password"] = "mypassword";
options.customOptions["encrypted"] = true;

auto result = handler->loadFile("config.json.enc", options);
```

## 配置选项

### ConfigFileOptions 结构

| 选项 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `format` | ConfigFileFormat | INI | 文件格式 |
| `encoding` | ConfigFileEncoding | UTF8 | 文件编码 |
| `createIfNotExists` | bool | true | 文件不存在时创建 |
| `backupOnWrite` | bool | false | 写入时备份 |
| `validateOnLoad` | bool | true | 加载时验证 |
| `watchForChanges` | bool | false | 监控文件变化 |
| `prettyPrint` | bool | true | 美化输出 |
| `indentSize` | int | 4 | 缩进大小 |
| `maxBackups` | int | 5 | 最大备份数 |

### 高级选项 (customOptions)

```cpp
ConfigFileOptions options;

// JSON特定选项
options.customOptions["enable_comments"] = true;
options.customOptions["merge_arrays"] = false;
options.customOptions["preserve_order"] = true;

// INI特定选项
options.customOptions["preserve_case"] = true;
options.customOptions["allow_duplicates"] = false;
options.customOptions["section_separator"] = "/";

// 通用选项
options.customOptions["password"] = "encryption_key";
options.customOptions["compression_level"] = 6;
options.customOptions["cache_enabled"] = true;
```

## 错误处理

### 结果类型

所有操作都返回结果类型，包含成功状态和详细错误信息：

```cpp
// 简单结果
SimpleResult result = handler->saveFile(path, data);
if (!result.success) {
    qDebug() << "Error:" << result.message;
    // 处理错误
}

// 带数据的结果
Result<QVariantMap> loadResult = handler->loadFile(path);
if (loadResult.success) {
    QVariantMap data = loadResult.data;
    // 使用数据
} else {
    qDebug() << "Load failed:" << loadResult.message;
}

// 字节数组结果
ByteArrayResult serializeResult = JsonConfigFileHandler::serializeJsonContent(data);
if (serializeResult.success) {
    QByteArray jsonBytes = serializeResult.data;
    // 使用字节数据
}
```

### 常见错误码

| 错误类型 | 描述 | 处理建议 |
|----------|------|----------|
| FileNotFound | 文件未找到 | 检查文件路径，考虑创建默认文件 |
| FileReadError | 文件读取失败 | 检查文件权限和磁盘空间 |
| FileWriteError | 文件写入失败 | 检查目录权限和磁盘空间 |
| ParseError | 文件解析失败 | 检查文件格式，考虑修复或重置 |
| ValidationError | 文件验证失败 | 检查文件内容是否符合要求 |
| PermissionError | 权限错误 | 检查文件系统权限 |

## 性能优化

### 缓存策略

```cpp
// 启用缓存
ConfigParameters config;
config["enable_cache"] = true;
config["cache_size"] = 100;  // 最大缓存文件数
config["cache_ttl"] = 3600;  // 缓存生存时间（秒）

handler->initialize(config);

// 预加载常用文件
handler->loadFile("app_config.json");  // 缓存该文件
handler->loadFile("user_config.json"); // 缓存该文件
```

### 批量操作

```cpp
// 批量加载
QStringList configFiles = {"app.json", "db.json", "network.json"};
QVariantMap mergedConfig = handler->mergeFiles(configFiles);

// 批量转换
for (const QString& file : configFiles) {
    QString iniFile = file;
    iniFile.replace(".json", ".ini");
    handler->convertFile(file, iniFile, ConfigFileFormat::INI);
}
```

### 内存管理

```cpp
// 定期清理缓存
connect(&cleanupTimer, &QTimer::timeout, [&handler]() {
    // 触发缓存清理（如果实现了该功能）
    handler->clearCache();
});

// 限制监控的文件数量
QStringList watchedFiles = handler->getWatchedFiles();
if (watchedFiles.size() > 50) {
    // 移除一些不重要的监控
    handler->unwatchFile(watchedFiles.first());
}
```

## 扩展和自定义

### 自定义解析器

```cpp
class CustomConfigParser : public IConfigFileParser {
public:
    ConfigFileFormat getSupportedFormat() const override {
        return ConfigFileFormat::CUSTOM;
    }
    
    Result<QVariantMap> parse(const QByteArray& data, 
                             const ConfigFileOptions& options) override {
        // 实现自定义格式解析
        QVariantMap result;
        // ... 解析逻辑
        return Result<QVariantMap>::success(result);
    }
    
    ByteArrayResult serialize(const QVariantMap& data, 
                             const ConfigFileOptions& options) override {
        // 实现自定义格式序列化
        QString customFormat;
        // ... 序列化逻辑
        return ByteArrayResult::success(customFormat.toUtf8());
    }
};

// 注册自定义解析器
auto factory = std::make_unique<ConfigFileHandlerFactory>();
factory->registerParser(ConfigFileFormat::CUSTOM, 
                       std::make_shared<CustomConfigParser>());
```

### 自定义验证器

```cpp
class CustomValidator {
public:
    static SimpleResult validateConfigData(const QVariantMap& data, 
                                          const QVariantMap& rules) {
        // 实现自定义验证逻辑
        for (auto it = rules.begin(); it != rules.end(); ++it) {
            QString key = it.key();
            QVariant rule = it.value();
            
            if (!data.contains(key)) {
                return SimpleResult::error("Missing required key: " + key);
            }
            
            // 更多验证逻辑...
        }
        
        return SimpleResult::success();
    }
};
```

## 测试和调试

### 单元测试

```cpp
#include <QtTest/QtTest>

class ConfigFileHandlerTest : public QObject {
    Q_OBJECT

private slots:
    void testJsonLoadSave();
    void testIniLoadSave();
    void testFormatConversion();
    void testFileValidation();
    void testFileBackup();
};

void ConfigFileHandlerTest::testJsonLoadSave() {
    // 创建测试数据
    QVariantMap testData;
    testData["name"] = "TestApp";
    testData["version"] = "1.0.0";
    
    // 保存文件
    auto handler = createHandler();
    QString testFile = "test_config.json";
    
    auto saveResult = handler->saveFile(testFile, testData);
    QVERIFY(saveResult.success);
    
    // 加载文件
    auto loadResult = handler->loadFile(testFile);
    QVERIFY(loadResult.success);
    QCOMPARE(loadResult.data, testData);
    
    // 清理
    QFile::remove(testFile);
}
```

### 性能测试

```cpp
void performanceTest() {
    QElapsedTimer timer;
    auto handler = createHandler();
    
    // 大文件测试
    QVariantMap largeData;
    for (int i = 0; i < 10000; ++i) {
        largeData[QString("key_%1").arg(i)] = i;
    }
    
    timer.start();
    handler->saveFile("large_config.json", largeData);
    qint64 saveTime = timer.elapsed();
    
    timer.restart();
    handler->loadFile("large_config.json");
    qint64 loadTime = timer.elapsed();
    
    qDebug() << "Save time:" << saveTime << "ms";
    qDebug() << "Load time:" << loadTime << "ms";
}
```

## 故障排除

### 常见问题

1. **文件格式检测失败**
   ```cpp
   // 手动指定格式
   ConfigFileOptions options;
   options.format = ConfigFileFormat::JSON;
   handler->loadFile("config.txt", options);
   ```

2. **编码问题**
   ```cpp
   // 指定编码
   ConfigFileOptions options;
   options.encoding = ConfigFileEncoding::UTF8;
   handler->loadFile("config.json", options);
   ```

3. **权限问题**
   ```cpp
   // 检查文件权限
   QFileInfo fileInfo("config.json");
   if (!fileInfo.isReadable()) {
       qDebug() << "File is not readable";
   }
   if (!fileInfo.isWritable()) {
       qDebug() << "File is not writable";
   }
   ```

### 调试技巧

```cpp
// 启用详细日志
qSetMessagePattern("[%{time}] %{category} %{type}: %{message}");

// 获取处理器状态
auto status = handler->getStatus();
for (const auto& info : status) {
    qDebug() << info.first << ":" << info.second;
}

// 检查支持的格式
QList<ConfigFileFormat> formats = handler->getSupportedFormats();
for (auto format : formats) {
    qDebug() << "Supported format:" << configFileFormatToString(format);
}
```

## 最佳实践

1. **选择合适的格式**
   - JSON: 结构化数据，支持嵌套
   - INI: 简单配置，分组清晰
   - XML: 复杂结构，需要验证
   - YAML: 人类可读，支持注释

2. **合理使用缓存**
   - 频繁访问的文件启用缓存
   - 大文件考虑部分加载
   - 定期清理过期缓存

3. **错误处理策略**
   - 始终检查操作结果
   - 提供默认配置作为备用
   - 记录详细错误信息

4. **文件监控注意事项**
   - 避免监控过多文件
   - 处理文件删除/重命名事件
   - 防止循环更新

## 版本历史

- v1.0.0 - 初始版本，支持JSON和INI格式
- 计划功能：YAML支持、配置模板、远程配置同步

## 相关模块

- [参数管理模块](../parameter/README.md) - 提供参数定义和管理
- [配置验证模块](../validation/README.md) - 提供高级验证功能
- [基础类型模块](../../foundation/README.md) - 提供基础类型定义