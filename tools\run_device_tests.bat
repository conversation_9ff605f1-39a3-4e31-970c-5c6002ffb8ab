@echo off
rem 设备模块测试运行脚本
rem 按照test_guideline.md规范运行测试

setlocal

echo ========================================
echo 设备模块测试运行器 (<PERSON><PERSON> Module Tester)
echo ========================================
echo.

echo 1. 检查测试目录结构...
if not exist "tests\unit\devices\sprm" (
    echo 错误：找不到单元测试目录 tests\unit\devices\sprm\
    echo 请确认测试已迁移到规范位置
    exit /b 1
)

if not exist "tests\integration\devices\sprm" (
    echo 错误：找不到集成测试目录 tests\integration\devices\sprm\
    echo 请确认测试已迁移到规范位置
    exit /b 1
)

echo 2. 构建设备模块...
call tools\build_device_module.bat
if errorlevel 1 (
    echo 错误：设备模块构建失败
    exit /b 1
)

echo 3. 创建测试构建目录...
if not exist "build\test" mkdir "build\test"

echo 4. 运行单元测试...
echo 正在运行SPRM设备单元测试...
if exist "tests\unit\devices\sprm\simple_sprm_test.cpp" (
    echo - simple_sprm_test: [测试文件存在]
) else (
    echo - simple_sprm_test: [缺失]
)

if exist "tests\unit\devices\sprm\SprmDeviceTest.cpp" (
    echo - SprmDeviceTest: [测试文件存在]
) else (
    echo - SprmDeviceTest: [缺失]
)

echo 5. 运行集成测试...
echo 正在运行SPRM设备集成测试...
if exist "tests\integration\devices\sprm\modern_sprm_integration_test.cpp" (
    echo - modern_sprm_integration_test: [测试文件存在]
) else (
    echo - modern_sprm_integration_test: [缺失]
)

echo.
echo ========================================
echo 测试概况完成
echo 测试文件位置: tests\unit\devices\sprm\
echo 集成测试位置: tests\integration\devices\sprm\
echo 构建输出位置: build\test\
echo ========================================
echo.
echo 注意：测试编译和执行需要进一步实现
echo 当前脚本验证了测试目录结构的规范性