#include <LA/Communication/Connection/SerialConnection.h>
#include <QDebug>
#include <QMutexLocker>
#include <QDateTime>

namespace LA {
namespace Communication {
namespace Connection {

SerialConnection::SerialConnection(QObject *parent)
    : IConnection(parent)
    , m_serialPort(new QSerialPort(this))
    , m_status(ConnectionStatus::Disconnected)
{
    // 连接Qt串口信号到我们的槽
    connect(m_serialPort, &QSerialPort::readyRead, 
            this, &SerialConnection::onReadyRead);
    connect(m_serialPort, QOverload<QSerialPort::SerialPortError>::of(&QSerialPort::error),
            this, &SerialConnection::onErrorOccurred);
    
    resetStatistics();
}

SerialConnection::~SerialConnection() {
    if (isOpen()) {
        close();
    }
}

// === IConnection接口实现 ===

SimpleResult SerialConnection::open(const ConnectionConfig& config) {
    QMutexLocker locker(&m_mutex);
    
    if (m_status == ConnectionStatus::Connected) {
        return SimpleResult::success(true);
    }
    
    m_config = config;
    
    // 获取串口名称
    QString portName = config.portName;
    if (portName.isEmpty()) {
        portName = config.parameters.value("portName").toString();
    }
    
    if (portName.isEmpty()) {
        m_lastError = "Port name not specified";
        return SimpleResult::failure(m_lastError);
    }
    
    setStatus(ConnectionStatus::Connecting);
    
    m_serialPort->setPortName(portName);
    
    // 应用串口配置
    if (!applySerialConfig()) {
        setStatus(ConnectionStatus::Disconnected);
        return SimpleResult::failure(m_lastError);
    }
    
    // 打开串口
    if (!m_serialPort->open(QIODevice::ReadWrite)) {
        m_lastError = QString("Failed to open serial port %1: %2")
                      .arg(portName)
                      .arg(m_serialPort->errorString());
        setStatus(ConnectionStatus::Error);
        return SimpleResult::failure(m_lastError);
    }
    
    setStatus(ConnectionStatus::Connected);
    m_lastError.clear();
    
    return SimpleResult::success(true);
}

SimpleResult SerialConnection::close() {
    QMutexLocker locker(&m_mutex);
    
    if (m_status == ConnectionStatus::Disconnected) {
        return SimpleResult::success(true);
    }
    
    if (m_serialPort->isOpen()) {
        m_serialPort->close();
    }
    
    setStatus(ConnectionStatus::Disconnected);
    return SimpleResult::success(true);
}

ConnectionStatus SerialConnection::status() const {
    QMutexLocker locker(&m_mutex);
    return m_status;
}

bool SerialConnection::isOpen() const {
    QMutexLocker locker(&m_mutex);
    return m_status == ConnectionStatus::Connected && 
           m_serialPort && m_serialPort->isOpen();
}

qint64 SerialConnection::write(const QByteArray& data) {
    QMutexLocker locker(&m_mutex);
    
    if (!isOpen()) {
        m_lastError = "Serial port not open";
        return -1;
    }
    
    qint64 bytesWritten = m_serialPort->write(data);
    if (bytesWritten == -1) {
        m_lastError = QString("Write failed: %1").arg(m_serialPort->errorString());
        return -1;
    }
    
    // 更新统计信息
    updateStatistics(0, bytesWritten);
    
    return bytesWritten;
}

QByteArray SerialConnection::read(qint64 maxBytes) {
    QMutexLocker locker(&m_mutex);
    
    if (!isOpen()) {
        return QByteArray();
    }
    
    QByteArray data;
    if (maxBytes > 0) {
        data = m_serialPort->read(maxBytes);
    } else {
        data = m_serialPort->readAll();
    }
    
    if (!data.isEmpty()) {
        updateStatistics(data.size(), 0);
    }
    
    return data;
}

qint64 SerialConnection::bytesAvailable() const {
    QMutexLocker locker(&m_mutex);
    return m_serialPort ? m_serialPort->bytesAvailable() : 0;
}

bool SerialConnection::waitForReadyRead(int timeout) {
    QMutexLocker locker(&m_mutex);
    return m_serialPort ? m_serialPort->waitForReadyRead(timeout) : false;
}

bool SerialConnection::waitForBytesWritten(int timeout) {
    QMutexLocker locker(&m_mutex);
    return m_serialPort ? m_serialPort->waitForBytesWritten(timeout) : false;
}

DeviceStatistics SerialConnection::getStatistics() const {
    QMutexLocker locker(&m_mutex);
    return m_statistics;
}

QString SerialConnection::errorString() const {
    QMutexLocker locker(&m_mutex);
    return m_lastError;
}

void SerialConnection::resetStatistics() {
    QMutexLocker locker(&m_mutex);
    m_statistics = DeviceStatistics();
    m_statistics.lastActivity = QDateTime::currentMSecsSinceEpoch();
}

// === 串口特定功能 ===

QStringList SerialConnection::getAvailablePorts() {
    QStringList portNames;
    const auto portInfos = QSerialPortInfo::availablePorts();
    for (const auto& portInfo : portInfos) {
        portNames << portInfo.portName();
    }
    return portNames;
}

QSerialPortInfo SerialConnection::getPortInfo() const {
    QMutexLocker locker(&m_mutex);
    if (m_serialPort && !m_serialPort->portName().isEmpty()) {
        return QSerialPortInfo(m_serialPort->portName());
    }
    return QSerialPortInfo();
}

// === 私有槽函数 ===

void SerialConnection::onReadyRead() {
    // 发出信号通知有数据可读
    emit readyRead();
}

void SerialConnection::onErrorOccurred(QSerialPort::SerialPortError error) {
    if (error != QSerialPort::NoError) {
        QMutexLocker locker(&m_mutex);
        
        m_lastError = m_serialPort->errorString();
        m_statistics.errorsCount++;
        
        // 对于严重错误，改变连接状态
        if (error != QSerialPort::TimeoutError) {
            setStatus(ConnectionStatus::Error);
        }
        
        emit errorOccurred(m_lastError);
    }
}

// === 私有辅助函数 ===

void SerialConnection::updateStatistics(qint64 bytesRead, qint64 bytesWritten) {
    qint64 currentTime = QDateTime::currentMSecsSinceEpoch();
    
    if (bytesRead > 0) {
        m_statistics.bytesReceived += bytesRead;
        m_statistics.packetsReceived++;
        m_statistics.lastActivity = currentTime;
    }
    
    if (bytesWritten > 0) {
        m_statistics.bytesSent += bytesWritten;
        m_statistics.packetsSent++;
        m_statistics.lastActivity = currentTime;
    }
}

void SerialConnection::setStatus(ConnectionStatus newStatus) {
    if (m_status != newStatus) {
        ConnectionStatus previousStatus = m_status;
        m_status = newStatus;
        
        // 发出状态变化信号
        emit statusChanged(newStatus);
        
        // 根据状态发出特定信号
        if (newStatus == ConnectionStatus::Connected && 
            previousStatus != ConnectionStatus::Connected) {
            emit readyRead(); // 可能有数据等待读取
        }
    }
}

bool SerialConnection::applySerialConfig() {
    if (!m_serialPort) {
        m_lastError = "Serial port not initialized";
        return false;
    }
    
    // 从配置中提取参数
    ConfigParameters params = m_config.parameters;
    
    // 波特率
    int baudRate = params.value("baudRate", 9600).toInt();
    if (!m_serialPort->setBaudRate(baudRate)) {
        m_lastError = QString("Failed to set baud rate to %1").arg(baudRate);
        return false;
    }
    
    // 数据位
    int dataBits = params.value("dataBits", 8).toInt();
    QSerialPort::DataBits db = QSerialPort::Data8;
    switch (dataBits) {
        case 5: db = QSerialPort::Data5; break;
        case 6: db = QSerialPort::Data6; break;
        case 7: db = QSerialPort::Data7; break;
        case 8: db = QSerialPort::Data8; break;
        default:
            m_lastError = QString("Invalid data bits: %1").arg(dataBits);
            return false;
    }
    if (!m_serialPort->setDataBits(db)) {
        m_lastError = "Failed to set data bits";
        return false;
    }
    
    // 校验位
    QString parity = params.value("parity", "None").toString();
    QSerialPort::Parity p = QSerialPort::NoParity;
    if (parity == "Even") p = QSerialPort::EvenParity;
    else if (parity == "Odd") p = QSerialPort::OddParity;
    else if (parity == "Mark") p = QSerialPort::MarkParity;
    else if (parity == "Space") p = QSerialPort::SpaceParity;
    else if (parity != "None") {
        m_lastError = QString("Invalid parity: %1").arg(parity);
        return false;
    }
    
    if (!m_serialPort->setParity(p)) {
        m_lastError = "Failed to set parity";
        return false;
    }
    
    // 停止位
    int stopBits = params.value("stopBits", 1).toInt();
    QSerialPort::StopBits sb = QSerialPort::OneStop;
    switch (stopBits) {
        case 1: sb = QSerialPort::OneStop; break;
        case 2: sb = QSerialPort::TwoStop; break;
        default:
            m_lastError = QString("Invalid stop bits: %1").arg(stopBits);
            return false;
    }
    if (!m_serialPort->setStopBits(sb)) {
        m_lastError = "Failed to set stop bits";
        return false;
    }
    
    // 流控
    QString flowControl = params.value("flowControl", "None").toString();
    QSerialPort::FlowControl fc = QSerialPort::NoFlowControl;
    if (flowControl == "Hardware") fc = QSerialPort::HardwareControl;
    else if (flowControl == "Software") fc = QSerialPort::SoftwareControl;
    else if (flowControl != "None") {
        m_lastError = QString("Invalid flow control: %1").arg(flowControl);
        return false;
    }
    
    if (!m_serialPort->setFlowControl(fc)) {
        m_lastError = "Failed to set flow control";
        return false;
    }
    
    return true;
}

} // namespace Connection
} // namespace Communication
} // namespace LA