# device_rules.yml
# 设备规则引擎配置 - DSL形式的业务规则

# ============================================
# 安全保护规则 (最高优先级)
# ============================================
safety_rules:
  - name: "激光器过热保护"
    priority: 1
    condition: "temperature > 70"
    action: 
      type: "disable_laser"
      message: "温度过高，自动关闭激光器"
      recovery_condition: "temperature < 60"
      
  - name: "测量距离超限保护"  
    priority: 2
    condition: "measurement_distance > max_range * 1.1"
    action:
      type: "stop_measurement"
      message: "测量距离超出安全范围"
      
  - name: "电压异常保护"
    priority: 3
    condition: "voltage < 4.5 || voltage > 5.5"
    action:
      type: "emergency_shutdown"
      message: "电压异常，紧急停机"

# ============================================  
# 性能优化规则
# ============================================
performance_rules:
  - name: "响应时间优化"
    condition: "avg_response_time > 1000"
    action:
      type: "optimize_speed"
      params:
        - reduce_measurement_samples
        - switch_to_fast_mode
        
  - name: "精度优化"
    condition: "accuracy_deviation > acceptable_threshold"
    action:
      type: "optimize_accuracy" 
      params:
        - enable_advanced_filtering
        - increase_measurement_samples
        
  - name: "通信优化"
    condition: "communication_errors > 3"
    action:
      type: "optimize_communication"
      params:
        - reduce_baudrate
        - enable_error_correction

# ============================================
# 设备模式切换规则  
# ============================================
mode_switch_rules:
  - name: "高精度模式触发"
    condition: "accuracy_required < 0.5"
    action:
      type: "switch_mode"
      target_mode: "high_precision"
      strategies:
        calibration: "multi_point"
        filtering: "kalman"
        measurement_count: 10
        
  - name: "快速模式触发"
    condition: "speed_priority == true || timeout < 1000"
    action:
      type: "switch_mode"
      target_mode: "fast"
      strategies:
        calibration: "factory_default"
        filtering: "none"
        measurement_count: 1
        
  - name: "节能模式触发"
    condition: "battery_level < 20"
    action:
      type: "switch_mode"
      target_mode: "power_saving"
      strategies:
        laser_power_reduction: 0.8
        measurement_interval: 2000

# ============================================
# 环境适应规则
# ============================================  
environment_rules:
  - name: "温度补偿规则"
    conditions:
      - condition: "temperature < 10"
        action: 
          type: "adjust_laser_power"
          factor: 1.05
      - condition: "temperature > 40"  
        action:
          type: "adjust_laser_power" 
          factor: 0.95
          
  - name: "湿度补偿规则"
    condition: "humidity > 80"
    action:
      type: "enable_moisture_protection"
      params:
        - increase_warmup_time
        - enable_desiccant_system
        
  - name: "震动环境适应"
    condition: "vibration_level > threshold"
    action:
      type: "enable_vibration_compensation"
      strategies:
        filtering: "adaptive_filter"
        measurement_count: 5

# ============================================
# 故障诊断和恢复规则
# ============================================
fault_recovery_rules:
  - name: "通信超时恢复"
    condition: "communication_timeout > 3"
    actions:
      - type: "restart_communication"
        delay: 1000
      - type: "reduce_baudrate"
        new_baudrate: "half_current"
      - type: "switch_protocol"
        fallback_protocol: "simple_text"
        
  - name: "测量不稳定恢复"
    condition: "measurement_stability < 90%"
    actions:
      - type: "recalibrate_sensor"
        calibration_type: "quick"
      - type: "enable_advanced_filtering" 
        filter_type: "kalman"
      - type: "increase_sample_count"
        factor: 2
        
  - name: "硬件故障检测"
    condition: "hardware_check_failed"
    actions:
      - type: "run_self_diagnostic"
      - type: "isolate_faulty_component"
      - type: "switch_to_backup_mode"

# ============================================
# 策略选择规则
# ============================================
strategy_selection:
  filtering:
    rules:
      - condition: "noise_level < 0.1"
        strategy: "none"
      - condition: "noise_level < 0.5" 
        strategy: "moving_average"
      - condition: "noise_level >= 0.5"
        strategy: "kalman"
        
  calibration:
    rules:
      - condition: "accuracy_required > 2.0"
        strategy: "factory_default"
      - condition: "accuracy_required > 1.0"
        strategy: "single_point" 
      - condition: "accuracy_required <= 1.0"
        strategy: "multi_point"
        
  communication:
    rules:
      - condition: "distance_to_host < 10"
        strategy: "high_speed"
        params: {baudrate: 115200}
      - condition: "electromagnetic_interference > threshold"
        strategy: "error_correction"
        params: {redundancy: 3}

# ============================================
# 规则引擎配置
# ============================================
engine_config:
  # 规则评估间隔 (毫秒)
  evaluation_interval: 1000
  
  # 规则优先级处理
  priority_handling: "highest_first"
  
  # 冲突规则解决策略
  conflict_resolution: "last_wins"
  
  # 规则执行超时 (毫秒)
  execution_timeout: 5000
  
  # 日志记录级别
  log_level: "INFO"
  
  # 规则缓存
  enable_rule_cache: true
  cache_ttl: 300000  # 5分钟

# ============================================
# 设备类型特定规则
# ============================================
device_specific_rules:
  SPRM-A1:
    max_range: 2000
    min_range: 50
    optimal_temperature: [10, 40]
    
  SPRM-A2: 
    max_range: 5000
    min_range: 30
    optimal_temperature: [5, 45]
    
  SPRM-A3:
    max_range: 10000
    min_range: 20
    optimal_temperature: [0, 50]

# ============================================  
# 规则版本控制
# ============================================
metadata:
  version: "1.0.0"
  author: "Device Team"
  created: "2025-01-22"
  description: "设备规则引擎配置 - 支持安全保护、性能优化、环境适应"
  
  # 变更历史
  changelog:
    - version: "1.0.0"
      date: "2025-01-22"  
      changes:
        - "初始规则集合"
        - "安全保护规则"
        - "性能优化规则"
        - "环境适应规则"