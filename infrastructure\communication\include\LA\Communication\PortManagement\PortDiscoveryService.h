#pragma once

#include <QObject>
#include <QList>
#include <QStringList>
#include <QVariantMap>
#include <QDateTime>
#include <QTimer>

namespace LA {
namespace Communication {
namespace PortManagement {

/**
 * @brief 端口状态枚举
 */
enum class PortStatus {
    Unknown,
    Available,
    Busy,
    Error,
    Disconnected
};

/**
 * @brief 端口类型枚举
 */
enum class PortType {
    Serial,
    Network,
    USB,
    Virtual
};

/**
 * @brief 端口能力信息
 */
struct PortCapability {
    QStringList supportedBaudRates;
    QStringList supportedDataBits;
    QStringList supportedStopBits;
    QStringList supportedParity;
    bool supportsFlowControl;
    int maxBufferSize;
};

/**
 * @brief 端口发现结果
 */
struct PortDiscoveryResult {
    QString portName;
    PortType portType;
    QString description;
    QString manufacturer;
    QString location;
    PortStatus status;
    PortCapability capability;
    QVariantMap properties;
    QDateTime discoveredTime;
};

/**
 * @brief 端口发现服务 - 最小接口
 * 
 * Linus: "只负责发现系统可用端口"
 * ✅ 负责: 端口扫描、状态监控、能力检测
 * ❌ 不涉及: 设备逻辑、协议处理、设备连接
 */
class PortDiscoveryService : public QObject {
    Q_OBJECT

public:
    explicit PortDiscoveryService(QObject *parent = nullptr);
    virtual ~PortDiscoveryService() = default;

    // ====== 端口发现 7- 单一职责 ======
    QList<PortDiscoveryResult> discoverPorts();
    QList<PortDiscoveryResult> discoverPortsByType(PortType type);
    
    // ====== 端口状态监控 ======
    PortStatus getPortStatus(const QString& portName);
    bool isPortAvailable(const QString& portName);
    
    // ====== 端口能力检测 ======
    PortCapability getPortCapability(const QString& portName);
    QStringList getSupportedBaudRates(const QString& portName);
    
    // ====== 实时监控 ======
    void startPortMonitoring();
    void stopPortMonitoring();

signals:
    void portDiscovered(const PortDiscoveryResult& result);
    void portRemoved(const QString& portName);
    void portStatusChanged(const QString& portName, PortStatus status);

private slots:
    void onMonitoringTimer();

private:
    // ====== 扫描方法 ======
    void scanSerialPorts();
    void scanNetworkPorts();
    void scanUSBPorts();
    
    // ====== 状态检测 ======
    PortStatus detectPortStatus(const QString& portName);
    PortCapability detectPortCapability(const QString& portName);
    
    // ====== 内部状态 ======
    bool m_isMonitoring;
    QList<PortDiscoveryResult> m_discoveredPorts;
    QTimer* m_monitoringTimer;
};

} // namespace PortManagement
} // namespace Communication
} // namespace LA
