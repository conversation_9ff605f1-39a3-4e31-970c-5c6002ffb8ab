/**
 * @file CommunicationCapability_stub.cpp
 * @brief 通信能力存根实现 - 当基础设施依赖不可用时的简化版本
 */

#include "CommunicationCapability.h"
#include <QDebug>
#include <QTimer>

namespace LA::Device::Capability {

CommunicationCapability::CommunicationCapability(QObject* parent)
    : IDeviceCapability(parent)
    , m_initialized(false)
    , m_connected(false)
    , m_commandProvider(nullptr)
{
    qWarning() << "[CommunicationCapability] Using stub implementation - infrastructure disabled";
}

CommunicationCapability::~CommunicationCapability() {
    // No cleanup needed in stub version
}

bool CommunicationCapability::initialize(const QVariantMap& config) {
    Q_UNUSED(config)
    
    qWarning() << "[CommunicationCapability] Stub initialization";
    m_initialized = true;
    m_communicationConfig = config;
    
    // Simulate successful initialization
    QTimer::singleShot(100, this, [this]() {
        emit capabilityReady();
    });
    
    return true;
}

bool CommunicationCapability::isInitialized() const {
    return m_initialized;
}

QVariantMap CommunicationCapability::getStatus() const {
    QVariantMap status;
    status["initialized"] = m_initialized;
    status["connected"] = m_connected;
    status["type"] = "stub";
    status["error"] = "Infrastructure dependencies not available";
    return status;
}

QVariantMap CommunicationCapability::sendDeviceCommand(const QString& command, const QVariantMap& params) {
    Q_UNUSED(command)
    Q_UNUSED(params)
    
    QVariantMap result;
    result["success"] = false;
    result["error"] = "Communication capability disabled - infrastructure not available";
    qWarning() << "[CommunicationCapability] sendDeviceCommand called but infrastructure is disabled";
    
    return result;
}

bool CommunicationCapability::connectToDevice(const QVariantMap& connectionParams) {
    Q_UNUSED(connectionParams)
    
    qWarning() << "[CommunicationCapability] connectToDevice called but infrastructure is disabled";
    m_connected = false;
    return false;
}

bool CommunicationCapability::disconnectFromDevice() {
    qWarning() << "[CommunicationCapability] disconnectFromDevice called";
    m_connected = false;
    return true;
}

bool CommunicationCapability::isConnected() const {
    return m_connected;
}

// Stub implementations for other methods
void CommunicationCapability::injectCommunicationServices(
    std::shared_ptr<Command::ICommandProvider> commandProvider) {
    m_commandProvider = commandProvider;
    qDebug() << "[CommunicationCapability] Command provider injected (stub mode)";
}

QVariantMap CommunicationCapability::getConnectionStatistics() const {
    QVariantMap stats;
    stats["connected"] = false;
    stats["error"] = "Stub implementation";
    return stats;
}

void CommunicationCapability::updateConnectionStatus() {
    // No-op in stub version
}

} // namespace LA::Device::Capability