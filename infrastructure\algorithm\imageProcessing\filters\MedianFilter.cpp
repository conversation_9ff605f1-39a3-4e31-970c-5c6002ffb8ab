#include "MedianFilter.h"
#include <QtMath>

namespace ImageProcessing {

MedianFilter::MedianFilter() {
    // 设置默认参数
    params_.strength = 1.0f;
    params_.enabled = true;
    params_.kernelSize = 3;
    
    logDebug("MedianFilter initialized with 3x3 kernel");
}

bool MedianFilter::apply(ImageDataU32& data) {
    try {
        validateInput(data);
        
        if (!params_.enabled) {
            logDebug("Filter disabled, skipping processing");
            return true;
        }
        
        logDebug(QString("Applying median filter to %1x%2 image with kernel size %3")
                .arg(data.width()).arg(data.height()).arg(params_.kernelSize));
        
        // 创建临时图像存储结果
        ImageDataU32 temp(data.width(), data.height());
        
        // 对每个像素应用中值滤波
        for (uint32_t y = 0; y < data.height(); ++y) {
            for (uint32_t x = 0; x < data.width(); ++x) {
                uint32_t medianResult = applyMedianAtPixel(data, x, y);
                
                // 应用滤波强度
                float originalValue = static_cast<float>(data.matrix()[y][x]);
                float filteredValue = originalValue + params_.strength * (static_cast<float>(medianResult) - originalValue);
                
                temp.matrix()[y][x] = safeFloatToUint32(filteredValue);
            }
        }
        
        // 复制结果回原图像
        data = std::move(temp);
        
        logDebug("Median filter applied successfully");
        return true;
        
    } catch (const ProcessingException& e) {
        qWarning() << "MedianFilter::apply failed:" << e.qMessage();
        return false;
    } catch (const std::exception& e) {
        qWarning() << "MedianFilter::apply failed:" << e.what();
        return false;
    }
}

bool MedianFilter::apply(const ImageDataU32& src, ImageDataU32& dst) {
    try {
        validateInput(src);
        
        // 确保目标图像尺寸正确
        if (dst.width() != src.width() || dst.height() != src.height()) {
            dst.resize(src.width(), src.height());
        }
        
        // 复制源数据到目标
        dst = src;
        
        // 应用滤波
        return apply(dst);
        
    } catch (const ProcessingException& e) {
        qWarning() << "MedianFilter::apply (src->dst) failed:" << e.qMessage();
        return false;
    } catch (const std::exception& e) {
        qWarning() << "MedianFilter::apply (src->dst) failed:" << e.what();
        return false;
    }
}

void MedianFilter::setParameters(const FilterParams& params) {
    const MedianParams* medianParams = dynamic_cast<const MedianParams*>(&params);
    if (!medianParams) {
        throw InvalidParameterException("params", "must be MedianParams type");
    }
    
    validateMedianParams(*medianParams);
    params_ = *medianParams;
    
    logDebug("Parameters updated: " + params_.toString());
}

std::unique_ptr<FilterParams> MedianFilter::getParameters() const {
    return std::make_unique<MedianParams>(params_);
}

QString MedianFilter::getAlgorithmName() const {
    return "MedianFilter";
}

QString MedianFilter::getDescription() const {
    return "Median filter for effective noise reduction while preserving edges, especially good for salt-and-pepper noise";
}

bool MedianFilter::isSupported(uint32_t width, uint32_t height) const {
    try {
        ValidationUtils::validatePositive(width, "width");
        ValidationUtils::validatePositive(height, "height");
        
        // 检查图像是否足够大以应用滤波核
        uint32_t minSize = static_cast<uint32_t>(params_.kernelSize);
        return width >= minSize && height >= minSize;
    } catch (const ProcessingException&) {
        return false;
    }
}

uint32_t MedianFilter::estimateProcessingTime(uint32_t width, uint32_t height) const {
    // 中值滤波需要排序，复杂度较高
    uint64_t totalPixels = static_cast<uint64_t>(width) * height;
    uint64_t kernelOps = static_cast<uint64_t>(params_.kernelSize) * params_.kernelSize;
    // 假设每个中值操作需要0.002毫秒（包含排序时间）
    return static_cast<uint32_t>((totalPixels * kernelOps) / 500);
}

void MedianFilter::reset() {
    params_.reset();
    logDebug("MedianFilter reset to default state");
}

QString MedianFilter::getVersion() const {
    return "1.0.0";
}

bool MedianFilter::isThreadSafe() const {
    return true; // 无状态操作，线程安全
}

bool MedianFilter::supportsInPlace() const {
    return false; // 需要临时缓冲区
}

void MedianFilter::setPreset(const QString& preset) {
    if (preset == "noise_reduction") {
        params_.kernelSize = 3;
        params_.strength = 1.0f;
    } else if (preset == "edge_preserve") {
        params_.kernelSize = 3;
        params_.strength = 0.8f;
    } else if (preset == "artifact_removal") {
        params_.kernelSize = 5;
        params_.strength = 1.0f;
    } else if (preset == "light_smooth") {
        params_.kernelSize = 3;
        params_.strength = 0.5f;
    } else {
        throw InvalidParameterException("preset", QString("unsupported preset: %1").arg(preset));
    }
    
    logDebug(QString("Set preset: %1 (kernelSize=%2, strength=%3)")
            .arg(preset).arg(params_.kernelSize).arg(params_.strength));
}

QStringList MedianFilter::getSupportedPresets() {
    return {"noise_reduction", "edge_preserve", "artifact_removal", "light_smooth"};
}

uint32_t MedianFilter::applyMedianAtPixel(const ImageDataU32& src, uint32_t x, uint32_t y) const {
    QVector<uint32_t> values;
    int halfKernel = params_.kernelSize / 2;
    
    // 收集邻域像素值
    for (int ky = 0; ky < params_.kernelSize; ++ky) {
        for (int kx = 0; kx < params_.kernelSize; ++kx) {
            int srcX = static_cast<int>(x) + kx - halfKernel;
            int srcY = static_cast<int>(y) + ky - halfKernel;
            
            uint32_t pixelValue = getSafePixelValue(src, srcX, srcY);
            values.append(pixelValue);
        }
    }
    
    return calculateMedian(values);
}

uint32_t MedianFilter::getSafePixelValue(const ImageDataU32& src, int x, int y) const {
    // 边界处理：镜像扩展
    if (x < 0) x = -x;
    if (y < 0) y = -y;
    if (x >= static_cast<int>(src.width())) x = 2 * (src.width() - 1) - x;
    if (y >= static_cast<int>(src.height())) y = 2 * (src.height() - 1) - y;
    
    // 确保在有效范围内
    x = qBound(0, x, static_cast<int>(src.width() - 1));
    y = qBound(0, y, static_cast<int>(src.height() - 1));
    
    return src.matrix()[y][x];
}

uint32_t MedianFilter::calculateMedian(QVector<uint32_t>& values) const {
    if (values.isEmpty()) {
        return 0;
    }
    
    // 排序并找到中值
    std::sort(values.begin(), values.end());
    
    int size = values.size();
    if (size % 2 == 1) {
        // 奇数个元素，返回中间元素
        return values[size / 2];
    } else {
        // 偶数个元素，返回中间两个元素的平均值
        uint64_t sum = static_cast<uint64_t>(values[size / 2 - 1]) + values[size / 2];
        return static_cast<uint32_t>(sum / 2);
    }
}

void MedianFilter::validateMedianParams(const MedianParams& params) const {
    params.validate();
}

void MedianFilter::logDebug(const QString& message) const {
    qDebug() << "[MedianFilter]" << message;
}

} // namespace ImageProcessing
