#pragma once

#include <QObject>
#include <QList>
#include <QStringList>
#include <QVariantMap>
#include <QDateTime>
#include "../Discovery/DeviceDiscoveryService.h"
// #include "LA/Communication/PortManagement/PortDiscoveryService.h"  // 暂时禁用，文件不存在
#include "IMatchingAlgorithm.h"  // 使用接口中定义的类型
#include "IPortScanner.h"  // 使用PortInfo类型

namespace LA {
namespace DeviceManagement {
namespace Matching {

// 匹配策略枚举已在IMatchingAlgorithm.h中定义
// enum class MatchingStrategy 已在接口中定义

/**
 * @brief 匹配评分
 */
struct MatchingScore {
    int totalScore;
    int portTypeScore;
    int capabilityScore;
    int availabilityScore;
    QString scoreDetails;
};

/**
 * @brief 匹配规则
 */
struct MatchingRule {
    QString ruleId;
    QString ruleName;
    QString deviceTypePattern;
    QString portTypePattern;
    int priority;
    bool isEnabled;
    QVariantMap ruleParameters;
};

// MatchingResult已在IMatchingAlgorithm.h中定义
// struct MatchingResult 已在接口中定义

/**
 * @brief 设备端口匹配器 - 纯粹匹配
 * 
 * Linus: "只负责设备和端口的匹配逻辑"
 * ✅ 负责: 匹配算法、兼容性验证、匹配评分
 * ❌ 不涉及: 设备实例化、端口打开、数据传输
 */
class DevicePortMatcher : public QObject {
    Q_OBJECT

public:
    explicit DevicePortMatcher(QObject *parent = nullptr);
    virtual ~DevicePortMatcher() = default;

    // ====== 匹配逻辑 - 单一职责 ======
    QList<MatchingResult> matchDevicesToPorts(
        const QList<Discovery::DeviceDiscoveryResult>& devices,
        const QList<PortInfo>& ports);
    
    MatchingResult findBestPortForDevice(
        const Discovery::DeviceDiscoveryResult& device,
        const QList<PortInfo>& availablePorts);
    
    // ====== 匹配规则管理 ======
    void addMatchingRule(const MatchingRule& rule);
    void removeMatchingRule(const QString& ruleId);
    QList<MatchingRule> getMatchingRules() const;
    
    // ====== 匹配策略 ======
    void setMatchingStrategy(MatchingStrategy strategy);
    MatchingStrategy getMatchingStrategy() const;
    
    // ====== 匹配验证 ======
    bool validateMatch(const Discovery::DeviceDiscoveryResult& device, 
                       const PortInfo& port);
    MatchingScore calculateMatchingScore(const Discovery::DeviceDiscoveryResult& device, 
                                         const PortInfo& port);

signals:
    void matchingCompleted(const QList<MatchingResult>& results);
    void matchingError(const QString& error);

private:
    // ====== 匹配算法 ======
    bool matchPortType(const Discovery::DeviceDiscoveryResult& device, 
                       const PortInfo& port);
    bool matchCapability(const Discovery::DeviceDiscoveryResult& device, 
                         const PortInfo& port);
    int calculatePortTypeScore(const Discovery::DeviceDiscoveryResult& device, 
                               const PortInfo& port);
    int calculateCapabilityScore(const Discovery::DeviceDiscoveryResult& device, 
                                 const PortInfo& port);
    
    // ====== 内部状态 ======
    QList<MatchingRule> m_rules;
    MatchingStrategy m_strategy;
};

} // namespace Matching
} // namespace DeviceManagement
} // namespace LA
