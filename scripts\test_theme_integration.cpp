/**
 * @file test_theme_integration.cpp
 * @brief 主题系统集成测试
 * 
 * 用于验证ThemeManager重构后的功能是否正常工作，
 * 特别是验证硬编码样式迁移到主题系统后的效果。
 */

#include <QApplication>
#include <QMainWindow>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QLabel>
#include <QComboBox>
#include <QDebug>

// 包含LA模块
#include <LA/Themes/ThemeManager.h>
#include <LA/SideBar/ActivityBar.h>

class ThemeTestWindow : public QMainWindow {
    Q_OBJECT

public:
    explicit ThemeTestWindow(QWidget* parent = nullptr) : QMainWindow(parent) {
        setupUI();
        connectSignals();
        
        // 应用初始主题
        applyTheme();
    }

private slots:
    void onThemeChanged(const QString& themeName) {
        qDebug() << "Theme changed to:" << themeName;
        applyTheme();
    }
    
    void onThemeComboChanged(const QString& themeName) {
        auto themeManager = &LA::Themes::ThemeManager::instance();
        themeManager->setCurrentTheme(themeName);
    }

private:
    void setupUI() {
        auto centralWidget = new QWidget(this);
        setCentralWidget(centralWidget);
        
        auto mainLayout = new QHBoxLayout(centralWidget);
        
        // 创建ActivityBar实例
        m_activityBar = new LA::SideBar::ActivityBar(this);
        
        // 添加一些测试活动
        m_activityBar->addActivity("file_explorer", QIcon(), "文件浏览器");
        m_activityBar->addActivity("plugins", QIcon(), "插件系统");
        m_activityBar->addActivity("tasks", QIcon(), "任务管理");
        
        // 添加底部按钮
        m_activityBar->addBottomButton("settings", QIcon(), "设置");
        
        mainLayout->addWidget(m_activityBar);
        
        // 创建控制面板
        auto controlPanel = createControlPanel();
        mainLayout->addWidget(controlPanel);
        
        setWindowTitle("LA Theme System Integration Test");
        resize(800, 600);
    }
    
    QWidget* createControlPanel() {
        auto panel = new QWidget(this);
        auto layout = new QVBoxLayout(panel);
        
        // 主题选择
        layout->addWidget(new QLabel("主题选择:"));
        m_themeCombo = new QComboBox(this);
        
        auto themeManager = &LA::Themes::ThemeManager::instance();
        auto themes = themeManager->getAvailableThemes();
        for (const QString& theme : themes) {
            m_themeCombo->addItem(theme);
        }
        
        layout->addWidget(m_themeCombo);
        
        // 测试按钮
        auto testButtons = createTestButtons();
        layout->addWidget(testButtons);
        
        // 状态显示
        auto statusArea = createStatusArea();
        layout->addWidget(statusArea);
        
        layout->addStretch();
        
        return panel;
    }
    
    QWidget* createTestButtons() {
        auto group = new QWidget(this);
        auto layout = new QVBoxLayout(group);
        
        layout->addWidget(new QLabel("测试按钮 (应用主题系统):"));
        
        // 主要按钮
        m_primaryButton = new QPushButton("主要按钮", this);
        layout->addWidget(m_primaryButton);
        
        // 次要按钮
        m_secondaryButton = new QPushButton("次要按钮", this);
        layout->addWidget(m_secondaryButton);
        
        // 危险按钮
        m_dangerButton = new QPushButton("危险按钮", this);
        layout->addWidget(m_dangerButton);
        
        return group;
    }
    
    QWidget* createStatusArea() {
        auto group = new QWidget(this);
        auto layout = new QVBoxLayout(group);
        
        layout->addWidget(new QLabel("状态指示器:"));
        
        // 成功状态
        m_successLabel = new QLabel("操作成功", this);
        layout->addWidget(m_successLabel);
        
        // 警告状态
        m_warningLabel = new QLabel("注意警告", this);
        layout->addWidget(m_warningLabel);
        
        // 错误状态
        m_errorLabel = new QLabel("发生错误", this);
        layout->addWidget(m_errorLabel);
        
        // 信息状态
        m_infoLabel = new QLabel("提示信息", this);
        layout->addWidget(m_infoLabel);
        
        return group;
    }
    
    void connectSignals() {
        auto themeManager = &LA::Themes::ThemeManager::instance();
        
        // 连接主题变化信号
        connect(themeManager, &LA::Themes::ThemeManager::themeChanged,
                this, &ThemeTestWindow::onThemeChanged);
        
        // 连接主题选择下拉框
        connect(m_themeCombo, QOverload<const QString&>::of(&QComboBox::currentTextChanged),
                this, &ThemeTestWindow::onThemeComboChanged);
        
        // 连接ActivityBar信号
        connect(m_activityBar, &LA::SideBar::ActivityBar::activityClicked,
                [](const QString& id) {
                    qDebug() << "Activity clicked:" << id;
                });
    }
    
    void applyTheme() {
        auto themeManager = &LA::Themes::ThemeManager::instance();
        
        // 应用主题到测试按钮
        themeManager->applyThemeToWidget(m_primaryButton, 
            LA::Themes::ThemeManager::ComponentType::Button);
        themeManager->applyThemeToWidget(m_secondaryButton, 
            LA::Themes::ThemeManager::ComponentType::Button);
        themeManager->applyThemeToWidget(m_dangerButton, 
            LA::Themes::ThemeManager::ComponentType::Button);
        
        // 应用主题到状态标签
        themeManager->applyThemeToWidget(m_successLabel, 
            LA::Themes::ThemeManager::ComponentType::StatusIndicator);
        themeManager->applyThemeToWidget(m_warningLabel, 
            LA::Themes::ThemeManager::ComponentType::StatusIndicator);
        themeManager->applyThemeToWidget(m_errorLabel, 
            LA::Themes::ThemeManager::ComponentType::StatusIndicator);
        themeManager->applyThemeToWidget(m_infoLabel, 
            LA::Themes::ThemeManager::ComponentType::StatusIndicator);
        
        qDebug() << "Theme applied to test window";
    }

private:
    LA::SideBar::ActivityBar* m_activityBar;
    QComboBox* m_themeCombo;
    
    // 测试按钮
    QPushButton* m_primaryButton;
    QPushButton* m_secondaryButton;
    QPushButton* m_dangerButton;
    
    // 状态标签
    QLabel* m_successLabel;
    QLabel* m_warningLabel;
    QLabel* m_errorLabel;
    QLabel* m_infoLabel;
};

int main(int argc, char *argv[]) {
    QApplication app(argc, argv);
    
    qDebug() << "Starting LA Theme System Integration Test...";
    
    // 创建测试窗口
    ThemeTestWindow window;
    window.show();
    
    // 输出当前主题信息
    auto themeManager = &LA::Themes::ThemeManager::instance();
    qDebug() << "Current theme:" << themeManager->currentTheme();
    qDebug() << "Available themes:" << themeManager->getAvailableThemes();
    qDebug() << "Dark mode:" << themeManager->isDarkMode();
    
    // 测试语义颜色获取
    auto primaryColor = themeManager->getSemanticColor(LA::Themes::ThemeManager::ColorRole::Primary);
    auto backgroundColor = themeManager->getSemanticColor(LA::Themes::ThemeManager::ColorRole::Background);
    qDebug() << "Primary color:" << primaryColor.name();
    qDebug() << "Background color:" << backgroundColor.name();
    
    // 测试尺寸获取
    auto borderRadius = themeManager->getMetric(LA::Themes::ThemeManager::Metric::BorderRadiusMedium);
    auto padding = themeManager->getMetric(LA::Themes::ThemeManager::Metric::PaddingMedium);
    qDebug() << "Border radius:" << borderRadius;
    qDebug() << "Padding:" << padding;
    
    qDebug() << "Test window created successfully. Use the theme selector to test theme switching.";
    
    return app.exec();
}

#include "test_theme_integration.moc"