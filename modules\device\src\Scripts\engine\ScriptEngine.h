/**
 * @file ScriptEngine.h
 * @brief Lua脚本引擎 - 四层架构第4层核心
 * 
 * 功能：
 * - 安全的Lua脚本执行环境
 * - 设备行为脚本化控制
 * - 沙箱模式和资源限制
 * - 与设备组件的API绑定
 */

#pragma once

#include <QObject>
#include <QString>
#include <QVariantMap>
#include <QVariantList>
#include <QStringList>
#include <QTimer>
#include <memory>

// Lua C API - conditional compilation for build compatibility
#ifdef ENABLE_LUA_SUPPORT
extern "C" {
#include "lua.h"
#include "lualib.h"  
#include "lauxlib.h"
}
#else
// Stub definitions when Lua is not available
typedef void lua_State;
#endif

namespace LA::Device::Script {

/**
 * @brief Lua脚本引擎 - 四层架构顶层
 * 
 * 设计原则：
 * - 安全第一：沙箱执行，资源限制
 * - 简单易用：业务人员可编写脚本
 * - 性能可控：执行时间和内存限制
 */
class ScriptEngine : public QObject {
    Q_OBJECT

public:
    explicit ScriptEngine(QObject* parent = nullptr);
    virtual ~ScriptEngine();

    // === 脚本管理 ===
    
    /**
     * @brief 加载设备行为脚本
     * @param scriptFile 脚本文件路径
     * @return 是否加载成功
     */
    bool loadScript(const QString& scriptFile);
    
    /**
     * @brief 重新加载脚本 (热更新)
     * @return 是否成功
     */
    bool reloadScript();
    
    /**
     * @brief 验证脚本语法
     * @param script 脚本内容
     * @return 验证结果和错误信息
     */
    QPair<bool, QString> validateScript(const QString& script);

    // === 设备脚本执行 ===
    
    /**
     * @brief 执行设备行为函数
     * @param deviceType 设备类型
     * @param event 事件名称 (如 onMeasurementStart)
     * @param params 事件参数
     * @return 执行结果
     */
    QVariantMap executeDeviceBehavior(const QString& deviceType, 
                                    const QString& event, 
                                    const QVariantMap& params);
    
    /**
     * @brief 执行策略选择脚本
     * @param strategyType 策略类型 (如 "filtering", "calibration")
     * @param input 输入参数
     * @return 策略选择结果
     */
    QVariantMap executeStrategySelection(const QString& strategyType, 
                                       const QVariantMap& input);
    
    /**
     * @brief 通用脚本函数调用
     * @param functionName 函数名
     * @param args 参数列表
     * @return 返回值
     */
    QVariant callFunction(const QString& functionName, 
                         const QVariantList& args = {});

    // === 安全和控制 ===
    
    /**
     * @brief 启用/禁用沙箱模式
     * @param enabled 是否启用
     */
    void setSandboxMode(bool enabled);
    
    /**
     * @brief 设置资源限制
     * @param memoryLimitKB 内存限制(KB)
     * @param timeLimitMs 执行时间限制(毫秒)
     */
    void setResourceLimits(int memoryLimitKB, int timeLimitMs);
    
    /**
     * @brief 设置脚本变量
     * @param name 变量名
     * @param value 变量值
     */
    void setVariable(const QString& name, const QVariant& value);
    
    /**
     * @brief 获取脚本变量
     * @param name 变量名
     * @return 变量值
     */
    QVariant getVariable(const QString& name);

    // === 调试和监控 ===
    
    /**
     * @brief 获取脚本执行统计
     * @return 执行次数、耗时等统计信息
     */
    QVariantMap getExecutionStats() const;
    
    /**
     * @brief 获取最后执行错误
     * @return 错误信息
     */
    QString getLastError() const;

Q_SIGNALS:
    void scriptLoaded(const QString& scriptFile);
    void scriptError(const QString& error);
    void executionTimeout();
    void memoryLimitExceeded();

private slots:
    void onExecutionTimeout();

private:
    // Lua状态机
    lua_State* m_luaState;
    QString m_currentScript;
    QString m_lastError;
    
    // 安全控制
    bool m_sandboxEnabled;
    int m_memoryLimitKB;
    int m_timeLimitMs;
    QTimer* m_timeoutTimer;
    
    // 执行统计
    int m_executionCount;
    qint64 m_totalExecutionTime;
    
    // === 内部方法 ===
    
    /**
     * @brief 初始化Lua状态机
     */
    bool initializeLua();
    
    /**
     * @brief 设置沙箱环境
     */
    void setupSandbox();
    
    /**
     * @brief 注册设备API到Lua
     */
    void registerDeviceAPI();
    
    /**
     * @brief Lua值转Qt值
     */
    QVariant luaToVariant(int index) const;
    
    /**
     * @brief Qt值转Lua值
     */
    void variantToLua(const QVariant& value);
    
    /**
     * @brief 检查内存使用
     */
    bool checkMemoryLimit() const;
    
    /**
     * @brief 清理Lua状态机
     */
    void cleanup();
    
    // === Lua C API 回调函数 ===
    static int luaDeviceExecuteCommand(lua_State* L);
    static int luaDeviceSetStrategy(lua_State* L);
    static int luaDeviceGetSpec(lua_State* L);
    static int luaDeviceGetEnvironment(lua_State* L);
    static int luaLogMessage(lua_State* L);
};

} // namespace LA::Device::Script