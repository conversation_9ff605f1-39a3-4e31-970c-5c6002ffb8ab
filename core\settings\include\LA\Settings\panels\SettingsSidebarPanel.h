#pragma once

#include "LA/SideBar/SidebarPanelBase.h"
#include "LA/SideBar/interfaces/ISidebarPanel.h"
#include "LA/Settings/SettingsManager.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QListWidget>
#include <QStackedWidget>
#include <QPushButton>
#include <QLabel>
#include <QSplitter>
#include <QGroupBox>
#include <QScrollArea>
#include <memory>

namespace LA {
namespace Settings {

/**
 * @brief 设置系统侧边栏面板
 * 
 * 在侧边栏中显示设置系统的面板，提供快速访问设置功能
 */
class SettingsSidebarPanel : public LA::SideBar::SidebarPanelBase {
    Q_OBJECT

public:
    explicit SettingsSidebarPanel(QWidget *parent = nullptr);
    virtual ~SettingsSidebarPanel() = default;

    /**
     * @brief 初始化面板
     * @return 初始化成功返回true
     */
    bool initializePanel();

    // ISidebarPanel接口实现
    void activate() override;
    void deactivate() override;

private slots:
    void onCategorySelectionChanged();
    void onOpenFullSettingsClicked();
    void onApplySettingsClicked();
    void onResetSettingsClicked();
    void onSettingsChanged();

private:
    void setupUI();
    void setupCategoryList();
    void setupPanelArea();
    void setupActionButtons();
    void connectSignals();
    void loadSettingsPanels();
    void updateCurrentPanel();
    void refreshPanelList();

private:
    // UI组件
    QVBoxLayout *m_mainLayout;
    QSplitter *m_splitter;
    
    // 左侧类别列表
    QGroupBox *m_categoryGroup;
    QListWidget *m_categoryList;
    
    // 右侧面板区域
    QGroupBox *m_panelGroup;
    QStackedWidget *m_panelStack;
    QLabel *m_noPanelLabel;
    
    // 底部操作按钮
    QHBoxLayout *m_buttonLayout;
    QPushButton *m_openFullSettingsButton;
    QPushButton *m_applyButton;
    QPushButton *m_resetButton;
    
    // 设置管理器
    SettingsManager *m_settingsManager;
    
    // 当前状态
    QString m_currentPanelId;
    bool m_hasUnsavedChanges;
};

} // namespace Settings
} // namespace LA
