#ifndef FILE_SYNC_H
#define FILE_SYNC_H

#include <QObject>
#include <QJsonObject>
#include <QJsonArray>
#include <QString>
#include <QStringList>
#include <QDateTime>
#include <QFileSystemWatcher>

/**
 * @brief 文件同步管理器
 * 
 * 负责JSON配置文件与C头文件之间的双向同步：
 * - JSON → 头文件：根据配置生成C宏定义
 * - 头文件 → JSON：解析C宏定义更新配置
 * - 文件监视：检测外部文件变更并同步
 * - 冲突处理：智能合并和冲突提示
 * 
 * 支持的宏格式：
 * - #define FEATURE_ENABLED 1
 * - #define MAX_RANGE 40.5
 * - #define DEVICE_NAME "Lidar-X100"
 */
class FileSync : public QObject
{
    Q_OBJECT

public:
    explicit FileSync(QObject *parent = nullptr);
    virtual ~FileSync();

    /**
     * @brief JSON配置转换为C头文件
     * @param configData JSON配置数据
     * @param headerPath 输出头文件路径
     * @param targetName 目标平台名称
     * @return 是否成功
     */
    bool jsonToHeader(const QJsonObject& configData, const QString& headerPath, 
                      const QString& targetName = QString());
    
    /**
     * @brief C头文件解析为JSON配置
     * @param headerPath 头文件路径
     * @param configData 输出JSON配置数据
     * @return 是否成功
     */
    bool headerToJson(const QString& headerPath, QJsonObject& configData);
    
    /**
     * @brief 启用文件监视
     * @param jsonPath JSON配置文件路径
     * @param headerPath 头文件路径
     */
    void enableFileWatching(const QString& jsonPath, const QString& headerPath);
    
    /**
     * @brief 禁用文件监视
     */
    void disableFileWatching();
    
    /**
     * @brief 检查文件冲突
     * @param jsonPath JSON文件路径
     * @param headerPath 头文件路径
     * @return 冲突信息，为空表示无冲突
     */
    QStringList checkConflicts(const QString& jsonPath, const QString& headerPath);
    
    /**
     * @brief 合并配置数据
     * @param baseConfig 基础配置
     * @param newConfig 新配置
     * @return 合并后的配置
     */
    QJsonObject mergeConfigs(const QJsonObject& baseConfig, const QJsonObject& newConfig);
    
    /**
     * @brief 设置同步选项
     */
    void setSyncOptions(const QJsonObject& options);
    
    /**
     * @brief 获取支持的文件格式
     */
    QStringList getSupportedFormats() const;

public slots:
    /**
     * @brief 执行完整同步
     * @param direction 同步方向："json_to_header" 或 "header_to_json"
     */
    void performSync(const QString& direction);

signals:
    /**
     * @brief 同步完成信号
     * @param direction 同步方向
     * @param success 是否成功
     */
    void syncCompleted(const QString& direction, bool success);
    
    /**
     * @brief 同步错误信号
     * @param error 错误描述
     */
    void syncError(const QString& error);
    
    /**
     * @brief 文件冲突信号
     * @param conflicts 冲突描述列表
     */
    void fileConflict(const QStringList& conflicts);
    
    /**
     * @brief 外部文件变更信号
     * @param filePath 变更的文件路径
     */
    void fileChanged(const QString& filePath);

private slots:
    void onFileChanged(const QString& path);
    void onDirectoryChanged(const QString& path);

private:
    // 头文件生成方法
    QString generateHeaderContent(const QJsonObject& configData, const QString& targetName);
    QString generateHeaderGuard(const QString& filename);
    QString generateFeatureDefines(const QJsonArray& features, const QString& targetName);
    QString generateDefineStatement(const QString& name, const QJsonValue& value);
    
    // 头文件解析方法
    bool parseHeaderFile(const QString& filePath, QJsonObject& configData);
    QStringList extractDefines(const QString& content);
    QPair<QString, QJsonValue> parseDefine(const QString& line);
    
    // 配置处理方法
    void processFeatureArray(const QJsonArray& features, const QString& targetName,
                            QStringList& defines, const QString& prefix = QString());
    void processFeatureObject(const QJsonObject& feature, const QString& targetName,
                             QStringList& defines, const QString& prefix);
    
    // 数据转换方法
    QString jsonValueToString(const QJsonValue& value);
    QJsonValue stringToJsonValue(const QString& str);
    QString sanitizeDefineName(const QString& name);
    QString generateDefineName(const QString& featureId, const QString& property = QString());
    
    // 冲突检测和合并
    bool detectConfigConflict(const QJsonObject& config1, const QJsonObject& config2);
    QJsonObject smartMergeFeatures(const QJsonArray& features1, const QJsonArray& features2);
    
    // 文件操作
    bool writeToFile(const QString& filePath, const QString& content);
    bool readFromFile(const QString& filePath, QString& content);
    bool createBackup(const QString& filePath);
    
    // 验证方法
    bool validateJsonConfig(const QJsonObject& config);
    bool validateHeaderFormat(const QString& content);
    
private:
    QFileSystemWatcher* m_fileWatcher;
    QJsonObject m_syncOptions;
    
    // 监视的文件路径
    QString m_jsonPath;
    QString m_headerPath;
    
    // 文件修改时间戳，用于冲突检测
    QDateTime m_jsonModified;
    QDateTime m_headerModified;
    
    // 同步状态
    bool m_syncInProgress;
    QString m_lastError;
    
    // 配置选项
    bool m_autoSync;              // 自动同步
    bool m_createBackup;          // 创建备份
    bool m_addTimestamp;          // 添加时间戳注释
    bool m_preserveComments;      // 保留注释
    QString m_headerGuardPrefix;  // 头文件保护符前缀
    
    // 格式化选项
    int m_indentSize;
    bool m_useSpaces;
    QString m_lineEnding;
    
    static const QStringList SUPPORTED_FORMATS;
    static const QString DEFAULT_HEADER_GUARD_PREFIX;
    static const QString GENERATED_HEADER_COMMENT;
};

#endif // FILE_SYNC_H