#ifndef LA_COMMUNICATIONTHREAD_H
#define LA_COMMUNICATIONTHREAD_H

#include "../../include/LA/Thread/ICommunicationThread.h"
#include <QMutex>
#include <QWaitCondition>
#include <QQueue>
#include <QTimer>

namespace LA {
namespace Thread {

/**
 * @brief 通信线程实现类
 * 
 * 实现设备通信线程的具体功能，支持串口、网络等多种通信方式。
 * 提供自动重连、错误恢复、协议解析等功能。
 */
class CommunicationThread : public ICommunicationThread
{
    Q_OBJECT

public:
    explicit CommunicationThread(const QString& deviceId, 
                               const ThreadConfig& config = ThreadConfig(),
                               QObject* parent = nullptr);
    ~CommunicationThread() override;

    // 基本信息
    QString getDeviceId() const override;
    QString getDeviceName() const override;
    CommunicationState getCommunicationState() const override;
    CommunicationType getCommunicationType() const override;
    
    // 连接管理
    bool connectToDevice() override;
    void disconnectFromDevice() override;
    bool isConnected() const override;
    bool isConnecting() const override;
    
    // 通信控制
    void startCommunication() override;
    void stopCommunication() override;
    bool isCommunicating() const override;
    void pauseCommunication() override;
    void resumeCommunication() override;
    
    // 数据传输
    bool sendData(const QByteArray& data) override;
    bool sendCommand(const QByteArray& command) override;
    void clearSendQueue() override;
    int getSendQueueSize() const override;
    
    // 配置管理
    void setCommunicationType(CommunicationType type) override;
    void setAutoReconnect(bool enable) override;
    bool isAutoReconnectEnabled() const override;
    void setReconnectInterval(int intervalMs) override;
    void setDataTimeout(int timeoutMs) override;
    
    // 统计信息
    qint64 getBytesSent() const override;
    qint64 getBytesReceived() const override;
    qint64 getMessagesSent() const override;
    qint64 getMessagesReceived() const override;
    qint64 getConnectionTime() const override;
    int getErrorCount() const override;
    
    // 错误处理
    QString getLastError() const override;
    void clearErrors() override;
    void resetStatistics() override;

public slots:
    void handleExternalCommand(const QByteArray& command) override;
    void handleConfigurationChange(const QString& key, const QVariant& value) override;
    void handleEmergencyStop() override;

protected:
    void run() override;
    
    // 子类需要实现的方法
    void initializeDevice() override;
    bool establishConnection() override;
    void processReceivedData(const QByteArray& data) override;
    bool parseProtocolFrame(const QByteArray& frame, QMap<QString, QString>& parsedData) override;
    void handleCommunicationError(const QString& error) override;

private slots:
    void onReconnectTimer();
    void onDataTimeoutTimer();

private:
    void setState(CommunicationState newState);
    void updateStatistics();
    void attemptReconnection();
    void processDataQueue();
    bool sendDataInternal(const QByteArray& data, bool isCommand = false);

private:
    // 基本配置
    QString m_deviceId;
    QString m_deviceName;
    ThreadConfig m_config;
    
    // 状态管理
    mutable QMutex m_mutex;
    CommunicationState m_state;
    CommunicationType m_communicationType;
    bool m_isRunning;
    bool m_isPaused;
    bool m_autoReconnect;
    
    // 数据队列
    QQueue<QByteArray> m_sendQueue;
    QQueue<QByteArray> m_commandQueue;
    QWaitCondition m_dataCondition;
    
    // 定时器
    QTimer* m_reconnectTimer;
    QTimer* m_dataTimeoutTimer;
    int m_reconnectInterval;
    int m_dataTimeout;
    
    // 统计信息
    qint64 m_bytesSent;
    qint64 m_bytesReceived;
    qint64 m_messagesSent;
    qint64 m_messagesReceived;
    qint64 m_connectionStartTime;
    int m_errorCount;
    QString m_lastError;
    
    Q_DISABLE_COPY(CommunicationThread)
};

} // namespace Thread
} // namespace LA

#endif // LA_COMMUNICATIONTHREAD_H