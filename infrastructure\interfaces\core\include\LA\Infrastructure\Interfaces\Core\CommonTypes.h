#pragma once

/**
 * @file CommonTypes.h
 * @brief LA项目基础类型定义 - 桥接到Foundation层
 * 
 * 🎯 Linus原则：这是一个桥接文件，将Foundation层的类型定义暴露给Infrastructure层
 * ✅ 职责：提供统一的类型访问路径，保持向后兼容性
 * ❌ 不涉及：重复定义类型，只是引用Foundation层定义
 * 
 * 这个文件解决了头文件路径问题，同时遵循"单一来源"原则
 */

// 引用Foundation层的统一类型定义
#include <LA/Foundation/Core/CommonTypes.h>

namespace LA {
namespace Infrastructure {
namespace Interfaces {
namespace Core {

// === 类型别名，保持向后兼容性 ===

// 基础类型别名
using ::LA::Foundation::Core::PortType;
using ::LA::Foundation::Core::PortStatus;
using ::LA::Foundation::Core::DeviceType;
using ::LA::Foundation::Core::DeviceInfo;
using ::LA::Foundation::Core::VersionInfo;
using ::LA::Foundation::Core::StatusInfo;
using ::LA::Foundation::Core::ErrorInfo;
using ::LA::Foundation::Core::EventInfo;
using ::LA::Foundation::Core::ConfigParameters;

// 回调函数类型别名
using ::LA::Foundation::Core::StatusCallback;
using ::LA::Foundation::Core::EventCallback;
using ::LA::Foundation::Core::ErrorCallback;
using ::LA::Foundation::Core::ProgressCallback;
using ::LA::Foundation::Core::DataCallback;

// 接口类型别名
using ::LA::Foundation::Core::IManager;
using ::LA::Foundation::Core::IComponent;
using ::LA::Foundation::Core::IService;

} // namespace Core
} // namespace Interfaces
} // namespace Infrastructure
} // namespace LA

/**
 * @brief 全局命名空间别名，简化使用
 */
namespace LAInfraCore = LA::Infrastructure::Interfaces::Core;
