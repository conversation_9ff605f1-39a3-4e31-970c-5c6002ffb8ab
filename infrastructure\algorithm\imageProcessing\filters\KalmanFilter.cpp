#include "KalmanFilter.h"
#include <QtMath>

namespace ImageProcessing {

KalmanFilter::KalmanFilter() : initialized_(false) {
    // 设置默认参数
    params_.strength = 1.0f;
    params_.enabled = true;
    params_.processNoise = 0.1f;
    params_.measurementNoise = 0.1f;
    params_.initialEstimate = 0.0f;
    
    logDebug("KalmanFilter initialized");
}

bool KalmanFilter::apply(ImageDataU32& data) {
    try {
        validateInput(data);
        
        if (!params_.enabled) {
            logDebug("Filter disabled, skipping processing");
            return true;
        }
        
        logDebug(QString("Applying <PERSON>lman filter to %1x%2 image")
                .arg(data.width()).arg(data.height()));
        
        // 初始化滤波器状态
        if (!initialized_ || estimates_.size() != static_cast<int>(data.height()) ||
            estimates_[0].size() != static_cast<int>(data.width())) {
            initializeFilter(data.width(), data.height());
        }
        
        // 对每个像素应用卡尔曼滤波
        for (uint32_t y = 0; y < data.height(); ++y) {
            for (uint32_t x = 0; x < data.width(); ++x) {
                float measurement = static_cast<float>(data.matrix()[y][x]);
                float prediction = kalmanPredict(x, y, measurement);
                float estimate = kalmanUpdate(x, y, measurement, prediction);
                
                // 应用滤波强度
                float filteredValue = measurement + params_.strength * (estimate - measurement);
                data.matrix()[y][x] = safeFloatToUint32(filteredValue);
            }
        }
        
        logDebug("Kalman filter applied successfully");
        return true;
        
    } catch (const ProcessingException& e) {
        qWarning() << "KalmanFilter::apply failed:" << e.qMessage();
        return false;
    } catch (const std::exception& e) {
        qWarning() << "KalmanFilter::apply failed:" << e.what();
        return false;
    }
}

bool KalmanFilter::apply(const ImageDataU32& src, ImageDataU32& dst) {
    try {
        validateInput(src);
        
        // 确保目标图像尺寸正确
        if (dst.width() != src.width() || dst.height() != src.height()) {
            dst.resize(src.width(), src.height());
        }
        
        // 复制源数据到目标
        dst = src;
        
        // 应用滤波
        return apply(dst);
        
    } catch (const ProcessingException& e) {
        qWarning() << "KalmanFilter::apply (src->dst) failed:" << e.qMessage();
        return false;
    } catch (const std::exception& e) {
        qWarning() << "KalmanFilter::apply (src->dst) failed:" << e.what();
        return false;
    }
}

void KalmanFilter::setParameters(const FilterParams& params) {
    const KalmanParams* kalmanParams = dynamic_cast<const KalmanParams*>(&params);
    if (!kalmanParams) {
        throw InvalidParameterException("params", "must be KalmanParams type");
    }
    
    validateKalmanParams(*kalmanParams);
    params_ = *kalmanParams;
    
    // 参数改变时需要重新初始化
    initialized_ = false;
    
    logDebug("Parameters updated: " + params_.toString());
}

std::unique_ptr<FilterParams> KalmanFilter::getParameters() const {
    return std::make_unique<KalmanParams>(params_);
}

QString KalmanFilter::getAlgorithmName() const {
    return "KalmanFilter";
}

QString KalmanFilter::getDescription() const {
    return "Kalman filter for image noise reduction and temporal smoothing";
}

bool KalmanFilter::isSupported(uint32_t width, uint32_t height) const {
    try {
        ValidationUtils::validatePositive(width, "width");
        ValidationUtils::validatePositive(height, "height");
        
        // 检查内存使用量（避免过大的图像）
        uint64_t totalPixels = static_cast<uint64_t>(width) * height;
        const uint64_t MAX_PIXELS = 100000000; // 100M像素限制
        
        return totalPixels <= MAX_PIXELS;
    } catch (const ProcessingException&) {
        return false;
    }
}

uint32_t KalmanFilter::estimateProcessingTime(uint32_t width, uint32_t height) const {
    // 卡尔曼滤波相对复杂，每个像素约需0.01毫秒
    uint64_t totalPixels = static_cast<uint64_t>(width) * height;
    return static_cast<uint32_t>(totalPixels / 100);
}

void KalmanFilter::reset() {
    params_.reset();
    initialized_ = false;
    estimates_.clear();
    errorCovariance_.clear();
    logDebug("KalmanFilter reset to default state");
}

QString KalmanFilter::getVersion() const {
    return "1.0.0";
}

bool KalmanFilter::isThreadSafe() const {
    return false; // 有内部状态，非线程安全
}

bool KalmanFilter::supportsInPlace() const {
    return true;
}

void KalmanFilter::initializeFilter(uint32_t width, uint32_t height) {
    logDebug(QString("Initializing Kalman filter for %1x%2 image").arg(width).arg(height));
    
    // 初始化状态估计矩阵
    estimates_.resize(height);
    errorCovariance_.resize(height);
    
    for (uint32_t y = 0; y < height; ++y) {
        estimates_[y].resize(width);
        errorCovariance_[y].resize(width);
        
        for (uint32_t x = 0; x < width; ++x) {
            estimates_[y][x] = params_.initialEstimate;
            errorCovariance_[y][x] = 1.0f; // 初始误差协方差
        }
    }
    
    initialized_ = true;
    logDebug("Kalman filter initialized successfully");
}

float KalmanFilter::kalmanPredict(uint32_t x, uint32_t y, float measurement) {
    // 预测步骤：状态预测（这里假设状态不变）
    float predictedEstimate = estimates_[y][x];
    
    // 预测误差协方差
    float predictedErrorCovariance = errorCovariance_[y][x] + params_.processNoise;
    errorCovariance_[y][x] = predictedErrorCovariance;
    
    return predictedEstimate;
}

float KalmanFilter::kalmanUpdate(uint32_t x, uint32_t y, float measurement, float prediction) {
    // 计算卡尔曼增益
    float kalmanGain = errorCovariance_[y][x] / (errorCovariance_[y][x] + params_.measurementNoise);
    
    // 更新状态估计
    float updatedEstimate = prediction + kalmanGain * (measurement - prediction);
    estimates_[y][x] = updatedEstimate;
    
    // 更新误差协方差
    errorCovariance_[y][x] = (1.0f - kalmanGain) * errorCovariance_[y][x];
    
    return updatedEstimate;
}

void KalmanFilter::validateKalmanParams(const KalmanParams& params) const {
    params.validate();
    
    ValidationUtils::validatePositive(params.processNoise, "processNoise");
    ValidationUtils::validatePositive(params.measurementNoise, "measurementNoise");
    ValidationUtils::validateRange(params.strength, 0.0f, 2.0f, "strength");
}

void KalmanFilter::logDebug(const QString& message) const {
    qDebug() << "[KalmanFilter]" << message;
}

} // namespace ImageProcessing
