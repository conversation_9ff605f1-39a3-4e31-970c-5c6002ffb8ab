/**
 * @file ConfigDrivenDeviceRegistry.h
 * @brief 配置驱动的设备注册系统 - 四层架构适配
 * 
 * 职责：
 * - 完全配置驱动的设备类型管理
 * - 消除静态注册的编译时依赖
 * - 支持运行时动态加载设备类型
 * - 与四层架构无缝集成
 */

#pragma once

#include <QObject>
#include <QString>
#include <QVariantMap>
#include <QJsonDocument>
#include <QFileSystemWatcher>
#include <memory>

namespace LA::Device::Registry {

/**
 * @brief 设备类型定义 - 完全来自配置
 */
struct ConfigDrivenDeviceType {
    QString deviceId;
    QString name;
    QString manufacturer;
    QString category;
    QString version;
    
    // 四层架构配置
    struct ArchitectureConfig {
        QVariantMap driver;         // 第1层：驱动配置
        QVariantList capabilities;  // 第2层：能力列表
        QVariantMap strategies;     // 第3层：策略配置
        QVariantMap script;         // 第4层：脚本配置
    } architecture;
    
    // 硬件规格
    QVariantMap hardwareSpecs;
    
    // 通信配置
    QVariantMap communication;
    
    // 环境配置
    QVariantMap environments;
    
    // 指令配置
    QVariantMap commands;
    
    // 元数据
    QDateTime createdTime;
    QDateTime lastModified;
    QString configFilePath;
};

/**
 * @brief 配置驱动的设备注册表
 * 
 * 设计原则：
 * - 零静态注册：所有设备类型来自配置文件
 * - 热加载支持：配置文件变化自动重载
 * - 版本管理：支持设备类型的版本控制
 * - 缓存优化：配置解析结果缓存，提高性能
 */
class ConfigDrivenDeviceRegistry : public QObject {
    Q_OBJECT

public:
    explicit ConfigDrivenDeviceRegistry(QObject* parent = nullptr);
    virtual ~ConfigDrivenDeviceRegistry() = default;

    // === 配置加载 ===
    
    /**
     * @brief 从目录加载所有设备配置
     * @param configDir 配置目录路径
     * @param recursive 是否递归搜索子目录
     * @return 加载的设备类型数量
     */
    int loadDeviceConfigurations(const QString& configDir, bool recursive = true);
    
    /**
     * @brief 从单个文件加载设备配置
     * @param configFile 配置文件路径
     * @return 是否成功
     */
    bool loadDeviceConfiguration(const QString& configFile);
    
    /**
     * @brief 从JSON数据加载设备配置
     * @param jsonData JSON配置数据
     * @param sourceFile 来源文件（用于跟踪）
     * @return 是否成功
     */
    bool loadFromJsonData(const QJsonDocument& jsonData, const QString& sourceFile = "");

    // === 设备类型查询 ===
    
    /**
     * @brief 获取所有注册的设备类型
     * @return 设备类型映射
     */
    QMap<QString, ConfigDrivenDeviceType> getAllDeviceTypes() const;
    
    /**
     * @brief 获取指定设备类型
     * @param deviceId 设备ID
     * @return 设备类型定义
     */
    ConfigDrivenDeviceType getDeviceType(const QString& deviceId) const;
    
    /**
     * @brief 是否存在指定设备类型
     * @param deviceId 设备ID
     * @return 是否存在
     */
    bool hasDeviceType(const QString& deviceId) const;
    
    /**
     * @brief 按类别获取设备类型
     * @param category 设备类别
     * @return 设备ID列表
     */
    QStringList getDeviceTypesByCategory(const QString& category) const;
    
    /**
     * @brief 按制造商获取设备类型
     * @param manufacturer 制造商
     * @return 设备ID列表
     */
    QStringList getDeviceTypesByManufacturer(const QString& manufacturer) const;

    // === 热更新支持 ===
    
    /**
     * @brief 启用配置文件监控
     * @param enable 是否启用
     */
    void setFileWatchingEnabled(bool enable);
    
    /**
     * @brief 是否启用了文件监控
     * @return 是否启用
     */
    bool isFileWatchingEnabled() const;
    
    /**
     * @brief 手动重新加载配置
     */
    void reloadConfigurations();

    // === 性能优化 ===
    
    /**
     * @brief 启用缓存
     * @param enable 是否启用
     */
    void setCacheEnabled(bool enable);
    
    /**
     * @brief 清除缓存
     */
    void clearCache();
    
    /**
     * @brief 获取缓存统计
     * @return 缓存统计信息
     */
    QVariantMap getCacheStatistics() const;

    // === 验证和诊断 ===
    
    /**
     * @brief 验证设备配置
     * @param config 设备配置
     * @return 验证结果
     */
    struct ValidationResult {
        bool valid = false;
        QStringList errors;
        QStringList warnings;
    };
    
    ValidationResult validateDeviceConfig(const QVariantMap& config) const;
    
    /**
     * @brief 获取注册表统计信息
     * @return 统计信息
     */
    QVariantMap getRegistryStatistics() const;

Q_SIGNALS:
    void deviceTypeLoaded(const QString& deviceId, const ConfigDrivenDeviceType& deviceType);
    void deviceTypeUpdated(const QString& deviceId, const ConfigDrivenDeviceType& deviceType);
    void deviceTypeRemoved(const QString& deviceId);
    void configurationReloaded(int deviceCount);
    void loadingError(const QString& error, const QString& file);

private slots:
    void onConfigFileChanged(const QString& filePath);
    void onConfigDirectoryChanged(const QString& dirPath);

private:
    QMap<QString, ConfigDrivenDeviceType> m_deviceTypes;
    QStringList m_configDirectories;
    QFileSystemWatcher* m_fileWatcher;
    bool m_fileWatchingEnabled;
    bool m_cacheEnabled;
    
    // 缓存
    mutable QMap<QString, QVariantMap> m_configCache;
    mutable QMap<QString, QDateTime> m_cacheTimestamps;
    
    // 统计
    struct Statistics {
        int totalDevicesLoaded = 0;
        int configFilesProcessed = 0;
        int loadErrors = 0;
        QDateTime lastLoadTime;
        qint64 totalLoadTimeMs = 0;
    } m_statistics;

    // === 内部方法 ===
    
    /**
     * @brief 解析设备配置JSON
     * @param json JSON文档
     * @param sourceFile 来源文件
     * @return 解析的设备类型列表
     */
    QList<ConfigDrivenDeviceType> parseDeviceConfigJson(const QJsonDocument& json, 
                                                        const QString& sourceFile);
    
    /**
     * @brief 转换JSON对象为设备类型
     * @param jsonObj JSON对象
     * @param deviceId 设备ID
     * @param sourceFile 来源文件
     * @return 设备类型定义
     */
    ConfigDrivenDeviceType convertJsonToDeviceType(const QJsonObject& jsonObj,
                                                   const QString& deviceId,
                                                   const QString& sourceFile);
    
    /**
     * @brief 验证四层架构配置
     * @param archConfig 架构配置
     * @param errors 错误列表
     * @return 是否有效
     */
    bool validateArchitectureConfig(const QVariantMap& archConfig, QStringList& errors) const;
    
    /**
     * @brief 更新文件监控
     */
    void updateFileWatching();
    
    /**
     * @brief 处理配置文件变化
     * @param filePath 文件路径
     */
    void handleConfigFileChange(const QString& filePath);
    
    /**
     * @brief 检查缓存有效性
     * @param filePath 文件路径
     * @return 是否有效
     */
    bool isCacheValid(const QString& filePath) const;
    
    /**
     * @brief 更新缓存
     * @param filePath 文件路径
     * @param config 配置数据
     */
    void updateCache(const QString& filePath, const QVariantMap& config);
};

/**
 * @brief 设备注册表管理器 - 单例模式
 */
class DeviceRegistryManager {
public:
    /**
     * @brief 获取全局注册表实例
     * @return 注册表实例
     */
    static ConfigDrivenDeviceRegistry* getInstance();
    
    /**
     * @brief 初始化注册表
     * @param configDirs 配置目录列表
     * @param enableHotReload 是否启用热重载
     * @return 是否成功
     */
    static bool initialize(const QStringList& configDirs, bool enableHotReload = true);
    
    /**
     * @brief 清理注册表
     */
    static void cleanup();

private:
    static std::unique_ptr<ConfigDrivenDeviceRegistry> s_instance;
};

} // namespace LA::Device::Registry

// =============================================================================
// 对比分析：静态注册 vs 配置驱动注册
// =============================================================================

/**
 * ## 静态注册方式 (UnifiedDeviceRegistry)
 * 
 * ### 优势：
 * - ✅ 编译时类型检查
 * - ✅ 性能好，无需运行时解析
 * - ✅ IDE智能提示支持
 * - ✅ 代码版本控制
 * 
 * ### 劣势：
 * - ❌ 添加新设备需要重新编译
 * - ❌ 无法运行时热更新
 * - ❌ 不符合配置驱动原则
 * - ❌ 与四层架构的外化配置冲突
 * - ❌ 客户定制需要修改源码
 * 
 * ## 配置驱动注册方式 (ConfigDrivenDeviceRegistry)
 * 
 * ### 优势：
 * - ✅ 完全配置驱动，无需重编译
 * - ✅ 支持运行时热更新
 * - ✅ 客户可自定义设备类型
 * - ✅ 与四层架构配置统一
 * - ✅ 支持版本控制和回滚
 * - ✅ 更好的可扩展性
 * 
 * ### 劣势：
 * - ❌ 运行时解析开销
 * - ❌ 配置错误只能运行时发现
 * - ❌ IDE支持相对较弱
 * 
 * ## 混合方案建议：
 * 
 * 1. **核心设备类型**：保留静态注册（SPRM-A1/A2/A3等主要产品）
 * 2. **扩展设备类型**：使用配置驱动（客户定制、新产品等）
 * 3. **渐进迁移**：逐步将静态注册迁移到配置驱动
 * 4. **开发模式**：开发环境使用静态注册，生产环境支持配置扩展
 */