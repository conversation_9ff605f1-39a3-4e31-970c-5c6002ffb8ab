#pragma once

#include "IParameterManager.h"
#include <QObject>
#include <QVariant>
#include <QVariantMap>
#include <QStringList>
#include <QDateTime>
#include <QMutex>
#include <QFileSystemWatcher>
#include <QTimer>
#include <memory>
#include <map>

namespace LA {
namespace Support {
namespace Config {

/**
 * @brief 参数管理器实现类
 * 
 * 提供完整的参数管理功能，包括：
 * - 参数定义和验证
 * - 参数值存取和监控
 * - 参数历史记录和备份
 * - 参数导入导出
 * - 线程安全操作
 */
class ParameterManager : public IParameterManager {
    Q_OBJECT

public:
    explicit ParameterManager(QObject* parent = nullptr);
    ~ParameterManager() override;

    // IManager 接口实现
    SimpleResult initialize(const ConfigParameters& config = {}) override;
    SimpleResult shutdown() override;
    bool isInitialized() const override;
    StatusInfoList getStatus() const override;
    VersionInfo getVersion() const override;

    // IParameterManager 接口实现
    SimpleResult defineParameter(const ParameterDefinition& definition) override;
    SimpleResult defineParameters(const QList<ParameterDefinition>& definitions) override;
    SimpleResult undefineParameter(const QString& key) override;
    bool isParameterDefined(const QString& key) const override;
    Result<ParameterDefinition> getParameterDefinition(const QString& key) const override;
    QList<ParameterDefinition> getAllParameterDefinitions() const override;

    SimpleResult setParameter(const QString& key, const QVariant& value, 
                             const QString& modifier = QString(),
                             const QString& reason = QString()) override;
    SimpleResult setParameters(const QVariantMap& parameters,
                              const QString& modifier = QString(),
                              const QString& reason = QString()) override;
    QVariant getParameter(const QString& key, const QVariant& defaultValue = QVariant()) const override;
    Result<ParameterValue> getParameterValue(const QString& key) const override;
    QVariantMap getParameters(const QStringList& keys) const override;
    Result<QList<ParameterDefinition>> queryParameters(const ParameterQuery& query) const override;

    SimpleResult resetParameter(const QString& key) override;
    SimpleResult resetParameters(const QStringList& keys) override;
    SimpleResult removeParameter(const QString& key) override;
    bool hasParameter(const QString& key) const override;
    SimpleResult validateParameter(const QString& key, const QVariant& value) const override;

    Result<QList<ParameterChangeRecord>> getParameterHistory(const QString& key, 
                                                           int limit = 100) const override;
    ByteArrayResult exportParameters(const ParameterQuery& query, 
                                   const QString& format = "json") const override;
    SimpleResult importParameters(const QByteArray& data, 
                                const QString& format = "json",
                                bool overwrite = false) override;

    StringResult backupParameters(const QString& backupName = QString()) override;
    SimpleResult restoreParameters(const QString& backupId) override;
    QStringList getBackupList() const override;
    SimpleResult deleteBackup(const QString& backupId) override;
    SimpleResult cleanupHistory(const QDateTime& beforeTime) override;
    StatusInfoList getStatistics() const override;

private slots:
    void onConfigFileChanged(const QString& filePath);
    void performPeriodicBackup();

private:
    // 内部结构和方法
    struct ParameterEntry {
        ParameterDefinition definition;
        ParameterValue value;
        QList<ParameterChangeRecord> history;
        
        ParameterEntry() = default;
        ParameterEntry(const ParameterDefinition& def) : definition(def) {
            value.key = def.key;
            value.value = def.defaultValue;
            value.isDefault = true;
            value.isValid = true;
            value.timestamp = QDateTime::currentDateTime();
        }
    };

    // 初始化和清理
    bool initializeStorage();
    bool initializeWatcher();
    void cleanupStorage();
    void cleanupWatcher();

    // 参数操作
    SimpleResult doSetParameter(const QString& key, const QVariant& value,
                               const QString& modifier, const QString& reason);
    bool validateParameterValue(const ParameterDefinition& definition, const QVariant& value) const;
    void recordParameterChange(const QString& key, const QVariant& oldValue, 
                              const QVariant& newValue, const QString& modifier, 
                              const QString& reason);

    // 数据持久化
    SimpleResult loadParametersFromStorage();
    SimpleResult saveParametersToStorage();
    SimpleResult loadParameterDefinitions();
    SimpleResult saveParameterDefinitions();

    // 文件操作
    QString getParameterFilePath() const;
    QString getDefinitionFilePath() const;
    QString getHistoryFilePath() const;
    QString getBackupDirectory() const;
    QString generateBackupId() const;

    // 格式转换
    QVariantMap serializeParameters(const ParameterQuery& query) const;
    SimpleResult deserializeParameters(const QVariantMap& data, bool overwrite);
    QByteArray exportToJson(const QVariantMap& data) const;
    QByteArray exportToXml(const QVariantMap& data) const;
    Result<QVariantMap> importFromJson(const QByteArray& data) const;
    Result<QVariantMap> importFromXml(const QByteArray& data) const;

    // 工具方法
    bool matchesQuery(const ParameterDefinition& definition, const ParameterQuery& query) const;
    void limitHistory(QList<ParameterChangeRecord>& history, int maxSize = 1000) const;
    QString formatParameterKey(const QString& key) const;

private:
    mutable QMutex m_mutex;                                      // 线程安全锁
    std::map<QString, ParameterEntry> m_parameters;              // 参数存储
    QFileSystemWatcher* m_fileWatcher;                          // 文件监控器
    QTimer* m_backupTimer;                                      // 定期备份计时器
    
    // 配置选项
    QString m_storageDirectory;                                 // 存储目录
    QString m_configFile;                                       // 配置文件路径
    int m_maxHistorySize;                                      // 最大历史记录数
    int m_backupInterval;                                      // 备份间隔（分钟）
    int m_maxBackups;                                          // 最大备份数
    bool m_autoSave;                                           // 自动保存
    bool m_watchFiles;                                         // 监控文件变化
    bool m_encryptStorage;                                     // 加密存储
    
    bool m_initialized;                                        // 初始化状态
};

/**
 * @brief 参数管理器工厂实现
 */
class ParameterManagerFactory : public IParameterManagerFactory {
public:
    std::shared_ptr<IParameterManager> createParameterManager(const ConfigParameters& config = {}) override;
};

}  // namespace Config
}  // namespace Support
}  // namespace LA