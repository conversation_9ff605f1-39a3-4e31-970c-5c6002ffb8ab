#include "LA/DeviceManagement/Core/SimpleTypes.h"

namespace LA {
namespace DeviceManagement {
namespace Core {

QString deviceTypeToString(DeviceType type) {
    switch (type) {
    case DeviceType::Unknown:   return "Unknown";
    case DeviceType::SPRM:      return "SPRM";
    case DeviceType::Motor:     return "Motor";
    case DeviceType::Sensor:    return "Sensor";
    case DeviceType::TestBoard: return "TestBoard";
    default:                    return "Unknown";
    }
}

DeviceType stringToDeviceType(const QString& typeStr) {
    if (typeStr == "SPRM") return DeviceType::SPRM;
    if (typeStr == "Motor") return DeviceType::Motor;
    if (typeStr == "Sensor") return DeviceType::Sensor;
    if (typeStr == "TestBoard") return DeviceType::TestBoard;
    return DeviceType::Unknown;
}

QString portTypeToString(PortType type) {
    switch (type) {
    case PortType::Unknown:   return "Unknown";
    case PortType::Serial:    return "Serial";
    case PortType::TCP:       return "TCP";
    case PortType::UDP:       return "UDP";
    case PortType::USB:       return "USB";
    default:                  return "Unknown";
    }
}

PortType stringToPortType(const QString& typeStr) {
    if (typeStr == "Serial") return PortType::Serial;
    if (typeStr == "TCP") return PortType::TCP;
    if (typeStr == "UDP") return PortType::UDP;
    if (typeStr == "USB") return PortType::USB;
    return PortType::Unknown;
}

QString portStatusToString(PortStatus status) {
    switch (status) {
    case PortStatus::Unknown:   return "Unknown";
    case PortStatus::Available: return "Available";
    case PortStatus::InUse:     return "InUse";
    case PortStatus::Busy:      return "Busy";
    case PortStatus::Error:     return "Error";
    case PortStatus::Offline:   return "Offline";
    default:                    return "Unknown";
    }
}

PortStatus stringToPortStatus(const QString& statusStr) {
    if (statusStr == "Available") return PortStatus::Available;
    if (statusStr == "InUse") return PortStatus::InUse;
    if (statusStr == "Busy") return PortStatus::Busy;
    if (statusStr == "Error") return PortStatus::Error;
    if (statusStr == "Offline") return PortStatus::Offline;
    return PortStatus::Unknown;
}

QString connectionStatusToString(ConnectionStatus status) {
    switch (status) {
    case ConnectionStatus::Unknown:       return "Unknown";
    case ConnectionStatus::Connected:     return "Connected";
    case ConnectionStatus::Disconnected: return "Disconnected";
    case ConnectionStatus::Connecting:    return "Connecting";
    case ConnectionStatus::Disconnecting: return "Disconnecting";
    case ConnectionStatus::Error:         return "Error";
    default:                              return "Unknown";
    }
}

ConnectionStatus stringToConnectionStatus(const QString& statusStr) {
    if (statusStr == "Connected") return ConnectionStatus::Connected;
    if (statusStr == "Disconnected") return ConnectionStatus::Disconnected;
    if (statusStr == "Connecting") return ConnectionStatus::Connecting;
    if (statusStr == "Disconnecting") return ConnectionStatus::Disconnecting;
    if (statusStr == "Error") return ConnectionStatus::Error;
    return ConnectionStatus::Unknown;
}

} // namespace Core
} // namespace DeviceManagement
} // namespace LA