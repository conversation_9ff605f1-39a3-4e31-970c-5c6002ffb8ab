#pragma once

#include <QObject>
#include <QString>
#include <QStringList>
#include <QMap>
#include <QVariantMap>
#include <QDateTime>
#include <memory>

// 前向声明
class BaseDevice;

namespace LA::DeviceManagement::Instances {

/**
 * @brief 设备实例管理器 - 运行时动态管理
 * 
 * 管理具体创建的设备对象实例，生命周期管理
 * 
 * Linus式设计：
 * - "做一件事，做好一件事"：只管设备实例，不管设备类型
 * - 运行时动态创建、管理、销毁设备实例
 * - 依赖DeviceTypeRegistry获取类型信息，职责分离
 */
class DeviceInstanceManager : public QObject {
    Q_OBJECT
    
public:
    explicit DeviceInstanceManager(QObject* parent = nullptr);
    ~DeviceInstanceManager();
    
    static DeviceInstanceManager* instance();
    
    // ====== 实例创建和销毁 ======
    QString createDeviceInstance(const QString& deviceType, 
                                const QVariantMap& config = {});
    bool destroyDeviceInstance(const QString& instanceId);
    bool destroyAllInstances();
    
    // ====== 实例查询 ======
    QStringList getActiveInstanceIds() const;                     // 当前有几个设备实例在运行
    QStringList getInstancesByType(const QString& deviceType) const;
    QStringList getInstancesByCategory(const QString& category) const;
    DeviceInstanceInfo getInstanceInfo(const QString& instanceId) const;
    int getActiveInstanceCount() const;
    
    // ====== 实例操作 ======
    bool connectInstance(const QString& instanceId);
    bool disconnectInstance(const QString& instanceId);
    bool isInstanceConnected(const QString& instanceId) const;
    
    bool executeInstanceCommand(const QString& instanceId,
                               const QString& command,
                               const QVariantMap& parameters = {});
    
    QVariantMap getInstanceData(const QString& instanceId) const;
    
    // ====== 生命周期管理 ======
    InstanceLifecycle getInstanceLifecycle(const QString& instanceId) const;
    bool startInstance(const QString& instanceId);
    bool stopInstance(const QString& instanceId);
    bool restartInstance(const QString& instanceId);
    
    bool initializeInstance(const QString& instanceId);
    bool finalizeInstance(const QString& instanceId);
    
    // ====== 实例监控 ======
    QDateTime getInstanceCreateTime(const QString& instanceId) const;
    QDateTime getInstanceLastActiveTime(const QString& instanceId) const;
    QVariantMap getInstanceStatistics(const QString& instanceId) const;
    
    // ====== 批量操作 ======
    QStringList connectAllInstances();
    QStringList disconnectAllInstances();
    QStringList startAllInstances();
    QStringList stopAllInstances();
    
signals:
    // 实例生命周期信号
    void instanceCreated(const QString& instanceId, const QString& deviceType);
    void instanceDestroyed(const QString& instanceId);
    void instanceLifecycleChanged(const QString& instanceId, InstanceLifecycle newState);
    
    // 实例连接信号
    void instanceConnected(const QString& instanceId);
    void instanceDisconnected(const QString& instanceId);
    void instanceConnectionError(const QString& instanceId, const QString& error);
    
    // 实例操作信号
    void instanceCommandExecuted(const QString& instanceId, const QString& command, bool success);
    void instanceDataReceived(const QString& instanceId, const QVariantMap& data);
    void instanceError(const QString& instanceId, const QString& error);
    
private slots:
    void onDeviceStateChanged();
    void onDeviceDataReceived();
    void onDeviceError();
    
private:
    // ====== 内部实现 ======
    QString generateInstanceId(const QString& deviceType);
    bool validateInstanceConfig(const QString& deviceType, const QVariantMap& config);
    BaseDevice* createDeviceObject(const QString& deviceType, const QVariantMap& config);
    void setupDeviceConnections(const QString& instanceId, BaseDevice* device);
    void cleanupDeviceConnections(const QString& instanceId);
    
    bool transitionInstanceState(const QString& instanceId, InstanceLifecycle newState);
    void updateInstanceActivity(const QString& instanceId);
    
private:
    static DeviceInstanceManager* s_instance;
    QMap<QString, DeviceInstanceData> m_activeInstances; // 运行时管理
    int m_nextInstanceNumber;
};

// ====== 数据结构定义 ======

/**
 * @brief 设备实例生命周期状态
 */
enum class InstanceLifecycle {
    Created,        // 已创建，未初始化
    Initialized,    // 已初始化，未启动
    Started,        // 已启动，可工作
    Connected,      // 已连接，通信正常
    Error,          // 错误状态
    Stopped,        // 已停止
    Destroyed       // 已销毁
};

/**
 * @brief 设备实例数据（运行时变化）
 */
struct DeviceInstanceData {
    QString instanceId;                  // 实例唯一ID
    QString deviceType;                  // 设备类型（引用类型注册表）
    QVariantMap instanceConfig;          // 实例特定配置
    BaseDevice* deviceObject;            // 设备对象指针
    
    // 状态信息
    InstanceLifecycle lifecycle;         // 生命周期状态
    bool isConnected;                    // 连接状态
    QString lastError;                   // 最后错误信息
    
    // 时间信息
    QDateTime createTime;                // 创建时间
    QDateTime lastActiveTime;            // 最后活动时间
    QDateTime lastConnectTime;           // 最后连接时间
    
    // 统计信息
    int commandCount;                    // 执行命令数量
    int errorCount;                      // 错误次数
    double totalOperationTime;           // 总运行时间(秒)
    QVariantMap runtimeData;             // 运行时数据
    
    DeviceInstanceData() 
        : deviceObject(nullptr)
        , lifecycle(InstanceLifecycle::Created)
        , isConnected(false)
        , createTime(QDateTime::currentDateTime())
        , lastActiveTime(QDateTime::currentDateTime())
        , commandCount(0)
        , errorCount(0)
        , totalOperationTime(0.0) {}
};

/**
 * @brief 设备实例信息（对外接口）
 */
struct DeviceInstanceInfo {
    QString instanceId;
    QString deviceType;
    QString manufacturer;
    QString description;
    InstanceLifecycle lifecycle;
    bool isConnected;
    QDateTime createTime;
    QDateTime lastActiveTime;
    int commandCount;
    int errorCount;
    QString lastError;
    QVariantMap config;
    
    bool isValid() const {
        return !instanceId.isEmpty() && !deviceType.isEmpty();
    }
};

// ====== 辅助函数 ======
QString instanceLifecycleToString(InstanceLifecycle lifecycle);
InstanceLifecycle stringToInstanceLifecycle(const QString& str);

} // namespace LA::DeviceManagement::Instances