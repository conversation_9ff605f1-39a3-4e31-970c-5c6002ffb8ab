#pragma once

#include "IConfigValidator.h"
#include <QObject>
#include <QMutex>
#include <QTimer>
#include <QThread>
#include <QThreadPool>
#include <QRunnable>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <memory>
#include <map>
#include <functional>

namespace LA {
namespace Support {
namespace Config {

/**
 * @brief 配置验证器实现类
 * 
 * 提供完整的配置验证功能，包括：
 * - 多种验证规则类型支持
 * - 并行验证支持
 * - 自定义验证函数支持
 * - 规则集管理
 * - 详细的验证报告
 */
class ConfigValidator : public IConfigValidator {
    Q_OBJECT

public:
    explicit ConfigValidator(QObject* parent = nullptr);
    ~ConfigValidator() override;

    // IManager 接口实现
    SimpleResult initialize(const ConfigParameters& config = {}) override;
    SimpleResult shutdown() override;
    bool isInitialized() const override;
    StatusInfoList getStatus() const override;
    VersionInfo getVersion() const override;

    // IConfigValidator 接口实现
    SimpleResult addRule(const ValidationRule& rule) override;
    SimpleResult addRules(const QList<ValidationRule>& rules) override;
    SimpleResult removeRule(const QString& ruleId) override;
    SimpleResult updateRule(const ValidationRule& rule) override;
    Result<ValidationRule> getRule(const QString& ruleId) const override;
    QList<ValidationRule> getAllRules() const override;
    QList<ValidationRule> queryRules(const QString& category = QString(),
                                    const QString& group = QString(),
                                    ValidationRuleType type = ValidationRuleType::UNKNOWN) const override;

    SimpleResult enableRule(const QString& ruleId) override;
    SimpleResult disableRule(const QString& ruleId) override;

    Result<ValidationReport> validate(const QVariantMap& data, 
                                    const ValidationOptions& options = ValidationOptions()) override;
    Result<QList<ValidationResult>> validateField(const QString& fieldPath, 
                                                 const QVariant& value,
                                                 const QVariantMap& context = QVariantMap()) override;
    Result<ValidationReport> validateFile(const QString& filePath,
                                         const ValidationOptions& options = ValidationOptions()) override;
    Result<ValidationReport> validateSchema(const QVariantMap& data,
                                           const QVariantMap& schema,
                                           const ValidationOptions& options = ValidationOptions()) override;

    SimpleResult createRuleSet(const QString& name, 
                              const QList<ValidationRule>& rules) override;
    SimpleResult loadRuleSet(const QString& name) override;
    SimpleResult saveRuleSet(const QString& name, 
                            const QString& filePath = QString()) override;

    ByteArrayResult exportRules(const QString& format = "json") override;
    SimpleResult importRules(const QByteArray& data, 
                            const QString& format = "json",
                            bool overwrite = false) override;
    SimpleResult clearRules() override;

    StatusInfoList getStatistics() const override;
    QList<ValidationRuleType> getSupportedRuleTypes() const override;

    SimpleResult registerCustomValidator(const QString& name,
                                        std::function<ValidationResult(const QVariant&, const ValidationRule&, const ValidationContext&)> validator) override;
    SimpleResult unregisterCustomValidator(const QString& name) override;

private slots:
    void onValidationTimeout();

private:
    // 内部结构
    struct RuleEntry {
        ValidationRule rule;
        QDateTime lastUsed;
        int usageCount;
        double averageExecutionTime;
        
        RuleEntry() : usageCount(0), averageExecutionTime(0.0) {}
        RuleEntry(const ValidationRule& r) : rule(r), usageCount(0), averageExecutionTime(0.0) {}
    };

    struct RuleSet {
        QString name;
        QString description;
        QStringList ruleIds;
        QDateTime created;
        QDateTime modified;
        QString filePath;
        
        RuleSet() = default;
        RuleSet(const QString& n) : name(n), created(QDateTime::currentDateTime()), modified(QDateTime::currentDateTime()) {}
    };

    // 验证任务
    class ValidationTask : public QRunnable {
    public:
        ValidationTask(ConfigValidator* validator, 
                      const ValidationRule& rule,
                      const QVariant& value,
                      const ValidationContext& context);
        
        void run() override;
        ValidationResult getResult() const { return m_result; }

    private:
        ConfigValidator* m_validator;
        ValidationRule m_rule;
        QVariant m_value;
        ValidationContext m_context;
        ValidationResult m_result;
    };

    // 初始化和清理
    bool initializeRuleStorage();
    bool initializeBuiltinRules();
    bool initializeCustomValidators();
    void cleanupRuleStorage();
    void cleanupCustomValidators();

    // 规则管理
    bool isRuleIdValid(const QString& ruleId) const;
    bool isRuleValid(const ValidationRule& rule) const;
    QString generateRuleId() const;
    void updateRuleStatistics(const QString& ruleId, double executionTime);

    // 验证执行
    ValidationResult executeRule(const ValidationRule& rule, 
                               const QVariant& value,
                               const ValidationContext& context);
    QList<ValidationResult> validateSingleField(const QString& fieldPath,
                                               const QVariant& value,
                                               const ValidationContext& context,
                                               const ValidationOptions& options);
    ValidationResult validateWithBuiltinFunction(const ValidationRule& rule,
                                                const QVariant& value,
                                                const ValidationContext& context);
    ValidationResult validateWithCustomFunction(const ValidationRule& rule,
                                               const QVariant& value,
                                               const ValidationContext& context);

    // 并行验证
    QList<ValidationResult> validateParallel(const QVariantMap& data,
                                            const ValidationOptions& options);
    QList<ValidationResult> validateSequential(const QVariantMap& data,
                                              const ValidationOptions& options);

    // 数据遍历
    void traverseData(const QVariantMap& data,
                     const QString& basePath,
                     std::function<void(const QString&, const QVariant&)> callback);
    QVariant getValueByPath(const QVariantMap& data, const QString& path);
    QStringList splitPath(const QString& path);

    // 报告生成
    ValidationReport generateReport(const QString& configName,
                                   const QString& configPath,
                                   const QList<ValidationResult>& results,
                                   double totalTime);
    void calculateReportStatistics(ValidationReport& report);

    // 规则集管理
    QString getRuleSetFilePath(const QString& name) const;
    SimpleResult saveRuleSetToFile(const RuleSet& ruleSet);
    Result<RuleSet> loadRuleSetFromFile(const QString& filePath);

    // 序列化和反序列化
    QJsonObject ruleToJson(const ValidationRule& rule) const;
    ValidationRule ruleFromJson(const QJsonObject& json) const;
    QJsonObject resultToJson(const ValidationResult& result) const;
    ValidationResult resultFromJson(const QJsonObject& json) const;
    QJsonObject reportToJson(const ValidationReport& report) const;
    ValidationReport reportFromJson(const QJsonObject& json) const;

    // 文件操作
    SimpleResult loadRulesFromFile(const QString& filePath);
    SimpleResult saveRulesToFile(const QString& filePath);

    // Schema验证
    ValidationResult validateJsonSchema(const QVariant& value,
                                      const QJsonObject& schema,
                                      const QString& path = QString());
    bool validateJsonType(const QVariant& value, const QString& expectedType);
    bool validateJsonEnum(const QVariant& value, const QJsonArray& enumValues);
    bool validateJsonProperties(const QVariantMap& object, const QJsonObject& properties, const QString& basePath, QList<ValidationResult>& results);

    // 内置验证函数
    ValidationResult validateTypeRule(const QVariant& value, const ValidationRule& rule, const ValidationContext& context);
    ValidationResult validateRangeRule(const QVariant& value, const ValidationRule& rule, const ValidationContext& context);
    ValidationResult validateLengthRule(const QVariant& value, const ValidationRule& rule, const ValidationContext& context);
    ValidationResult validatePatternRule(const QVariant& value, const ValidationRule& rule, const ValidationContext& context);
    ValidationResult validateEnumRule(const QVariant& value, const ValidationRule& rule, const ValidationContext& context);
    ValidationResult validateRequiredRule(const QVariant& value, const ValidationRule& rule, const ValidationContext& context);
    ValidationResult validateDependencyRule(const QVariant& value, const ValidationRule& rule, const ValidationContext& context);

    // 工具方法
    bool matchesRuleFilter(const ValidationRule& rule, 
                          const QString& category,
                          const QString& group,
                          ValidationRuleType type) const;
    ValidationSeverity calculateOverallSeverity(const QList<ValidationResult>& results) const;
    QString formatValidationMessage(const ValidationRule& rule, 
                                   const QVariant& actualValue,
                                   const QString& context = QString()) const;

private:
    mutable QMutex m_mutex;                                                      // 线程安全锁
    std::map<QString, RuleEntry> m_rules;                                       // 验证规则映射
    std::map<QString, RuleSet> m_ruleSets;                                      // 规则集映射
    std::map<QString, std::function<ValidationResult(const QVariant&, const ValidationRule&, const ValidationContext&)>> m_customValidators; // 自定义验证器
    
    QTimer* m_timeoutTimer;                                                     // 超时计时器
    QThreadPool* m_threadPool;                                                  // 线程池
    
    // 配置选项
    QString m_storageDirectory;                                                 // 存储目录
    QString m_ruleSetDirectory;                                                 // 规则集目录
    bool m_enableParallelValidation;                                           // 启用并行验证
    int m_maxThreads;                                                          // 最大线程数
    double m_defaultTimeoutMs;                                                 // 默认超时时间
    bool m_saveStatistics;                                                     // 保存统计信息
    bool m_enableCaching;                                                      // 启用缓存
    int m_maxCacheSize;                                                        // 最大缓存大小
    
    // 统计信息
    mutable int m_totalValidations;                                            // 总验证次数
    mutable int m_successfulValidations;                                       // 成功验证次数
    mutable int m_failedValidations;                                          // 失败验证次数
    mutable double m_averageExecutionTime;                                     // 平均执行时间
    mutable QDateTime m_lastValidationTime;                                    // 最后验证时间
    
    bool m_initialized;                                                        // 初始化状态
    volatile bool m_validationInProgress;                                      // 验证进行中标志
};

/**
 * @brief 配置验证器工厂实现
 */
class ConfigValidatorFactory : public IConfigValidatorFactory {
public:
    std::shared_ptr<IConfigValidator> createConfigValidator(const ConfigParameters& config = {}) override;
};

/**
 * @brief 验证规则构建器
 * 
 * 提供流式API来构建验证规则
 */
class ValidationRuleBuilder {
public:
    ValidationRuleBuilder();

    ValidationRuleBuilder& id(const QString& id);
    ValidationRuleBuilder& name(const QString& name);
    ValidationRuleBuilder& description(const QString& description);
    ValidationRuleBuilder& type(ValidationRuleType type);
    ValidationRuleBuilder& severity(ValidationSeverity severity);
    ValidationRuleBuilder& field(const QString& fieldPath);
    ValidationRuleBuilder& expectedValue(const QVariant& value);
    ValidationRuleBuilder& range(const QVariant& min, const QVariant& max);
    ValidationRuleBuilder& allowedValues(const QStringList& values);
    ValidationRuleBuilder& pattern(const QString& pattern);
    ValidationRuleBuilder& customFunction(const QString& functionName);
    ValidationRuleBuilder& dependency(const QString& fieldPath);
    ValidationRuleBuilder& dependencies(const QStringList& fieldPaths);
    ValidationRuleBuilder& enabled(bool enabled = true);
    ValidationRuleBuilder& priority(int priority);
    ValidationRuleBuilder& category(const QString& category);
    ValidationRuleBuilder& group(const QString& group);
    ValidationRuleBuilder& parameter(const QString& key, const QVariant& value);
    ValidationRuleBuilder& metadata(const QString& key, const QVariant& value);

    ValidationRule build();

private:
    ValidationRule m_rule;
};

/**
 * @brief 预定义验证规则集
 */
class StandardValidationRules {
public:
    // 基础类型验证规则
    static QList<ValidationRule> createBasicTypeRules();
    
    // 数值范围验证规则
    static QList<ValidationRule> createNumericRangeRules();
    
    // 字符串格式验证规则
    static QList<ValidationRule> createStringFormatRules();
    
    // 必需字段验证规则
    static QList<ValidationRule> createRequiredFieldRules(const QStringList& requiredFields);
    
    // 网络配置验证规则
    static QList<ValidationRule> createNetworkConfigRules();
    
    // 数据库配置验证规则
    static QList<ValidationRule> createDatabaseConfigRules();
    
    // 安全配置验证规则
    static QList<ValidationRule> createSecurityConfigRules();
    
    // 性能配置验证规则
    static QList<ValidationRule> createPerformanceConfigRules();
};

}  // namespace Config
}  // namespace Support
}  // namespace LA