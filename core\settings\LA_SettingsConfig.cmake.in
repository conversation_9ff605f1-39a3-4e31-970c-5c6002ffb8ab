# LA Settings Library Config File

@PACKAGE_INIT@

# 检查组件
set(_LA_Settings_supported_components Settings)

foreach(_comp ${LA_Settings_FIND_COMPONENTS})
    if (NOT _comp IN_LIST _LA_Settings_supported_components)
        set(LA_Settings_FOUND False)
        set(LA_Settings_NOT_FOUND_MESSAGE "Unsupported component: ${_comp}")
    endif()
endforeach()

# 查找依赖
find_dependency(Qt5 REQUIRED COMPONENTS Core Widgets Gui)

# 包含目标文件
include("${CMAKE_CURRENT_LIST_DIR}/LA_SettingsTargets.cmake")

# 设置变量
set(LA_Settings_LIBRARIES LA::Settings)
set(LA_Settings_INCLUDE_DIRS "${PACKAGE_PREFIX_DIR}/include")

check_required_components(LA_Settings)
