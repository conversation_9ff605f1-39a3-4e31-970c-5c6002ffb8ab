/**
 * @file SprmDevice.h
 * @brief SPRM设备具体实现类 - 四层架构统一设备
 * 
 * 职责：
 * - 组合四层架构的所有组件
 * - 提供SPRM设备的完整功能接口
 * - 管理设备生命周期和状态
 * - 实现设备特定的业务逻辑
 */

#pragma once

#include "../../include/LA/Device/Core/IDevice.h"
#include "LA/Device/Core/Device.h"
#include "../../drivers/sprm/SprmA1Driver.h"
#include "../../capabilities/ranging/LaserRangingCapability.h"
#include "../../strategies/filtering/KalmanFilter.h"
#include "../../Scripts/engine/ScriptEngine.h"
#include <QObject>
#include <QString>
#include <QVariantMap>
#include <QTimer>
#include <memory>

namespace LA::Device::Devices {

// Forward declarations
namespace Driver = LA::Device::Driver;
namespace Capability = LA::Device::Capability;
namespace Strategy = LA::Device::Strategy;
namespace Script = LA::Device::Script;

/**
 * @brief SPRM设备实现类
 * 
 * 集成四层架构：
 * - Script Layer: Lua脚本业务逻辑
 * - Strategy Layer: Kalman滤波算法
 * - Capability Layer: 激光测距功能
 * - Driver Layer: SPRM-A1硬件驱动
 */
class SprmDevice : public LA::Device::Core::IDevice {
    Q_OBJECT

public:
    explicit SprmDevice(const QString& deviceModel = "SPRM-A1", QObject* parent = nullptr);
    virtual ~SprmDevice() = default;

    // === 设备基础接口 ===
    
    bool initialize(const QVariantMap& config);
    bool connect();
    bool disconnect();
    QVariantMap sendCommand(const QString& command, const QVariantMap& params);
    QString getDeviceType() const { return "SprmDevice"; }
    QString getDeviceModel() const { return m_deviceModel; }
    bool isConnected() const;
    
    // === 增强IDevice接口实现 ===
    QString getSerialNumber() const override;
    QVariantMap getVersionInfo() const override;
    QStringList getSupportedCapabilities() const override;
    bool hasCapability(const QString& capability) const override;
    QVariantMap getCapabilityConfig(const QString& capability) const override;
    QVariantMap getCurrentConfig() const override;
    bool updateConfig(const QVariantMap& config) override;
    bool resetToDefaultConfig() override;
    QVariantMap validateConfig(const QVariantMap& config) const override;
    QVariantMap getDeviceStatus() const override;
    QVariantMap performSelfTest() override;
    QVariantMap getStatistics() const override;
    QList<QVariantMap> getRecentErrors(int count = 10) const override;
    bool clearErrors() override;
    bool attemptRecovery() override;

    // === SPRM设备特定接口 ===
    
    /**
     * @brief 执行激光测距
     * @param mode 测距模式 ("single", "continuous", "average")
     * @param params 测距参数
     * @return 测距结果
     */
    QVariantMap performRanging(const QString& mode, const QVariantMap& params = {});
    
    /**
     * @brief 开始连续测距
     * @param interval 测量间隔(ms)
     * @return 操作结果
     */
    QVariantMap startContinuousRanging(int interval = 100);
    
    /**
     * @brief 停止连续测距
     * @return 操作结果
     */
    QVariantMap stopContinuousRanging();
    
    /**
     * @brief 校准设备
     * @param referenceDistance 参考距离(mm)
     * @param calibrationType 校准类型
     * @return 校准结果
     */
    QVariantMap calibrateDevice(double referenceDistance, const QString& calibrationType = "single_point");
    
    /**
     * @brief 设置激光功率
     * @param power 功率等级 (1.0-10.0 mW)
     * @return 操作结果
     */
    QVariantMap setLaserPower(double power);
    
    
    /**
     * @brief 获取历史测距数据
     * @param count 获取数量 (-1表示全部)
     * @return 历史数据
     */
    QVariantList getHistoryData(int count = -1);

    // === 配置管理 ===
    
    /**
     * @brief 加载设备配置
     * @param configFile 配置文件路径
     * @return 是否成功
     */
    bool loadConfiguration(const QString& configFile);
    
    /**
     * @brief 更新测距配置
     * @param config 配置参数
     */
    void updateRangingConfig(const QVariantMap& config);
    
    /**
     * @brief 更新滤波配置
     * @param config 配置参数
     */
    void updateFilteringConfig(const QVariantMap& config);
    
    /**
     * @brief 切换环境模式
     * @param environment 环境名称 ("indoor", "outdoor", "industrial")
     * @return 操作结果
     */
    QVariantMap switchEnvironmentMode(const QString& environment);

    // === 脚本和规则引擎 ===
    
    /**
     * @brief 加载设备脚本
     * @param scriptFile 脚本文件路径
     * @return 是否成功
     */
    bool loadDeviceScript(const QString& scriptFile);
    
    /**
     * @brief 执行脚本命令
     * @param event 事件名称
     * @param params 事件参数
     * @return 执行结果
     */
    QVariantMap executeScriptEvent(const QString& event, const QVariantMap& params = {});
    
    /**
     * @brief 获取设备规格信息
     * @return 规格信息
     */
    QVariantMap getDeviceSpecifications() const;

Q_SIGNALS:
    void rangingCompleted(const QVariantMap& result);
    void continuousRangingData(const QVariantMap& data);
    void deviceError(const QString& error);
    void deviceStatusChanged(const QString& status);
    void calibrationCompleted(const QVariantMap& result);

public slots:
    void onRangingCompleted(const QVariantMap& result);
    void onContinuousRangingData(const QVariantMap& data);
    void onDeviceError(const QString& error);

private slots:
    void onDriverError(const QString& error);
    void onConnectionStatusChanged(bool connected);

private:
    QString m_deviceModel;
    bool m_initialized;
    QString m_currentEnvironment;
    
    // 设备配置
    QVariantMap m_deviceConfig;
    QVariantMap m_rangingConfig;
    QVariantMap m_filteringConfig;
    
    // 四层架构组件 (在基类Device中定义)
    // - m_driver (Driver::IDriver)
    // - m_capabilities (vector<Capability::ICapability>)  
    // - m_strategies (map<QString, Strategy::IStrategy>)
    // - m_scriptEngine (Script::ScriptEngine)
    
    // SPRM特定组件引用
    Driver::SprmA1Driver* m_sprmDriver;
    Capability::LaserRangingCapability* m_rangingCapability;
    Strategy::KalmanFilter* m_kalmanFilter;
    Script::ScriptEngine* m_scriptEngine;
    
    // === 内部方法 ===
    
    /**
     * @brief 初始化四层架构组件
     * @return 是否成功
     */
    bool initializeComponents();
    
    /**
     * @brief 应用环境配置
     * @param environment 环境配置
     */
    void applyEnvironmentConfig(const QVariantMap& environment);
    
    /**
     * @brief 处理脚本执行结果
     * @param result 脚本结果
     * @param originalCommand 原始命令
     * @param originalParams 原始参数
     * @return 处理后的结果
     */
    QVariantMap processScriptResult(const QVariantMap& result, 
                                  const QString& originalCommand,
                                  const QVariantMap& originalParams);
    
    /**
     * @brief 验证设备配置
     * @param config 配置数据
     * @return 是否有效
     */
    bool validateConfiguration(const QVariantMap& config);
    
    /**
     * @brief 创建错误结果
     * @param error 错误信息
     * @return 错误结果
     */
    QVariantMap createErrorResult(const QString& error);
    
    /**
     * @brief 记录设备事件
     * @param event 事件名称
     * @param data 事件数据
     */
    void logDeviceEvent(const QString& event, const QVariantMap& data = {});
    
    /**
     * @brief 更新设备状态
     * @param status 新状态
     */
    void updateDeviceStatus(const QString& status);
};

/**
 * @brief SPRM设备工厂
 */
class SprmDeviceFactory {
public:
    /**
     * @brief 创建SPRM设备实例
     * @param model 设备型号
     * @param config 初始配置
     * @return 设备实例
     */
    static std::unique_ptr<SprmDevice> createDevice(const QString& model, 
                                                  const QVariantMap& config = {});
    
    /**
     * @brief 从配置文件创建设备
     * @param configFile 配置文件路径
     * @return 设备实例
     */
    static std::unique_ptr<SprmDevice> createFromConfig(const QString& configFile);
    
    /**
     * @brief 获取支持的设备型号列表
     * @return 型号列表
     */
    static QStringList getSupportedModels();
};

/**
 * @brief SPRM设备管理器
 */
class SprmDeviceManager : public QObject {
    Q_OBJECT

public:
    explicit SprmDeviceManager(QObject* parent = nullptr);
    virtual ~SprmDeviceManager() = default;

    /**
     * @brief 注册SPRM设备
     * @param deviceId 设备ID
     * @param device 设备实例
     * @return 是否成功
     */
    bool registerDevice(const QString& deviceId, std::unique_ptr<SprmDevice> device);
    
    /**
     * @brief 获取设备
     * @param deviceId 设备ID
     * @return 设备指针
     */
    SprmDevice* getDevice(const QString& deviceId);
    
    /**
     * @brief 移除设备
     * @param deviceId 设备ID
     * @return 是否成功
     */
    bool removeDevice(const QString& deviceId);
    
    /**
     * @brief 获取所有设备ID列表
     * @return ID列表
     */
    QStringList getDeviceIds() const;
    
    /**
     * @brief 连接所有设备
     * @return 连接结果
     */
    QVariantMap connectAllDevices();
    
    /**
     * @brief 断开所有设备
     * @return 断开结果
     */
    QVariantMap disconnectAllDevices();

Q_SIGNALS:
    void deviceRegistered(const QString& deviceId);
    void deviceRemoved(const QString& deviceId);
    void deviceConnected(const QString& deviceId);
    void deviceDisconnected(const QString& deviceId);

private:
    std::map<QString, std::unique_ptr<SprmDevice>> m_devices;
};

} // namespace LA::Device::Devices