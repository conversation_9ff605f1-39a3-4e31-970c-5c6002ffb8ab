# SPRM设备四层架构重构

## 概述

本项目实现了SPRM激光测距传感器的四层组合架构重构，遵循Linus原则"做一件事，做好它"，通过组合而非继承的方式构建清晰、可维护的设备抽象层。

## 四层架构设计

```
┌─────────────────────────────────────────────────┐
│                第4层：脚本/规则引擎层                 │
│  - Lua脚本业务逻辑                                │
│  - DSL规则配置                                   │
│  - 沙箱安全执行                                   │
│  - 版本控制和测试                                 │
└─────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────┐
│                第3层：策略层                      │
│  - KalmanFilter 卡尔曼滤波                       │
│  - 算法策略选择                                   │
│  - 参数动态调整                                   │
└─────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────┐
│                第2层：能力层                      │
│  - LaserRangingCapability 激光测距                │
│  - 功能组合                                      │
│  - 业务逻辑封装                                   │
└─────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────┐
│                第1层：驱动层                      │
│  - SprmA1Driver 硬件驱动                        │
│  - 协议封装                                      │
│  - 硬件差异隔离                                   │
└─────────────────────────────────────────────────┘
```

## 目录结构

```
modules/device_new/
├── core/                           # 核心设备抽象
│   ├── Device.h                    # 四层架构统一设备类
│   └── Device.cpp
├── drivers/                        # 第1层：驱动层
│   └── sprm/
│       ├── SprmA1Driver.h          # SPRM-A1硬件驱动
│       └── SprmA1Driver.cpp
├── capabilities/                   # 第2层：能力层
│   └── ranging/
│       ├── LaserRangingCapability.h # 激光测距能力
│       └── LaserRangingCapability.cpp
├── strategies/                     # 第3层：策略层
│   └── filtering/
│       ├── KalmanFilter.h          # 卡尔曼滤波策略
│       └── KalmanFilter.cpp
├── scripts/                        # 第4层：脚本层
│   ├── engine/
│   │   ├── ScriptEngine.h          # 脚本引擎
│   │   └── ScriptEngine.cpp
│   ├── lua/devices/
│   │   └── sprm_behavior.lua       # SPRM设备业务脚本
│   └── rules/
│       └── device_rules.yml        # DSL规则配置
├── devices/                        # 设备实现
│   └── sprm/
│       ├── SprmDevice.h            # SPRM设备统一实现
│       └── SprmDevice.cpp
├── config/                         # 配置文件
│   └── devices/
│       └── sprm_devices.json       # SPRM设备配置
├── tests/                          # 测试代码
│   ├── SprmDeviceTest.h            # 单元测试
│   ├── SprmDeviceTest.cpp
│   └── simple_sprm_test.cpp        # 简单功能测试
└── CMakeLists.txt                  # 构建配置
```

## 核心特性

### 1. 四层组合架构
- **驱动层**：硬件抽象，协议封装
- **能力层**：功能组合，业务封装
- **策略层**：算法策略，动态选择
- **脚本层**：业务逻辑，规则引擎

### 2. 脚本化业务逻辑
- Lua脚本支持，允许非工程师调整设备行为
- 沙箱执行环境，确保安全性
- DSL规则配置，声明式业务规则
- 版本控制和热更新支持

### 3. 配置驱动
- JSON设备配置文件
- 环境适应性配置（室内、户外、工业）
- 动态参数调整
- 分层配置管理

### 4. 完整测试覆盖
- 单元测试（SprmDeviceTest）
- 集成测试（simple_sprm_test）
- 性能测试和内存测试
- 模拟设备支持

## 快速开始

### 1. 构建项目

```bash
# Windows
.\tools\build_and_test_sprm.bat

# Linux/Mac
mkdir build && cd build
cmake .. -DCMAKE_BUILD_TYPE=Debug -DBUILD_TESTS=ON
make
```

### 2. 运行测试

```bash
# 简单功能测试
./build/Debug/simple_sprm_test

# 完整单元测试
./build/Debug/SprmDeviceTest

# 或使用CTest
ctest --output-on-failure
```

### 3. 使用示例

```cpp
#include "devices/sprm/SprmDevice.h"

// 创建设备
auto device = LA::Device::Devices::SprmDeviceFactory::createDevice("SPRM-A1");

// 初始化配置
QVariantMap config;
config["basic_info"] = QVariantMap{{"model", "SPRM-A1"}};
device->initialize(config);

// 连接设备
device->connect();

// 执行测距
auto result = device->performRanging("single");
```

## 设备配置示例

```json
{
  "device_configurations": {
    "SPRM-A1": {
      "basic_info": {
        "model": "SPRM-A1",
        "manufacturer": "Nova",
        "category": "RangingSensor"
      },
      "four_layer_architecture": {
        "driver": {
          "type": "SprmA1Driver",
          "config": {
            "timeout_ms": 3000,
            "retry_count": 3
          }
        },
        "capabilities": [
          {
            "name": "LaserRanging",
            "config": {
              "mode": "single",
              "average_samples": 3
            }
          }
        ],
        "strategies": {
          "filtering": {
            "default": "kalman",
            "options": {
              "kalman": {
                "process_noise": 0.01,
                "measurement_noise": 0.1
              }
            }
          }
        },
        "script": {
          "behavior_script": "sprm_behavior.lua",
          "rule_config": "device_rules.yml",
          "sandbox_enabled": true
        }
      }
    }
  }
}
```

## Lua脚本示例

```lua
-- sprm_behavior.lua
SCRIPT_VERSION = "1.0.0"

function handle_device_event(device_type, event, params)
    log_info("Processing " .. event .. " for " .. device_type)
    
    if event == "before_measurement" then
        -- 温度补偿逻辑
        local temperature = params["temperature"] or 25.0
        if temperature < 10 then
            return {
                success = true,
                adjusted_power = params["power"] * 1.05,
                message = "Low temperature compensation applied"
            }
        end
    end
    
    return { success = true }
end
```

## DSL规则示例

```yaml
# device_rules.yml
safety_rules:
  - name: "激光器过热保护"
    priority: 1
    condition: "temperature > 70"
    action:
      type: "disable_laser"
      message: "温度过高，自动关闭激光器"

performance_rules:
  - name: "精度优化"
    condition: "accuracy_deviation > acceptable_threshold"
    action:
      type: "optimize_accuracy"
      params:
        - enable_advanced_filtering
        - increase_measurement_samples
```

## 架构优势

### 1. 符合Linus原则
- 每个组件职责单一
- 组合优于继承
- 接口清晰明确

### 2. 高度可扩展
- 新设备型号容易添加
- 新功能通过组合实现
- 插件化架构支持

### 3. 业务逻辑外化
- 脚本化业务规则
- 非工程师可调整
- 热更新支持

### 4. 配置驱动
- 声明式配置
- 环境适应性
- 版本管理

## 依赖项

### 必需依赖
- Qt5 (Core, Network, SerialPort)
- CMake 3.16+
- C++17 编译器

### 可选依赖
- Lua 5.3+ (脚本引擎支持)
- Qt5Test (单元测试)

## 贡献指南

### 添加新设备型号
1. 在`drivers/`下创建新驱动
2. 实现`IDriver`接口
3. 在配置文件中添加设备规格
4. 编写相应测试

### 添加新能力
1. 在`capabilities/`下创建能力组件
2. 实现`ICapability`接口
3. 在设备中组合该能力
4. 更新配置文件

### 添加新策略
1. 在`strategies/`下创建策略组件
2. 实现`IStrategy`接口
3. 在配置中添加策略选项
4. 编写单元测试

## 已知限制

1. **硬件依赖**：完整功能需要实际SPRM设备
2. **Lua依赖**：脚本功能需要Lua运行时
3. **平台限制**：串口通信在某些平台可能受限

## 未来改进

1. **更多设备支持**：添加SPRM-A2、SPRM-A3支持
2. **网络通信**：支持TCP/UDP协议
3. **数据库集成**：历史数据持久化
4. **可视化界面**：设备管理GUI
5. **云端集成**：远程监控和控制

## 许可证

本项目采用MIT许可证。详见LICENSE文件。

## 联系方式

- 项目维护者：Device Team
- 技术支持：<EMAIL>
- 问题报告：使用GitHub Issues