#pragma once

/**
 * @file PortTypes.h
 * @brief 端口管理类型定义 - 纯通信层职责
 *
 * ✅ 职责：定义纯通信相关的端口类型、状态、配置
 * ❌ 不涉及：设备业务逻辑、设备识别、设备管理
 * 🎯 原则：只关心"如何通信"，不关心"和谁通信"
 */

// === Linus原则：引用Foundation层统一定义，避免重复 ===
#include "support/foundation/core/CommonTypes.h"

#include "../Connection/ConnectionTypes.h"
#include <QDateTime>
#include <QMetaType>
#include <QString>
#include <QVariantMap>

namespace LA {
namespace Communication {
namespace PortManagement {

using namespace LA::Communication::Connection;


// 使用Foundation层的统一类型定义
using PortType   = ::LA::Foundation::Core::PortType;
using PortStatus = ::LA::Foundation::Core::PortStatus;

/**
 * @brief 端口操作结果
 */
enum class PortOperationResult {
    Success          = 0,  // 成功
    Failed           = 1,  // 失败
    Timeout          = 2,  // 超时
    NotFound         = 3,  // 未找到
    AlreadyExists    = 4,  // 已存在
    InvalidConfig    = 5,  // 配置无效
    PermissionDenied = 6   // 权限拒绝
};

/**
 * @brief 端口发现模式
 */
enum class PortDiscoveryMode {
    Manual      = 0,  // 手动发现
    Automatic   = 1,  // 自动发现
    Periodic    = 2,  // 定期发现
    EventDriven = 3   // 事件驱动发现
};

/**
 * @brief 端口配置结构
 */
struct PortConfig {
    QString          portId;            // 端口ID
    QString          displayName;       // 显示名称
    PortType         type;              // 端口类型
    QString          description;       // 端口描述
    QVariantMap      parameters;        // 自定义参数
    ConnectionConfig connectionConfig;  // 连接配置
    bool             autoConnect;       // 自动连接
    bool             persistent;        // 持久化配置
    qint32           timeout;           // 超时时间（毫秒）

    PortConfig() : type(PortType::Unknown), autoConnect(false), persistent(true), timeout(5000) {
    }
};

/**
 * @brief 端口信息结构
 */
struct PortInfo {
    QString     portId;        // 端口ID
    QString     displayName;   // 显示名称
    PortType    type;          // 端口类型
    QString     description;   // 端口描述
    QString     manufacturer;  // 制造商
    QString     serialNumber;  // 序列号
    QString     driverName;    // 驱动名称
    QString     devicePath;    // 设备路径
    PortStatus  status;        // 端口状态
    QDateTime   createTime;    // 创建时间
    QDateTime   lastUsedTime;  // 最后使用时间
    QDateTime   lastSeenTime;  // 最后发现时间
    QVariantMap metadata;      // 元数据

    PortInfo()
        : type(PortType::Unknown),
          status(PortStatus::Unknown),
          createTime(QDateTime::currentDateTime()),
          lastUsedTime(QDateTime::currentDateTime()),
          lastSeenTime(QDateTime::currentDateTime()) {
    }
};

/**
 * @brief 端口统计信息
 */
struct PortStatistics {
    quint64   totalConnections;       // 总连接次数
    quint64   successfulConnections;  // 成功连接次数
    quint64   failedConnections;      // 失败连接次数
    quint64   bytesTransmitted;       // 传输字节数
    quint64   bytesReceived;          // 接收字节数
    quint32   errorCount;             // 错误计数
    QDateTime firstUsed;              // 首次使用时间
    QDateTime lastUsed;               // 最后使用时间
    qint64    totalUptime;            // 总在线时间（毫秒）

    PortStatistics()
        : totalConnections(0),
          successfulConnections(0),
          failedConnections(0),
          bytesTransmitted(0),
          bytesReceived(0),
          errorCount(0),
          firstUsed(QDateTime::currentDateTime()),
          lastUsed(QDateTime::currentDateTime()),
          totalUptime(0) {
    }
};

/**
 * @brief 端口事件类型
 */
enum class PortEvent {
    Discovered    = 0,  // 发现端口
    Removed       = 1,  // 移除端口
    Connected     = 2,  // 连接建立
    Disconnected  = 3,  // 连接断开
    StatusChanged = 4,  // 状态变化
    Error         = 5,  // 错误发生
    ConfigChanged = 6   // 配置变化
};

/**
 * @brief 端口事件信息
 */
struct PortEventInfo {
    PortEvent   event;      // 事件类型
    QString     portId;     // 端口ID
    QString     message;    // 事件消息
    QVariantMap data;       // 事件数据
    QDateTime   timestamp;  // 事件时间戳

    PortEventInfo(PortEvent e = PortEvent::Discovered, const QString &id = QString(), const QString &msg = QString())
        : event(e), portId(id), message(msg), timestamp(QDateTime::currentDateTime()) {
    }
};

/**
 * @brief 端口发现配置
 */
struct PortDiscoveryConfig {
    PortDiscoveryMode mode;                 // 发现模式
    qint32            scanInterval;         // 扫描间隔（毫秒）
    QList<PortType>   enabledTypes;         // 启用的端口类型
    bool              includeVirtualPorts;  // 包含虚拟端口
    bool              includeBusyPorts;     // 包含忙碌端口
    qint32            timeout;              // 发现超时（毫秒）

    PortDiscoveryConfig() : mode(PortDiscoveryMode::Automatic), scanInterval(5000), includeVirtualPorts(false), includeBusyPorts(false), timeout(10000) {
        enabledTypes << PortType::Serial << PortType::Network;
    }
};

/**
 * @brief 端口管理器配置
 */
struct PortManagerConfig {
    QString             name;                  // 管理器名称
    qint32              maxPorts;              // 最大端口数
    bool                autoDiscovery;         // 自动发现
    bool                autoReconnect;         // 自动重连
    qint32              reconnectInterval;     // 重连间隔（毫秒）
    qint32              maxReconnectAttempts;  // 最大重连次数
    PortDiscoveryConfig discoveryConfig;       // 发现配置
    QString             configFilePath;        // 配置文件路径

    PortManagerConfig()
        : name("Default Port Manager"), maxPorts(100), autoDiscovery(true), autoReconnect(true), reconnectInterval(1000), maxReconnectAttempts(3) {
    }
};

/**
 * @brief 端口工具类
 */
class PortUtils {
  public:
    /**
     * @brief 端口类型转字符串
     */
    static QString portTypeToString(PortType type);

    /**
     * @brief 字符串转端口类型
     */
    static PortType stringToPortType(const QString &typeString);

    /**
     * @brief 端口状态转字符串
     */
    static QString portStatusToString(PortStatus status);

    /**
     * @brief 字符串转端口状态
     */
    static PortStatus stringToPortStatus(const QString &statusString);

    /**
     * @brief 端口事件转字符串
     */
    static QString portEventToString(PortEvent event);

    /**
     * @brief 生成端口ID
     */
    static QString generatePortId(PortType type, const QString &identifier = QString());

    /**
     * @brief 验证端口ID
     */
    static bool isValidPortId(const QString &portId);

    /**
     * @brief 验证端口配置
     */
    static bool isValidPortConfig(const PortConfig &config);

    /**
     * @brief 格式化端口信息
     */
    static QString formatPortInfo(const PortInfo &info);

    /**
     * @brief 格式化端口统计
     */
    static QString formatPortStatistics(const PortStatistics &stats);

    /**
     * @brief 获取默认端口配置
     */
    static PortConfig getDefaultPortConfig(PortType type);

    /**
     * @brief 获取默认发现配置
     */
    static PortDiscoveryConfig getDefaultDiscoveryConfig();

    /**
     * @brief 获取默认管理器配置
     */
    static PortManagerConfig getDefaultManagerConfig();

    /**
     * @brief 端口类型到连接类型转换
     */
    static ConnectionType portTypeToConnectionType(PortType portType);

    /**
     * @brief 连接类型到端口类型转换
     */
    static PortType connectionTypeToPortType(ConnectionType connectionType);

    /**
     * @brief 连接状态到端口状态转换
     */
    static PortStatus connectionStateToPortStatus(ConnectionState connectionState);

    /**
     * @brief 端口状态到连接状态转换
     */
    static ConnectionState portStatusToConnectionState(PortStatus portStatus);
};

}  // namespace PortManagement
}  // namespace Communication
}  // namespace LA

// 注册元类型以支持Qt信号槽 - 暂时禁用以解决命名空间污染问题
// TODO: 修复命名空间污染后重新启用
// Q_DECLARE_METATYPE(LA::Communication::PortManagement::PortType)
// Q_DECLARE_METATYPE(LA::Communication::PortManagement::PortStatus)
// Q_DECLARE_METATYPE(LA::Communication::PortManagement::PortEvent)
// Q_DECLARE_METATYPE(LA::Communication::PortManagement::PortInfo)
// Q_DECLARE_METATYPE(LA::Communication::PortManagement::PortConfig)
// Q_DECLARE_METATYPE(LA::Communication::PortManagement::PortEventInfo)
// Q_DECLARE_METATYPE(LA::Communication::PortManagement::PortStatistics)
