#pragma once

#include "IResourceManager.h"
#include <QObject>
#include <QMutex>
#include <QTimer>
#include <QCache>
#include <QFileSystemWatcher>
#include <QNetworkAccessManager>
#include <QNetworkReply>
#include <QThread>
#include <QQueue>
#include <QWaitCondition>
#include <memory>
#include <map>

namespace LA {
namespace Support {
namespace Resource {

// 前向声明
class AsyncLoadWorker;

/**
 * @brief 资源管理器实现类
 * 
 * 提供完整的资源管理功能，包括：
 * - 多种类型资源支持
 * - 智能缓存管理
 * - 异步加载
 * - 文件监控
 * - 网络资源支持
 */
class ResourceManager : public IResourceManager {
    Q_OBJECT

public:
    explicit ResourceManager(QObject* parent = nullptr);
    ~ResourceManager() override;

    // IManager 接口实现
    SimpleResult initialize(const ConfigParameters& config = {}) override;
    SimpleResult shutdown() override;
    bool isInitialized() const override;
    StatusInfoList getStatus() const override;

    // 资源注册
    bool registerResource(const ResourceInfo& info) override;
    bool registerResources(const QList<ResourceInfo>& resources) override;
    void unregisterResource(const QString& id) override;

    // 资源查询
    bool hasResource(const QString& id) const override;
    Result<ResourceInfo> getResourceInfo(const QString& id) const override;
    QList<ResourceInfo> getAllResourceInfo() const override;
    QList<ResourceInfo> getResourcesByType(ResourceType type) const override;
    QList<ResourceInfo> getResourcesByCategory(const QString& category) const override;
    QList<ResourceInfo> searchResources(const QString& query, const ConfigParameters& filters = {}) const override;

    // 资源加载
    Result<ResourceData> loadResource(const QString& id, const ResourceLoadOptions& options = {}) override;
    SimpleResult loadResourceAsync(const QString& id, const ResourceLoadOptions& options = {}) override;
    SimpleResult preloadResource(const QString& id) override;
    void unloadResource(const QString& id) override;

    // 特定类型资源获取
    Result<QPixmap> getImage(const QString& id, const QSize& size = QSize()) override;
    Result<QIcon> getIcon(const QString& id, const QSize& size = QSize()) override;
    StringResult getText(const QString& id, const QString& encoding = "UTF-8") override;
    ByteArrayResult getBinary(const QString& id) override;

    // 资源操作
    SimpleResult saveResource(const QString& id, const QByteArray& data, const ResourceInfo& info = {}) override;
    SimpleResult deleteResource(const QString& id) override;
    SimpleResult copyResource(const QString& sourceId, const QString& targetId) override;
    SimpleResult moveResource(const QString& sourceId, const QString& targetId) override;

    // 缓存管理
    void setCacheConfig(const ResourceCacheConfig& config) override;
    ResourceCacheConfig getCacheConfig() const override;
    void clearCache() override;
    void clearExpiredCache() override;
    StatusInfoList getCacheStatistics() const override;

    // 资源维护
    SimpleResult refreshResource(const QString& id) override;
    SimpleResult validateResource(const QString& id) override;
    QStringList getResourceDependencies(const QString& id) const override;
    SimpleResult setResourceDependencies(const QString& id, const QStringList& dependencies) override;

    /**
     * @brief 注册资源提供者
     */
    bool registerProvider(const QString& name, std::shared_ptr<IResourceProvider> provider);

    /**
     * @brief 注销资源提供者
     */
    void unregisterProvider(const QString& name);

    /**
     * @brief 获取资源提供者
     */
    std::shared_ptr<IResourceProvider> getProvider(const QString& name) const;

    /**
     * @brief 设置文件系统监控
     */
    void setFileSystemWatching(bool enabled);

    /**
     * @brief 获取统计信息
     */
    QVariantMap getStatistics() const;

private slots:
    void handleFileChanged(const QString& path);
    void handleDirectoryChanged(const QString& path);
    void processAsyncQueue();
    void performPeriodicMaintenance();
    void handleNetworkReply();

private:
    // 前向声明异步加载工作器

    // 内部方法
    ResourceData loadResourceInternal(const QString& id, const ResourceLoadOptions& options);
    ResourceData loadFromFile(const ResourceInfo& info, const ResourceLoadOptions& options);
    ResourceData loadFromEmbedded(const ResourceInfo& info, const ResourceLoadOptions& options);
    ResourceData loadFromNetwork(const ResourceInfo& info, const ResourceLoadOptions& options);
    ResourceData loadFromProvider(const ResourceInfo& info, const ResourceLoadOptions& options);

    QVariant processResourceData(const ResourceData& data);
    QPixmap processImage(const QByteArray& data, const QSize& targetSize = QSize());
    QIcon processIcon(const QByteArray& data, const QSize& targetSize = QSize());

    bool validateResourceData(const ResourceData& data);
    QString calculateChecksum(const QByteArray& data);
    QString detectMimeType(const QByteArray& data, const QString& filename = QString());

    void updateResourceStatus(const QString& id, ResourceStatus status);
    void addToCache(const QString& id, const ResourceData& data);
    ResourceData getFromCache(const QString& id);
    bool isInCache(const QString& id) const;
    void removeFromCache(const QString& id);

    void setupFileWatching(const ResourceInfo& info);
    void removeFileWatching(const QString& path);

    void initializeBuiltinProviders();
    void loadPersistentCache();
    void savePersistentCache();

    QString resolveResourcePath(const QString& path) const;
    ResourceInfo createResourceInfoFromFile(const QString& filePath);

private:
    std::map<QString, ResourceInfo> m_resources;            // 资源注册表
    std::map<QString, std::shared_ptr<IResourceProvider>> m_providers; // 资源提供者
    std::map<QString, QStringList> m_dependencies;         // 资源依赖关系

    QCache<QString, ResourceData> m_cache;                  // 内存缓存
    ResourceCacheConfig m_cacheConfig;                      // 缓存配置

    AsyncLoadWorker* m_asyncWorker;                         // 异步加载工作器
    QFileSystemWatcher* m_fileWatcher;                      // 文件系统监控
    QNetworkAccessManager* m_networkManager;               // 网络管理器
    QTimer* m_maintenanceTimer;                             // 维护定时器

    mutable QMutex m_mutex;                                 // 线程安全锁
    bool m_initialized;                                     // 初始化状态
    bool m_fileWatchingEnabled;                             // 文件监控启用状态

    // 统计信息
    mutable qint64 m_totalResources;                        // 总资源数
    mutable qint64 m_loadedResources;                       // 已加载资源数
    mutable qint64 m_cacheHits;                             // 缓存命中数
    mutable qint64 m_cacheMisses;                           // 缓存失误数
    mutable qint64 m_totalLoadTime;                         // 总加载时间
    mutable QDateTime m_creationTime;                       // 创建时间
};

/**
 * @brief 文件资源提供者
 */
class FileResourceProvider : public IResourceProvider {
public:
    QString getName() const override;
    QList<ResourceType> getSupportedTypes() const override;
    bool canProvide(const QString& path) const override;
    Result<ResourceData> loadResource(const QString& path, const ResourceLoadOptions& options = {}) override;
    Result<ResourceInfo> getResourceInfo(const QString& path) const override;

private:
    ResourceType detectResourceType(const QString& path) const;
    QString detectMimeType(const QString& path) const;
};

/**
 * @brief 嵌入资源提供者
 */
class EmbeddedResourceProvider : public IResourceProvider {
public:
    QString getName() const override;
    QList<ResourceType> getSupportedTypes() const override;
    bool canProvide(const QString& path) const override;
    Result<ResourceData> loadResource(const QString& path, const ResourceLoadOptions& options = {}) override;
    Result<ResourceInfo> getResourceInfo(const QString& path) const override;

private:
    ResourceType detectResourceType(const QString& path) const;
};

/**
 * @brief 网络资源提供者
 */
class NetworkResourceProvider : public IResourceProvider {
public:
    NetworkResourceProvider(QNetworkAccessManager* manager);
    QString getName() const override;
    QList<ResourceType> getSupportedTypes() const override;
    bool canProvide(const QString& path) const override;
    Result<ResourceData> loadResource(const QString& path, const ResourceLoadOptions& options = {}) override;
    Result<ResourceInfo> getResourceInfo(const QString& path) const override;

private:
    QNetworkAccessManager* m_networkManager;
    ResourceType detectResourceType(const QString& url) const;
};

/**
 * @brief 资源工厂实现
 */
class ResourceFactory : public IResourceFactory {
public:
    std::shared_ptr<IResourceManager> createResourceManager(const ConfigParameters& config = {}) override;
    bool registerProvider(const QString& name, std::shared_ptr<IResourceProvider> provider) override;
    void unregisterProvider(const QString& name) override;
    std::shared_ptr<IResourceProvider> getProvider(const QString& name) const override;

private:
    std::map<QString, std::shared_ptr<IResourceProvider>> m_providers;
    mutable QMutex m_mutex;
};

// 工具函数
QString resourceTypeToString(ResourceType type);
ResourceType stringToResourceType(const QString& typeStr);
QString resourceStatusToString(ResourceStatus status);
ResourceStatus stringToResourceStatus(const QString& statusStr);
QString resourceSourceToString(ResourceSource source);
ResourceSource stringToResourceSource(const QString& sourceStr);

}  // namespace Resource
}  // namespace Support
}  // namespace LA