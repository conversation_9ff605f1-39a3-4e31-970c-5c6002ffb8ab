@echo off
REM 运行单元测试脚本 - 遵循test_guideline.md规范
REM 快速反馈层测试，适用于PR验证

echo ==========================================
echo          LA 单元测试执行工具
echo ==========================================

setlocal enabledelayedexpansion

REM 设置构建目录
set BUILD_DIR=%~dp0..\build
set TEST_DIR=%BUILD_DIR%\test

REM 检查构建目录是否存在
if not exist "%BUILD_DIR%" (
    echo 错误: 构建目录不存在 %BUILD_DIR%
    echo 请先运行 cmake 构建项目
    exit /b 1
)

echo 开始执行单元测试...
echo.

REM 设置测试环境变量
set QT_QPA_PLATFORM=minimal
set GTEST_COLOR=yes
set GTEST_OUTPUT=xml:%TEST_DIR%\unit_test_results.xml

REM 初始化计数器
set /a TOTAL_TESTS=0
set /a PASSED_TESTS=0
set /a FAILED_TESTS=0

echo === 设备指令单元测试 ===
if exist "%TEST_DIR%\unit\device\command\SprmCommandProviderTest.exe" (
    echo 运行 SPRM指令提供者测试...
    "%TEST_DIR%\unit\device\command\SprmCommandProviderTest.exe" --gtest_output=xml:%TEST_DIR%\sprm_command_test_results.xml
    set /a TEST_RESULT=!ERRORLEVEL!
    set /a TOTAL_TESTS+=1
    
    if !TEST_RESULT! EQU 0 (
        echo ✅ SPRM指令提供者测试通过
        set /a PASSED_TESTS+=1
    ) else (
        echo ❌ SPRM指令提供者测试失败 ^(错误码: !TEST_RESULT!^)
        set /a FAILED_TESTS+=1
    )
) else (
    echo ⏭️ 跳过 SPRM指令提供者测试 - 可执行文件不存在
)

echo.
echo === 其他单元测试模块 ===
REM 这里可以添加其他单元测试模块

REM 搜索并运行所有单元测试
for /r "%TEST_DIR%\unit" %%i in (*Test.exe) do (
    if not "%%~ni"=="SprmCommandProviderTest" (
        echo 运行 %%~ni...
        "%%i" --gtest_output=xml:%TEST_DIR%\%%~ni_results.xml
        set /a TEST_RESULT=!ERRORLEVEL!
        set /a TOTAL_TESTS+=1
        
        if !TEST_RESULT! EQU 0 (
            echo ✅ %%~ni 通过
            set /a PASSED_TESTS+=1
        ) else (
            echo ❌ %%~ni 失败 ^(错误码: !TEST_RESULT!^)
            set /a FAILED_TESTS+=1
        )
    )
)

echo.
echo ==========================================
echo           单元测试执行完成
echo ==========================================
echo 总测试数: !TOTAL_TESTS!
echo 通过数: !PASSED_TESTS!
echo 失败数: !FAILED_TESTS!

if !FAILED_TESTS! GTR 0 (
    echo.
    echo ❌ 存在失败的测试！
    echo 详细结果请查看: %TEST_DIR%\*_results.xml
    echo.
    exit /b 1
) else (
    echo.
    echo ✅ 所有单元测试通过！
    echo.
    exit /b 0
)

endlocal
