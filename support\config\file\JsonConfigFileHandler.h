#pragma once

#include "IConfigFileHandler.h"
#include <QObject>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QJsonValue>
#include <QJsonParseError>

namespace LA {
namespace Support {
namespace Config {

/**
 * @brief JSON 配置文件处理器
 * 
 * 专门处理JSON格式的配置文件，提供：
 * - JSON文件的读写操作
 * - JSON格式验证
 * - 美化输出支持
 * - 注释支持（通过特殊字段）
 * - JSON Schema验证
 */
class JsonConfigFileHandler : public QObject {
    Q_OBJECT

public:
    explicit JsonConfigFileHandler(QObject* parent = nullptr);
    ~JsonConfigFileHandler() override;

    /**
     * @brief 加载JSON配置文件
     */
    static Result<QVariantMap> loadJsonFile(const QString& filePath, 
                                           const ConfigFileOptions& options = ConfigFileOptions());

    /**
     * @brief 保存JSON配置文件
     */
    static SimpleResult saveJsonFile(const QString& filePath, 
                                    const QVariantMap& data,
                                    const ConfigFileOptions& options = ConfigFileOptions());

    /**
     * @brief 解析JSON内容
     */
    static Result<QVariantMap> parseJsonContent(const QByteArray& content, 
                                               const ConfigFileOptions& options = ConfigFileOptions());

    /**
     * @brief 序列化为JSON内容
     */
    static ByteArrayResult serializeJsonContent(const QVariantMap& data, 
                                               const ConfigFileOptions& options = ConfigFileOptions());

    /**
     * @brief 验证JSON数据
     */
    static SimpleResult validateJsonData(const QVariantMap& data, 
                                        const QVariantMap& schema = QVariantMap());

    /**
     * @brief 验证JSON Schema
     */
    static SimpleResult validateJsonSchema(const QVariantMap& data, 
                                          const QJsonObject& schema);

    /**
     * @brief 美化JSON输出
     */
    static QString formatJsonWithComments(const QVariantMap& data, 
                                         const ConfigFileOptions& options = ConfigFileOptions());

    /**
     * @brief 合并JSON对象
     */
    static QVariantMap mergeJsonObjects(const QVariantMap& base, 
                                       const QVariantMap& overlay,
                                       bool deepMerge = true);

    /**
     * @brief 展平JSON对象（转为点分隔键）
     */
    static QVariantMap flattenJsonObject(const QVariantMap& data, 
                                        const QString& prefix = QString(),
                                        const QString& separator = ".");

    /**
     * @brief 反展平JSON对象（从点分隔键还原）
     */
    static QVariantMap unflattenJsonObject(const QVariantMap& flatData, 
                                          const QString& separator = ".");

    /**
     * @brief 获取JSON路径值
     */
    static QVariant getJsonPathValue(const QVariantMap& data, 
                                    const QString& jsonPath);

    /**
     * @brief 设置JSON路径值
     */
    static bool setJsonPathValue(QVariantMap& data, 
                                const QString& jsonPath, 
                                const QVariant& value);

    /**
     * @brief 删除JSON路径
     */
    static bool removeJsonPath(QVariantMap& data, 
                              const QString& jsonPath);

    /**
     * @brief 检查JSON路径是否存在
     */
    static bool hasJsonPath(const QVariantMap& data, 
                           const QString& jsonPath);

    /**
     * @brief 生成JSON差异
     */
    static QVariantMap generateJsonDiff(const QVariantMap& oldData, 
                                       const QVariantMap& newData);

    /**
     * @brief 应用JSON差异
     */
    static QVariantMap applyJsonDiff(const QVariantMap& baseData, 
                                    const QVariantMap& diff);

    /**
     * @brief 转换为紧凑JSON
     */
    static QByteArray toCompactJson(const QVariantMap& data);

    /**
     * @brief 转换为美化JSON
     */
    static QByteArray toPrettyJson(const QVariantMap& data, 
                                  int indentSize = 2);

private:
    // 内部辅助方法
    static QString escapeJsonString(const QString& str);
    static QString unescapeJsonString(const QString& str);
    static QString generateIndent(int level, int indentSize);
    static void formatJsonValue(QString& result, 
                               const QVariant& value, 
                               int indentLevel, 
                               const ConfigFileOptions& options);
    static void formatJsonObject(QString& result, 
                                const QVariantMap& object, 
                                int indentLevel, 
                                const ConfigFileOptions& options);
    static void formatJsonArray(QString& result, 
                               const QVariantList& array, 
                               int indentLevel, 
                               const ConfigFileOptions& options);

    // JSON Schema 验证辅助方法
    static bool validateJsonType(const QVariant& value, const QString& expectedType);
    static bool validateJsonEnum(const QVariant& value, const QJsonArray& enumValues);
    static bool validateJsonRange(const QVariant& value, 
                                 const QJsonValue& minimum, 
                                 const QJsonValue& maximum);
    static bool validateJsonPattern(const QString& value, const QString& pattern);
    static bool validateJsonObject(const QVariantMap& object, const QJsonObject& schema);
    static bool validateJsonArray(const QVariantList& array, const QJsonObject& schema);

    // JSON路径操作辅助方法
    static QStringList parseJsonPath(const QString& jsonPath);
    static QVariant getNestedValue(const QVariant& data, const QStringList& pathComponents);
    static bool setNestedValue(QVariant& data, const QStringList& pathComponents, const QVariant& value);
    static bool removeNestedValue(QVariant& data, const QStringList& pathComponents);

    // 差异计算辅助方法
    static void generateObjectDiff(const QVariantMap& oldObj, 
                                  const QVariantMap& newObj, 
                                  QVariantMap& diff, 
                                  const QString& prefix = QString());
    static void generateArrayDiff(const QVariantList& oldArray, 
                                 const QVariantList& newArray, 
                                 QVariantMap& diff, 
                                 const QString& prefix);

signals:
    /**
     * @brief JSON解析错误信号
     */
    void jsonParseError(const QString& filePath, const QString& error);

    /**
     * @brief JSON验证错误信号
     */
    void jsonValidationError(const QString& filePath, const QString& error);

    /**
     * @brief JSON文件保存完成信号
     */
    void jsonFileSaved(const QString& filePath);
};

// JsonConfigFileParser类定义在ConfigFileHandler.h中，避免重复定义
class JsonConfigFileParser;

// 工具函数
QString configFileFormatToString(ConfigFileFormat format);
ConfigFileFormat stringToConfigFileFormat(const QString& formatStr);
QString configFileEncodingToString(ConfigFileEncoding encoding);
ConfigFileEncoding stringToConfigFileEncoding(const QString& encodingStr);

/**
 * @brief JSON配置文件工具类
 */
class JsonConfigUtils {
public:
    /**
     * @brief 检查JSON文件是否有效
     */
    static bool isValidJsonFile(const QString& filePath);

    /**
     * @brief 获取JSON文件编码
     */
    static ConfigFileEncoding detectJsonEncoding(const QString& filePath);

    /**
     * @brief 转换JSON文件编码
     */
    static SimpleResult convertJsonEncoding(const QString& filePath, 
                                           ConfigFileEncoding targetEncoding);

    /**
     * @brief 修复JSON文件格式
     */
    static SimpleResult repairJsonFile(const QString& filePath, 
                                      const QString& backupPath = QString());

    /**
     * @brief 压缩JSON文件
     */
    static SimpleResult compressJsonFile(const QString& filePath);

    /**
     * @brief 比较两个JSON文件
     */
    static Result<QVariantMap> compareJsonFiles(const QString& file1, 
                                               const QString& file2);

    /**
     * @brief 生成JSON文件统计信息
     */
    static QVariantMap generateJsonStatistics(const QString& filePath);
};

}  // namespace Config
}  // namespace Support
}  // namespace LA