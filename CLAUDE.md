# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

LA is a modular industrial software built with Qt 5.x framework and C++17. It uses a 5-layer architecture with plugin system, dependency injection, and comprehensive device communication support.


## Development 

### Development Guidelines

[[development_guideline]]

### Development Documentation

[[development]]

### TEST Guidelines

[[test_guideline]]