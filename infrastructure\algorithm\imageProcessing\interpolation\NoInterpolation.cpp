#include "NoInterpolation.h"
#include <QDebug>

namespace ImageProcessing {

NoInterpolation::NoInterpolation() {
    logDebug("NoInterpolation algorithm initialized");
}

bool NoInterpolation::interpolate(const ImageDataU32 &src, ImageDataU32 &dst) {
    logDebug(QString("NoInterpolation: copying original data %1x%2").arg(src.width()).arg(src.height()));

    // 直接复制原始数据，不进行任何插值处理
    dst = src;
    return true;
}

void NoInterpolation::setParameters(const InterpolationParams &params) {
    m_params = params;
    logDebug("NoInterpolation: parameters set (no effect on processing)");
}

InterpolationParams NoInterpolation::getParameters() const {
    return m_params;
}

QString NoInterpolation::getAlgorithmName() const {
    return "NoInterpolation";
}

QString NoInterpolation::getVersion() const {
    return "1.0.0";
}

QString NoInterpolation::getDescription() const {
    return "No interpolation - directly copies input data without any processing";
}

bool NoInterpolation::isSupported(uint32_t srcWidth, uint32_t srcHeight, uint32_t dstWidth, uint32_t dstHeight) const {
    Q_UNUSED(srcWidth)
    Q_UNUSED(srcHeight)
    Q_UNUSED(dstWidth)
    Q_UNUSED(dstHeight)

    // NoInterpolation总是支持的，因为它不做任何处理
    return true;
}

uint32_t NoInterpolation::estimateProcessingTime(uint32_t srcWidth, uint32_t srcHeight, uint32_t dstWidth, uint32_t dstHeight) const {
    Q_UNUSED(srcWidth)
    Q_UNUSED(srcHeight)
    Q_UNUSED(dstWidth)
    Q_UNUSED(dstHeight)

    // NoInterpolation处理时间极短，返回1毫秒
    return 1;
}

void NoInterpolation::reset() {
    m_params = InterpolationParams();
    logDebug("NoInterpolation: reset to default parameters");
}

void NoInterpolation::logDebug(const QString &message) const {
    qDebug() << "[NoInterpolation]" << message;
}

}  // namespace ImageProcessing
