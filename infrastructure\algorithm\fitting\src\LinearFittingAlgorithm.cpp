#include "LA/Algorithm/Fitting/Fitting.h"
#include <QDebug>

namespace LA {
namespace Algorithm {  
namespace Fitting {

/**
 * @brief LinearFittingAlgorithm实现
 * 线性拟合算法的具体实现
 */
class LinearFittingAlgorithm : public IFittingAlgorithm {
public:
    LinearFittingAlgorithm() = default;
    ~LinearFittingAlgorithm() override = default;
    
    FittingResult fit(const QVector<QPointF>& points) override {
        FittingResult result;
        result.success = false;
        
        if (points.size() < 2) {
            result.error = "至少需要2个点进行线性拟合";
            return result;
        }
        
        // 计算线性拟合 y = ax + b
        double sumX = 0, sumY = 0, sumXY = 0, sumXX = 0;
        int n = points.size();
        
        for (const auto& point : points) {
            sumX += point.x();
            sumY += point.y();
            sumXY += point.x() * point.y();
            sumXX += point.x() * point.x();
        }
        
        double denominator = n * sumXX - sumX * sumX;
        if (qAbs(denominator) < 1e-10) {
            result.error = "数据点共线，无法进行线性拟合";
            return result;
        }
        
        double a = (n * sumXY - sumX * sumY) / denominator;
        double b = (sumY - a * sumX) / n;
        
        // 计算R²决定系数
        double ssRes = 0, ssTot = 0;
        double meanY = sumY / n;
        
        for (const auto& point : points) {
            double predictedY = a * point.x() + b;
            ssRes += (point.y() - predictedY) * (point.y() - predictedY);
            ssTot += (point.y() - meanY) * (point.y() - meanY);
        }
        
        result.rSquared = 1.0 - (ssRes / ssTot);
        result.coefficients = QVector<double>{a, b};
        result.equation = QString("y = %1x + %2").arg(a, 0, 'f', 6).arg(b, 0, 'f', 6);
        result.success = true;
        
        return result;
    }
    
    QString getName() const override {
        return "LinearFitting";
    }
    
    QString getDescription() const override {
        return "线性拟合算法 (y = ax + b)";
    }
};

} // namespace Fitting
} // namespace Algorithm
} // namespace LA