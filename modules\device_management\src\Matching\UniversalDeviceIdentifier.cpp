#include "LA/DeviceManagement/Matching/UniversalDeviceIdentifier.h"
#include <QDebug>
#include <QRegExp>

namespace LA {
namespace DeviceManagement {
namespace Matching {

// 使用简化类型定义
using LA::DeviceManagement::Core::DeviceInfo;
using LA::DeviceManagement::Core::DeviceType;

UniversalDeviceIdentifier::UniversalDeviceIdentifier() {
    initializeDefaultRules();
    initializeCapabilities();

    qDebug() << "UniversalDeviceIdentifier initialized with" << m_rules.size() << "rules for" << m_supportedTypes.size() << "device types";
}

QString UniversalDeviceIdentifier::identifyDeviceType(const QByteArray &response) {
    if (!isValidDeviceResponse(response)) {
        return QString();
    }

    // 1. 首先尝试通过规则匹配
    QString deviceType = matchByPattern(response);
    if (!deviceType.isEmpty()) {
        qDebug() << "Device identified by pattern:" << deviceType;
        return deviceType;
    }

    // 2. 尝试通过关键词匹配
    deviceType = matchByKeywords(response);
    if (!deviceType.isEmpty()) {
        qDebug() << "Device identified by keywords:" << deviceType;
        return deviceType;
    }

    // 3. 尝试通过数据结构匹配
    deviceType = matchByStructure(response);
    if (!deviceType.isEmpty()) {
        qDebug() << "Device identified by structure:" << deviceType;
        return deviceType;
    }

    qDebug() << "Could not identify device type from response:" << response.toHex();
    return QString();
}

DeviceInfo UniversalDeviceIdentifier::extractDeviceInfo(const QByteArray &response) {
    QString deviceType = identifyDeviceType(response);
    if (deviceType.isEmpty()) {
        return DeviceInfo();
    }

    DeviceInfo info;

    // 根据设备类型选择相应的提取方法
    if (deviceType.toUpper() == "SPRM") {
        info = extractSprmInfo(response);
    } else if (deviceType.toUpper() == "MOTOR") {
        info = extractMotorInfo(response);
    } else if (deviceType.toUpper() == "SENSOR") {
        info = extractSensorInfo(response);
    } else {
        info = extractGenericInfo(deviceType, response);
    }

    // 设置通用信息
    info.deviceType = DeviceType::Generic;  // 默认为通用设备类型
    // info.rawResponse  = response;  // 字段不存在，暂时移除
    info.capabilities = getDeviceCapabilities(deviceType);

    // 暂时禁用QDebug输出以解决命名空间污染问题
    // qDebug() << "Extracted device info:" << info.deviceType << "Model:" << info.deviceModel << "ID:" << info.deviceId;

    return info;
}

QStringList UniversalDeviceIdentifier::getDeviceCapabilities(const QString &deviceType) {
    return m_deviceCapabilities.value(deviceType.toUpper(), QStringList());
}

bool UniversalDeviceIdentifier::isValidDeviceResponse(const QByteArray &response) {
    if (response.isEmpty() || response.size() < 2) {
        return false;
    }

    // 检查是否包含可见字符
    for (const char c : response) {
        if (c >= 32 && c <= 126) {
            return true;  // 找到可见ASCII字符
        }
    }

    // 检查是否是有效的二进制响应
    if (response.size() >= 3) {
        quint8 first  = static_cast<quint8>(response[0]);
        quint8 second = static_cast<quint8>(response[1]);

        // 常见的设备响应头
        if ((first == 0xAA && second == 0x55) ||  // SPRM常用头
            (first > 0 && first < 248) ||         // Modbus地址范围
            (first == 0x02 || first == 0x03)) {   // 其他协议头
            return true;
        }
    }

    return false;
}

void UniversalDeviceIdentifier::addIdentificationRule(const IdentificationRule &rule) {
    m_rules.append(rule);

    if (!m_supportedTypes.contains(rule.deviceType)) {
        m_supportedTypes.append(rule.deviceType);
    }

    qDebug() << "Added identification rule for device type:" << rule.deviceType;
}

QStringList UniversalDeviceIdentifier::getSupportedDeviceTypes() {
    return m_supportedTypes;
}

void UniversalDeviceIdentifier::initializeDefaultRules() {
    initializeSprmRules();
    initializeMotorRules();
    initializeSensorRules();
}

void UniversalDeviceIdentifier::initializeSprmRules() {
    // SPRM设备识别规则
    IdentificationRule sprmRule;
    sprmRule.deviceType  = "SPRM";
    sprmRule.pattern     = "SPRM";
    sprmRule.patternType = "contains";
    sprmRule.priority    = 100;

    // 数据提取规则
    sprmRule.extractRules["model"]   = "SPRM[_V]*([\\d\\.]+)";
    sprmRule.extractRules["id"]      = "ID:([\\d]+)";
    sprmRule.extractRules["version"] = "V([\\d\\.]+)";

    addIdentificationRule(sprmRule);

    // SPRM二进制协议规则
    IdentificationRule sprmBinaryRule;
    sprmBinaryRule.deviceType  = "SPRM";
    sprmBinaryRule.pattern     = QByteArray::fromHex("AA55");
    sprmBinaryRule.patternType = "contains";
    sprmBinaryRule.priority    = 90;
    addIdentificationRule(sprmBinaryRule);
}

void UniversalDeviceIdentifier::initializeMotorRules() {
    // 电机设备识别规则
    IdentificationRule motorRule;
    motorRule.deviceType  = "MOTOR";
    motorRule.pattern     = "MOTOR|STEP|SERVO";
    motorRule.patternType = "regex";
    motorRule.priority    = 80;

    motorRule.extractRules["model"]  = "MODEL:([\\w\\d-]+)";
    motorRule.extractRules["id"]     = "SN:([\\d]+)";
    motorRule.extractRules["status"] = "ST:([\\w]+)";

    addIdentificationRule(motorRule);
}

void UniversalDeviceIdentifier::initializeSensorRules() {
    // 传感器设备识别规则
    IdentificationRule sensorRule;
    sensorRule.deviceType  = "SENSOR";
    sensorRule.pattern     = "SENSOR|TEMP|HUMID|PRESS";
    sensorRule.patternType = "regex";
    sensorRule.priority    = 70;

    sensorRule.extractRules["model"] = "([\\w]+SENSOR[\\w]*)";
    sensorRule.extractRules["value"] = "VALUE:([\\d\\.]+)";
    sensorRule.extractRules["unit"]  = "UNIT:([\\w]+)";

    addIdentificationRule(sensorRule);
}

void UniversalDeviceIdentifier::initializeCapabilities() {
    // SPRM设备能力
    m_deviceCapabilities["SPRM"] =
        QStringList({"Distance_Measurement", "High_Precision", "Serial_Communication", "Binary_Protocol", "Calibration", "Real_Time_Data"});

    // 电机设备能力
    m_deviceCapabilities["MOTOR"] =
        QStringList({"Position_Control", "Speed_Control", "Torque_Control", "Status_Monitoring", "Parameter_Setting", "Serial_Communication"});

    // 传感器设备能力
    m_deviceCapabilities["SENSOR"] =
        QStringList({"Data_Collection", "Real_Time_Monitoring", "Threshold_Alert", "Data_Logging", "Calibration", "Serial_Communication"});

    m_supportedTypes = m_deviceCapabilities.keys();
}

QString UniversalDeviceIdentifier::matchByPattern(const QByteArray &response) {
    // 按优先级排序规则
    auto sortedRules = m_rules;
    std::sort(sortedRules.begin(), sortedRules.end(), [](const IdentificationRule &a, const IdentificationRule &b) { return a.priority > b.priority; });

    for (const auto &rule : sortedRules) {
        bool matched = false;

        if (rule.patternType == "exact") {
            matched = (response == rule.pattern);
        } else if (rule.patternType == "contains") {
            matched = response.contains(rule.pattern);
        } else if (rule.patternType == "regex") {
            QRegExp regex(QString::fromLatin1(rule.pattern));
            matched = regex.indexIn(QString::fromLatin1(response)) != -1;
        }

        if (matched) {
            return rule.deviceType;
        }
    }

    return QString();
}

QString UniversalDeviceIdentifier::matchByKeywords(const QByteArray &response) {
    QString responseStr = QString::fromLatin1(response).toUpper();

    // SPRM关键词
    if (containsKeywords(response, {"SPRM", "RANGE", "DISTANCE", "TOF"})) {
        return "SPRM";
    }

    // 电机关键词
    if (containsKeywords(response, {"MOTOR", "STEP", "SERVO", "RPM", "POSITION"})) {
        return "MOTOR";
    }

    // 传感器关键词
    if (containsKeywords(response, {"SENSOR", "TEMP", "HUMIDITY", "PRESSURE", "VALUE"})) {
        return "SENSOR";
    }

    return QString();
}

QString UniversalDeviceIdentifier::matchByStructure(const QByteArray &response) {
    if (response.size() < 4) {
        return QString();
    }

    quint8 header1 = static_cast<quint8>(response[0]);
    quint8 header2 = static_cast<quint8>(response[1]);

    // SPRM协议结构：AA 55 ...
    if (header1 == 0xAA && header2 == 0x55) {
        return "SPRM";
    }

    // Modbus RTU结构
    if (header1 > 0 && header1 < 248 && response.size() >= 5) {
        quint8 function = static_cast<quint8>(response[1]);
        if (function == 0x03 || function == 0x04 || function == 0x06 || function == 0x10) {
            return "MOTOR";  // 假设Modbus设备是电机
        }
    }

    return QString();
}

DeviceInfo UniversalDeviceIdentifier::extractSprmInfo(const QByteArray &response) {
    DeviceInfo info;
    info.deviceType = DeviceType::Generic;  // SPRM设备归类为通用设备

    QString responseStr = QString::fromLatin1(response);

    // 提取型号
    info.deviceModel = extractValueByRegex(response, "SPRM[_V]*([\\d\\.]+)");
    if (info.deviceModel.isEmpty()) {
        info.deviceModel = "SPRM_Unknown";
    }

    // 提取设备ID
    info.deviceId = extractValueByRegex(response, "ID:([\\d]+)");

    // 提取固件版本
    info.firmwareVersion = extractValueByRegex(response, "V([\\d\\.]+)");

    // 设置属性
    info.properties["protocol"]  = "Binary";
    info.properties["interface"] = "Serial";

    return info;
}

DeviceInfo UniversalDeviceIdentifier::extractMotorInfo(const QByteArray &response) {
    DeviceInfo info;
    info.deviceType = DeviceType::Motor;  // 电机设备

    // 提取型号
    info.deviceModel = extractValueByRegex(response, "MODEL:([\\w\\d-]+)");
    if (info.deviceModel.isEmpty()) {
        info.deviceModel = "Motor_Unknown";
    }

    // 提取序列号
    info.deviceId = extractValueByRegex(response, "SN:([\\d]+)");

    // 设置属性
    info.properties["status"]   = extractValueByRegex(response, "ST:([\\w]+)");
    info.properties["protocol"] = "Modbus";

    return info;
}

DeviceInfo UniversalDeviceIdentifier::extractSensorInfo(const QByteArray &response) {
    DeviceInfo info;
    info.deviceType = DeviceType::Sensor;  // 传感器设备

    // 提取型号
    info.deviceModel = extractValueByRegex(response, "([\\w]+SENSOR[\\w]*)");
    if (info.deviceModel.isEmpty()) {
        info.deviceModel = "Sensor_Unknown";
    }

    // 提取当前值
    QString value = extractValueByRegex(response, "VALUE:([\\d\\.]+)");
    QString unit  = extractValueByRegex(response, "UNIT:([\\w]+)");

    info.properties["currentValue"] = value;
    info.properties["unit"]         = unit;

    return info;
}

DeviceInfo UniversalDeviceIdentifier::extractGenericInfo(const QString &deviceType, const QByteArray &response) {
    DeviceInfo info;
    info.deviceType  = DeviceType::Generic;  // 通用设备类型
    info.deviceModel = QString("%1_Generic").arg(deviceType);

    // 尝试提取通用信息
    info.deviceId        = extractValueByRegex(response, "ID:([\\d\\w]+)");
    info.firmwareVersion = extractValueByRegex(response, "VER:([\\d\\.]+)");

    return info;
}

QString UniversalDeviceIdentifier::extractValue(const QByteArray &data, const QString &key) {
    QString dataStr = QString::fromLatin1(data);
    QString pattern = key + ":([\\w\\d\\.\\-]+)";
    return extractValueByRegex(data, pattern);
}

QString UniversalDeviceIdentifier::extractValueByRegex(const QByteArray &data, const QString &pattern) {
    QRegExp regex(pattern, Qt::CaseInsensitive);
    QString dataStr = QString::fromLatin1(data);

    if (regex.indexIn(dataStr) != -1) {
        return regex.cap(1);
    }

    return QString();
}

bool UniversalDeviceIdentifier::containsKeywords(const QByteArray &data, const QStringList &keywords) {
    QString dataStr = QString::fromLatin1(data).toUpper();

    for (const QString &keyword : keywords) {
        if (dataStr.contains(keyword.toUpper())) {
            return true;
        }
    }

    return false;
}

QVariantMap UniversalDeviceIdentifier::parseProperties(const QByteArray &response, const QVariantMap &rules) {
    QVariantMap properties;

    for (auto it = rules.begin(); it != rules.end(); ++it) {
        QString value = extractValueByRegex(response, it.value().toString());
        if (!value.isEmpty()) {
            properties[it.key()] = value;
        }
    }

    return properties;
}

}  // namespace Matching
}  // namespace DeviceManagement
}  // namespace LA