#include "../include/IDeviceRegistry.h"
#include <QMutex>
#include <QMutexLocker>
#include <QDebug>
#include <QDateTime>
#include <memory>

namespace LA {
namespace DeviceManagement {

/**
 * @brief 简化的设备注册表实现
 * 
 * 遵循Linus开发哲学：
 * - 最小可行实现：先让基本功能工作
 * - 单一职责：专注于设备信息管理
 * - 线程安全：使用QMutex保护共享数据
 */
class SimpleDeviceRegistry : public IDeviceRegistry {
    Q_OBJECT

public:
    explicit SimpleDeviceRegistry(QObject* parent = nullptr);
    ~SimpleDeviceRegistry() override = default;

    // === 设备注册管理 ===
    SimpleResult registerDevice(const DeviceInfo& device) override;
    SimpleResult unregisterDevice(const QString& deviceId) override;
    bool isDeviceRegistered(const QString& deviceId) const override;

    // === 设备信息查询 ===
    DeviceInfo getDeviceInfo(const QString& deviceId) const override;
    DeviceInfoList getAllDevices() const override;
    DeviceInfoList getDevicesByType(DeviceType type) const override;
    QStringList getAllDeviceIds() const override;

    // === 设备命令管理 ===
    QStringList getSupportedCommands(const QString& deviceId) const override;
    bool supportsCommand(const QString& deviceId, const QString& commandId) const override;
    ConfigParameters getCommandDefinition(const QString& deviceId, const QString& commandId) const override;

    // === 设备配置管理 ===
    SimpleResult updateDeviceConfig(const QString& deviceId, const ConfigParameters& parameters) override;
    ConfigParameters getDeviceConfig(const QString& deviceId) const override;

    // === 状态查询 ===
    DeviceStatistics getStatistics() const override;
    int getDeviceCount() const override;
    QString errorString() const override;
    SimpleResult clear() override;

private:
    mutable QMutex m_mutex;  // 线程安全锁
    
    // 核心数据存储
    QHash<QString, DeviceInfo> m_devices;           // 设备信息存储
    QHash<QString, ConfigParameters> m_configs;    // 设备配置存储
    QHash<QString, QStringList> m_commands;        // 设备命令存储
    
    // 统计信息
    DeviceStatistics m_statistics;
    QString m_lastError;
    
    // 辅助方法
    void updateStatistics();
    void logOperation(const QString& operation, const QString& deviceId);
};

// 实现构造函数
SimpleDeviceRegistry::SimpleDeviceRegistry(QObject* parent) 
    : IDeviceRegistry(parent) {
    
    qDebug() << "SimpleDeviceRegistry: 初始化";
    
    // 初始化统计信息
    m_statistics.bytesReceived = 0;
    m_statistics.bytesSent = 0;
    m_statistics.packetsReceived = 0;
    m_statistics.packetsSent = 0;
    m_statistics.errorsCount = 0;
    m_statistics.lastActivity = QDateTime::currentMSecsSinceEpoch();
    m_statistics.connectionUptime = 0.0;
    
    // 注册一些示例设备类型用于测试
    DeviceInfo virtualDevice;
    virtualDevice.deviceId = "virtual_test_device";
    virtualDevice.deviceName = "Virtual Test Device";
    virtualDevice.deviceType = DeviceType::Virtual;
    virtualDevice.supportedPorts = QStringList{"Virtual"};
    virtualDevice.manufacturer = "LA System";
    virtualDevice.model = "VirtualDevice v1.0";
    virtualDevice.description = "Virtual device for testing";
    
    registerDevice(virtualDevice);
    
    qDebug() << "SimpleDeviceRegistry: 初始化完成，注册了示例设备";
}

// 设备注册管理实现
SimpleResult SimpleDeviceRegistry::registerDevice(const DeviceInfo& device) {
    QMutexLocker locker(&m_mutex);
    
    if (device.deviceId.isEmpty()) {
        m_lastError = "Device ID cannot be empty";
        return SimpleResult::failure(m_lastError);
    }
    
    if (m_devices.contains(device.deviceId)) {
        m_lastError = QString("Device already registered: %1").arg(device.deviceId);
        return SimpleResult::failure(m_lastError);
    }
    
    // 注册设备
    m_devices[device.deviceId] = device;
    
    // 初始化默认配置
    ConfigParameters defaultConfig;
    defaultConfig["enabled"] = true;
    defaultConfig["registrationTime"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    m_configs[device.deviceId] = defaultConfig;
    
    // 初始化默认命令
    QStringList defaultCommands;
    defaultCommands << "getStatus" << "reset" << "getInfo";
    m_commands[device.deviceId] = defaultCommands;
    
    updateStatistics();
    logOperation("register", device.deviceId);
    
    // 发送信号
    emit deviceRegistered(device);
    
    return SimpleResult::success(true);
}

SimpleResult SimpleDeviceRegistry::unregisterDevice(const QString& deviceId) {
    QMutexLocker locker(&m_mutex);
    
    if (!m_devices.contains(deviceId)) {
        m_lastError = QString("Device not found: %1").arg(deviceId);
        return SimpleResult::failure(m_lastError);
    }
    
    // 移除设备及相关数据
    m_devices.remove(deviceId);
    m_configs.remove(deviceId);
    m_commands.remove(deviceId);
    
    updateStatistics();
    logOperation("unregister", deviceId);
    
    // 发送信号
    emit deviceUnregistered(deviceId);
    
    return SimpleResult::success(true);
}

bool SimpleDeviceRegistry::isDeviceRegistered(const QString& deviceId) const {
    QMutexLocker locker(&m_mutex);
    return m_devices.contains(deviceId);
}

// 设备信息查询实现
DeviceInfo SimpleDeviceRegistry::getDeviceInfo(const QString& deviceId) const {
    QMutexLocker locker(&m_mutex);
    return m_devices.value(deviceId, DeviceInfo());
}

DeviceInfoList SimpleDeviceRegistry::getAllDevices() const {
    QMutexLocker locker(&m_mutex);
    
    DeviceInfoList devices;
    for (auto it = m_devices.begin(); it != m_devices.end(); ++it) {
        devices.append(it.value());
    }
    return devices;
}

DeviceInfoList SimpleDeviceRegistry::getDevicesByType(DeviceType type) const {
    QMutexLocker locker(&m_mutex);
    
    DeviceInfoList devices;
    for (auto it = m_devices.begin(); it != m_devices.end(); ++it) {
        if (it.value().deviceType == type) {
            devices.append(it.value());
        }
    }
    return devices;
}

QStringList SimpleDeviceRegistry::getAllDeviceIds() const {
    QMutexLocker locker(&m_mutex);
    return m_devices.keys();
}

// 设备命令管理实现
QStringList SimpleDeviceRegistry::getSupportedCommands(const QString& deviceId) const {
    QMutexLocker locker(&m_mutex);
    return m_commands.value(deviceId, QStringList());
}

bool SimpleDeviceRegistry::supportsCommand(const QString& deviceId, const QString& commandId) const {
    QMutexLocker locker(&m_mutex);
    return m_commands.value(deviceId, QStringList()).contains(commandId);
}

ConfigParameters SimpleDeviceRegistry::getCommandDefinition(const QString& deviceId, const QString& commandId) const {
    QMutexLocker locker(&m_mutex);
    
    if (!supportsCommand(deviceId, commandId)) {
        return ConfigParameters();
    }
    
    // 返回命令的基本定义
    ConfigParameters definition;
    definition["commandId"] = commandId;
    definition["deviceId"] = deviceId;
    definition["timeout"] = 5000;
    definition["retryCount"] = 3;
    
    return definition;
}

// 设备配置管理实现
SimpleResult SimpleDeviceRegistry::updateDeviceConfig(const QString& deviceId, const ConfigParameters& parameters) {
    QMutexLocker locker(&m_mutex);
    
    if (!m_devices.contains(deviceId)) {
        m_lastError = QString("Device not found: %1").arg(deviceId);
        return SimpleResult::failure(m_lastError);
    }
    
    m_configs[deviceId] = parameters;
    logOperation("updateConfig", deviceId);
    
    // 发送信号
    emit deviceConfigUpdated(deviceId, parameters);
    
    return SimpleResult::success(true);
}

ConfigParameters SimpleDeviceRegistry::getDeviceConfig(const QString& deviceId) const {
    QMutexLocker locker(&m_mutex);
    return m_configs.value(deviceId, ConfigParameters());
}

// 状态查询实现
DeviceStatistics SimpleDeviceRegistry::getStatistics() const {
    QMutexLocker locker(&m_mutex);
    return m_statistics;
}

int SimpleDeviceRegistry::getDeviceCount() const {
    QMutexLocker locker(&m_mutex);
    return m_devices.size();
}

QString SimpleDeviceRegistry::errorString() const {
    QMutexLocker locker(&m_mutex);
    return m_lastError;
}

SimpleResult SimpleDeviceRegistry::clear() {
    QMutexLocker locker(&m_mutex);
    
    m_devices.clear();
    m_configs.clear();
    m_commands.clear();
    
    updateStatistics();
    logOperation("clear", "all");
    
    return SimpleResult::success(true);
}

// 辅助方法实现
void SimpleDeviceRegistry::updateStatistics() {
    m_statistics.lastActivity = QDateTime::currentMSecsSinceEpoch();
    m_statistics.packetsReceived = m_devices.size(); // 简化统计
}

void SimpleDeviceRegistry::logOperation(const QString& operation, const QString& deviceId) {
    qDebug() << QString("SimpleDeviceRegistry: %1 device %2").arg(operation, deviceId);
}

/**
 * @brief 设备注册表工厂实现
 */
class SimpleDeviceRegistryFactory : public IDeviceRegistryFactory {
public:
    std::shared_ptr<IDeviceRegistry> createDeviceRegistry() override {
        return std::make_shared<SimpleDeviceRegistry>();
    }
    
    bool isSupported() const override {
        return true;
    }
};

// 全局工厂函数
std::shared_ptr<IDeviceRegistry> createDeviceRegistry() {
    return std::make_shared<SimpleDeviceRegistry>();
}

std::shared_ptr<IDeviceRegistryFactory> createDeviceRegistryFactory() {
    return std::make_shared<SimpleDeviceRegistryFactory>();
}

} // namespace DeviceManagement  
} // namespace LA

// 包含MOC文件
#include "SimpleDeviceRegistry.moc"