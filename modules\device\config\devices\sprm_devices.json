{"device_configurations": {"SPRM-A1": {"basic_info": {"model": "SPRM-A1", "manufacturer": "Nova", "category": "RangingSensor", "description": "650nm红光激光测距传感器", "version": "1.0"}, "hardware_specs": {"laser_wavelength": "650nm", "laser_type": "红光激光二极管", "laser_power": 5.0, "receiver_type": "APD", "measurement_range": {"min": 50, "max": 2000, "unit": "mm"}, "accuracy": {"typical": 1.0, "unit": "mm"}, "resolution": {"value": 0.1, "unit": "mm"}, "response_time": {"typical": 10, "unit": "ms"}, "operating_temperature": {"min": -10, "max": 60, "unit": "°C"}, "protection_level": "IP65"}, "communication": {"protocol": "RS485", "baudrate": 19200, "data_bits": 8, "parity": "None", "stop_bits": 1, "device_address": 133}, "four_layer_architecture": {"driver": {"type": "SprmA1Driver", "config": {"timeout_ms": 3000, "retry_count": 3, "protocol_version": "1.0"}}, "capabilities": [{"name": "LaserR<PERSON>ing", "config": {"mode": "single", "average_samples": 3, "accuracy_threshold": 1.0, "enable_filtering": true, "enable_validation": true}}, {"name": "SerialCommunication", "config": {"port_name": "auto_detect", "baudrate": 19200, "timeout_ms": 2000}}, {"name": "AutoCalibration", "config": {"type": "single_point", "points": [1000], "tolerance": 0.5}}], "strategies": {"filtering": {"default": "moving_average", "options": {"moving_average": {"window_size": 5, "remove_outliers": true}, "kalman": {"process_noise": 0.01, "measurement_noise": 0.1, "initial_covariance": 1.0}}}, "calibration": {"default": "single_point", "options": {"single_point": {"reference_distance": 1000, "tolerance": 1.0}, "multi_point": {"reference_distances": [50, 500, 1000, 1500, 2000], "tolerance": 0.5}}}, "communication": {"default": "standard_retry", "options": {"standard_retry": {"max_retries": 3, "timeout_ms": 1000}, "adaptive_retry": {"initial_timeout": 500, "max_timeout": 3000, "backoff_factor": 2.0}}}}, "script": {"behavior_script": "sprm_behavior.lua", "rule_config": "device_rules.yml", "sandbox_enabled": true, "resource_limits": {"memory_limit_kb": 1024, "time_limit_ms": 5000}}}, "commands": {"START_MEASURE": {"code": 1, "description": "开始测量", "parameters": ["timeout", "precision_mode"], "response_type": "measurement_result"}, "STOP_MEASURE": {"code": 2, "description": "停止测量", "parameters": [], "response_type": "status"}, "GET_DISTANCE": {"code": 3, "description": "获取距离值", "parameters": ["sample_count"], "response_type": "distance_data"}, "CALIBRATE": {"code": 4, "description": "校准传感器", "parameters": ["reference_distance", "calibration_type"], "response_type": "calibration_result"}, "SET_LASER_POWER": {"code": 5, "description": "设置激光功率", "parameters": ["power_level"], "response_type": "status"}, "GET_STATUS": {"code": 6, "description": "获取设备状态", "parameters": [], "response_type": "device_status"}}, "environments": {"indoor": {"recommended_power": 3.0, "filter_strategy": "moving_average", "calibration_interval": 168}, "outdoor": {"recommended_power": 5.0, "filter_strategy": "kalman", "calibration_interval": 24}, "industrial": {"recommended_power": 4.5, "filter_strategy": "kalman", "calibration_interval": 72, "vibration_compensation": true}}}, "SPRM-A2": {"basic_info": {"model": "SPRM-A2", "manufacturer": "Nova", "category": "RangingSensor", "description": "780nm近红外激光测距传感器", "version": "2.0"}, "hardware_specs": {"laser_wavelength": "780nm", "laser_type": "近红外激光二极管", "laser_power": 8.0, "receiver_type": "PIN", "measurement_range": {"min": 30, "max": 5000, "unit": "mm"}, "accuracy": {"typical": 0.5, "unit": "mm"}, "resolution": {"value": 0.05, "unit": "mm"}, "response_time": {"typical": 5, "unit": "ms"}, "operating_temperature": {"min": -20, "max": 70, "unit": "°C"}, "protection_level": "IP67"}, "communication": {"protocol": "CAN", "baudrate": 500000, "device_id": "0x185", "protocol_version": "2.0B"}, "four_layer_architecture": {"driver": {"type": "SprmA2Driver", "config": {"timeout_ms": 2000, "retry_count": 3, "protocol_version": "2.0"}}, "capabilities": [{"name": "LaserR<PERSON>ing", "config": {"mode": "average", "average_samples": 5, "accuracy_threshold": 0.5, "enable_filtering": true, "enable_validation": true}}, {"name": "CANCommunication", "config": {"can_id": "0x185", "baudrate": 500000, "timeout_ms": 1000}}, {"name": "PrecisionCalibration", "config": {"type": "multi_point", "points": [30, 100, 500, 1000, 3000, 5000], "tolerance": 0.25}}], "strategies": {"filtering": {"default": "kalman", "options": {"kalman": {"process_noise": 0.005, "measurement_noise": 0.05, "initial_covariance": 0.5}, "extended_kalman": {"process_noise": 0.005, "measurement_noise": 0.05, "nonlinear_function": "quadratic"}}}, "calibration": {"default": "multi_point", "options": {"multi_point": {"reference_distances": [30, 100, 500, 1000, 3000, 5000], "tolerance": 0.25, "temperature_compensation": true}, "adaptive": {"initial_points": [100, 1000, 3000], "adaptive_threshold": 0.1, "max_points": 10}}}}, "script": {"behavior_script": "sprm_behavior.lua", "rule_config": "device_rules.yml", "sandbox_enabled": true, "resource_limits": {"memory_limit_kb": 2048, "time_limit_ms": 3000}}}, "commands": {"START_MEASURE": {"code": 1, "description": "开始高精度测量", "parameters": ["timeout", "precision_mode", "sample_rate"], "response_type": "measurement_result"}, "GET_DISTANCE": {"code": 3, "description": "获取高精度距离值", "parameters": ["sample_count", "filter_type"], "response_type": "distance_data"}, "CALIBRATE": {"code": 4, "description": "多点校准", "parameters": ["calibration_points", "temperature_compensation"], "response_type": "calibration_result"}, "SET_PRECISION_MODE": {"code": 10, "description": "设置精度模式", "parameters": ["mode", "parameters"], "response_type": "status"}}, "environments": {"precision_lab": {"recommended_power": 6.0, "filter_strategy": "extended_kalman", "calibration_interval": 12, "temperature_compensation": true}, "manufacturing": {"recommended_power": 8.0, "filter_strategy": "kalman", "calibration_interval": 48, "vibration_compensation": true, "dust_protection": true}}}}, "global_settings": {"script_engine": {"lua_version": "5.4", "default_timeout": 5000, "max_memory": 4096, "sandbox_mode": true}, "rule_engine": {"evaluation_interval": 1000, "priority_handling": "highest_first", "conflict_resolution": "last_wins"}, "logging": {"level": "INFO", "enable_script_logs": true, "enable_strategy_logs": false, "enable_driver_logs": true}}, "metadata": {"version": "2.0.0", "created": "2025-01-22", "description": "SPRM设备四层架构配置", "author": "Device Team"}}