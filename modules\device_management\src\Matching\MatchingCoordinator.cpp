#include "LA/DeviceManagement/Matching/MatchingCoordinator.h"
#include <QDebug>
#include <QTimer>
#include <QtConcurrent>

namespace LA {
namespace DeviceManagement {
namespace Matching {

MatchingCoordinator::MatchingCoordinator(QObject *parent) : QObject(parent) {
    qDebug() << "MatchingCoordinator initialized";
}

void MatchingCoordinator::setPortScanner(std::unique_ptr<IPortScanner> portScanner) {
    m_portScanner = std::move(portScanner);
    qDebug() << "PortScanner injected";
}

void MatchingCoordinator::setDeviceProber(std::unique_ptr<IDeviceProber> deviceProber) {
    m_deviceProber = std::move(deviceProber);
    qDebug() << "DeviceProber injected";
}

void MatchingCoordinator::setDeviceIdentifier(std::unique_ptr<IDeviceIdentifier> deviceIdentifier) {
    m_deviceIdentifier = std::move(deviceIdentifier);
    qDebug() << "DeviceIdentifier injected";
}

void MatchingCoordinator::setMatchingAlgorithm(std::unique_ptr<IMatchingAlgorithm> matchingAlgorithm) {
    m_matchingAlgorithm = std::move(matchingAlgorithm);
    qDebug() << "MatchingAlgorithm injected";
}

void MatchingCoordinator::setRegistrationManager(std::shared_ptr<LA::DeviceManagement::Registration::IRegistrationManager> registrationManager) {
    m_registrationManager = registrationManager;
    qDebug() << "RegistrationManager injected - 完整5阶段流程已配置";
}

MatchingResult MatchingCoordinator::performAutoMatching(const DeviceProbeConfig &probeConfig, const MatchingConfig &matchingConfig) {
    qDebug() << "========================================";
    qDebug() << "🎯 MatchingCoordinator: 开始执行4阶段自动匹配流程";
    qDebug() << "========================================";

    if (m_isRunning) {
        qDebug() << "⚠️ MatchingCoordinator: 另一个匹配流程正在运行中";
        m_lastError = "Another matching process is already running";
        return MatchingResult();
    }

    m_isRunning = true;
    m_lastError.clear();
    qDebug() << "✅ MatchingCoordinator: 匹配流程已启动，设置运行状态";
    emit matchingStarted();

    try {
        // 验证依赖
        validateDependencies();

        // 第一阶段：端口发现
        qDebug() << "🔍 MatchingCoordinator: 阶段1 - 端口发现开始";
        emitProgress(1, 4, "Discovering ports...");
        auto ports = discoverPorts();
        qDebug() << "🔍 MatchingCoordinator: 阶段1完成，发现" << ports.size() << "个端口";
        if (ports.isEmpty()) {
            qDebug() << "❌ MatchingCoordinator: 没有发现可用端口，终止匹配流程";
            throw QString("No available ports found");
        }
        for (const auto &port : ports) {
            // 使用Foundation::Core::PortType，可以安全转换为int
            qDebug() << "  📌 端口:" << port.portName << "类型:" << static_cast<int>(port.type);
        }

        // 第二阶段：设备探测和识别
        qDebug() << "🔧 MatchingCoordinator: 阶段2 - 设备探测识别开始";
        emitProgress(2, 4, "Probing and identifying devices...");
        auto devices = probeAndIdentifyDevices(ports, probeConfig);
        qDebug() << "🔧 MatchingCoordinator: 阶段2完成，识别到" << devices.size() << "个设备";
        if (devices.isEmpty()) {
            qWarning() << "⚠️ MatchingCoordinator: 没有识别到设备，但继续执行匹配流程";
        }
        for (const auto &device : devices) {
            qDebug() << "  📱 设备:" << device.deviceId << "类型:" << static_cast<int>(device.deviceType);
        }

        // 第三阶段：匹配计算
        qDebug() << "🎯 MatchingCoordinator: 阶段3 - 匹配计算开始";
        emitProgress(3, 4, "Calculating matches...");
        auto result = calculateMatches(ports, devices, matchingConfig);
        qDebug() << "🎯 MatchingCoordinator: 阶段3完成，成功匹配" << result.successMatches.size() << "对";

        // 第四阶段：注册管理
        qDebug() << "📝 MatchingCoordinator: 阶段4 - 设备注册管理开始";
        emitProgress(4, 4, "Registering matched devices...");
        bool registrationSuccess = registerMatchedDevices(result);
        if (!registrationSuccess) {
            qWarning() << "⚠️ MatchingCoordinator: 设备注册阶段失败，但匹配结果仍然有效";
            // 注意：注册失败不影响匹配结果，只是影响后续的生命周期管理
        } else {
            qDebug() << "✅ MatchingCoordinator: 阶段4完成，设备注册成功";
        }

        // 匹配流程完成
        qDebug() << "MatchingCoordinator: 完整4阶段匹配流程执行完毕";

        m_isRunning = false;
        emit matchingCompleted(result);
        return result;

    } catch (const QString &error) {
        m_lastError = error;
        m_isRunning = false;
        emit matchingError(error);
        return MatchingResult();
    }
}

void MatchingCoordinator::performAutoMatchingAsync(const DeviceProbeConfig &probeConfig, const MatchingConfig &matchingConfig) {
    // 使用QtConcurrent异步执行
    auto future = QtConcurrent::run([this, probeConfig, matchingConfig]() { return performAutoMatching(probeConfig, matchingConfig); });

    // 监控异步执行完成
    QTimer::singleShot(0, this, &MatchingCoordinator::onAsyncMatchingFinished);
}

QList<PortInfo> MatchingCoordinator::discoverPorts() {
    if (!m_portScanner) {
        throw QString("PortScanner not available");
    }

    qDebug() << "Starting port discovery...";
    auto ports = m_portScanner->scanAvailablePorts();
    qDebug() << "Found" << ports.size() << "ports";

    return ports;
}

QList<DeviceInfo> MatchingCoordinator::probeAndIdentifyDevices(const QList<PortInfo> &ports, const DeviceProbeConfig &config) {
    if (!m_deviceProber || !m_deviceIdentifier) {
        throw QString("DeviceProber or DeviceIdentifier not available");
    }

    QList<DeviceInfo> devices;
    int               currentPort = 0;

    for (const auto &port : ports) {
        currentPort++;
        emitProgress(currentPort, ports.size(), QString("Probing port %1...").arg(port.portName));

        qDebug() << "Probing port:" << port.portName;

        // 探测设备
        auto probeResult = m_deviceProber->probeDevice(port.portName, config);
        if (!probeResult.isValid()) {
            qDebug() << "No valid response from port:" << port.portName;
            continue;
        }

        // 识别设备
        auto deviceInfo = m_deviceIdentifier->extractDeviceInfo(probeResult.response);
        if (!deviceInfo.isValid()) {
            qDebug() << "Could not identify device on port:" << port.portName;
            continue;
        }

        qDebug() << "Identified device:" << static_cast<int>(deviceInfo.deviceType) << "on port:" << port.portName;
        devices.append(deviceInfo);
    }

    return devices;
}

MatchingResult MatchingCoordinator::calculateMatches(const QList<PortInfo> &ports, const QList<DeviceInfo> &devices, const MatchingConfig &config) {
    if (!m_matchingAlgorithm) {
        throw QString("MatchingAlgorithm not available");
    }

    qDebug() << "Starting matching calculation...";
    qDebug() << "Ports:" << ports.size() << "Devices:" << devices.size();

    return m_matchingAlgorithm->matchDevicesToPorts(devices, ports, config);
}

bool MatchingCoordinator::registerMatchedDevices(const MatchingResult &matchingResult) {
    if (!m_registrationManager) {
        qWarning() << "MatchingCoordinator: 注册管理器未注入，跳过注册阶段";
        return false;
    }

    qDebug() << "MatchingCoordinator: 开始注册" << matchingResult.successMatches.size() << "个匹配的设备";

    int successCount = 0;
    for (const auto &matchPair : matchingResult.successMatches) {
        try {
            auto registrationResult = m_registrationManager->registerDevicePort(matchPair.device, matchPair.port);

            if (registrationResult.success) {
                successCount++;
                qDebug() << "MatchingCoordinator: 成功注册设备" << matchPair.device.deviceId << "到端口" << matchPair.port.portName;
            } else {
                qWarning() << "MatchingCoordinator: 注册设备" << matchPair.device.deviceId << "失败:" << registrationResult.errorMessage;
            }
        } catch (const std::exception &e) {
            qWarning() << "MatchingCoordinator: 注册设备" << matchPair.device.deviceId << "时发生异常:" << e.what();
        }
    }

    bool allSuccess = (successCount == matchingResult.successMatches.size());
    qDebug() << "MatchingCoordinator: 注册完成，成功" << successCount << "个，总计" << matchingResult.successMatches.size() << "个";

    return allSuccess;
}

bool MatchingCoordinator::isReady() const {
    // 注意：RegistrationManager是可选的，不注入也可以执行匹配（只是无法注册）
    bool basicReady = m_portScanner && m_deviceProber && m_deviceIdentifier && m_matchingAlgorithm && !m_isRunning;
    return basicReady;
}

QString MatchingCoordinator::getLastError() const {
    return m_lastError;
}

void MatchingCoordinator::onAsyncMatchingFinished() {
    // 异步执行完成的处理
    if (m_isRunning) {
        // 如果还在运行，延迟检查
        QTimer::singleShot(100, this, &MatchingCoordinator::onAsyncMatchingFinished);
    }
}

void MatchingCoordinator::validateDependencies() {
    QStringList missingDependencies;

    if (!m_portScanner) {
        missingDependencies << "PortScanner";
    }
    if (!m_deviceProber) {
        missingDependencies << "DeviceProber";
    }
    if (!m_deviceIdentifier) {
        missingDependencies << "DeviceIdentifier";
    }
    if (!m_matchingAlgorithm) {
        missingDependencies << "MatchingAlgorithm";
    }

    if (!missingDependencies.isEmpty()) {
        throw QString("Missing dependencies: %1").arg(missingDependencies.join(", "));
    }
}

void MatchingCoordinator::emitProgress(int current, int total, const QString &status) {
    emit matchingProgress(current, total, status);
    qDebug() << QString("Progress: %1/%2 - %3").arg(current).arg(total).arg(status);
}

}  // namespace Matching
}  // namespace DeviceManagement
}  // namespace LA