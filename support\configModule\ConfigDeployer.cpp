#include "ConfigDeployer.h"
#include <QApplication>
#include <QDir>
#include <QFile>
#include <QFileInfo>
#include <QJsonDocument>
#include <QJsonObject>
#include <QDateTime>
#include <QDebug>

namespace Config {

ConfigDeployer::ConfigDeployer() {
    // 源配置目录：项目中的配置文件
    m_sourceConfigDir = QApplication::applicationDirPath() + "/../config";
    
    // 目标配置目录：执行文件目录下的配置
    m_targetConfigDir = QApplication::applicationDirPath() + "/config";
}

ConfigDeployer::DeployResult ConfigDeployer::deployAllConfigs(DeployStrategy strategy) {
    DeployResult result;
    result.success = true;
    
    qDebug() << "[ConfigDeployer] Starting config deployment with strategy:" << static_cast<int>(strategy);
    
    // 确保目标目录存在
    if (!ensureDirectoryExists(m_targetConfigDir)) {
        result.success = false;
        result.message = "Failed to create target config directory: " + m_targetConfigDir;
        return result;
    }
    
    // 获取所有配置文件列表
    QStringList configFiles = getConfigFileList();
    
    for (const QString &configFile : configFiles) {
        QString sourceFile = m_sourceConfigDir + "/" + configFile;
        QString targetFile = m_targetConfigDir + "/" + configFile;
        
        // 确保目标子目录存在
        QFileInfo targetInfo(targetFile);
        if (!ensureDirectoryExists(targetInfo.absolutePath())) {
            result.errorFiles.append(configFile);
            continue;
        }
        
        if (copyConfigFile(sourceFile, targetFile, strategy)) {
            result.deployedFiles.append(configFile);
            qDebug() << "[ConfigDeployer] Deployed:" << configFile;
        } else {
            if (QFile::exists(targetFile)) {
                result.skippedFiles.append(configFile);
                qDebug() << "[ConfigDeployer] Skipped:" << configFile;
            } else {
                result.errorFiles.append(configFile);
                qDebug() << "[ConfigDeployer] Failed:" << configFile;
            }
        }
    }
    
    // 生成结果消息
    result.message = QString("Deployed: %1, Skipped: %2, Errors: %3")
                        .arg(result.deployedFiles.size())
                        .arg(result.skippedFiles.size())
                        .arg(result.errorFiles.size());
    
    if (!result.errorFiles.isEmpty()) {
        result.success = false;
    }
    
    return result;
}

ConfigDeployer::DeployResult ConfigDeployer::deployModuleConfig(const QString &moduleName, DeployStrategy strategy) {
    DeployResult result;
    result.success = true;
    
    QString moduleDir = "modules/" + moduleName;
    QString sourceModuleDir = m_sourceConfigDir + "/" + moduleDir;
    QString targetModuleDir = m_targetConfigDir + "/" + moduleDir;
    
    // 确保目标模块目录存在
    if (!ensureDirectoryExists(targetModuleDir)) {
        result.success = false;
        result.message = "Failed to create target module directory: " + targetModuleDir;
        return result;
    }
    
    // 获取模块配置文件
    QDir sourceDir(sourceModuleDir);
    QStringList filters;
    filters << "*.json" << "*.ini" << "*.xml";
    QStringList moduleFiles = sourceDir.entryList(filters, QDir::Files);
    
    for (const QString &fileName : moduleFiles) {
        QString sourceFile = sourceModuleDir + "/" + fileName;
        QString targetFile = targetModuleDir + "/" + fileName;
        QString relativeFile = moduleDir + "/" + fileName;
        
        if (copyConfigFile(sourceFile, targetFile, strategy)) {
            result.deployedFiles.append(relativeFile);
        } else {
            if (QFile::exists(targetFile)) {
                result.skippedFiles.append(relativeFile);
            } else {
                result.errorFiles.append(relativeFile);
            }
        }
    }
    
    result.message = QString("Module %1 - Deployed: %2, Skipped: %3, Errors: %4")
                        .arg(moduleName)
                        .arg(result.deployedFiles.size())
                        .arg(result.skippedFiles.size())
                        .arg(result.errorFiles.size());
    
    return result;
}

bool ConfigDeployer::copyConfigFile(const QString &sourceFile, const QString &targetFile, DeployStrategy strategy) {
    if (!QFile::exists(sourceFile)) {
        qDebug() << "[ConfigDeployer] Source file not found:" << sourceFile;
        return false;
    }
    
    bool targetExists = QFile::exists(targetFile);
    
    switch (strategy) {
        case DeployStrategy::SkipExisting:
            if (targetExists) {
                return false; // 跳过，但不是错误
            }
            break;
            
        case DeployStrategy::UpdateIfNewer:
            if (targetExists && !needsUpdate(sourceFile, targetFile)) {
                return false; // 跳过，但不是错误
            }
            break;
            
        case DeployStrategy::BackupAndOverwrite:
            if (targetExists) {
                createBackup(targetFile);
            }
            break;
            
        case DeployStrategy::OverwriteAll:
            // 直接覆盖
            break;
    }
    
    // 如果目标文件存在，先删除
    if (targetExists) {
        QFile::remove(targetFile);
    }
    
    // 复制文件
    return QFile::copy(sourceFile, targetFile);
}

bool ConfigDeployer::needsUpdate(const QString &sourceFile, const QString &targetFile) const {
    QFileInfo sourceInfo(sourceFile);
    QFileInfo targetInfo(targetFile);
    
    // 比较修改时间
    if (sourceInfo.lastModified() > targetInfo.lastModified()) {
        return true;
    }
    
    // 比较版本号（如果是JSON文件）
    if (sourceFile.endsWith(".json") && targetFile.endsWith(".json")) {
        QString sourceVersion = getConfigVersion(sourceFile);
        QString targetVersion = getConfigVersion(targetFile);
        
        if (!sourceVersion.isEmpty() && !targetVersion.isEmpty()) {
            return compareFileVersions(sourceVersion, targetVersion) > 0;
        }
    }
    
    return false;
}

QString ConfigDeployer::createBackup(const QString &filePath) const {
    QString timestamp = QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss");
    QString backupPath = filePath + ".backup_" + timestamp;
    
    if (QFile::copy(filePath, backupPath)) {
        qDebug() << "[ConfigDeployer] Created backup:" << backupPath;
        return backupPath;
    }
    
    return QString();
}

bool ConfigDeployer::ensureDirectoryExists(const QString &dirPath) const {
    QDir dir;
    return dir.mkpath(dirPath);
}

QString ConfigDeployer::getSourceConfigDir() const {
    return m_sourceConfigDir;
}

QString ConfigDeployer::getTargetConfigDir() const {
    return m_targetConfigDir;
}

QStringList ConfigDeployer::getConfigFileList() const {
    QStringList configFiles;
    
    // 系统配置文件
    configFiles << "system/system_config.json";
    
    // 模块配置文件
    configFiles << "modules/lenAdjust/facula_config.json";
    configFiles << "modules/lenAdjust/algorithm_config.json";
    configFiles << "modules/lenAdjust/hardware_config.json";
    
    // 可以根据需要添加更多配置文件
    
    return configFiles;
}

QString ConfigDeployer::getConfigVersion(const QString &filePath) const {
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        return QString();
    }
    
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(file.readAll(), &error);
    if (error.error != QJsonParseError::NoError) {
        return QString();
    }
    
    QJsonObject obj = doc.object();
    return obj.value("_version").toString();
}

int ConfigDeployer::compareFileVersions(const QString &version1, const QString &version2) const {
    // 简单的版本比较，可以根据需要扩展
    return QString::compare(version1, version2);
}

ConfigResult ConfigDeployer::validateConfigFile(const QString &filePath) const {
    ConfigResult result;
    
    if (!QFile::exists(filePath)) {
        result.success = false;
        result.message = "Config file not found: " + filePath;
        return result;
    }
    
    if (filePath.endsWith(".json")) {
        QFile file(filePath);
        if (!file.open(QIODevice::ReadOnly)) {
            result.success = false;
            result.message = "Cannot open config file: " + filePath;
            return result;
        }
        
        QJsonParseError error;
        QJsonDocument::fromJson(file.readAll(), &error);
        if (error.error != QJsonParseError::NoError) {
            result.success = false;
            result.message = "JSON parse error: " + error.errorString();
            return result;
        }
    }
    
    result.success = true;
    result.message = "Config file is valid";
    return result;
}

} // namespace Config
