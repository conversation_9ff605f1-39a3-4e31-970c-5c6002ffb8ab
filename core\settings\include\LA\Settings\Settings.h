#pragma once

/**
 * @file Settings.h
 * @brief LA设置系统统一包含头文件
 *
 * 此文件包含了LA设置系统的所有公共接口和类，
 * 使用者只需包含此文件即可使用完整的设置系统功能。
 */

// 核心接口
#include "interfaces/ISettingsManager.h"
#include "interfaces/ISettingsPanel.h"

// 核心实现
#include "SettingsManager.h"
#include "SettingsPanel.h"

// 具体面板实现
#include "panels/GeneralSettingsPanel.h"
#include "panels/ThemeSettingsPanel.h"

/**
 * @namespace LA::Settings
 * @brief LA设置系统命名空间
 *
 * 包含了所有设置系统相关的类和接口
 */
namespace LA {
namespace Settings {

/**
 * @brief 初始化设置系统
 *
 * 此函数应在应用程序启动时调用，用于初始化设置系统
 * 并注册默认的设置面板
 *
 * @return 初始化成功返回true，否则返回false
 */
bool initializeSettingsSystem();

/**
 * @brief 清理设置系统
 *
 * 此函数应在应用程序退出时调用，用于清理设置系统资源
 */
void cleanupSettingsSystem();

/**
 * @brief 获取设置管理器实例
 *
 * @return 设置管理器实例指针
 */
SettingsManager *getSettingsManager();

/**
 * @brief 注册默认设置面板
 *
 * 注册系统提供的默认设置面板，包括：
 * - 通用设置面板
 * - 主题设置面板
 * - 系统设置面板
 *
 * @param parentContainer 父容器指针，面板将以此为父容器创建
 * @return 注册成功返回true，否则返回false
 */
bool registerDefaultPanels(QWidget *parentContainer);

/**
 * @brief 创建设置对话框
 *
 * 创建一个包含所有已注册设置面板的设置对话框
 *
 * @param parent 父窗口指针
 * @return 设置对话框指针
 */
QDialog *createSettingsDialog(QWidget *parent = nullptr);

// Settings库保持独立，UI集成由使用方（主程序）负责
// registerSettingsToSidebar函数已移除，避免循环依赖

}  // namespace Settings
}  // namespace LA
