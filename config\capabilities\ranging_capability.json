{"capability_type": "ranging", "display_name": "测距能力", "description": "激光测距功能模块", "version": "1.0.0", "supported_strategies": {"basic_ranging": {"name": "基础测距策略", "description": "标准的激光测距算法", "parameters": {"sample_count": {"type": "integer", "default": 10, "min": 1, "max": 100, "description": "采样次数"}, "timeout_ms": {"type": "integer", "default": 1000, "min": 100, "max": 5000, "description": "测量超时时间"}}}, "precision_ranging": {"name": "精密测距策略", "description": "高精度激光测距算法，包含温度补偿", "parameters": {"sample_count": {"type": "integer", "default": 50, "min": 10, "max": 200, "description": "采样次数"}, "temperature_compensation": {"type": "boolean", "default": true, "description": "启用温度补偿"}, "filter_type": {"type": "enum", "default": "kalman", "options": ["none", "median", "average", "kalman"], "description": "数据滤波类型"}}}}, "data_formats": {"measurement_result": {"distance_mm": {"type": "float", "unit": "mm", "description": "测量距离"}, "signal_strength": {"type": "integer", "unit": "percent", "description": "信号强度", "optional": true}, "temperature": {"type": "float", "unit": "celsius", "description": "测量时的温度", "optional": true}, "measurement_time": {"type": "integer", "unit": "ms", "description": "测量耗时"}, "quality_indicator": {"type": "enum", "options": ["good", "fair", "poor"], "description": "测量质量指示"}}}, "error_codes": {"RANGING_OK": 0, "RANGING_TIMEOUT": 1001, "RANGING_OUT_OF_RANGE": 1002, "RANGING_WEAK_SIGNAL": 1003, "RANGING_HARDWARE_ERROR": 1004, "RANGING_CALIBRATION_REQUIRED": 1005}, "requirements": {"min_update_rate_ms": 1, "max_range_mm": 10000, "min_accuracy_mm": 0.1}}