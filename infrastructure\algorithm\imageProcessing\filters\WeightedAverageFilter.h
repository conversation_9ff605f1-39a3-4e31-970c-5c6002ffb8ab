#ifndef IMAGEPROCESSING_WEIGHTEDAVERAGEFILTER_H
#define IMAGEPROCESSING_WEIGHTEDAVERAGEFILTER_H

#include "../interfaces/IImageFilter.h"
#include "../common/ValidationUtils.h"
#include <QVector>
#include <QDebug>

namespace ImageProcessing {

/**
 * @brief 加权均值滤波器实现
 * 
 * 实现加权均值滤波算法，支持自定义权重矩阵
 * 适用于平滑处理和噪声抑制
 */
class WeightedAverageFilter : public IImageFilter {
public:
    /**
     * @brief 构造函数
     */
    WeightedAverageFilter();

    /**
     * @brief 析构函数
     */
    ~WeightedAverageFilter() override = default;

    // IImageFilter接口实现
    bool apply(ImageDataU32& data) override;
    bool apply(const ImageDataU32& src, ImageDataU32& dst) override;
    void setParameters(const FilterParams& params) override;
    std::unique_ptr<FilterParams> getParameters() const override;
    QString getAlgorithmName() const override;
    QString getDescription() const override;
    bool isSupported(uint32_t width, uint32_t height) const override;
    uint32_t estimateProcessingTime(uint32_t width, uint32_t height) const override;
    void reset() override;
    QString getVersion() const override;
    bool isThreadSafe() const override;
    bool supportsInPlace() const override;

    /**
     * @brief 设置预定义的权重模式
     * @param type 权重类型
     */
    void setPredefinedWeights(const QString& type);

    /**
     * @brief 获取支持的权重类型列表
     * @return 权重类型列表
     */
    static QStringList getSupportedWeightTypes();

private:
    WeightedAverageParams params_;    ///< 滤波参数

    /**
     * @brief 应用加权均值操作到单个像素
     * @param src 源图像
     * @param x 像素X坐标
     * @param y 像素Y坐标
     * @return 加权均值结果
     */
    float applyWeightedAverageAtPixel(const ImageDataU32& src, uint32_t x, uint32_t y) const;

    /**
     * @brief 获取安全的像素值（处理边界）
     * @param src 源图像
     * @param x X坐标
     * @param y Y坐标
     * @return 像素值
     */
    uint32_t getSafePixelValue(const ImageDataU32& src, int x, int y) const;

    /**
     * @brief 归一化权重矩阵
     */
    void normalizeWeights();

    /**
     * @brief 创建预定义的权重矩阵
     * @param type 权重类型
     * @return 权重矩阵
     */
    QVector<QVector<float>> createPredefinedWeights(const QString& type) const;

    /**
     * @brief 验证加权均值参数
     * @param params 参数对象
     * @throws InvalidParameterException 如果参数无效
     */
    void validateWeightedAverageParams(const WeightedAverageParams& params) const;

    /**
     * @brief 记录调试信息
     * @param message 调试信息
     */
    void logDebug(const QString& message) const;
};

} // namespace ImageProcessing

#endif // IMAGEPROCESSING_WEIGHTEDAVERAGEFILTER_H
