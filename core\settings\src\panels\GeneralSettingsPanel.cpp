#include "LA/Settings/panels/GeneralSettingsPanel.h"
#include "LA/Themes/ThemeManager.h"
#include <QComboBox>
#include <QFileDialog>
#include <QDateTime>
#include <QDebug>
#include <QDir>
#include <QFile>
#include <QGridLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QMessageBox>
#include <QStandardPaths>
#include <QTextStream>
#include <QVBoxLayout>

namespace LA {
namespace Settings {

GeneralSettingsPanel::GeneralSettingsPanel(QWidget *parent)
    : SettingsPanel("general", tr("通用设置"), tr("配置应用程序的基本设置，包括语言、启动选项等"), ":/icons/settings/general.png", tr("通用"), 10, parent),
      m_languageGroup(nullptr),
      m_languageComboBox(nullptr),
      m_languageDescriptionLabel(nullptr),
      m_startupGroup(nullptr),
      m_startWithSystemCheckBox(nullptr),
      m_restoreLastSessionCheckBox(nullptr),
      m_showSplashScreenCheckBox(nullptr),
      m_checkUpdatesCheckBox(nullptr),
      m_autoSaveGroup(nullptr),
      m_autoSaveEnabledCheckBox(nullptr),
      m_autoSaveIntervalSpinBox(nullptr),
      m_autoSaveIntervalLabel(nullptr),
      m_workspaceGroup(nullptr),
      m_workspacePathLineEdit(nullptr),
      m_browseWorkspaceButton(nullptr),
      m_workspaceDescriptionLabel(nullptr),
      m_buttonLayout(nullptr),
      m_resetToDefaultsButton(nullptr),
      m_applyButton(nullptr),
      m_currentLanguage("zh_CN"),
      m_startWithSystem(false),
      m_restoreLastSession(true),
      m_showSplashScreen(true),
      m_checkUpdates(true),
      m_autoSaveEnabled(true),
      m_autoSaveInterval(5) {
    // 设置默认工作空间路径
    m_workspacePath = QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation) + "/LA_Workspace";

    // 创建UI
    createUI();

    // 现在可以安全地加载设置了，因为派生类已经完全构造
    loadSettings();
}

void GeneralSettingsPanel::createUI() {
    qDebug() << "GeneralSettingsPanel::createUI() 开始";
    
    // 确保GeneralSettingsPanel能够填充整个父容器
    setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
    qDebug() << "GeneralSettingsPanel: 设置了Expanding尺寸策略";
    qDebug() << "GeneralSettingsPanel: 当前尺寸:" << size();

    // 获取基类已经创建的内容布局
    QVBoxLayout *contentLayout = getContentLayout();
    if (!contentLayout) {
        qWarning() << "GeneralSettingsPanel: 无法获取内容布局";
        return;
    }

    // 设置语言
    qDebug() << "GeneralSettingsPanel: 创建语言设置组";
    setupLanguageSettings();
    if (m_languageGroup) {
        contentLayout->addWidget(m_languageGroup);
        qDebug() << "GeneralSettingsPanel: 语言设置组已添加";
    }

    // 启动设置
    qDebug() << "GeneralSettingsPanel: 创建启动设置组";
    setupStartupSettings();
    if (m_startupGroup) {
        contentLayout->addWidget(m_startupGroup);
        qDebug() << "GeneralSettingsPanel: 启动设置组已添加";
    }

    // 自动保存设置
    qDebug() << "GeneralSettingsPanel: 创建自动保存设置组";
    setupAutoSaveSettings();
    if (m_autoSaveGroup) {
        contentLayout->addWidget(m_autoSaveGroup);
        qDebug() << "GeneralSettingsPanel: 自动保存设置组已添加";
    }

    // 工作空间设置
    qDebug() << "GeneralSettingsPanel: 创建工作空间设置组";
    setupWorkspaceSettings();
    if (m_workspaceGroup) {
        contentLayout->addWidget(m_workspaceGroup);
        qDebug() << "GeneralSettingsPanel: 工作空间设置组已添加";
    }

    // 操作按钮已在SettingsDialog底部统一管理，无需在面板内部重复添加

    // 添加弹性空间
    contentLayout->addStretch();
    qDebug() << "GeneralSettingsPanel::createUI() 完成";
}

void GeneralSettingsPanel::setupLanguageSettings() {
    m_languageGroup     = new QGroupBox(tr("语言设置"), this);
    // 屏蔽子容器色块，专注排查SettingsPanel容器问题
    // m_languageGroup->setStyleSheet("QGroupBox { background-color: yellow; border: 2px solid orange; }");
    // 设置正确的尺寸策略，让其扩展填充可用空间
    m_languageGroup->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
    // 设置最小尺寸确保QGroupBox能够正确显示和扩展
    m_languageGroup->setMinimumSize(500, 100);
    m_languageGroup->resize(600, 150);
    
    QVBoxLayout *layout = new QVBoxLayout(m_languageGroup);

    // 语言选择
    QHBoxLayout *languageLayout = new QHBoxLayout();
    languageLayout->addWidget(new QLabel(tr("界面语言:"), this));

    m_languageComboBox = new QComboBox(this);
    loadAvailableLanguages();
    languageLayout->addWidget(m_languageComboBox);
    languageLayout->addStretch();

    layout->addLayout(languageLayout);

    // 语言描述
    m_languageDescriptionLabel = new QLabel(tr("更改语言设置需要重启应用程序才能生效"), this);
    // 使用主题系统替换硬编码样式
    auto themeManager = &LA::Themes::ThemeManager::instance();
    QColor mutedColor = themeManager->getSemanticColor(LA::Themes::ThemeManager::ColorRole::TextMuted);
    QFont captionFont = themeManager->getFont(LA::Themes::ThemeManager::FontRole::Caption);
    
    m_languageDescriptionLabel->setStyleSheet(QString("color: %1;").arg(mutedColor.name()));
    m_languageDescriptionLabel->setFont(captionFont);
    layout->addWidget(m_languageDescriptionLabel);
}

void GeneralSettingsPanel::setupStartupSettings() {
    m_startupGroup      = new QGroupBox(tr("启动设置"), this);
    // 调试：为QGroupBox添加黄色背景
    // 屏蔽子容器色块，专注排查SettingsPanel容器问题
    // m_startupGroup->setStyleSheet("QGroupBox { background-color: yellow; border: 2px solid orange; }");
    // 设置正确的尺寸策略，让其扩展填充可用空间
    m_startupGroup->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
    // 设置最小尺寸确保QGroupBox能够正确显示和扩展
    m_startupGroup->setMinimumSize(500, 100);
    m_startupGroup->resize(600, 150);
    
    QVBoxLayout *layout = new QVBoxLayout(m_startupGroup);

    m_startWithSystemCheckBox = new QCheckBox(tr("开机自动启动"), this);
    layout->addWidget(m_startWithSystemCheckBox);

    m_restoreLastSessionCheckBox = new QCheckBox(tr("启动时恢复上次会话"), this);
    layout->addWidget(m_restoreLastSessionCheckBox);

    m_showSplashScreenCheckBox = new QCheckBox(tr("显示启动画面"), this);
    layout->addWidget(m_showSplashScreenCheckBox);

    m_checkUpdatesCheckBox = new QCheckBox(tr("启动时检查更新"), this);
    layout->addWidget(m_checkUpdatesCheckBox);
}

void GeneralSettingsPanel::setupAutoSaveSettings() {
    m_autoSaveGroup     = new QGroupBox(tr("自动保存"), this);
    // 调试：为QGroupBox添加黄色背景
    // 屏蔽子容器色块，专注排查SettingsPanel容器问题
    // m_autoSaveGroup->setStyleSheet("QGroupBox { background-color: yellow; border: 2px solid orange; }");
    // 设置正确的尺寸策略，让其扩展填充可用空间
    m_autoSaveGroup->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
    // 设置最小尺寸确保QGroupBox能够正确显示和扩展
    m_autoSaveGroup->setMinimumSize(500, 100);
    m_autoSaveGroup->resize(600, 150);
    
    QVBoxLayout *layout = new QVBoxLayout(m_autoSaveGroup);

    m_autoSaveEnabledCheckBox = new QCheckBox(tr("启用自动保存"), this);
    layout->addWidget(m_autoSaveEnabledCheckBox);

    QHBoxLayout *intervalLayout = new QHBoxLayout();
    intervalLayout->addWidget(new QLabel(tr("自动保存间隔:"), this));

    m_autoSaveIntervalSpinBox = new QSpinBox(this);
    m_autoSaveIntervalSpinBox->setRange(1, 60);
    m_autoSaveIntervalSpinBox->setSuffix(tr(" 分钟"));
    intervalLayout->addWidget(m_autoSaveIntervalSpinBox);

    m_autoSaveIntervalLabel = new QLabel(this);
    intervalLayout->addWidget(m_autoSaveIntervalLabel);
    intervalLayout->addStretch();

    layout->addLayout(intervalLayout);
}

void GeneralSettingsPanel::setupWorkspaceSettings() {
    m_workspaceGroup    = new QGroupBox(tr("工作空间"), this);
    QVBoxLayout *layout = new QVBoxLayout(m_workspaceGroup);

    QHBoxLayout *pathLayout = new QHBoxLayout();
    pathLayout->addWidget(new QLabel(tr("默认工作空间:"), this));

    m_workspacePathLineEdit = new QLineEdit(this);
    pathLayout->addWidget(m_workspacePathLineEdit);

    m_browseWorkspaceButton = new QPushButton(tr("浏览..."), this);
    pathLayout->addWidget(m_browseWorkspaceButton);

    layout->addLayout(pathLayout);

    m_workspaceDescriptionLabel = new QLabel(tr("工作空间用于存储项目文件和配置"), this);
    // 使用主题系统替换硬编码样式
    auto* themeManager = &LA::Themes::ThemeManager::instance();
    QColor mutedColor = themeManager->getSemanticColor(LA::Themes::ThemeManager::ColorRole::TextMuted);
    QFont captionFont = themeManager->getFont(LA::Themes::ThemeManager::FontRole::Caption);
    
    m_workspaceDescriptionLabel->setStyleSheet(QString("color: %1;").arg(mutedColor.name()));
    m_workspaceDescriptionLabel->setFont(captionFont);
    layout->addWidget(m_workspaceDescriptionLabel);
}

void GeneralSettingsPanel::setupActionButtons() {
    m_buttonLayout = new QHBoxLayout();

    m_resetToDefaultsButton = new QPushButton(tr("恢复默认"), this);
    m_buttonLayout->addWidget(m_resetToDefaultsButton);

    m_buttonLayout->addStretch();

    m_applyButton = new QPushButton(tr("应用"), this);
    m_applyButton->setDefault(true);
    m_buttonLayout->addWidget(m_applyButton);
}

void GeneralSettingsPanel::connectSignals() {
    connect(m_languageComboBox, QOverload<const QString &>::of(&QComboBox::currentTextChanged), this, &GeneralSettingsPanel::onLanguageChanged);

    connect(m_startWithSystemCheckBox, &QCheckBox::toggled, this, &GeneralSettingsPanel::onStartupBehaviorChanged);
    connect(m_restoreLastSessionCheckBox, &QCheckBox::toggled, this, &GeneralSettingsPanel::onStartupBehaviorChanged);
    connect(m_showSplashScreenCheckBox, &QCheckBox::toggled, this, &GeneralSettingsPanel::onStartupBehaviorChanged);
    connect(m_checkUpdatesCheckBox, &QCheckBox::toggled, this, &GeneralSettingsPanel::onStartupBehaviorChanged);

    connect(m_autoSaveEnabledCheckBox, &QCheckBox::toggled, this, &GeneralSettingsPanel::onAutoSaveChanged);
    connect(m_autoSaveIntervalSpinBox, QOverload<int>::of(&QSpinBox::valueChanged), this, &GeneralSettingsPanel::onAutoSaveIntervalChanged);

    connect(m_workspacePathLineEdit, &QLineEdit::textChanged, this, &GeneralSettingsPanel::onWorkspacePathChanged);
    connect(m_browseWorkspaceButton, &QPushButton::clicked, this, &GeneralSettingsPanel::onBrowseWorkspacePath);

    // 操作按钮的信号连接已移至SettingsDialog统一管理
    // connect(m_resetToDefaultsButton, &QPushButton::clicked, this, &GeneralSettingsPanel::onResetToDefaults);
    // connect(m_applyButton, &QPushButton::clicked, this, &GeneralSettingsPanel::applySettings);
}

void GeneralSettingsPanel::loadAvailableLanguages() {
    m_languageComboBox->clear();
    m_languageComboBox->addItem(tr("简体中文"), "zh_CN");
    m_languageComboBox->addItem(tr("English"), "en_US");
    m_languageComboBox->addItem(tr("繁體中文"), "zh_TW");
    m_languageComboBox->addItem(tr("日本語"), "ja_JP");
}

void GeneralSettingsPanel::loadSpecificSettings() {
    auto settings = getSettings();
    if (!settings)
        return;

    // 加载语言设置
    m_currentLanguage = settings->value("language", "zh_CN").toString();
    int languageIndex = m_languageComboBox->findData(m_currentLanguage);
    if (languageIndex >= 0) {
        m_languageComboBox->setCurrentIndex(languageIndex);
    }

    // 加载启动设置
    m_startWithSystem    = settings->value("startWithSystem", false).toBool();
    m_restoreLastSession = settings->value("restoreLastSession", true).toBool();
    m_showSplashScreen   = settings->value("showSplashScreen", true).toBool();
    m_checkUpdates       = settings->value("checkUpdates", true).toBool();

    m_startWithSystemCheckBox->setChecked(m_startWithSystem);
    m_restoreLastSessionCheckBox->setChecked(m_restoreLastSession);
    m_showSplashScreenCheckBox->setChecked(m_showSplashScreen);
    m_checkUpdatesCheckBox->setChecked(m_checkUpdates);

    // 加载自动保存设置
    m_autoSaveEnabled  = settings->value("autoSaveEnabled", true).toBool();
    m_autoSaveInterval = settings->value("autoSaveInterval", 5).toInt();

    m_autoSaveEnabledCheckBox->setChecked(m_autoSaveEnabled);
    m_autoSaveIntervalSpinBox->setValue(m_autoSaveInterval);
    m_autoSaveIntervalSpinBox->setEnabled(m_autoSaveEnabled);

    // 加载工作空间设置
    QString defaultWorkspace = QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation) + "/LA_Workspace";
    m_workspacePath          = settings->value("workspacePath", defaultWorkspace).toString();
    m_workspacePathLineEdit->setText(m_workspacePath);
}

void GeneralSettingsPanel::saveSpecificSettings() {
    auto settings = getSettings();
    if (!settings)
        return;

    settings->setValue("language", m_currentLanguage);
    settings->setValue("startWithSystem", m_startWithSystem);
    settings->setValue("restoreLastSession", m_restoreLastSession);
    settings->setValue("showSplashScreen", m_showSplashScreen);
    settings->setValue("checkUpdates", m_checkUpdates);
    settings->setValue("autoSaveEnabled", m_autoSaveEnabled);
    settings->setValue("autoSaveInterval", m_autoSaveInterval);
    settings->setValue("workspacePath", m_workspacePath);
}

void GeneralSettingsPanel::resetSpecificSettings() {
    m_currentLanguage    = "zh_CN";
    m_startWithSystem    = false;
    m_restoreLastSession = true;
    m_showSplashScreen   = true;
    m_checkUpdates       = true;
    m_autoSaveEnabled    = true;
    m_autoSaveInterval   = 5;
    m_workspacePath      = QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation) + "/LA_Workspace";

    // 更新UI
    int languageIndex = m_languageComboBox->findData(m_currentLanguage);
    if (languageIndex >= 0) {
        m_languageComboBox->setCurrentIndex(languageIndex);
    }

    m_startWithSystemCheckBox->setChecked(m_startWithSystem);
    m_restoreLastSessionCheckBox->setChecked(m_restoreLastSession);
    m_showSplashScreenCheckBox->setChecked(m_showSplashScreen);
    m_checkUpdatesCheckBox->setChecked(m_checkUpdates);
    m_autoSaveEnabledCheckBox->setChecked(m_autoSaveEnabled);
    m_autoSaveIntervalSpinBox->setValue(m_autoSaveInterval);
    m_workspacePathLineEdit->setText(m_workspacePath);
}

bool GeneralSettingsPanel::validateSpecificSettings() {
    // 验证工作空间路径
    return validateWorkspacePath();
}

void GeneralSettingsPanel::applySpecificSettings() {
    // 应用语言设置（需要重启）
    if (m_currentLanguage != getSettings()->value("language", "zh_CN").toString()) {
        QMessageBox::information(this, tr("语言设置"), tr("语言设置将在下次启动时生效"));
    }

    // 创建工作空间目录
    QDir dir;
    if (!dir.exists(m_workspacePath)) {
        dir.mkpath(m_workspacePath);
    }
}

void GeneralSettingsPanel::onLanguageChanged(const QString &/*language*/) {
    QString languageCode = m_languageComboBox->currentData().toString();
    if (m_currentLanguage != languageCode) {
        m_currentLanguage = languageCode;
        markAsModified();
    }
}

void GeneralSettingsPanel::onStartupBehaviorChanged() {
    m_startWithSystem    = m_startWithSystemCheckBox->isChecked();
    m_restoreLastSession = m_restoreLastSessionCheckBox->isChecked();
    m_showSplashScreen   = m_showSplashScreenCheckBox->isChecked();
    m_checkUpdates       = m_checkUpdatesCheckBox->isChecked();
    markAsModified();
}

void GeneralSettingsPanel::onAutoSaveChanged(bool enabled) {
    m_autoSaveEnabled = enabled;
    m_autoSaveIntervalSpinBox->setEnabled(enabled);
    markAsModified();
}

void GeneralSettingsPanel::onAutoSaveIntervalChanged(int minutes) {
    m_autoSaveInterval = minutes;
    markAsModified();
}

void GeneralSettingsPanel::onWorkspacePathChanged() {
    QString newPath = m_workspacePathLineEdit->text();
    if (m_workspacePath != newPath) {
        m_workspacePath = newPath;
        markAsModified();
    }
}

void GeneralSettingsPanel::onBrowseWorkspacePath() {
    QString dir = QFileDialog::getExistingDirectory(this, tr("选择工作空间目录"), m_workspacePath);
    if (!dir.isEmpty()) {
        m_workspacePathLineEdit->setText(dir);
    }
}

void GeneralSettingsPanel::onResetToDefaults() {
    int ret = QMessageBox::question(this, tr("恢复默认设置"), tr("确定要恢复所有设置为默认值吗？"), QMessageBox::Yes | QMessageBox::No);
    if (ret == QMessageBox::Yes) {
        resetSpecificSettings();
    }
}

bool GeneralSettingsPanel::validateWorkspacePath() {
    QString path = m_workspacePathLineEdit->text();
    QDir    dir(path);

    if (path.isEmpty()) {
        m_workspaceDescriptionLabel->setText(tr("工作空间路径不能为空"));
        m_workspaceDescriptionLabel->setStyleSheet("color: red; font-size: 12px;");
        return false;
    }

    if (!dir.exists() && !dir.mkpath(path)) {
        m_workspaceDescriptionLabel->setText(tr("无法创建工作空间目录"));
        // 使用主题系统的错误颜色
        auto* themeManager = &LA::Themes::ThemeManager::instance();
        QColor errorColor = themeManager->getSemanticColor(LA::Themes::ThemeManager::ColorRole::Error);
        QFont captionFont = themeManager->getFont(LA::Themes::ThemeManager::FontRole::Caption);
        
        m_workspaceDescriptionLabel->setStyleSheet(QString("color: %1;").arg(errorColor.name()));
        m_workspaceDescriptionLabel->setFont(captionFont);
        return false;
    }

    m_workspaceDescriptionLabel->setText(tr("工作空间用于存储项目文件和配置"));
    // 使用主题系统的正常颜色
    auto* themeManager = &LA::Themes::ThemeManager::instance();
    QColor mutedColor = themeManager->getSemanticColor(LA::Themes::ThemeManager::ColorRole::TextMuted);
    QFont captionFont = themeManager->getFont(LA::Themes::ThemeManager::FontRole::Caption);
    
    m_workspaceDescriptionLabel->setStyleSheet(QString("color: %1;").arg(mutedColor.name()));
    m_workspaceDescriptionLabel->setFont(captionFont);
    return true;
}

}  // namespace Settings
}  // namespace LA

// MOC文件由CMake AutoMoc自动处理，无需手动包含
