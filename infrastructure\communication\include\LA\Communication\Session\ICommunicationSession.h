#pragma once

/**
 * @file ICommunicationSession.h
 * @brief Linus式通信会话接口定义 - Layer 3 最小接口设计
 * 
 * 遵循Linus Torvalds的设计哲学：
 * - "Good programmers worry about data structures" - 基于CommonTypes.h的数据结构
 * - 最小接口原则 - 只负责会话管理，组合Layer 1和Layer 2
 * - 单一职责 - 纯粹的端到端通信会话
 * - Layer 3接口 - 依赖Layer 1-2，为Layer 4提供会话抽象
 */

#include <QObject>
#include <QString>
#include <QByteArray>
#include <memory>
#include "../../../../../../support/foundation/core/CommonTypes.h"

// 前向声明Layer 1和Layer 2组件
namespace LA {
namespace Communication {
namespace Connection { class IConnection; }
namespace Protocol { class IProtocol; }
namespace Command { class ICommandHandler; }
}
}

namespace LA {
namespace Communication {
namespace Session {

// 使用Foundation层的标准类型
using namespace LA::Foundation::Core;

/**
 * @brief 纯粹的通信会话接口
 * 
 * Linus式设计原则:
 * - 最小接口: 只提供必需的端到端通信功能
 * - 组合设计: 组合连接、协议、命令处理器
 * - 可测试性: 每个方法都可以独立测试
 * - 可替换性: 各组件可以独立替换
 */
class ICommunicationSession : public QObject {
    Q_OBJECT

public:
    explicit ICommunicationSession(QObject* parent = nullptr) : QObject(parent) {}
    virtual ~ICommunicationSession() = default;

    // === 会话管理 ===
    
    /**
     * @brief 打开通信会话
     * @param config 会话配置参数
     * @return 操作结果
     */
    virtual SimpleResult openSession(const ConfigParameters& config) = 0;
    
    /**
     * @brief 关闭通信会话
     * @return 操作结果
     */
    virtual SimpleResult closeSession() = 0;
    
    /**
     * @brief 检查会话是否已打开
     * @return 是否已打开
     */
    virtual bool isSessionOpen() const = 0;
    
    /**
     * @brief 获取会话状态
     * @return 连接状态
     */
    virtual ConnectionStatus getSessionStatus() const = 0;

    // === 数据通信 ===
    
    /**
     * @brief 发送原始数据
     * @param data 要发送的数据
     * @return 发送的字节数，-1表示失败
     */
    virtual qint64 sendRawData(const QByteArray& data) = 0;
    
    /**
     * @brief 接收原始数据
     * @param maxBytes 最大字节数，-1表示读取所有
     * @return 接收到的数据
     */
    virtual QByteArray receiveRawData(qint64 maxBytes = -1) = 0;
    
    /**
     * @brief 发送协议消息
     * @param message 消息数据映射
     * @return 操作结果
     */
    virtual SimpleResult sendMessage(const QVariantMap& message) = 0;
    
    /**
     * @brief 接收协议消息
     * @return 接收到的消息，失败返回空映射
     */
    virtual QVariantMap receiveMessage() = 0;

    // === 命令处理 ===
    
    /**
     * @brief 执行命令
     * @param command 命令数据映射
     * @return 命令执行结果
     */
    virtual CommandResult executeCommand(const QVariantMap& command) = 0;
    
    /**
     * @brief 发送命令并等待响应
     * @param command 命令数据映射
     * @param timeout 超时时间(ms)
     * @return 命令执行结果
     */
    virtual CommandResult sendCommandAndWait(const QVariantMap& command, int timeout = 5000) = 0;

    // === 组件管理 ===
    
    /**
     * @brief 设置连接组件
     * @param connection 连接组件智能指针
     * @return 操作结果
     */
    virtual SimpleResult setConnection(std::shared_ptr<Connection::IConnection> connection) = 0;
    
    /**
     * @brief 设置协议组件
     * @param protocol 协议组件智能指针
     * @return 操作结果
     */
    virtual SimpleResult setProtocol(std::shared_ptr<Protocol::IProtocol> protocol) = 0;
    
    /**
     * @brief 设置命令处理器
     * @param handler 命令处理器智能指针
     * @return 操作结果
     */
    virtual SimpleResult setCommandHandler(std::shared_ptr<Command::ICommandHandler> handler) = 0;

    // === 状态查询 ===
    
    /**
     * @brief 获取统计信息
     * @return 设备统计信息
     */
    virtual DeviceStatistics getStatistics() const = 0;
    
    /**
     * @brief 获取最后错误信息
     * @return 错误描述
     */
    virtual QString errorString() const = 0;
    
    /**
     * @brief 重置统计信息
     */
    virtual void resetStatistics() = 0;
    
    /**
     * @brief 获取会话配置
     * @return 配置参数
     */
    virtual ConfigParameters getSessionConfig() const = 0;

signals:
    /**
     * @brief 会话状态变化信号
     * @param status 新状态
     */
    void sessionStatusChanged(ConnectionStatus status);
    
    /**
     * @brief 数据接收信号
     * @param data 接收到的原始数据
     */
    void dataReceived(const QByteArray& data);
    
    /**
     * @brief 消息接收信号
     * @param message 接收到的协议消息
     */
    void messageReceived(const QVariantMap& message);
    
    /**
     * @brief 命令执行完成信号
     * @param command 原始命令
     * @param result 执行结果
     */
    void commandExecuted(const QVariantMap& command, const CommandResult& result);
    
    /**
     * @brief 会话错误信号
     * @param error 错误描述
     */
    void sessionError(const QString& error);
};

/**
 * @brief 通信会话工厂接口
 * 
 * 用于创建不同类型的通信会话
 */
class ICommunicationSessionFactory {
public:
    virtual ~ICommunicationSessionFactory() = default;
    
    /**
     * @brief 创建通信会话实例
     * @param type 会话类型
     * @return 通信会话实例的智能指针
     */
    virtual std::shared_ptr<ICommunicationSession> createSession(const QString& type) = 0;
    
    /**
     * @brief 检查是否支持指定会话类型
     * @param type 会话类型
     * @return 是否支持
     */
    virtual bool supportsSessionType(const QString& type) const = 0;
    
    /**
     * @brief 获取支持的会话类型列表
     * @return 支持的类型列表
     */
    virtual QStringList getSupportedSessionTypes() const = 0;
};

} // namespace Session
} // namespace Communication
} // namespace LA