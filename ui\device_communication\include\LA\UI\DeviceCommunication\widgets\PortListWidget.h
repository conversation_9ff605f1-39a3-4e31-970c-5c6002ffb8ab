#pragma once

/**
 * @file PortListWidget.h  
 * @brief 端口列表UI组件 (移动自infrastructure/communication)
 * 
 * Linus重构：将UI组件从infrastructure移动到专门的UI库
 * - 保持原有功能不变
 * - 清理命名空间，适配新的架构
 * - 移除对infrastructure内部UI依赖的引用
 */

#include <LA/Communication/PortManagement/IPortManager.h>
#include <QAction>
#include <QComboBox>
#include <QContextMenuEvent>
#include <QGroupBox>
#include <QHBoxLayout>
#include <QLabel>
#include <QLineEdit>
#include <QMenu>
#include <QProgressBar>
#include <QPushButton>
#include <QTimer>
#include <QTreeWidget>
#include <QTreeWidgetItem>
#include <QVBoxLayout>
#include <QWidget>
#include <memory>

namespace LA {
namespace UI {
namespace DeviceCommunication {

/**
 * @brief 端口列表管理组件
 * 
 * 从infrastructure/communication/UI/PortWidget移动而来
 * 专注于端口列表的显示和基本操作
 */
class PortListWidget : public QWidget {
    Q_OBJECT

public:
    explicit PortListWidget(QWidget *parent = nullptr);
    virtual ~PortListWidget();

    // 核心接口
    void setPortManager(std::shared_ptr<LA::Communication::PortManagement::IPortManager> portManager);
    std::shared_ptr<LA::Communication::PortManagement::IPortManager> getPortManager() const;
    
    void refreshPortList();
    QString getCurrentPortId() const;
    void selectPort(const QString &portId);
    
    // 配置选项
    void setShowDetails(bool show);
    void setAutoRefresh(bool autoRefresh, int intervalMs = 5000);

signals:
    void portSelectionChanged(const QString &portId);
    void portConnectRequested(const QString &portId);
    void portDisconnectRequested(const QString &portId);
    void portConfigRequested(const QString &portId);
    void portDeleteRequested(const QString &portId);

protected:
    void contextMenuEvent(QContextMenuEvent *event) override;

private slots:
    // UI事件处理
    void handlePortSelectionChanged();
    void handleRefreshClicked();
    void handleConnectClicked();
    void handleDisconnectClicked();
    void handleConfigClicked();
    void handleDeleteClicked();
    void handlePortTypeFilterChanged();
    void handleAutoRefreshTimer();
    
    // 端口管理器事件
    void handlePortDiscovered(const LA::Communication::PortManagement::PortInfo &portInfo);
    void handlePortRemoved(const QString &portId);
    void handlePortStatusChanged(const QString &portId, LA::Communication::PortManagement::PortStatus status);
    void handlePortError(const QString &portId, const QString &error);

private:
    // UI初始化
    void initializeUI();
    void initializeToolbar();
    void initializePortList();
    void initializeButtons();
    void initializeDetailsPanel();
    void initializeContextMenu();
    void connectSignals();
    
    // 端口列表管理
    void updatePortItem(const LA::Communication::PortManagement::PortInfo &portInfo);
    void removePortItem(const QString &portId);
    QTreeWidgetItem *findPortItem(const QString &portId) const;
    QTreeWidgetItem *createPortItem(const LA::Communication::PortManagement::PortInfo &portInfo);
    
    // UI状态更新
    void updatePortDetails(const QString &portId);
    void updateButtonStates();
    void updateStatusBar();
    void applyFilters();
    
    // 辅助方法
    QString getPortStatusIcon(LA::Communication::PortManagement::PortStatus status) const;
    QString getPortTypeIcon(LA::Communication::PortManagement::PortType type) const;
    QString formatPortStatistics(const LA::Communication::PortManagement::PortStatistics &stats) const;

private:
    // UI组件
    QVBoxLayout *m_mainLayout;
    QVBoxLayout *m_toolbarLayout;
    QHBoxLayout *m_buttonLayout;
    
    // 工具栏
    QWidget *     m_toolbarGroup;
    QPushButton * m_refreshButton;
    QComboBox *   m_typeFilterCombo;
    QLabel *      m_statusLabel;
    QProgressBar *m_progressBar;
    
    // 端口列表
    QTreeWidget *m_portTree;
    
    // 操作按钮
    QWidget *    m_buttonGroup;
    QPushButton *m_connectButton;
    QPushButton *m_disconnectButton;
    QPushButton *m_configButton;
    QPushButton *m_deleteButton;
    
    // 详细信息面板
    QWidget *m_detailsGroup;
    QLabel * m_portIdLabel;
    QLabel * m_portTypeLabel;
    QLabel * m_portStatusLabel;
    QLabel * m_portDescLabel;
    QLabel * m_portStatsLabel;
    
    // 右键菜单
    QMenu *  m_contextMenu;
    QAction *m_connectAction;
    QAction *m_disconnectAction;
    QAction *m_configAction;
    QAction *m_deleteAction;
    QAction *m_refreshAction;
    QAction *m_propertiesAction;
    
    // 定时器和状态
    QTimer *m_autoRefreshTimer;
    std::shared_ptr<LA::Communication::PortManagement::IPortManager> m_portManager;
    bool m_showDetails;
    bool m_autoRefresh;
};

} // namespace DeviceCommunication
} // namespace UI
} // namespace LA