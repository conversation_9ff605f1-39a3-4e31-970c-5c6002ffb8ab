#include "JsonConfigFileHandler.h"
#include <QFile>
#include <QFileInfo>
#include <QDir>
#include <QTextStream>
#include <QJsonParseError>
#include <QRegularExpression>
#include <QCryptographicHash>
#include <QDebug>

namespace LA {
namespace Support {
namespace Config {

JsonConfigFileHandler::JsonConfigFileHandler(QObject* parent)
    : QObject(parent)
{
}

JsonConfigFileHandler::~JsonConfigFileHandler()
{
}

Result<QVariantMap> JsonConfigFileHandler::loadJsonFile(const QString& filePath, 
                                                       const ConfigFileOptions& options)
{
    QFile file(filePath);
    if (!file.exists()) {
        return Result<QVariantMap>::error("File not found: " + filePath);
    }

    if (!file.open(QIODevice::ReadOnly)) {
        return Result<QVariantMap>::error("Cannot open file for reading: " + filePath);
    }

    QByteArray content = file.readAll();
    file.close();

    return parseJsonContent(content, options);
}

SimpleResult JsonConfigFileHandler::saveJsonFile(const QString& filePath, 
                                                const QVariantMap& data,
                                                const ConfigFileOptions& options)
{
    // 确保目录存在
    QFileInfo fileInfo(filePath);
    QDir dir = fileInfo.absoluteDir();
    if (!dir.exists() && !dir.mkpath(".")) {
        return SimpleResult::error("Cannot create directory: " + dir.absolutePath());
    }

    // 生成内容
    auto contentResult = serializeJsonContent(data, options);
    if (!contentResult.isSuccess()) {
        return SimpleResult::error("Failed to serialize JSON content: " + contentResult.message());
    }

    // 写入文件
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly)) {
        return SimpleResult::error("Cannot open file for writing: " + filePath);
    }

    qint64 bytesWritten = file.write(contentResult.value());
    file.close();

    if (bytesWritten != contentResult.value().size()) {
        return SimpleResult::error("Failed to write complete content to file: " + filePath);
    }

    return SimpleResult(true);
}

Result<QVariantMap> JsonConfigFileHandler::parseJsonContent(const QByteArray& content, 
                                                           const ConfigFileOptions& options)
{
    Q_UNUSED(options)

    QJsonParseError parseError;
    QJsonDocument doc = QJsonDocument::fromJson(content, &parseError);

    if (parseError.error != QJsonParseError::NoError) {
        return Result<QVariantMap>::error(
            QString("JSON parse error at offset %1: %2")
                .arg(parseError.offset)
                .arg(parseError.errorString())
        );
    }

    if (!doc.isObject()) {
        return Result<QVariantMap>::error("JSON document is not an object");
    }

    return Result<QVariantMap>(doc.object().toVariantMap());
}

ByteArrayResult JsonConfigFileHandler::serializeJsonContent(const QVariantMap& data, 
                                                           const ConfigFileOptions& options)
{
    if (options.prettyPrint) {
        if (options.customOptions.contains("enable_comments") && 
            options.customOptions["enable_comments"].toBool()) {
            // 生成带注释的JSON
            QString jsonWithComments = formatJsonWithComments(data, options);
            return ByteArrayResult(jsonWithComments.toUtf8());
        } else {
            // 标准美化JSON
            QJsonDocument doc(QJsonObject::fromVariantMap(data));
            return ByteArrayResult(doc.toJson(QJsonDocument::Indented));
        }
    } else {
        // 紧凑JSON
        QJsonDocument doc(QJsonObject::fromVariantMap(data));
        return ByteArrayResult(doc.toJson(QJsonDocument::Compact));
    }
}

SimpleResult JsonConfigFileHandler::validateJsonData(const QVariantMap& data, 
                                                    const QVariantMap& schema)
{
    if (schema.isEmpty()) {
        // 基本验证：检查是否为有效的QVariantMap
        QJsonDocument doc(QJsonObject::fromVariantMap(data));
        if (doc.isNull()) {
            return SimpleResult::error("Invalid data structure for JSON serialization");
        }
        return SimpleResult(true);
    }

    // Schema验证
    QJsonObject schemaObj = QJsonObject::fromVariantMap(schema);
    return validateJsonSchema(data, schemaObj);
}

SimpleResult JsonConfigFileHandler::validateJsonSchema(const QVariantMap& data, 
                                                      const QJsonObject& schema)
{
    // 基础Schema验证实现
    // 这里只实现简单的类型和必需字段验证
    
    // 检查类型
    if (schema.contains("type")) {
        QString expectedType = schema["type"].toString();
        if (expectedType == "object") {
            // 当前数据是对象，符合要求
        } else {
            return SimpleResult::error("Schema type mismatch: expected " + expectedType);
        }
    }

    // 检查必需字段
    if (schema.contains("required")) {
        QJsonArray requiredFields = schema["required"].toArray();
        for (const auto& fieldValue : requiredFields) {
            QString fieldName = fieldValue.toString();
            if (!data.contains(fieldName)) {
                return SimpleResult::error("Required field missing: " + fieldName);
            }
        }
    }

    // 检查属性
    if (schema.contains("properties")) {
        QJsonObject properties = schema["properties"].toObject();
        for (auto it = properties.begin(); it != properties.end(); ++it) {
            QString fieldName = it.key();
            QJsonObject fieldSchema = it.value().toObject();
            
            if (data.contains(fieldName)) {
                QVariant fieldValue = data[fieldName];
                
                // 检查字段类型
                if (fieldSchema.contains("type")) {
                    QString fieldType = fieldSchema["type"].toString();
                    if (!validateJsonType(fieldValue, fieldType)) {
                        return SimpleResult::error(
                            QString("Field '%1' type mismatch: expected %2")
                                .arg(fieldName, fieldType)
                        );
                    }
                }

                // 检查枚举值
                if (fieldSchema.contains("enum")) {
                    QJsonArray enumValues = fieldSchema["enum"].toArray();
                    if (!validateJsonEnum(fieldValue, enumValues)) {
                        return SimpleResult::error(
                            QString("Field '%1' value not in enum list").arg(fieldName)
                        );
                    }
                }

                // 检查数值范围
                if (fieldSchema.contains("minimum") || fieldSchema.contains("maximum")) {
                    QJsonValue minimum = fieldSchema["minimum"];
                    QJsonValue maximum = fieldSchema["maximum"];
                    if (!validateJsonRange(fieldValue, minimum, maximum)) {
                        return SimpleResult::error(
                            QString("Field '%1' value out of range").arg(fieldName)
                        );
                    }
                }

                // 检查字符串模式
                if (fieldSchema.contains("pattern") && fieldValue.type() == QVariant::String) {
                    QString pattern = fieldSchema["pattern"].toString();
                    if (!validateJsonPattern(fieldValue.toString(), pattern)) {
                        return SimpleResult::error(
                            QString("Field '%1' does not match pattern").arg(fieldName)
                        );
                    }
                }
            }
        }
    }

    return SimpleResult(true);
}

QString JsonConfigFileHandler::formatJsonWithComments(const QVariantMap& data, 
                                                     const ConfigFileOptions& options)
{
    QString result = "{\n";
    
    auto it = data.begin();
    while (it != data.end()) {
        QString key = it.key();
        QVariant value = it.value();
        
        // 生成缩进
        QString indent = generateIndent(1, options.indentSize);
        
        // 添加注释（如果存在）
        QString commentKey = key + "_comment";
        if (data.contains(commentKey)) {
            result += indent + "// " + data[commentKey].toString() + "\n";
        }
        
        // 添加键值对
        result += indent + "\"" + escapeJsonString(key) + "\": ";
        formatJsonValue(result, value, 1, options);
        
        ++it;
        if (it != data.end()) {
            result += ",";
        }
        result += "\n";
    }
    
    result += "}";
    return result;
}

QVariantMap JsonConfigFileHandler::mergeJsonObjects(const QVariantMap& base, 
                                                   const QVariantMap& overlay,
                                                   bool deepMerge)
{
    QVariantMap result = base;
    
    for (auto it = overlay.begin(); it != overlay.end(); ++it) {
        QString key = it.key();
        QVariant overlayValue = it.value();
        
        if (deepMerge && result.contains(key)) {
            QVariant baseValue = result[key];
            
            // 如果两个值都是对象，递归合并
            if (baseValue.type() == QVariant::Map && overlayValue.type() == QVariant::Map) {
                QVariantMap baseMap = baseValue.toMap();
                QVariantMap overlayMap = overlayValue.toMap();
                result[key] = mergeJsonObjects(baseMap, overlayMap, true);
            } else {
                result[key] = overlayValue; // 覆盖
            }
        } else {
            result[key] = overlayValue; // 添加或覆盖
        }
    }
    
    return result;
}

QVariantMap JsonConfigFileHandler::flattenJsonObject(const QVariantMap& data, 
                                                    const QString& prefix,
                                                    const QString& separator)
{
    QVariantMap result;
    
    for (auto it = data.begin(); it != data.end(); ++it) {
        QString key = it.key();
        QVariant value = it.value();
        QString fullKey = prefix.isEmpty() ? key : prefix + separator + key;
        
        if (value.type() == QVariant::Map) {
            // 递归展平嵌套对象
            QVariantMap nestedResult = flattenJsonObject(value.toMap(), fullKey, separator);
            for (auto nestedIt = nestedResult.begin(); nestedIt != nestedResult.end(); ++nestedIt) {
                result[nestedIt.key()] = nestedIt.value();
            }
        } else {
            result[fullKey] = value;
        }
    }
    
    return result;
}

QVariantMap JsonConfigFileHandler::unflattenJsonObject(const QVariantMap& flatData, 
                                                      const QString& separator)
{
    QVariantMap result;
    
    for (auto it = flatData.begin(); it != flatData.end(); ++it) {
        QString key = it.key();
        QVariant value = it.value();
        
        QStringList keyParts = key.split(separator);
        QVariantMap* currentMap = &result;
        
        for (int i = 0; i < keyParts.size() - 1; ++i) {
            QString part = keyParts[i];
            if (!currentMap->contains(part)) {
                (*currentMap)[part] = QVariantMap();
            }
            currentMap = reinterpret_cast<QVariantMap*>(&(*currentMap)[part]);
        }
        
        (*currentMap)[keyParts.last()] = value;
    }
    
    return result;
}

// 私有辅助方法实现
QString JsonConfigFileHandler::escapeJsonString(const QString& str)
{
    QString result = str;
    result.replace("\\", "\\\\");
    result.replace("\"", "\\\"");
    result.replace("\n", "\\n");
    result.replace("\r", "\\r");
    result.replace("\t", "\\t");
    return result;
}

QString JsonConfigFileHandler::generateIndent(int level, int indentSize)
{
    return QString(level * indentSize, ' ');
}

void JsonConfigFileHandler::formatJsonValue(QString& result, 
                                           const QVariant& value, 
                                           int indentLevel, 
                                           const ConfigFileOptions& options)
{
    switch (value.type()) {
    case QVariant::Bool:
        result += value.toBool() ? "true" : "false";
        break;
    case QVariant::Int:
    case QVariant::LongLong:
    case QVariant::UInt:
    case QVariant::ULongLong:
        result += QString::number(value.toLongLong());
        break;
    case QVariant::Double:
        result += QString::number(value.toDouble());
        break;
    case QVariant::String:
        result += "\"" + escapeJsonString(value.toString()) + "\"";
        break;
    case QVariant::Map:
        formatJsonObject(result, value.toMap(), indentLevel, options);
        break;
    case QVariant::List:
        formatJsonArray(result, value.toList(), indentLevel, options);
        break;
    default:
        result += "\"" + escapeJsonString(value.toString()) + "\"";
        break;
    }
}

void JsonConfigFileHandler::formatJsonObject(QString& result, 
                                            const QVariantMap& object, 
                                            int indentLevel, 
                                            const ConfigFileOptions& options)
{
    result += "{\n";
    
    auto it = object.begin();
    while (it != object.end()) {
        QString indent = generateIndent(indentLevel + 1, options.indentSize);
        result += indent + "\"" + escapeJsonString(it.key()) + "\": ";
        formatJsonValue(result, it.value(), indentLevel + 1, options);
        
        ++it;
        if (it != object.end()) {
            result += ",";
        }
        result += "\n";
    }
    
    result += generateIndent(indentLevel, options.indentSize) + "}";
}

void JsonConfigFileHandler::formatJsonArray(QString& result, 
                                           const QVariantList& array, 
                                           int indentLevel, 
                                           const ConfigFileOptions& options)
{
    result += "[\n";
    
    for (int i = 0; i < array.size(); ++i) {
        QString indent = generateIndent(indentLevel + 1, options.indentSize);
        result += indent;
        formatJsonValue(result, array[i], indentLevel + 1, options);
        
        if (i < array.size() - 1) {
            result += ",";
        }
        result += "\n";
    }
    
    result += generateIndent(indentLevel, options.indentSize) + "]";
}

bool JsonConfigFileHandler::validateJsonType(const QVariant& value, const QString& expectedType)
{
    if (expectedType == "string") {
        return value.type() == QVariant::String;
    } else if (expectedType == "number") {
        return value.type() == QVariant::Int || value.type() == QVariant::Double ||
               value.type() == QVariant::LongLong || value.type() == QVariant::UInt ||
               value.type() == QVariant::ULongLong;
    } else if (expectedType == "integer") {
        return value.type() == QVariant::Int || value.type() == QVariant::LongLong ||
               value.type() == QVariant::UInt || value.type() == QVariant::ULongLong;
    } else if (expectedType == "boolean") {
        return value.type() == QVariant::Bool;
    } else if (expectedType == "array") {
        return value.type() == QVariant::List;
    } else if (expectedType == "object") {
        return value.type() == QVariant::Map;
    }
    
    return false;
}

bool JsonConfigFileHandler::validateJsonEnum(const QVariant& value, const QJsonArray& enumValues)
{
    for (const auto& enumValue : enumValues) {
        if (value == enumValue.toVariant()) {
            return true;
        }
    }
    return false;
}

bool JsonConfigFileHandler::validateJsonRange(const QVariant& value, 
                                             const QJsonValue& minimum, 
                                             const QJsonValue& maximum)
{
    double numValue = value.toDouble();
    
    if (minimum.isDouble() && numValue < minimum.toDouble()) {
        return false;
    }
    
    if (maximum.isDouble() && numValue > maximum.toDouble()) {
        return false;
    }
    
    return true;
}

bool JsonConfigFileHandler::validateJsonPattern(const QString& value, const QString& pattern)
{
    QRegularExpression regex(pattern);
    return regex.match(value).hasMatch();
}

// 工具函数实现
QString configFileFormatToString(ConfigFileFormat format)
{
    switch (format) {
    case ConfigFileFormat::JSON: return "JSON";
    case ConfigFileFormat::INI: return "INI";
    case ConfigFileFormat::XML: return "XML";
    case ConfigFileFormat::YAML: return "YAML";
    case ConfigFileFormat::TOML: return "TOML";
    case ConfigFileFormat::PROPERTIES: return "PROPERTIES";
    case ConfigFileFormat::CUSTOM: return "CUSTOM";
    default: return "UNKNOWN";
    }
}

ConfigFileFormat stringToConfigFileFormat(const QString& formatStr)
{
    QString upper = formatStr.toUpper();
    if (upper == "JSON") return ConfigFileFormat::JSON;
    if (upper == "INI") return ConfigFileFormat::INI;
    if (upper == "XML") return ConfigFileFormat::XML;
    if (upper == "YAML" || upper == "YML") return ConfigFileFormat::YAML;
    if (upper == "TOML") return ConfigFileFormat::TOML;
    if (upper == "PROPERTIES") return ConfigFileFormat::PROPERTIES;
    if (upper == "CUSTOM") return ConfigFileFormat::CUSTOM;
    return ConfigFileFormat::UNKNOWN;
}

QString configFileEncodingToString(ConfigFileEncoding encoding)
{
    switch (encoding) {
    case ConfigFileEncoding::UTF8: return "UTF-8";
    case ConfigFileEncoding::UTF16: return "UTF-16";
    case ConfigFileEncoding::ASCII: return "ASCII";
    case ConfigFileEncoding::LATIN1: return "Latin-1";
    case ConfigFileEncoding::LOCAL: return "Local";
    default: return "Unknown";
    }
}

ConfigFileEncoding stringToConfigFileEncoding(const QString& encodingStr)
{
    QString upper = encodingStr.toUpper();
    if (upper == "UTF-8" || upper == "UTF8") return ConfigFileEncoding::UTF8;
    if (upper == "UTF-16" || upper == "UTF16") return ConfigFileEncoding::UTF16;
    if (upper == "ASCII") return ConfigFileEncoding::ASCII;
    if (upper == "LATIN-1" || upper == "LATIN1") return ConfigFileEncoding::LATIN1;
    if (upper == "LOCAL") return ConfigFileEncoding::LOCAL;
    return ConfigFileEncoding::UTF8; // 默认
}

}  // namespace Config
}  // namespace Support
}  // namespace LA