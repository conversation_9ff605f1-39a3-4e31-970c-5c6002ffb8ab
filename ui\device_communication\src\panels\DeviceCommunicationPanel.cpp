#include "LA/UI/DeviceCommunication/panels/DeviceCommunicationPanel.h"
#include "LA/UI/DeviceCommunication/widgets/DevicePortWidget.h"
// #include <LA/DeviceManagement/IDeviceRegistry.h>  // 暂时注释，等待实现
// #include <LA/DeviceManagement/Discovery/IDeviceDiscoveryService.h>  // 暂时注释，等待实现
#include <LA/Communication/PortManagement/IPortManager.h>

#include <QApplication>
#include <QDebug>
#include <QLabel>
#include <QStyle>
#include <QVBoxLayout>


namespace LA {
namespace UI {
namespace DeviceCommunication {

DeviceCommunicationPanel::DeviceCommunicationPanel(QWidget *parent)
    : RightSidebarPanel("device_communication_unified", parent), m_layout(nullptr), m_devicePortWidget(nullptr) {
    qDebug() << "DeviceCommunicationPanel: Linus式统一面板构造开始";

    // 设置面板基本属性
    setTitle(tr("设备通信"));
    setIcon(QApplication::style()->standardIcon(QStyle::SP_ComputerIcon));
    setCollapsible(true);
    setClosable(false);  // 核心功能面板不能关闭

    // 设置面板大小
    setMinimumContentHeight(400);
    setMaximumContentHeight(800);

    // 初始化UI
    setupContentUI();
    connectSignals();

    qDebug() << "DeviceCommunicationPanel: 构造完成";
}

void DeviceCommunicationPanel::setupContentUI() {
    QWidget *contentWidget = getContentWidget();
    if (!contentWidget) {
        qWarning() << "DeviceCommunicationPanel: 无法获取内容组件";
        return;
    }

    // 创建主布局
    m_layout = new QVBoxLayout(contentWidget);
    m_layout->setContentsMargins(4, 4, 4, 4);
    m_layout->setSpacing(4);

    try {
        // 创建Linus式设备端口统一组件
        qDebug() << "DeviceCommunicationPanel: 创建DevicePortWidget...";
        m_devicePortWidget = new DevicePortWidget(contentWidget);

        if (m_devicePortWidget) {
            m_layout->addWidget(m_devicePortWidget);
            qDebug() << "DeviceCommunicationPanel: DevicePortWidget创建成功";
        } else {
            qWarning() << "DeviceCommunicationPanel: DevicePortWidget创建失败";
        }

    } catch (const std::exception &e) {
        qWarning() << "DeviceCommunicationPanel: 创建UI组件异常:" << e.what();

        // 创建备用UI
        QLabel *errorLabel = new QLabel("设备通信面板初始化失败", contentWidget);
        errorLabel->setAlignment(Qt::AlignCenter);
        errorLabel->setStyleSheet("QLabel { background-color: #ffebee; color: #c62828; padding: 20px; }");
        m_layout->addWidget(errorLabel);
    }
}

void DeviceCommunicationPanel::connectSignals() {
    if (m_devicePortWidget) {
        // 连接设备端口组件的信号
        connect(m_devicePortWidget, &DevicePortWidget::deviceConnected, this, &DeviceCommunicationPanel::onDeviceConnected);

        connect(m_devicePortWidget, &DevicePortWidget::deviceDisconnected, this, &DeviceCommunicationPanel::onDeviceDisconnected);

        connect(m_devicePortWidget, &DevicePortWidget::connectionFailed, this, &DeviceCommunicationPanel::onConnectionFailed);

        connect(m_devicePortWidget, &DevicePortWidget::statusChanged, this, [this](const QString &status) {
            qDebug() << "DeviceCommunicationPanel: 状态更新:" << status;
        });
    }
}

// 设置后端服务
void DeviceCommunicationPanel::setDeviceRegistry(std::shared_ptr<LA::DeviceManagement::IDeviceRegistry> registry) {
    qDebug() << "DeviceCommunicationPanel: 设置设备注册表 (存根实现)";
    // m_deviceRegistry = registry;  // 暂时注释，等待实现

    if (m_devicePortWidget) {
        m_devicePortWidget->setDeviceRegistry(registry);
    }
}

void DeviceCommunicationPanel::setDeviceDiscoveryService(std::shared_ptr<LA::DeviceManagement::Discovery::IDeviceDiscoveryService> service) {
    qDebug() << "DeviceCommunicationPanel: 设置设备发现服务 (存根实现)";
    // m_discoveryService = service;  // 暂时注释，等待实现

    if (m_devicePortWidget) {
        m_devicePortWidget->setDeviceDiscoveryService(service);
    }
}

void DeviceCommunicationPanel::setPortManager(std::shared_ptr<LA::Communication::PortManagement::IPortManager> manager) {
    qDebug() << "DeviceCommunicationPanel: 设置端口管理器";
    m_portManager = manager;

    if (m_devicePortWidget) {
        m_devicePortWidget->setPortManager(manager);
    }
}

void DeviceCommunicationPanel::setDeviceMatchingService(std::shared_ptr<LA::Communication::DeviceMatching::IDeviceMatchingService> service) {
    qDebug() << "DeviceCommunicationPanel: 设置设备匹配服务";

    if (m_devicePortWidget) {
        m_devicePortWidget->setDeviceMatchingService(service);
    }
}

// Linus: "分层架构协调器集成"
void DeviceCommunicationPanel::setDeviceManagementOrchestrator(LA::DeviceManagement::DeviceManagementOrchestrator *orchestrator) {
    qDebug() << "DeviceCommunicationPanel: 设置设备管理协调器";

    if (m_devicePortWidget) {
        m_devicePortWidget->setDeviceManagementOrchestrator(orchestrator);
        qDebug() << "DeviceCommunicationPanel: 协调器已传递给DevicePortWidget";
    } else {
        qWarning() << "DeviceCommunicationPanel: DevicePortWidget未就绪，无法设置协调器";
    }
}

// RightSidebarPanel 接口实现
void DeviceCommunicationPanel::updateContent() {
    qDebug() << "DeviceCommunicationPanel: 更新面板内容";

    if (m_devicePortWidget) {
        // 延迟刷新，避免频繁刷新
        QTimer::singleShot(500, m_devicePortWidget, &DevicePortWidget::refreshDevicesAndPorts);
    }

    updatePanelStatus();
}

// 功能接口
void DeviceCommunicationPanel::refreshDevicesAndPorts() {
    if (m_devicePortWidget) {
        m_devicePortWidget->refreshDevicesAndPorts();
    }
}

int DeviceCommunicationPanel::getConnectedDeviceCount() const {
    if (m_devicePortWidget) {
        return m_devicePortWidget->getConnectedDeviceCount();
    }
    return 0;
}

QStringList DeviceCommunicationPanel::getConnectedDevices() const {
    if (m_devicePortWidget) {
        return m_devicePortWidget->getConnectedDevices();
    }
    return QStringList();
}

// 槽函数实现
void DeviceCommunicationPanel::onDeviceConnected(const QString &deviceId, const QString &portName) {
    qDebug() << "DeviceCommunicationPanel: 设备已连接 -" << deviceId << "到端口" << portName;

    // 更新面板标题显示连接数
    updatePanelStatus();

    // 发射面板级别的信号
    emit deviceConnectionChanged(deviceId, portName, true);
}

void DeviceCommunicationPanel::onDeviceDisconnected(const QString &deviceId, const QString &portName) {
    qDebug() << "DeviceCommunicationPanel: 设备已断开 -" << deviceId << "从端口" << portName;

    // 更新面板标题显示连接数
    updatePanelStatus();

    // 发射面板级别的信号
    emit deviceConnectionChanged(deviceId, portName, false);
}

void DeviceCommunicationPanel::onConnectionFailed(const QString &deviceId, const QString &portName, const QString &error) {
    qWarning() << "DeviceCommunicationPanel: 连接失败 -" << deviceId << "到" << portName << "错误:" << error;

    // 发射错误信号
    emit connectionError(deviceId, portName, error);
}

void DeviceCommunicationPanel::updatePanelStatus() {
    int connectedCount = getConnectedDeviceCount();

    // 更新面板标题显示连接状态
    if (connectedCount > 0) {
        setTitle(tr("设备通信 (%1)").arg(connectedCount));
    } else {
        setTitle(tr("设备通信"));
    }

    // 可以根据连接状态更新图标
    if (connectedCount > 0) {
        setIcon(QApplication::style()->standardIcon(QStyle::SP_DialogApplyButton));
    } else {
        setIcon(QApplication::style()->standardIcon(QStyle::SP_ComputerIcon));
    }
}

}  // namespace DeviceCommunication
}  // namespace UI
}  // namespace LA