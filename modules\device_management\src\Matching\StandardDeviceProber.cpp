#include "LA/DeviceManagement/Matching/StandardDeviceProber.h"
#include <QDebug>
#include <QThread>
#include <QElapsedTimer>
#include <QMutexLocker>
#include <QDateTime>

namespace LA {
namespace DeviceManagement {
namespace Matching {

ProbeResult StandardDeviceProber::probeDevice(const QString& portName, const DeviceProbeConfig& config)
{
    qDebug() << "Probing device on port:" << portName;
    
    QMutexLocker locker(&m_mutex);
    m_cancelFlags[portName] = false;
    locker.unlock();
    
    ProbeResult result;
    result.portName = portName;
    result.isSuccessful = false;
    
    QElapsedTimer timer;
    timer.start();
    
    try {
        // 判断端口类型并选择相应的探测方法
        if (portName.contains(":")) {
            // 网络端口格式 IP:Port
            auto parts = portName.split(":");
            if (parts.size() == 2) {
                bool ok;
                int port = parts[1].toInt(&ok);
                if (ok) {
                    result = probeNetworkDevice(parts[0], port, config);
                } else {
                    result.errorMessage = "Invalid port format";
                }
            } else {
                result.errorMessage = "Invalid network address format";
            }
        } else {
            // 串口
            result = probeSerialDevice(portName, config);
        }
        
        result.responseTimeMs = timer.elapsed();
        
    } catch (const QString& error) {
        result.errorMessage = error;
        result.responseTimeMs = timer.elapsed();
    }
    
    locker.relock();
    m_cancelFlags.remove(portName);
    
    qDebug() << "Probe result for" << portName << ":" << result.isSuccessful 
             << "Response time:" << result.responseTimeMs << "ms";
    
    return result;
}

bool StandardDeviceProber::sendProbeCommand(const QString& portName, const QByteArray& command, int timeoutMs)
{
    if (portName.contains(":")) {
        // 网络设备
        auto parts = portName.split(":");
        if (parts.size() == 2) {
            bool ok;
            int port = parts[1].toInt(&ok);
            if (ok) {
                QTcpSocket socket;
                if (connectToNetworkDevice(parts[0], port, socket, timeoutMs)) {
                    qint64 written = socket.write(command);
                    return written == command.size() && socket.waitForBytesWritten(timeoutMs);
                }
            }
        }
        return false;
    } else {
        // 串口设备
        QSerialPort port(portName);
        if (openSerialPort(portName, port)) {
            qint64 written = port.write(command);
            bool success = written == command.size() && port.waitForBytesWritten(timeoutMs);
            port.close();
            return success;
        }
        return false;
    }
}

QByteArray StandardDeviceProber::receiveProbeResponse(const QString& portName, int timeoutMs)
{
    if (portName.contains(":")) {
        // 网络设备
        auto parts = portName.split(":");
        if (parts.size() == 2) {
            bool ok;
            int port = parts[1].toInt(&ok);
            if (ok) {
                QTcpSocket socket;
                if (connectToNetworkDevice(parts[0], port, socket, timeoutMs)) {
                    if (socket.waitForReadyRead(timeoutMs)) {
                        return socket.readAll();
                    }
                }
            }
        }
        return QByteArray();
    } else {
        // 串口设备
        QSerialPort port(portName);
        if (openSerialPort(portName, port)) {
            if (port.waitForReadyRead(timeoutMs)) {
                QByteArray response = port.readAll();
                port.close();
                return response;
            }
            port.close();
        }
        return QByteArray();
    }
}

void StandardDeviceProber::cancelProbe(const QString& portName)
{
    QMutexLocker locker(&m_mutex);
    m_cancelFlags[portName] = true;
    qDebug() << "Probe cancelled for port:" << portName;
}

ProbeResult StandardDeviceProber::probeSerialDevice(const QString& portName, const DeviceProbeConfig& config)
{
    ProbeResult result;
    result.portName = portName;
    result.isSuccessful = false;
    
    QSerialPort port(portName);
    if (!openSerialPort(portName, port)) {
        result.errorMessage = QString("Cannot open serial port: %1").arg(port.errorString());
        return result;
    }
    
    // 获取探测指令列表
    auto probeCommands = getStandardProbeCommands(config.deviceTypes);
    if (probeCommands.isEmpty()) {
        probeCommands.append(createUniversalProbeCommand());
    }
    
    QByteArray totalResponse;
    bool gotResponse = false;
    
    for (int retry = 0; retry < config.retryCount && !gotResponse; ++retry) {
        // 检查是否被取消
        {
            QMutexLocker locker(&m_mutex);
            if (m_cancelFlags.value(portName, false)) {
                result.errorMessage = "Probe cancelled";
                port.close();
                return result;
            }
        }
        
        for (const auto& command : probeCommands) {
            qDebug() << "Sending probe command to" << portName << ":" << command.toHex();
            
            // 发送指令
            if (port.write(command) != command.size()) {
                continue;
            }
            
            if (!port.waitForBytesWritten(config.timeoutMs)) {
                continue;
            }
            
            // 等待响应
            if (port.waitForReadyRead(config.timeoutMs)) {
                QByteArray response = port.readAll();
                
                // 可能需要等待更多数据
                QThread::msleep(50);
                if (port.waitForReadyRead(100)) {
                    response += port.readAll();
                }
                
                if (isValidResponse(response)) {
                    totalResponse = response;
                    gotResponse = true;
                    break;
                }
            }
        }
    }
    
    port.close();
    
    if (gotResponse) {
        result.isSuccessful = true;
        result.response = totalResponse;
        qDebug() << "Got valid response from" << portName << ":" << totalResponse.toHex();
    } else {
        result.errorMessage = "No valid response received";
    }
    
    return result;
}

ProbeResult StandardDeviceProber::probeNetworkDevice(const QString& address, int port, const DeviceProbeConfig& config)
{
    ProbeResult result;
    result.portName = QString("%1:%2").arg(address).arg(port);
    result.isSuccessful = false;
    
    QTcpSocket socket;
    if (!connectToNetworkDevice(address, port, socket, config.timeoutMs)) {
        result.errorMessage = QString("Cannot connect to %1:%2").arg(address).arg(port);
        return result;
    }
    
    // 获取探测指令
    auto probeCommands = getStandardProbeCommands(config.deviceTypes);
    if (probeCommands.isEmpty()) {
        probeCommands.append(createUniversalProbeCommand());
    }
    
    QByteArray totalResponse;
    bool gotResponse = false;
    
    for (int retry = 0; retry < config.retryCount && !gotResponse; ++retry) {
        // 检查是否被取消
        {
            QMutexLocker locker(&m_mutex);
            if (m_cancelFlags.value(result.portName, false)) {
                result.errorMessage = "Probe cancelled";
                socket.disconnectFromHost();
                return result;
            }
        }
        
        for (const auto& command : probeCommands) {
            if (socket.write(command) != command.size()) {
                continue;
            }
            
            if (socket.waitForReadyRead(config.timeoutMs)) {
                QByteArray response = socket.readAll();
                
                if (isValidResponse(response)) {
                    totalResponse = response;
                    gotResponse = true;
                    break;
                }
            }
        }
    }
    
    socket.disconnectFromHost();
    
    if (gotResponse) {
        result.isSuccessful = true;
        result.response = totalResponse;
    } else {
        result.errorMessage = "No valid response received";
    }
    
    return result;
}

bool StandardDeviceProber::openSerialPort(const QString& portName, QSerialPort& port)
{
    if (!port.open(QIODevice::ReadWrite)) {
        return false;
    }
    
    configureSerialPort(port);
    return true;
}

void StandardDeviceProber::configureSerialPort(QSerialPort& port)
{
    // 通用串口配置
    port.setBaudRate(QSerialPort::Baud115200);
    port.setDataBits(QSerialPort::Data8);
    port.setParity(QSerialPort::NoParity);
    port.setStopBits(QSerialPort::OneStop);
    port.setFlowControl(QSerialPort::NoFlowControl);
    
    // 清空缓冲区
    port.clear();
}

bool StandardDeviceProber::connectToNetworkDevice(const QString& address, int port, QTcpSocket& socket, int timeoutMs)
{
    socket.connectToHost(address, port);
    return socket.waitForConnected(timeoutMs);
}

QList<QByteArray> StandardDeviceProber::getStandardProbeCommands(const QStringList& deviceTypes)
{
    QList<QByteArray> commands;
    
    for (const QString& deviceType : deviceTypes) {
        if (deviceType.toUpper() == "SPRM") {
            // SPRM设备常用探测指令
            commands.append(QByteArray::fromHex("AA55030000000358"));  // SPRM查询指令
            commands.append("ID?\r\n");                               // 通用ID查询
            commands.append("VER?\r\n");                              // 版本查询
        } else if (deviceType.toUpper() == "MOTOR") {
            // 电机设备探测指令
            commands.append("ST?\r\n");                               // 状态查询
            commands.append(QByteArray::fromHex("010300000002C40B")); // Modbus查询
        } else if (deviceType.toUpper() == "SENSOR") {
            // 传感器设备探测指令
            commands.append("READ?\r\n");                             // 读取指令
            commands.append(QByteArray::fromHex("010400000001319C")); // Modbus读取
        }
    }
    
    // 如果没有特定指令，添加通用指令
    if (commands.isEmpty()) {
        commands << createUniversalProbeCommand();
    }
    
    return commands;
}

QByteArray StandardDeviceProber::createUniversalProbeCommand()
{
    // 创建通用探测指令
    return "ID?\r\n";
}

bool StandardDeviceProber::isValidResponse(const QByteArray& response)
{
    if (response.isEmpty() || response.size() < 2) {
        return false;
    }
    
    // 检查是否包含常见的设备响应模式
    QString responseStr = QString::fromLatin1(response);
    
    // 常见的有效响应模式
    QStringList validPatterns = {
        "SPRM", "VER", "ID:", "OK", "ERROR", "READY",
        "V1.", "V2.", "SN:", "MODEL:", "STATUS:"
    };
    
    for (const QString& pattern : validPatterns) {
        if (responseStr.contains(pattern, Qt::CaseInsensitive)) {
            return true;
        }
    }
    
    // 检查是否是有效的十六进制响应（如Modbus）
    if (response.size() >= 3 && (quint8)response[0] > 0 && (quint8)response[1] > 0) {
        return true;
    }
    
    return false;
}

int StandardDeviceProber::calculateResponseTime(qint64 startTime)
{
    return static_cast<int>(QDateTime::currentMSecsSinceEpoch() - startTime);
}

} // namespace Matching
} // namespace DeviceManagement
} // namespace LA