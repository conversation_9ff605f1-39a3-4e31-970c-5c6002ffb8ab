#pragma once

/*
 * Linus式UI设计原则：
 * 1. "UI is policy, not mechanism" - 这里只管怎么显示，不管怎么实现
 * 2. "Make the common case fast" - 最常用的操作最明显
 * 3. "Show don't tell" - 用户一眼就能看懂当前状态
 */

#include <QComboBox>
#include <QGroupBox>
#include <QLabel>
#include <QMap>
#include <QPushButton>
#include <QSerialPortInfo>
#include <QSet>
#include <QTableWidget>
#include <QTimer>
#include <QVBoxLayout>
#include <QWidget>
#include <memory>


// 前向声明 - Linus: "Don't expose implementation details in headers"
namespace LA {
namespace DeviceManagement {
class IDeviceRegistry;
class DeviceManagementOrchestrator;  // Linus: "UI层依赖业务协调层"
namespace Discovery {
class IDeviceDiscoveryService;
}
}  // namespace DeviceManagement
namespace Communication {
namespace PortManagement {
class IPortManager;
}
namespace DeviceMatching {
class IDeviceMatchingService;
struct DeviceMatchResult;
struct CommunicableDevice;
}  // namespace DeviceMatching
}  // namespace Communication
namespace Foundation {
namespace Core {
struct DeviceInfo;
struct PortInfo;
}  // namespace Core
}  // namespace Foundation
}  // namespace LA

namespace LA {
namespace UI {
namespace DeviceCommunication {

/**
 * @brief 设备-端口统一管理组件
 *
 * Linus式设计思路：
 * - 用户不关心设备和端口是两个系统，他们只想连接设备
 * - 最常见的操作（自动连接）应该最显眼
 * - 状态一目了然，操作一击即中
 */
class DevicePortWidget : public QWidget {
    Q_OBJECT

  public:
    // 状态管理枚举
    enum class StatusType { Info, Success, Warning, Error };
    enum class ConnectionStatus {
        Connected,         // 已连接
        CanConnect,        // 待连接
        NoPort,            // 无端口
        UnknownDevice,     // 待识别（旧状态，保持兼容）
        Incompatible,      // 不兼容
        Unidentified,      // 未识别（新状态）
        ManualIdentified,  // 手动识别（新状态）
        AutoIdentified     // 自动识别（新状态）
    };

    explicit DevicePortWidget(QWidget *parent = nullptr);
    virtual ~DevicePortWidget() = default;

    // Linus: "分层架构 - 设置业务协调层"
    void setDeviceManagementOrchestrator(LA::DeviceManagement::DeviceManagementOrchestrator *orchestrator);

    // 设置后端服务 - Linus: "Dependency injection, not global state" - 保留兼容性
    void setDeviceRegistry(std::shared_ptr<LA::DeviceManagement::IDeviceRegistry> registry);
    void setDeviceDiscoveryService(std::shared_ptr<LA::DeviceManagement::Discovery::IDeviceDiscoveryService> service);
    void setPortManager(std::shared_ptr<LA::Communication::PortManagement::IPortManager> manager);
    void setDeviceMatchingService(std::shared_ptr<LA::Communication::DeviceMatching::IDeviceMatchingService> service);

    // 主要操作接口
    void refreshDevicesAndPorts();
    void autoConnectAll();
    void disconnectAll();

    // 状态查询
    int         getConnectedDeviceCount() const;
    QStringList getConnectedDevices() const;
    bool        isDeviceConnected(const QString &deviceId) const;

  signals:
    // Linus: "Signals should describe what happened, not what should happen"
    void deviceConnected(const QString &deviceId, const QString &portName);
    void deviceDisconnected(const QString &deviceId, const QString &portName);
    void connectionFailed(const QString &deviceId, const QString &portName, const QString &error);
    void statusChanged(const QString &message);

  public slots:
    // 暂时注释，等待Foundation::Core类型实现
    // void onDeviceDiscovered(const LA::Foundation::Core::DeviceInfo& device);
    void onDeviceLost(const QString &deviceId);
    // void onPortAdded(const LA::Foundation::Core::PortInfo& port);
    void onPortRemoved(const QString &portName);

  private slots:
    void onAutoRefreshTimer();
    void onTableCellClicked(int row, int column);
    void onConnectButtonClicked();
    void onDisconnectButtonClicked();
    void onHeaderSectionResized(int logicalIndex, int oldSize, int newSize);
    void onHeaderSectionDoubleClicked(int logicalIndex);

    // 设备匹配相关槽函数
    void onAutoMatchStarted(const QString &portName, int deviceCount);
    void onMatchingProgressChanged(const QString &portName, int progress, const QString &currentDevice);
    void onAutoMatchCompleted(const QString &portName, const LA::Communication::DeviceMatching::DeviceMatchResult &result);
    void onManualMatchCompleted(const QString &portName, const LA::Communication::DeviceMatching::DeviceMatchResult &result);
    void onMatchingStatusChanged(const QString &portName, const QString &status);
    void onMatchingFailed(const QString &portName, const QString &error);

  private:
    // Linus: "防抖机制和增量更新方法"
    void doRefreshDevicesAndPorts();
    void updatePortListIncrementally();
    void updatePortListFromOrchestrator(const QStringList &availablePorts);  // Linus: "协调器端口更新"
    void doFullRefresh();

    // Linus: "细粒度增量更新 - 最低消耗原则"
    void        updatePortTable();
    void        addPortRow(const QString &portName);
    void        removePortRow(const QString &portName);
    void        updatePortRow(const QString &portName);
    int         findPortRowIndex(const QString &portName) const;
    QStringList getCurrentPorts() const;
    QStringList getTablePorts() const;

    // Linus: "端口生命周期管理"
    void handlePortAdded(const QString &portName);
    void handlePortRemoved(const QString &portName);
    void cleanupPortResources(const QString &portName);

    // UI组件
    void setupUI();
    void createStatusSection(QVBoxLayout *layout);
    void createDevicePortTable(QVBoxLayout *layout);
    void createActionButtons(QVBoxLayout *layout);
    void connectSignals();

    // 表格管理
    void         addDevicePortRow(int                           row,
                                  const QString &               deviceName,
                                  const QString &               portName,
                                  ConnectionStatus              status,
                                  const QMap<QString, QString> &userSelections = QMap<QString, QString>());
    void         updateDevicePortRow(int row, ConnectionStatus status);
    void         removeDevicePortRow(const QString &deviceId);
    QPushButton *createActionButton(const QString &deviceId, const QString &portName, ConnectionStatus status);

    // 连接逻辑
    bool    connectDeviceToPort(const QString &deviceId, const QString &portName);
    void    disconnectDevice(const QString &deviceId);
    QString findBestPortForDevice(const QString &deviceId, const QStringList &availablePorts) const;
    bool    isDevicePortCompatible(const QString &deviceId, const QString &portName) const;

    // 状态管理
    void updateStatus(const QString &message, StatusType type);
    void updateStatistics();

    QString getStatusText(ConnectionStatus status) const;
    QColor  getStatusColor(ConnectionStatus status) const;
    QString getDeviceDisplayName(const QString &deviceId) const;
    void    tryIdentifyDevice(const QString &portName);
    void    startDeviceIdentification(const QString &portName);                // 新方法：启动设备识别
    void    connectIdentifiedDevice(const QString &portName);                  // 新方法：连接已识别设备
    void    updatePortStatus(const QString &portName, const QString &status);  // 新方法：更新端口状态
    QString identifyDeviceType(const QSerialPortInfo &portInfo) const;
    int     calculateOptimalColumnWidth(int columnIndex) const;

    // 手动匹配功能
    void showManualMatchDialog(const QString &deviceId);

  private:
    // Linus: "UI层依赖业务协调层，实现分层架构"
    LA::DeviceManagement::DeviceManagementOrchestrator *m_deviceManagementOrchestrator;

    // 后端服务 - 暂时保留兼容性
    // std::shared_ptr<LA::DeviceManagement::IDeviceRegistry> m_deviceRegistry;
    // std::shared_ptr<LA::DeviceManagement::Discovery::IDeviceDiscoveryService> m_discoveryService;
    std::shared_ptr<LA::Communication::PortManagement::IPortManager>          m_portManager;
    std::shared_ptr<LA::Communication::DeviceMatching::IDeviceMatchingService> m_deviceMatchingService;

    // UI组件
    QLabel *      m_statusLabel;
    QTableWidget *m_devicePortTable;
    QPushButton * m_autoConnectBtn;
    QPushButton * m_refreshBtn;
    QPushButton * m_disconnectAllBtn;

    // 状态数据
    QTimer *               m_autoRefreshTimer;
    QMap<QString, QString> m_devicePortMap;            // deviceId -> portName
    QMap<QString, QString> m_portDeviceMap;            // portName -> deviceId
    QSet<QString>          m_attemptedAutoMatchPorts;  // 已尝试自动匹配的端口列表
};

}  // namespace DeviceCommunication
}  // namespace UI
}  // namespace LA