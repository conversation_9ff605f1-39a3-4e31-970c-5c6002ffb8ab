/**
 * @file SidebarIntegrationExample.cpp
 * @brief 设置系统与侧边栏集成示例
 * 
 * 此文件展示了如何将设置系统集成到LA项目的侧边栏中
 * 这些代码应该在主应用程序的初始化过程中调用
 */

#include "LA/Settings/Settings.h"
#include "LA/Settings/SettingsPanelFactory.h"
#include "LA/SideBar/SidebarManager.h"
#include "LA/SideBar/SidebarPanelFactory.h"
#include "LA/SideBar/SidebarRegistry.h"
#include <QDebug>
#include <memory>

namespace LA {
namespace Settings {
namespace Examples {

/**
 * @brief 集成设置系统到侧边栏的完整示例
 * 
 * 此函数展示了完整的集成流程，包括：
 * 1. 初始化设置系统
 * 2. 创建设置面板工厂
 * 3. 注册到侧边栏系统
 * 4. 添加面板到侧边栏
 * 
 * @param sidebarManager 侧边栏管理器实例
 * @param sidebarRegistry 侧边栏注册器实例
 * @return 集成成功返回true
 */
bool integrateSettingsToSidebar(LA::SideBar::SidebarManager *sidebarManager, 
                               std::shared_ptr<LA::SideBar::ISidebarRegistry> sidebarRegistry) {
    if (!sidebarManager || !sidebarRegistry) {
        qCritical() << "Settings Integration: Invalid sidebar manager or registry";
        return false;
    }

    try {
        // 1. 初始化设置系统
        qDebug() << "Settings Integration: Initializing settings system...";
        if (!initializeSettingsSystem()) {
            qCritical() << "Settings Integration: Failed to initialize settings system";
            return false;
        }

        // 2. 创建设置面板工厂
        qDebug() << "Settings Integration: Creating settings panel factory...";
        auto settingsFactory = std::make_shared<LA::SideBar::SidebarPanelFactory>();
        
        // 注册设置面板创建器
        settingsFactory->registerPanelCreator(
            "settings",
            [](QWidget *parent) -> std::unique_ptr<LA::SideBar::ISidebarPanel> {
                return SettingsPanelFactory::createSettingsPanel(parent);
            },
            "Settings Panel"
        );

        // 3. 注册工厂到侧边栏注册器
        qDebug() << "Settings Integration: Registering factory to sidebar registry...";
        sidebarRegistry->registerFactory("settings", settingsFactory);
        sidebarRegistry->setCategoryDescription("settings", "Application settings panels");

        // 4. 添加设置面板到侧边栏
        qDebug() << "Settings Integration: Adding settings panel to sidebar...";
        if (!sidebarManager->addPanel("settings", "settings")) {
            qWarning() << "Settings Integration: Failed to add settings panel to sidebar";
            return false;
        }

        qDebug() << "Settings Integration: Settings system successfully integrated to sidebar";
        return true;

    } catch (const std::exception &e) {
        qCritical() << "Settings Integration: Exception during integration:" << e.what();
        return false;
    }
}

/**
 * @brief 简化的集成函数
 * 
 * 提供一个更简单的集成接口，适用于标准的集成场景
 * 
 * @param sidebarManager 侧边栏管理器实例
 * @return 集成成功返回true
 */
bool quickIntegrateSettings(LA::SideBar::SidebarManager *sidebarManager) {
    if (!sidebarManager) {
        qCritical() << "Settings Integration: Invalid sidebar manager";
        return false;
    }

    // 获取侧边栏注册器（假设从管理器可以获取）
    // 注意：这里需要根据实际的SidebarManager接口进行调整
    // auto registry = sidebarManager->getRegistry();
    // return integrateSettingsToSidebar(sidebarManager, registry);
    
    qDebug() << "Settings Integration: Quick integration placeholder";
    return true;
}

/**
 * @brief 在主窗口初始化时调用的集成函数
 * 
 * 此函数应该在MainWindow的初始化过程中调用
 * 通常在创建侧边栏系统之后立即调用
 */
void integrateSettingsInMainWindow() {
    qDebug() << "Settings Integration: Starting main window integration...";
    
    // 示例代码 - 需要根据实际的MainWindow实现进行调整
    /*
    // 获取主窗口的侧边栏管理器
    auto mainWindow = MainWindow::instance();
    if (mainWindow) {
        auto sidebarManager = mainWindow->getSidebarManager();
        auto sidebarRegistry = mainWindow->getSidebarRegistry();
        
        if (sidebarManager && sidebarRegistry) {
            integrateSettingsToSidebar(sidebarManager, sidebarRegistry);
        }
    }
    */
    
    qDebug() << "Settings Integration: Main window integration completed";
}

/**
 * @brief 检查设置系统是否已正确集成到侧边栏
 * 
 * @param sidebarManager 侧边栏管理器实例
 * @return 集成正确返回true
 */
bool verifySettingsIntegration(LA::SideBar::SidebarManager *sidebarManager) {
    if (!sidebarManager) {
        return false;
    }

    // 检查设置面板是否存在
    bool hasSettingsPanel = sidebarManager->hasPanel("settings");
    if (!hasSettingsPanel) {
        qWarning() << "Settings Integration: Settings panel not found in sidebar";
        return false;
    }

    // 检查面板是否可以正常显示
    if (!sidebarManager->showPanel("settings")) {
        qWarning() << "Settings Integration: Failed to show settings panel";
        return false;
    }

    qDebug() << "Settings Integration: Verification successful";
    return true;
}

} // namespace Examples
} // namespace Settings
} // namespace LA
