{"device_type": "SPRM", "model": "A1_standard", "display_name": "SPRM-A1 标准版", "category": "sensor", "manufacturer": "CSPC", "description": "SPRM-A1标准版激光测距传感器 - 四层架构配置驱动版本", "version": "2.0.0", "last_updated": "2025-08-23", "command_mapping": {"query_distance": {"protocol_id": "0x09", "type": "Read", "description": "查询距离测量值", "parameters": [], "response_fields": ["distance", "confidence", "timestamp"], "timeout_ms": 5000}, "set_mode": {"protocol_id": "0x06", "type": "Write", "description": "设置测量模式", "parameters": ["mode_value"], "response_fields": ["status"], "timeout_ms": 3000}, "calibrate": {"protocol_id": "0x01", "type": "Control", "description": "执行校准命令", "parameters": ["calibration_type", "reference_distance"], "response_fields": ["calibration_result", "calibration_data"], "timeout_ms": 10000}, "get_version": {"protocol_id": "0x14", "type": "Read", "description": "获取固件版本信息", "parameters": [], "response_fields": ["version", "build_date"], "timeout_ms": 3000}, "get_chip_id": {"protocol_id": "0x0C", "type": "Query", "description": "查询芯片ID", "parameters": [], "response_fields": ["chip_id"], "timeout_ms": 3000}, "set_laser_power": {"protocol_id": "0x08", "type": "Write", "description": "设置激光功率", "parameters": ["power_level"], "response_fields": ["status"], "timeout_ms": 2000}}, "capabilities": [{"type": "communication", "dependencies": ["communication_session", "protocol_handler"], "config": {"protocol_type": "HTKJ", "enable_heartbeat": true, "heartbeat_interval": 30000}}, {"type": "ranging", "dependencies": ["algorithm_module"], "config": {"modes": ["single", "continuous"], "default_mode": "single", "average_samples": 3, "enable_filtering": true, "filter_type": "kalman", "min_range_mm": 50, "max_range_mm": 2000, "accuracy_mm": 1}}, {"type": "calibration", "dependencies": ["algorithm_module", "communication_capability"], "config": {"supported_flows": ["single_point", "cross_talk", "offset"], "default_flow": "single_point", "auto_save": true, "calibration_points": 1}}], "calibration_flows": {"single_point": {"flow_name": "单点校准", "description": "使用单个参考距离进行校准", "steps": [{"step_id": "prepare", "step_name": "准备校准", "command": "set_mode", "parameters": {"mode": "calibration"}, "timeout_ms": 5000, "required": true}, {"step_id": "xtalk_calibration", "step_name": "交叉标定", "command": "calibrate", "parameters": {"calibration_type": "xtalk"}, "timeout_ms": 10000, "required": true}, {"step_id": "offset_calibration", "step_name": "偏移校准", "command": "calibrate", "parameters": {"calibration_type": "offset"}, "timeout_ms": 10000, "required": true}]}}, "environments": {"indoor": {"description": "室内环境配置", "recommended_power": 5.0, "filter_strategy": "kalman", "calibration_interval": 24}, "outdoor": {"description": "室外环境配置", "recommended_power": 8.0, "filter_strategy": "moving_average", "calibration_interval": 12}}, "ranging": {"mode": "single", "average_samples": 3, "accuracy_threshold": 1.0, "timeout_ms": 5000, "continuous_interval_ms": 100, "enable_filtering": true, "enable_validation": true}, "filtering": {"type": "kalman", "process_noise": 0.01, "measurement_noise": 0.1, "initial_covariance": 1.0}, "hardware_info": {"laser_wavelength_nm": 940, "laser_power_max_mw": 10, "laser_power_default_mw": 5, "power_consumption_mw": 200, "operating_temperature_range": "-20~60°C", "dimensions": "25x15x8mm", "accuracy": "±1mm", "resolution": "1mm"}, "script_file": "devices/sprm_behavior.lua"}