#include "ThreadManager.h"
#include "../pool/ThreadPool.h"
#include "../communication/CommunicationThread.h"
#include <QCoreApplication>
#include <QDebug>

namespace LA {
namespace Thread {

// 静态成员初始化
ThreadManager* ThreadManager::s_instance = nullptr;
QMutex ThreadManager::s_instanceMutex;

ThreadManager::ThreadManager(QObject* parent)
    : IThreadManager(parent)
    , m_threadPool(nullptr)
    , m_globalConfig(ThreadConfig("Global"))
{
    // 初始化线程池
    m_threadPool = new ThreadPool(this);
    
    // 连接线程池信号
    connect(m_threadPool, &ThreadPool::taskCompleted,
            this, &ThreadManager::onThreadPoolTaskCompleted);
    
    // 启动统计更新定时器
    m_statisticsTimer = new QTimer(this);
    connect(m_statisticsTimer, &QTimer::timeout,
            this, &ThreadManager::updateStatistics);
    m_statisticsTimer->start(1000); // 每秒更新一次统计
    
    qDebug() << "ThreadManager initialized";
}

ThreadManager::~ThreadManager()
{
    stopAllThreads();
    qDebug() << "ThreadManager destroyed";
}

IThreadManager* ThreadManager::instance()
{
    if (s_instance == nullptr) {
        QMutexLocker locker(&s_instanceMutex);
        if (s_instance == nullptr) {
            s_instance = new ThreadManager();
            
            // 确保在应用程序退出时清理
            QCoreApplication* app = QCoreApplication::instance();
            if (app) {
                QObject::connect(app, &QCoreApplication::aboutToQuit, [](){ 
                    delete s_instance; 
                    s_instance = nullptr; 
                });
            }
        }
    }
    return s_instance;
}

QThread* ThreadManager::createThread(const QString& name, QObject* worker)
{
    QMutexLocker locker(&m_mutex);
    
    if (m_threads.contains(name)) {
        qWarning() << "Thread already exists:" << name;
        return m_threads[name];
    }
    
    QThread* thread = new QThread(this);
    thread->setObjectName(name);
    
    if (worker) {
        worker->moveToThread(thread);
    }
    
    // 连接线程信号
    connect(thread, &QThread::started, this, [this, name]() {
        emit threadStarted(name);
        updateThreadState(name, ThreadState::Running);
    });
    
    connect(thread, &QThread::finished, this, [this, name]() {
        emit threadFinished(name);
        updateThreadState(name, ThreadState::Stopped);
    });
    
    m_threads[name] = thread;
    m_threadStates[name] = ThreadState::Idle;
    m_threadStatistics[name] = ThreadStatistics();
    m_threadStatistics[name].threadName = name;
    
    qDebug() << "Created thread:" << name;
    return thread;
}

bool ThreadManager::startThread(const QString& name)
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_threads.contains(name)) {
        qWarning() << "Thread not found:" << name;
        return false;
    }
    
    QThread* thread = m_threads[name];
    if (thread->isRunning()) {
        qWarning() << "Thread already running:" << name;
        return true;
    }
    
    try {
        thread->start();
        m_threadStartTimes[name] = QDateTime::currentMSecsSinceEpoch();
        updateThreadState(name, ThreadState::Running);
        
        qDebug() << "Started thread:" << name;
        return true;
    } catch (const std::exception& e) {
        qCritical() << "Failed to start thread" << name << ":" << e.what();
        updateThreadState(name, ThreadState::Error);
        emit threadError(name, QString::fromStdString(e.what()));
        return false;
    }
}

bool ThreadManager::stopThread(const QString& name, int timeoutMs)
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_threads.contains(name)) {
        qWarning() << "Thread not found:" << name;
        return false;
    }
    
    QThread* thread = m_threads[name];
    if (!thread->isRunning()) {
        return true;
    }
    
    updateThreadState(name, ThreadState::Stopping);
    
    thread->quit();
    if (!thread->wait(timeoutMs)) {
        qWarning() << "Thread" << name << "did not stop within timeout, terminating";
        thread->terminate();
        thread->wait(1000);
    }
    
    updateThreadState(name, ThreadState::Stopped);
    qDebug() << "Stopped thread:" << name;
    return true;
}

bool ThreadManager::isThreadRunning(const QString& name) const
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_threads.contains(name)) {
        return false;
    }
    
    return m_threads[name]->isRunning();
}

ThreadState ThreadManager::getThreadState(const QString& name) const
{
    QMutexLocker locker(&m_mutex);
    
    return m_threadStates.value(name, ThreadState::Stopped);
}

IThreadPool* ThreadManager::getThreadPool() const
{
    return m_threadPool;
}

void ThreadManager::setMaxThreadCount(int count)
{
    if (m_threadPool) {
        m_threadPool->setMaxThreadCount(count);
    }
}

int ThreadManager::maxThreadCount() const
{
    return m_threadPool ? m_threadPool->maxThreadCount() : 0;
}

int ThreadManager::activeThreadCount() const
{
    return m_threadPool ? m_threadPool->activeThreadCount() : 0;
}

ICommunicationThread* ThreadManager::createCommunicationThread(const QString& deviceId, const ThreadConfig& config)
{
    QMutexLocker locker(&m_mutex);
    
    if (m_communicationThreads.contains(deviceId)) {
        qWarning() << "Communication thread already exists for device:" << deviceId;
        return m_communicationThreads[deviceId];
    }
    
    CommunicationThread* commThread = new CommunicationThread(deviceId, config, this);
    
    // 连接通信线程信号
    connect(commThread, &CommunicationThread::deviceConnected,
            this, &ThreadManager::onCommunicationThreadConnected);
    connect(commThread, &CommunicationThread::deviceDisconnected,
            this, &ThreadManager::onCommunicationThreadDisconnected);
    connect(commThread, &CommunicationThread::connectionError,
            this, &ThreadManager::onCommunicationThreadError);
    
    m_communicationThreads[deviceId] = commThread;
    
    emit communicationThreadCreated(deviceId);
    qDebug() << "Created communication thread for device:" << deviceId;
    
    return commThread;
}

ICommunicationThread* ThreadManager::getCommunicationThread(const QString& deviceId) const
{
    QMutexLocker locker(&m_mutex);
    
    return m_communicationThreads.value(deviceId, nullptr);
}

bool ThreadManager::removeCommunicationThread(const QString& deviceId)
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_communicationThreads.contains(deviceId)) {
        return false;
    }
    
    ICommunicationThread* commThread = m_communicationThreads[deviceId];
    commThread->stopCommunication();
    commThread->quit();
    commThread->wait(5000);
    
    m_communicationThreads.remove(deviceId);
    commThread->deleteLater();
    
    emit communicationThreadRemoved(deviceId);
    qDebug() << "Removed communication thread for device:" << deviceId;
    
    return true;
}

QStringList ThreadManager::getActiveThreads() const
{
    QMutexLocker locker(&m_mutex);
    
    QStringList activeThreads;
    for (auto it = m_threads.begin(); it != m_threads.end(); ++it) {
        if (it.value()->isRunning()) {
            activeThreads << it.key();
        }
    }
    
    return activeThreads;
}

QStringList ThreadManager::getCommunicationThreads() const
{
    QMutexLocker locker(&m_mutex);
    
    return m_communicationThreads.keys();
}

ThreadStatistics ThreadManager::getThreadStatistics(const QString& name) const
{
    QMutexLocker locker(&m_mutex);
    
    return m_threadStatistics.value(name, ThreadStatistics());
}

QList<ThreadStatistics> ThreadManager::getAllThreadStatistics() const
{
    QMutexLocker locker(&m_mutex);
    
    return m_threadStatistics.values();
}

void ThreadManager::stopAllThreads(int timeoutMs)
{
    qDebug() << "Stopping all threads...";
    
    // 停止所有通信线程
    QStringList deviceIds = getCommunicationThreads();
    for (const QString& deviceId : deviceIds) {
        removeCommunicationThread(deviceId);
    }
    
    // 停止所有普通线程
    QStringList threadNames = getActiveThreads();
    for (const QString& name : threadNames) {
        stopThread(name, timeoutMs / threadNames.size());
    }
    
    // 停止线程池
    if (m_threadPool) {
        m_threadPool->clear();
        m_threadPool->waitForDone(timeoutMs);
    }
    
    qDebug() << "All threads stopped";
}

void ThreadManager::pauseAllThreads()
{
    // 暂停线程池
    if (m_threadPool) {
        m_threadPool->pause();
    }
    
    // 暂停所有通信线程
    for (ICommunicationThread* commThread : m_communicationThreads.values()) {
        commThread->pauseCommunication();
    }
    
    qDebug() << "All threads paused";
}

void ThreadManager::resumeAllThreads()
{
    // 恢复线程池
    if (m_threadPool) {
        m_threadPool->resume();
    }
    
    // 恢复所有通信线程
    for (ICommunicationThread* commThread : m_communicationThreads.values()) {
        commThread->resumeCommunication();
    }
    
    qDebug() << "All threads resumed";
}

void ThreadManager::setGlobalThreadConfig(const ThreadConfig& config)
{
    QMutexLocker locker(&m_mutex);
    m_globalConfig = config;
}

ThreadConfig ThreadManager::getGlobalThreadConfig() const
{
    QMutexLocker locker(&m_mutex);
    return m_globalConfig;
}

void ThreadManager::updateThreadState(const QString& name, ThreadState newState)
{
    ThreadState oldState = m_threadStates.value(name, ThreadState::Stopped);
    m_threadStates[name] = newState;
    m_threadStatistics[name].state = newState;
    
    if (oldState != newState) {
        emit threadStateChanged(name, oldState, newState);
    }
}

void ThreadManager::updateStatistics()
{
    QMutexLocker locker(&m_mutex);
    
    qint64 currentTime = QDateTime::currentMSecsSinceEpoch();
    int totalThreads = m_threads.size() + m_communicationThreads.size();
    int activeThreads = getActiveThreads().size() + 
                       (m_threadPool ? m_threadPool->activeThreadCount() : 0);
    
    // 更新线程统计
    for (auto it = m_threadStatistics.begin(); it != m_threadStatistics.end(); ++it) {
        const QString& name = it.key();
        ThreadStatistics& stats = it.value();
        
        if (m_threadStartTimes.contains(name) && isThreadRunning(name)) {
            stats.runningTime = currentTime - m_threadStartTimes[name];
        }
        
        emit threadStatisticsUpdated(name, stats);
    }
    
    // 计算平均CPU使用率
    double avgCpuUsage = 0.0;
    if (!m_threadStatistics.isEmpty()) {
        double totalCpu = 0.0;
        for (const ThreadStatistics& stats : m_threadStatistics.values()) {
            totalCpu += stats.cpuUsage;
        }
        avgCpuUsage = totalCpu / m_threadStatistics.size();
    }
    
    emit globalStatisticsUpdated(totalThreads, activeThreads, avgCpuUsage);
}

void ThreadManager::onThreadPoolTaskCompleted()
{
    // 线程池任务完成处理
}

void ThreadManager::onCommunicationThreadConnected(const QString& deviceId)
{
    qDebug() << "Communication thread connected:" << deviceId;
}

void ThreadManager::onCommunicationThreadDisconnected(const QString& deviceId)
{
    qDebug() << "Communication thread disconnected:" << deviceId;
}

void ThreadManager::onCommunicationThreadError(const QString& deviceId, const QString& error)
{
    qWarning() << "Communication thread error for device" << deviceId << ":" << error;
}

} // namespace Thread
} // namespace LA