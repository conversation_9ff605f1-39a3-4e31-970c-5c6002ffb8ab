#include <LA/Communication/Manager/ICommunicationManager.h>
#include <LA/Communication/Session/ICommunicationSession.h>
#include <QDebug>
#include <QMutexLocker>
#include <QDateTime>
#include <QTimer>

namespace LA {
namespace Communication {
namespace Manager {

/**
 * @brief 基础通信管理器实现
 * 
 * 管理多个通信会话的Linus式实现
 */
class BasicCommunicationManager : public ICommunicationManager {
    Q_OBJECT

public:
    explicit BasicCommunicationManager(QObject* parent = nullptr);
    virtual ~BasicCommunicationManager();

    // === 管理器生命周期 ===
    SimpleResult initialize(const ConfigParameters& config = {}) override;
    SimpleResult shutdown() override;
    bool isInitialized() const override;

    // === 会话管理 ===
    SimpleResult createSession(const QString& sessionId, 
                             const QString& sessionType, 
                             const ConfigParameters& config) override;
    SimpleResult destroySession(const QString& sessionId) override;
    std::shared_ptr<Session::ICommunicationSession> getSession(const QString& sessionId) override;
    bool hasSession(const QString& sessionId) const override;

    // === 批量操作 ===
    SimpleResult openAllSessions() override;
    SimpleResult closeAllSessions() override;
    QStringList getAllSessionIds() const override;
    QStringList getSessionsByStatus(ConnectionStatus status) const override;

    // === 配置管理 ===
    SimpleResult updateSessionConfig(const QString& sessionId, 
                                   const ConfigParameters& config) override;
    ConfigParameters getSessionConfig(const QString& sessionId) const override;
    SimpleResult setGlobalConfig(const ConfigParameters& config) override;
    ConfigParameters getGlobalConfig() const override;

    // === 状态查询 ===
    DeviceStatistics getManagerStatistics() const override;
    DeviceStatistics getSessionStatistics(const QString& sessionId) const override;
    QString errorString() const override;
    void resetStatistics() override;
    int getSessionCount() const override;

    // === 监控和诊断 ===
    SimpleResult performHealthCheck() override;
    QString getStatusReport() const override;

private slots:
    void onSessionStatusChanged(ConnectionStatus status);
    void onSessionError(const QString& error);

private:
    mutable QMutex m_mutex;
    
    // 状态
    bool m_initialized;
    ConfigParameters m_globalConfig;
    DeviceStatistics m_managerStats;
    QString m_lastError;
    
    // 会话管理
    struct SessionInfo {
        std::shared_ptr<Session::ICommunicationSession> session;
        QString sessionType;
        ConfigParameters config;
        qint64 createTime;
    };
    
    QMap<QString, SessionInfo> m_sessions;
    
    // 内部方法
    void updateManagerStatistics();
    void connectSessionSignals(const QString& sessionId, std::shared_ptr<Session::ICommunicationSession> session);
    void disconnectSessionSignals(std::shared_ptr<Session::ICommunicationSession> session);
    std::shared_ptr<Session::ICommunicationSession> createSessionInstance(const QString& sessionType);
};

BasicCommunicationManager::BasicCommunicationManager(QObject* parent)
    : ICommunicationManager(parent)
    , m_initialized(false)
{
    resetStatistics();
}

BasicCommunicationManager::~BasicCommunicationManager() {
    if (m_initialized) {
        shutdown();
    }
}

// === 管理器生命周期 ===

SimpleResult BasicCommunicationManager::initialize(const ConfigParameters& config) {
    QMutexLocker locker(&m_mutex);
    
    if (m_initialized) {
        return SimpleResult(true);
    }
    
    m_lastError.clear();
    m_globalConfig = config;
    
    // 应用全局配置
    int maxSessions = config.value("maxSessions", 100).toInt();
    if (maxSessions <= 0) {
        m_lastError = "Invalid maxSessions configuration";
        return SimpleResult::failure(m_lastError);
    }
    
    m_initialized = true;
    emit managerStatusChanged(true);
    
    qDebug() << "Communication manager initialized with max sessions:" << maxSessions;
    return SimpleResult(true);
}

SimpleResult BasicCommunicationManager::shutdown() {
    QMutexLocker locker(&m_mutex);
    
    if (!m_initialized) {
        return SimpleResult(true);
    }
    
    // 关闭所有会话
    locker.unlock();
    closeAllSessions();
    locker.relock();
    
    // 清理所有会话
    for (auto it = m_sessions.begin(); it != m_sessions.end(); ++it) {
        disconnectSessionSignals(it.value().session);
    }
    m_sessions.clear();
    
    m_initialized = false;
    emit managerStatusChanged(false);
    
    qDebug() << "Communication manager shutdown completed";
    return SimpleResult(true);
}

bool BasicCommunicationManager::isInitialized() const {
    QMutexLocker locker(&m_mutex);
    return m_initialized;
}

// === 会话管理 ===

SimpleResult BasicCommunicationManager::createSession(const QString& sessionId, 
                                                     const QString& sessionType, 
                                                     const ConfigParameters& config) {
    QMutexLocker locker(&m_mutex);
    
    if (!m_initialized) {
        m_lastError = "Manager not initialized";
        return SimpleResult::failure(m_lastError);
    }
    
    if (sessionId.isEmpty()) {
        m_lastError = "Empty session ID";
        return SimpleResult::failure(m_lastError);
    }
    
    if (m_sessions.contains(sessionId)) {
        m_lastError = QString("Session '%1' already exists").arg(sessionId);
        return SimpleResult::failure(m_lastError);
    }
    
    // 检查会话数量限制
    int maxSessions = m_globalConfig.value("maxSessions", 100).toInt();
    if (m_sessions.size() >= maxSessions) {
        m_lastError = QString("Maximum session limit (%1) reached").arg(maxSessions);
        return SimpleResult::failure(m_lastError);
    }
    
    // 创建会话实例
    auto session = createSessionInstance(sessionType);
    if (!session) {
        m_lastError = QString("Failed to create session of type '%1'").arg(sessionType);
        return SimpleResult::failure(m_lastError);
    }
    
    // 存储会话信息
    SessionInfo sessionInfo;
    sessionInfo.session = session;
    sessionInfo.sessionType = sessionType;
    sessionInfo.config = config;
    sessionInfo.createTime = QDateTime::currentMSecsSinceEpoch();
    
    m_sessions[sessionId] = sessionInfo;
    
    // 连接会话信号
    connectSessionSignals(sessionId, session);
    
    updateManagerStatistics();
    emit sessionCreated(sessionId, sessionType);
    
    qDebug() << "Created session:" << sessionId << "type:" << sessionType;
    return SimpleResult(true);
}

SimpleResult BasicCommunicationManager::destroySession(const QString& sessionId) {
    QMutexLocker locker(&m_mutex);
    
    if (!m_sessions.contains(sessionId)) {
        m_lastError = QString("Session '%1' not found").arg(sessionId);
        return SimpleResult::failure(m_lastError);
    }
    
    auto sessionInfo = m_sessions[sessionId];
    
    // 关闭会话
    if (sessionInfo.session && sessionInfo.session->isSessionOpen()) {
        sessionInfo.session->closeSession();
    }
    
    // 断开信号连接
    disconnectSessionSignals(sessionInfo.session);
    
    // 移除会话
    m_sessions.remove(sessionId);
    
    updateManagerStatistics();
    emit sessionDestroyed(sessionId);
    
    qDebug() << "Destroyed session:" << sessionId;
    return SimpleResult(true);
}

std::shared_ptr<Session::ICommunicationSession> BasicCommunicationManager::getSession(const QString& sessionId) {
    QMutexLocker locker(&m_mutex);
    
    if (m_sessions.contains(sessionId)) {
        return m_sessions[sessionId].session;
    }
    
    return nullptr;
}

bool BasicCommunicationManager::hasSession(const QString& sessionId) const {
    QMutexLocker locker(&m_mutex);
    return m_sessions.contains(sessionId);
}

// === 批量操作 ===

SimpleResult BasicCommunicationManager::openAllSessions() {
    QMutexLocker locker(&m_mutex);
    
    QStringList failedSessions;
    
    for (auto it = m_sessions.begin(); it != m_sessions.end(); ++it) {
        if (it.value().session && !it.value().session->isSessionOpen()) {
            auto result = it.value().session->openSession(it.value().config);
            if (!result.isSuccess) {
                failedSessions.append(it.key());
            }
        }
    }
    
    if (!failedSessions.isEmpty()) {
        m_lastError = QString("Failed to open sessions: %1").arg(failedSessions.join(", "));
        return SimpleResult::failure(m_lastError);
    }
    
    return SimpleResult(true);
}

SimpleResult BasicCommunicationManager::closeAllSessions() {
    QMutexLocker locker(&m_mutex);
    
    for (auto it = m_sessions.begin(); it != m_sessions.end(); ++it) {
        if (it.value().session && it.value().session->isSessionOpen()) {
            it.value().session->closeSession();
        }
    }
    
    return SimpleResult(true);
}

QStringList BasicCommunicationManager::getAllSessionIds() const {
    QMutexLocker locker(&m_mutex);
    return m_sessions.keys();
}

QStringList BasicCommunicationManager::getSessionsByStatus(ConnectionStatus status) const {
    QMutexLocker locker(&m_mutex);
    
    QStringList matchingSessions;
    
    for (auto it = m_sessions.begin(); it != m_sessions.end(); ++it) {
        if (it.value().session && it.value().session->getSessionStatus() == status) {
            matchingSessions.append(it.key());
        }
    }
    
    return matchingSessions;
}

// === 配置管理 ===

SimpleResult BasicCommunicationManager::updateSessionConfig(const QString& sessionId, 
                                                           const ConfigParameters& config) {
    QMutexLocker locker(&m_mutex);
    
    if (!m_sessions.contains(sessionId)) {
        m_lastError = QString("Session '%1' not found").arg(sessionId);
        return SimpleResult::failure(m_lastError);
    }
    
    m_sessions[sessionId].config = config;
    return SimpleResult(true);
}

ConfigParameters BasicCommunicationManager::getSessionConfig(const QString& sessionId) const {
    QMutexLocker locker(&m_mutex);
    
    if (m_sessions.contains(sessionId)) {
        return m_sessions[sessionId].config;
    }
    
    return ConfigParameters();
}

SimpleResult BasicCommunicationManager::setGlobalConfig(const ConfigParameters& config) {
    QMutexLocker locker(&m_mutex);
    m_globalConfig = config;
    return SimpleResult(true);
}

ConfigParameters BasicCommunicationManager::getGlobalConfig() const {
    QMutexLocker locker(&m_mutex);
    return m_globalConfig;
}

// === 状态查询 ===

DeviceStatistics BasicCommunicationManager::getManagerStatistics() const {
    QMutexLocker locker(&m_mutex);
    return m_managerStats;
}

DeviceStatistics BasicCommunicationManager::getSessionStatistics(const QString& sessionId) const {
    QMutexLocker locker(&m_mutex);
    
    if (m_sessions.contains(sessionId) && m_sessions[sessionId].session) {
        return m_sessions[sessionId].session->getStatistics();
    }
    
    return DeviceStatistics();
}

QString BasicCommunicationManager::errorString() const {
    QMutexLocker locker(&m_mutex);
    return m_lastError;
}

void BasicCommunicationManager::resetStatistics() {
    QMutexLocker locker(&m_mutex);
    m_managerStats = DeviceStatistics();
    m_managerStats.lastActivity = QDateTime::currentMSecsSinceEpoch();
    m_lastError.clear();
}

int BasicCommunicationManager::getSessionCount() const {
    QMutexLocker locker(&m_mutex);
    return m_sessions.size();
}

// === 监控和诊断 ===

SimpleResult BasicCommunicationManager::performHealthCheck() {
    QMutexLocker locker(&m_mutex);
    
    int totalSessions = m_sessions.size();
    int activeSessions = 0;
    int errorSessions = 0;
    
    for (auto it = m_sessions.begin(); it != m_sessions.end(); ++it) {
        if (it.value().session) {
            ConnectionStatus status = it.value().session->getSessionStatus();
            if (status == ConnectionStatus::Connected) {
                activeSessions++;
            } else if (status == ConnectionStatus::Error) {
                errorSessions++;
            }
        }
    }
    
    qDebug() << "Health check - Total:" << totalSessions 
             << "Active:" << activeSessions 
             << "Errors:" << errorSessions;
    
    return SimpleResult(true);
}

QString BasicCommunicationManager::getStatusReport() const {
    QMutexLocker locker(&m_mutex);
    
    int totalSessions = m_sessions.size();
    int activeSessions = getSessionsByStatus(ConnectionStatus::Connected).size();
    int errorSessions = getSessionsByStatus(ConnectionStatus::Error).size();
    
    qint64 uptime = QDateTime::currentMSecsSinceEpoch() - m_managerStats.lastActivity;
    
    return QString("Communication Manager Status Report:\n"
                   "  Initialized: %1\n"
                   "  Total Sessions: %2\n"
                   "  Active Sessions: %3\n"
                   "  Error Sessions: %4\n"
                   "  Manager Uptime: %5ms\n"
                   "  Max Sessions: %6")
           .arg(m_initialized ? "Yes" : "No")
           .arg(totalSessions)
           .arg(activeSessions)
           .arg(errorSessions)
           .arg(uptime)
           .arg(m_globalConfig.value("maxSessions", 100).toInt());
}

// === 私有槽函数 ===

void BasicCommunicationManager::onSessionStatusChanged(ConnectionStatus status) {
    // 查找发送信号的会话
    QObject* sender = QObject::sender();
    for (auto it = m_sessions.begin(); it != m_sessions.end(); ++it) {
        if (it.value().session.get() == sender) {
            emit sessionStatusChanged(it.key(), status);
            break;
        }
    }
}

void BasicCommunicationManager::onSessionError(const QString& error) {
    emit managerError(QString("Session error: %1").arg(error));
}

// === 私有辅助方法 ===

void BasicCommunicationManager::updateManagerStatistics() {
    m_managerStats.lastActivity = QDateTime::currentMSecsSinceEpoch();
}

void BasicCommunicationManager::connectSessionSignals(const QString& sessionId, 
                                                     std::shared_ptr<Session::ICommunicationSession> session) {
    Q_UNUSED(sessionId)
    
    connect(session.get(), &Session::ICommunicationSession::sessionStatusChanged,
            this, &BasicCommunicationManager::onSessionStatusChanged);
    connect(session.get(), &Session::ICommunicationSession::sessionError,
            this, &BasicCommunicationManager::onSessionError);
}

void BasicCommunicationManager::disconnectSessionSignals(std::shared_ptr<Session::ICommunicationSession> session) {
    if (session) {
        disconnect(session.get(), nullptr, this, nullptr);
    }
}

std::shared_ptr<Session::ICommunicationSession> BasicCommunicationManager::createSessionInstance(const QString& sessionType) {
    // 这里应该使用工厂模式创建具体的会话实例
    // 暂时返回nullptr，实际实现中会使用SessionFactory
    Q_UNUSED(sessionType)
    
    qWarning() << "Session creation not implemented for type:" << sessionType;
    return nullptr;
}

} // namespace Manager
} // namespace Communication
} // namespace LA

#include "BasicCommunicationManager.moc"