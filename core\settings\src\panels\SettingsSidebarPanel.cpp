#include "LA/Settings/panels/SettingsSidebarPanel.h"
#include "LA/Settings/Settings.h"
#include <QApplication>
#include <QDebug>
#include <QMessageBox>

namespace LA {
namespace Settings {

SettingsSidebarPanel::SettingsSidebarPanel(QWidget *parent)
    : SidebarPanelBase(QStringLiteral("settings"), tr("设置"), QIcon(QStringLiteral(":/icons/settings.png")), parent),
      m_mainLayout(nullptr),
      m_splitter(nullptr),
      m_categoryGroup(nullptr),
      m_categoryList(nullptr),
      m_panelGroup(nullptr),
      m_panelStack(nullptr),
      m_noPanelLabel(nullptr),
      m_buttonLayout(nullptr),
      m_openFullSettingsButton(nullptr),
      m_applyButton(nullptr),
      m_resetButton(nullptr),
      m_settingsManager(nullptr),
      m_hasUnsavedChanges(false) {

    qDebug() << "SettingsSidebarPanel created";
}

bool SettingsSidebarPanel::initializePanel() {
    try {
        // 获取设置管理器实例
        m_settingsManager = SettingsManager::instance();
        if (!m_settingsManager) {
            qWarning() << "Failed to get SettingsManager instance";
            return false;
        }

        // 设置UI
        setupUI();
        connectSignals();
        loadSettingsPanels();

        qDebug() << "SettingsSidebarPanel initialized successfully";
        return true;
    } catch (const std::exception &e) {
        qCritical() << "Exception in SettingsSidebarPanel::initializePanel:" << e.what();
        return false;
    }
}

void SettingsSidebarPanel::activate() {
    SidebarPanelBase::activate();
    refreshPanelList();
    qDebug() << "SettingsSidebarPanel activated";
}

void SettingsSidebarPanel::deactivate() {
    // 检查是否有未保存的更改
    if (m_hasUnsavedChanges) {
        int ret = QMessageBox::question(
            this, tr("未保存的更改"), tr("您有未保存的设置更改，是否要保存？"), QMessageBox::Save | QMessageBox::Discard | QMessageBox::Cancel);

        if (ret == QMessageBox::Save) {
            onApplySettingsClicked();
        } else if (ret == QMessageBox::Cancel) {
            return;  // 取消停用
        }
    }

    SidebarPanelBase::deactivate();
    qDebug() << "SettingsSidebarPanel deactivated";
}

void SettingsSidebarPanel::setupUI() {
    // 主布局，使用主题系统设置间距
    m_mainLayout = new QVBoxLayout(this);
    
    // 获取主题系统度量，替换硬编码值
    auto themeManager = LA::Themes::ThemeManager::instance();
    int padding = themeManager.getMetric(LA::Themes::ThemeManager::Metric::PaddingMedium);
    int spacing = themeManager.getMetric(LA::Themes::ThemeManager::Metric::SpacingMedium);
    
    m_mainLayout->setContentsMargins(padding, padding, padding, padding);
    m_mainLayout->setSpacing(spacing);

    // 创建分割器
    m_splitter = new QSplitter(Qt::Horizontal, this);
    m_mainLayout->addWidget(m_splitter);

    // 设置左右面板
    setupCategoryList();
    setupPanelArea();
    setupActionButtons();

    // 优化分割器比例和尺寸，给设置内容更多空间
    m_splitter->setStretchFactor(0, 1);  // 类别列表区域
    m_splitter->setStretchFactor(1, 5);  // 设置面板区域（增加比例到5，给内容更多空间）
    
    // 使用响应式尺寸计算，适配不同屏幕
    int availableWidth = qMax(800, this->width());  // 获取可用宽度，最小800px
    int categoryWidth = qMin(180, availableWidth / 5);   // 类别列表宽度，减少到180px
    int panelWidth = availableWidth - categoryWidth;     // 面板区域使用剩余宽度
    m_splitter->setSizes({categoryWidth, panelWidth});
    
    // 设置更合理的最小尺寸，防止过度压缩
    m_splitter->widget(0)->setMinimumWidth(150);   // 类别列表最小宽度
    m_splitter->widget(1)->setMinimumWidth(500);   // 面板区域最小宽度增加到500px，给内容更多空间
    
    // 设置分割器样式，使用主题系统
    auto themeManager = LA::Themes::ThemeManager::instance();
    QColor handleColor = themeManager.getSemanticColor(LA::Themes::ThemeManager::ColorRole::SplitterHandle);
    int handleWidth = themeManager.getMetric(LA::Themes::ThemeManager::Metric::BorderWidthMedium);
    
    m_splitter->setStyleSheet(QString(
        "QSplitter::handle { background-color: %1; width: %2px; }"
        "QSplitter::handle:hover { background-color: %3; }"
    ).arg(handleColor.name())
     .arg(handleWidth)
     .arg(handleColor.lighter(120).name()));
}

void SettingsSidebarPanel::setupCategoryList() {
    // 类别组，应用主题系统样式
    m_categoryGroup = new QGroupBox(tr("设置类别"), this);
    QVBoxLayout *categoryLayout = new QVBoxLayout(m_categoryGroup);
    
    // 获取主题系统度量
    auto themeManager = LA::Themes::ThemeManager::instance();
    int padding = themeManager.getMetric(LA::Themes::ThemeManager::Metric::PaddingSmall);
    
    categoryLayout->setContentsMargins(padding, padding, padding, padding);

    // 类别列表，移除最大宽度限制
    m_categoryList = new QListWidget(m_categoryGroup);
    // m_categoryList->setMaximumWidth(150);  // 移除限制，让分割器控制尺寸
    m_categoryList->setAlternatingRowColors(true);
    
    // 应用主题系统样式
    themeManager.applyThemeToWidget(m_categoryList, LA::Themes::ThemeManager::ComponentType::Table);
    
    categoryLayout->addWidget(m_categoryList);
    m_splitter->addWidget(m_categoryGroup);
}

void SettingsSidebarPanel::setupPanelArea() {
    // 面板组
    m_panelGroup = new QGroupBox(tr("设置面板"), this);
    QVBoxLayout *panelLayout = new QVBoxLayout(m_panelGroup);
    
    // 设置更紧凑的边距，给内容更多空间
    auto themeManager = LA::Themes::ThemeManager::instance();
    int padding = themeManager.getMetric(LA::Themes::ThemeManager::Metric::PaddingSmall);
    panelLayout->setContentsMargins(padding, padding, padding, padding);
    panelLayout->setSpacing(padding);

    // 创建滚动区域，确保内容可以滚动
    QScrollArea *scrollArea = new QScrollArea(m_panelGroup);
    scrollArea->setWidgetResizable(true);  // 允许内容自适应大小
    scrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    scrollArea->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    scrollArea->setFrameShape(QFrame::NoFrame);  // 移除边框，更清爽
    
    // 应用主题样式到滚动区域
    themeManager.applyThemeToWidget(scrollArea, LA::Themes::ThemeManager::ComponentType::ScrollArea);

    // 面板堆栈
    m_panelStack = new QStackedWidget();
    m_panelStack->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);

    // 无面板标签，使用主题系统样式替换硬编码
    m_noPanelLabel = new QLabel(tr("请选择一个设置类别"));
    m_noPanelLabel->setAlignment(Qt::AlignCenter);
    
    // 使用主题系统颜色替换硬编码样式
    QColor textMuted = themeManager.getSemanticColor(LA::Themes::ThemeManager::ColorRole::TextMuted);
    QFont subtitleFont = themeManager.getFont(LA::Themes::ThemeManager::FontRole::Subtitle);
    
    m_noPanelLabel->setStyleSheet(QString("color: %1;").arg(textMuted.name()));
    m_noPanelLabel->setFont(subtitleFont);
    
    m_panelStack->addWidget(m_noPanelLabel);
    
    // 将面板堆栈放入滚动区域
    scrollArea->setWidget(m_panelStack);
    
    // 将滚动区域添加到面板布局
    panelLayout->addWidget(scrollArea);
    m_splitter->addWidget(m_panelGroup);
}

void SettingsSidebarPanel::setupActionButtons() {
    // 按钮布局，使用主题系统间距，设置合理的边距
    m_buttonLayout = new QHBoxLayout();
    
    auto themeManager = LA::Themes::ThemeManager::instance();
    int spacing = themeManager.getMetric(LA::Themes::ThemeManager::Metric::SpacingMedium);
    int padding = themeManager.getMetric(LA::Themes::ThemeManager::Metric::PaddingMedium);
    
    m_buttonLayout->setSpacing(spacing);
    m_buttonLayout->setContentsMargins(padding, padding, padding, padding);

    // 打开完整设置按钮 - 使用更紧凑的样式
    m_openFullSettingsButton = new QPushButton(tr("完整设置"), this);
    m_openFullSettingsButton->setToolTip(tr("打开完整的设置对话框"));
    m_openFullSettingsButton->setMaximumHeight(36);  // 限制按钮高度
    themeManager.applyThemeToWidget(m_openFullSettingsButton, LA::Themes::ThemeManager::ComponentType::Button);
    m_buttonLayout->addWidget(m_openFullSettingsButton);

    m_buttonLayout->addStretch();

    // 应用按钮，应用主题系统样式 - 使用更紧凑的样式
    m_applyButton = new QPushButton(tr("应用"), this);
    m_applyButton->setEnabled(false);
    m_applyButton->setMaximumHeight(36);  // 限制按钮高度
    themeManager.applyThemeToWidget(m_applyButton, LA::Themes::ThemeManager::ComponentType::Button);
    m_buttonLayout->addWidget(m_applyButton);

    // 重置按钮，应用主题系统样式 - 使用更紧凑的样式
    m_resetButton = new QPushButton(tr("重置"), this);
    m_resetButton->setMaximumHeight(36);  // 限制按钮高度
    themeManager.applyThemeToWidget(m_resetButton, LA::Themes::ThemeManager::ComponentType::Button);
    m_buttonLayout->addWidget(m_resetButton);

    // 在主布局最后添加按钮布局，确保它位于底部但不遮挡内容
    m_mainLayout->addLayout(m_buttonLayout);
}

void SettingsSidebarPanel::connectSignals() {
    // 类别选择变化
    connect(m_categoryList, &QListWidget::currentRowChanged, this, &SettingsSidebarPanel::onCategorySelectionChanged);

    // 按钮点击
    connect(m_openFullSettingsButton, &QPushButton::clicked, this, &SettingsSidebarPanel::onOpenFullSettingsClicked);
    connect(m_applyButton, &QPushButton::clicked, this, &SettingsSidebarPanel::onApplySettingsClicked);
    connect(m_resetButton, &QPushButton::clicked, this, &SettingsSidebarPanel::onResetSettingsClicked);

    // 设置管理器信号
    if (m_settingsManager) {
        connect(m_settingsManager, &SettingsManager::settingChanged, this, &SettingsSidebarPanel::onSettingsChanged);
    }
}

void SettingsSidebarPanel::loadSettingsPanels() {
    if (!m_settingsManager) {
        return;
    }

    // 获取所有注册的面板
    auto panelIds = m_settingsManager->getAllPanelIds();

    for (const QString &panelId : panelIds) {
        auto panel = m_settingsManager->getPanel(panelId);
        if (panel) {
            // 添加到类别列表
            m_categoryList->addItem(panel->getDisplayName());

            // 添加到面板堆栈
            m_panelStack->addWidget(panel->getWidget());
        }
    }

    qDebug() << "Loaded" << panelIds.size() << "settings panels";
}

void SettingsSidebarPanel::onCategorySelectionChanged() {
    int currentRow = m_categoryList->currentRow();
    if (currentRow >= 0 && currentRow < m_panelStack->count() - 1) {
        // +1 因为第一个是"无面板"标签
        m_panelStack->setCurrentIndex(currentRow + 1);
        updateCurrentPanel();
    }
}

void SettingsSidebarPanel::onOpenFullSettingsClicked() {
    // 创建并显示完整设置对话框
    auto dialog = createSettingsDialog(this);
    if (dialog) {
        dialog->exec();
        delete dialog;

        // 刷新当前面板
        refreshPanelList();
    }
}

void SettingsSidebarPanel::onApplySettingsClicked() {
    if (m_settingsManager) {
        m_settingsManager->saveAllSettings();
        m_hasUnsavedChanges = false;
        m_applyButton->setEnabled(false);

        QMessageBox::information(this, tr("设置"), tr("设置已保存"));
    }
}

void SettingsSidebarPanel::onResetSettingsClicked() {
    int ret = QMessageBox::question(this, tr("重置设置"), tr("确定要重置当前面板的设置吗？此操作不可撤销。"), QMessageBox::Yes | QMessageBox::No);

    if (ret == QMessageBox::Yes && m_settingsManager) {
        // 重置当前面板设置
        auto panelIds   = m_settingsManager->getAllPanelIds();
        int  currentRow = m_categoryList->currentRow();

        if (currentRow >= 0 && currentRow < panelIds.size()) {
            QString panelId = panelIds[currentRow];
            auto    panel   = m_settingsManager->getPanel(panelId);
            if (panel) {
                panel->resetSettings();
                m_hasUnsavedChanges = true;
                m_applyButton->setEnabled(true);
            }
        }
    }
}

void SettingsSidebarPanel::onSettingsChanged() {
    m_hasUnsavedChanges = true;
    m_applyButton->setEnabled(true);
}

void SettingsSidebarPanel::updateCurrentPanel() {
    auto panelIds   = m_settingsManager->getAllPanelIds();
    int  currentRow = m_categoryList->currentRow();

    if (currentRow >= 0 && currentRow < panelIds.size()) {
        m_currentPanelId = panelIds[currentRow];
        qDebug() << "Current panel changed to:" << m_currentPanelId;
    }
}

void SettingsSidebarPanel::refreshPanelList() {
    if (!m_settingsManager) {
        return;
    }

    // 重新加载面板列表
    m_categoryList->clear();

    // 清除面板堆栈（保留无面板标签）
    while (m_panelStack->count() > 1) {
        QWidget *widget = m_panelStack->widget(1);
        m_panelStack->removeWidget(widget);
    }

    // 重新加载
    loadSettingsPanels();
}

}  // namespace Settings
}  // namespace LA

#include "SettingsSidebarPanel.moc"
