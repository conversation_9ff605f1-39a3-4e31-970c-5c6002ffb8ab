# 配置验证模块 (Configuration Validation Module)

## 概述

配置验证模块提供了完整的配置数据验证功能，支持多种验证规则类型、并行验证、自定义验证函数以及详细的验证报告生成。

## 功能特性

### 核心功能
- **多种验证规则** - 支持类型检查、范围检查、长度检查、模式匹配等
- **规则管理** - 动态添加、删除、更新验证规则
- **并行验证** - 支持多线程并行验证提高性能
- **自定义验证器** - 允许注册自定义验证函数
- **详细报告** - 生成包含统计信息的详细验证报告

### 高级功能
- **规则集管理** - 支持规则集的创建、保存和加载
- **Schema验证** - 支持JSON Schema格式的配置验证
- **验证缓存** - 缓存验证结果提高重复验证性能
- **统计分析** - 收集验证统计信息和性能指标
- **规则导入导出** - 支持规则的批量导入导出

## 验证规则类型

| 规则类型 | 描述 | 参数 | 示例 |
|----------|------|------|------|
| `TYPE_CHECK` | 数据类型验证 | expectedValue | string, int, bool |
| `RANGE_CHECK` | 数值范围验证 | minValue, maxValue | 1-100 |
| `LENGTH_CHECK` | 长度验证 | minValue, maxValue | 字符串/数组长度 |
| `PATTERN_CHECK` | 正则表达式验证 | pattern | 邮箱、IP地址 |
| `ENUM_CHECK` | 枚举值验证 | allowedValues | [red, green, blue] |
| `REQUIRED_CHECK` | 必需字段验证 | - | 字段不能为空 |
| `DEPENDENCY_CHECK` | 依赖关系验证 | dependencies | 条件字段依赖 |
| `CUSTOM_CHECK` | 自定义验证 | customFunction | 用户定义逻辑 |
| `SCHEMA_CHECK` | Schema验证 | - | JSON Schema |

## 使用方法

### 基本验证

```cpp
#include <LA/Support/Config/Validation/ConfigValidator.h>

using namespace LA::Support::Config;

// 创建验证器
auto factory = std::make_unique<ConfigValidatorFactory>();
auto validator = factory->createConfigValidator();

// 创建验证规则
ValidationRule rule;
rule.id = "port_range_check";
rule.name = "Port Range Validation";
rule.type = ValidationRuleType::RANGE_CHECK;
rule.fieldPath = "server.port";
rule.minValue = 1;
rule.maxValue = 65535;
rule.severity = ValidationSeverity::ERROR;

// 添加规则
validator->addRule(rule);

// 验证数据
QVariantMap config;
config["server"] = QVariantMap{{"port", 8080}, {"host", "localhost"}};

auto result = validator->validate(config);
if (result.success) {
    ValidationReport report = result.data;
    qDebug() << "Validation result:" << (report.isValid ? "PASSED" : "FAILED");
    qDebug() << "Total rules:" << report.totalRules;
    qDebug() << "Passed:" << report.passedRules;
    qDebug() << "Failed:" << report.failedRules;
} else {
    qDebug() << "Validation error:" << result.message;
}
```

### 使用验证规则构建器

```cpp
// 使用构建器创建复杂规则
ValidationRule emailRule = ValidationRuleBuilder()
    .id("email_validation")
    .name("Email Format Validation")
    .description("Validates email address format")
    .type(ValidationRuleType::PATTERN_CHECK)
    .field("user.email")
    .pattern(R"(^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$)")
    .severity(ValidationSeverity::ERROR)
    .category("user_data")
    .group("format_validation")
    .build();

validator->addRule(emailRule);

// 创建数据库配置验证规则
QList<ValidationRule> dbRules;

dbRules.append(ValidationRuleBuilder()
    .id("db_host_required")
    .name("Database Host Required")
    .type(ValidationRuleType::REQUIRED_CHECK)
    .field("database.host")
    .severity(ValidationSeverity::ERROR)
    .build());

dbRules.append(ValidationRuleBuilder()
    .id("db_port_range")
    .name("Database Port Range")
    .type(ValidationRuleType::RANGE_CHECK)
    .field("database.port")
    .range(1, 65535)
    .severity(ValidationSeverity::ERROR)
    .build());

dbRules.append(ValidationRuleBuilder()
    .id("db_type_enum")
    .name("Database Type")
    .type(ValidationRuleType::ENUM_CHECK)
    .field("database.type")
    .allowedValues({"mysql", "postgresql", "sqlite", "oracle"})
    .severity(ValidationSeverity::ERROR)
    .build());

validator->addRules(dbRules);
```

### 自定义验证器

```cpp
// 注册自定义验证函数
validator->registerCustomValidator("password_strength", 
    [](const QVariant& value, const ValidationRule& rule, const ValidationContext& context) -> ValidationResult {
        QString password = value.toString();
        ValidationResult result(rule.id, ValidationStatus::PASSED, "Password validation passed");
        
        // 检查密码强度
        bool hasUpper = password.contains(QRegularExpression("[A-Z]"));
        bool hasLower = password.contains(QRegularExpression("[a-z]"));
        bool hasDigit = password.contains(QRegularExpression("[0-9]"));
        bool hasSpecial = password.contains(QRegularExpression("[!@#$%^&*(),.?\":{}|<>]"));
        
        int strength = hasUpper + hasLower + hasDigit + hasSpecial;
        
        if (password.length() < 8) {
            result.status = ValidationStatus::FAILED;
            result.message = "Password must be at least 8 characters long";
        } else if (strength < 3) {
            result.status = ValidationStatus::WARNING;
            result.message = "Password should contain uppercase, lowercase, digits, and special characters";
        } else {
            result.message = "Password strength is good";
        }
        
        return result;
    });

// 使用自定义验证器
ValidationRule passwordRule = ValidationRuleBuilder()
    .id("password_strength_check")
    .name("Password Strength Check")
    .type(ValidationRuleType::CUSTOM_CHECK)
    .field("user.password")
    .customFunction("password_strength")
    .severity(ValidationSeverity::WARNING)
    .build();

validator->addRule(passwordRule);
```

### 并行验证

```cpp
// 启用并行验证
ConfigParameters config;
config["enable_parallel"] = true;
config["max_threads"] = 8;
validator->initialize(config);

// 配置验证选项
ValidationOptions options;
options.parallel = true;
options.timeoutMs = 60000;  // 60秒超时
options.stopOnFirstError = false;
options.includeWarnings = true;

// 执行并行验证
auto result = validator->validate(largeConfigData, options);
```

### Schema验证

```cpp
// JSON Schema验证
QVariantMap schema;
schema["type"] = "object";
schema["required"] = QStringList{"name", "version", "settings"};

QVariantMap properties;
properties["name"] = QVariantMap{{"type", "string"}, {"minLength", 1}};
properties["version"] = QVariantMap{{"type", "string"}, {"pattern", R"(\d+\.\d+\.\d+)"}};
properties["settings"] = QVariantMap{{"type", "object"}};
schema["properties"] = properties;

// 验证配置是否符合Schema
auto schemaResult = validator->validateSchema(config, schema);
if (schemaResult.success) {
    ValidationReport report = schemaResult.data;
    for (const auto& result : report.results) {
        qDebug() << "Rule:" << result.ruleName 
                 << "Status:" << validationStatusToString(result.status)
                 << "Message:" << result.message;
    }
}
```

### 规则集管理

```cpp
// 创建标准规则集
QList<ValidationRule> networkRules = StandardValidationRules::createNetworkConfigRules();
QList<ValidationRule> databaseRules = StandardValidationRules::createDatabaseConfigRules();
QList<ValidationRule> securityRules = StandardValidationRules::createSecurityConfigRules();

// 创建综合规则集
QList<ValidationRule> appRules;
appRules.append(networkRules);
appRules.append(databaseRules);
appRules.append(securityRules);

// 保存规则集
validator->createRuleSet("application_config", appRules);
validator->saveRuleSet("application_config", "rulesets/app_config.json");

// 加载规则集
validator->loadRuleSet("application_config");

// 导出和导入规则
auto exportResult = validator->exportRules("json");
if (exportResult.success) {
    QByteArray rulesData = exportResult.data;
    // 保存到文件或传输
}

// 从文件导入规则
QFile file("imported_rules.json");
if (file.open(QIODevice::ReadOnly)) {
    QByteArray data = file.readAll();
    validator->importRules(data, "json", false);  // 不覆盖现有规则
}
```

### 验证报告分析

```cpp
// 详细分析验证报告
auto validationResult = validator->validate(config);
if (validationResult.success) {
    ValidationReport report = validationResult.data;
    
    qDebug() << "=== Validation Report ===";
    qDebug() << "Config:" << report.configName;
    qDebug() << "Timestamp:" << report.timestamp.toString();
    qDebug() << "Total time:" << report.totalTime << "ms";
    qDebug() << "Overall result:" << (report.isValid ? "VALID" : "INVALID");
    
    qDebug() << "\n=== Statistics ===";
    qDebug() << "Total rules:" << report.totalRules;
    qDebug() << "Passed:" << report.passedRules;
    qDebug() << "Failed:" << report.failedRules;
    qDebug() << "Warnings:" << report.warningCount;
    qDebug() << "Errors:" << report.errorCount;
    
    qDebug() << "\n=== Detailed Results ===";
    for (const auto& result : report.results) {
        if (result.status != ValidationStatus::PASSED) {
            qDebug() << QString("Rule: %1 | Field: %2 | Status: %3 | Message: %4")
                        .arg(result.ruleName)
                        .arg(result.fieldPath)
                        .arg(validationStatusToString(result.status))
                        .arg(result.message);
        }
    }
    
    // 按严重级别分组
    QMap<ValidationSeverity, QList<ValidationResult>> resultsBySeverity;
    for (const auto& result : report.results) {
        resultsBySeverity[result.severity].append(result);
    }
    
    qDebug() << "\n=== Results by Severity ===";
    for (auto it = resultsBySeverity.begin(); it != resultsBySeverity.end(); ++it) {
        ValidationSeverity severity = it.key();
        QList<ValidationResult> results = it.value();
        qDebug() << validationSeverityToString(severity) << ":" << results.size() << "results";
    }
}
```

## 标准验证规则集

### 网络配置验证

```cpp
QList<ValidationRule> networkRules = StandardValidationRules::createNetworkConfigRules();
// 包含以下验证：
// - 主机名格式验证
// - 端口范围验证 (1-65535)
// - IP地址格式验证
// - URL格式验证
// - 网络超时设置验证
```

### 数据库配置验证

```cpp
QList<ValidationRule> databaseRules = StandardValidationRules::createDatabaseConfigRules();
// 包含以下验证：
// - 数据库类型枚举验证
// - 连接字符串格式验证
// - 连接池设置验证
// - 用户名和密码长度验证
// - 数据库名称格式验证
```

### 安全配置验证

```cpp
QList<ValidationRule> securityRules = StandardValidationRules::createSecurityConfigRules();
// 包含以下验证：
// - 密码强度验证
// - 加密算法枚举验证
// - 密钥长度验证
// - 安全端口验证
// - 证书路径验证
```

## 验证选项配置

### ValidationOptions 详解

```cpp
ValidationOptions options;

// 基本选项
options.enabled = true;                      // 启用验证
options.stopOnFirstError = false;           // 不在第一个错误时停止
options.includeWarnings = true;             // 包含警告
options.includeInfo = false;                // 不包含信息级别
options.minSeverity = ValidationSeverity::WARNING; // 最小严重级别

// 规则过滤
options.enabledRules = {"rule1", "rule2"};  // 仅启用特定规则
options.disabledRules = {"rule3"};          // 禁用特定规则
options.enabledCategories = {"security"};   // 仅启用特定分类
options.disabledCategories = {"performance"}; // 禁用特定分类

// 性能设置
options.maxErrors = 100;                     // 最大错误数
options.maxWarnings = 100;                  // 最大警告数
options.timeoutMs = 30000;                  // 超时时间（毫秒）
options.parallel = true;                    // 启用并行验证
options.threadCount = 4;                    // 线程数

// 自定义选项
options.customOptions["cache_results"] = true;
options.customOptions["detailed_messages"] = true;
```

## 性能优化

### 并行验证配置

```cpp
// 优化并行验证性能
ConfigParameters config;
config["enable_parallel"] = true;
config["max_threads"] = std::thread::hardware_concurrency(); // 使用CPU核心数
config["enable_caching"] = true;           // 启用结果缓存
config["max_cache_size"] = 5000;          // 缓存大小

validator->initialize(config);

// 配置验证选项
ValidationOptions options;
options.parallel = true;
options.threadCount = 0;  // 0表示使用所有可用线程
```

### 缓存策略

```cpp
// 启用验证结果缓存
ConfigParameters config;
config["enable_caching"] = true;
config["max_cache_size"] = 10000;          // 最大缓存条目数
config["cache_ttl"] = 3600;               // 缓存生存时间（秒）

// 预热缓存
validator->validate(commonConfig, options); // 缓存常用配置的验证结果
```

### 规则优化

```cpp
// 按优先级排序规则，快速失败的规则优先
ValidationRule criticalRule = ValidationRuleBuilder()
    .priority(100)  // 高优先级
    .severity(ValidationSeverity::CRITICAL)
    .build();

ValidationRule warningRule = ValidationRuleBuilder()
    .priority(10)   // 低优先级  
    .severity(ValidationSeverity::WARNING)
    .build();
```

## 错误处理和调试

### 详细错误信息

```cpp
// 启用详细错误信息
ValidationOptions options;
options.customOptions["detailed_messages"] = true;
options.customOptions["include_stack_trace"] = true;

auto result = validator->validate(config, options);
if (!result.success) {
    qDebug() << "Validation failed:" << result.message;
    
    // 检查每个验证结果
    ValidationReport report = result.data;
    for (const auto& validationResult : report.results) {
        if (validationResult.status == ValidationStatus::FAILED) {
            qDebug() << "Failed rule:" << validationResult.ruleName;
            qDebug() << "Field path:" << validationResult.fieldPath;
            qDebug() << "Expected:" << validationResult.expectedValue;
            qDebug() << "Actual:" << validationResult.actualValue;
            qDebug() << "Message:" << validationResult.message;
            qDebug() << "Suggestion:" << validationResult.suggestion;
        }
    }
}
```

### 性能分析

```cpp
// 获取验证器统计信息
auto stats = validator->getStatistics();
for (const auto& stat : stats) {
    qDebug() << stat.first << ":" << stat.second;
}

// 分析规则性能
QList<ValidationRule> allRules = validator->getAllRules();
for (const auto& rule : allRules) {
    // 获取规则统计信息（需要在实现中添加）
    qDebug() << "Rule" << rule.name << "- Usage count:" << rule.metadata["usage_count"];
    qDebug() << "Average execution time:" << rule.metadata["avg_execution_time"];
}
```

## 测试和验证

### 单元测试示例

```cpp
#include <QtTest/QtTest>

class ConfigValidatorTest : public QObject {
    Q_OBJECT

private slots:
    void initTestCase();
    void testBasicValidation();
    void testRangeValidation();
    void testPatternValidation();
    void testCustomValidation();
    void testParallelValidation();
    void testRuleSetManagement();
    void cleanupTestCase();

private:
    std::shared_ptr<IConfigValidator> validator;
};

void ConfigValidatorTest::testRangeValidation() {
    // 创建范围验证规则
    ValidationRule rule = ValidationRuleBuilder()
        .id("test_range")
        .type(ValidationRuleType::RANGE_CHECK)
        .field("test.value")
        .range(1, 100)
        .build();
    
    validator->addRule(rule);
    
    // 测试有效值
    QVariantMap validConfig{{"test", QVariantMap{{"value", 50}}}};
    auto result = validator->validate(validConfig);
    QVERIFY(result.success);
    QVERIFY(result.data.isValid);
    
    // 测试无效值
    QVariantMap invalidConfig{{"test", QVariantMap{{"value", 150}}}};
    result = validator->validate(invalidConfig);
    QVERIFY(result.success);
    QVERIFY(!result.data.isValid);
    QVERIFY(result.data.errorCount > 0);
}
```

## 扩展和自定义

### 自定义验证规则类型

```cpp
// 扩展验证规则类型
enum class CustomValidationRuleType {
    IP_ADDRESS_CHECK = 1000,
    URL_CHECK,
    FILE_PATH_CHECK,
    CUSTOM_BUSINESS_LOGIC
};

// 注册自定义验证函数
validator->registerCustomValidator("ip_address_check",
    [](const QVariant& value, const ValidationRule& rule, const ValidationContext& context) -> ValidationResult {
        QString ip = value.toString();
        QHostAddress address(ip);
        
        ValidationResult result(rule.id, ValidationStatus::PASSED, "IP address is valid");
        
        if (address.isNull()) {
            result.status = ValidationStatus::FAILED;
            result.message = "Invalid IP address format: " + ip;
        }
        
        return result;
    });
```

### 自定义报告生成器

```cpp
class CustomReportGenerator {
public:
    static QString generateHtmlReport(const ValidationReport& report) {
        QString html = "<html><head><title>Validation Report</title></head><body>";
        html += QString("<h1>Validation Report - %1</h1>").arg(report.configName);
        html += QString("<p>Timestamp: %1</p>").arg(report.timestamp.toString());
        html += QString("<p>Total Time: %1 ms</p>").arg(report.totalTime);
        html += QString("<p>Overall Result: %1</p>").arg(report.isValid ? "VALID" : "INVALID");
        
        html += "<h2>Statistics</h2><table>";
        html += QString("<tr><td>Total Rules:</td><td>%1</td></tr>").arg(report.totalRules);
        html += QString("<tr><td>Passed:</td><td>%1</td></tr>").arg(report.passedRules);
        html += QString("<tr><td>Failed:</td><td>%1</td></tr>").arg(report.failedRules);
        html += QString("<tr><td>Warnings:</td><td>%1</td></tr>").arg(report.warningCount);
        html += QString("<tr><td>Errors:</td><td>%1</td></tr>").arg(report.errorCount);
        html += "</table>";
        
        html += "<h2>Detailed Results</h2><table>";
        html += "<tr><th>Rule</th><th>Field</th><th>Status</th><th>Message</th></tr>";
        
        for (const auto& result : report.results) {
            if (result.status != ValidationStatus::PASSED) {
                html += QString("<tr><td>%1</td><td>%2</td><td>%3</td><td>%4</td></tr>")
                        .arg(result.ruleName)
                        .arg(result.fieldPath)
                        .arg(validationStatusToString(result.status))
                        .arg(result.message);
            }
        }
        
        html += "</table></body></html>";
        return html;
    }
    
    static QByteArray generateJsonReport(const ValidationReport& report) {
        QJsonObject json;
        json["reportId"] = report.reportId;
        json["configName"] = report.configName;
        json["timestamp"] = report.timestamp.toString(Qt::ISODate);
        json["totalTime"] = report.totalTime;
        json["isValid"] = report.isValid;
        
        QJsonObject stats;
        stats["totalRules"] = report.totalRules;
        stats["passedRules"] = report.passedRules;
        stats["failedRules"] = report.failedRules;
        stats["warningCount"] = report.warningCount;
        stats["errorCount"] = report.errorCount;
        json["statistics"] = stats;
        
        QJsonArray results;
        for (const auto& result : report.results) {
            QJsonObject resultObj;
            resultObj["ruleId"] = result.ruleId;
            resultObj["ruleName"] = result.ruleName;
            resultObj["status"] = validationStatusToString(result.status);
            resultObj["severity"] = validationSeverityToString(result.severity);
            resultObj["fieldPath"] = result.fieldPath;
            resultObj["message"] = result.message;
            resultObj["executionTime"] = result.executionTime;
            results.append(resultObj);
        }
        json["results"] = results;
        
        return QJsonDocument(json).toJson();
    }
};

// 使用自定义报告生成器
auto validationResult = validator->validate(config);
if (validationResult.success) {
    ValidationReport report = validationResult.data;
    
    // 生成HTML报告
    QString htmlReport = CustomReportGenerator::generateHtmlReport(report);
    QFile htmlFile("validation_report.html");
    if (htmlFile.open(QIODevice::WriteOnly)) {
        htmlFile.write(htmlReport.toUtf8());
    }
    
    // 生成JSON报告
    QByteArray jsonReport = CustomReportGenerator::generateJsonReport(report);
    QFile jsonFile("validation_report.json");
    if (jsonFile.open(QIODevice::WriteOnly)) {
        jsonFile.write(jsonReport);
    }
}
```

## 最佳实践

1. **规则设计原则**
   - 每个规则只检查一个特定方面
   - 使用清晰描述性的规则名称
   - 设置合适的严重级别
   - 提供有用的错误消息和修复建议

2. **性能优化**
   - 将快速失败的规则设置为高优先级
   - 合理使用并行验证
   - 启用结果缓存for常用配置
   - 定期清理过期规则

3. **错误处理**
   - 始终检查验证结果
   - 提供降级策略for验证失败
   - 记录详细错误信息用于调试
   - 实现超时处理机制

## 版本历史

- v1.0.0 - 初始版本，包含基本验证功能
- 计划功能：动态规则更新、分布式验证、机器学习辅助规则生成

## 相关模块

- [参数管理模块](../parameter/README.md) - 提供参数定义和管理
- [配置文件处理模块](../file/README.md) - 提供配置文件读写
- [基础类型模块](../../foundation/README.md) - 提供基础类型定义