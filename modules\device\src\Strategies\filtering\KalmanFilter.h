/**
 * @file KalmanFilter.h
 * @brief 卡尔曼滤波策略 - 四层架构第3层
 * 
 * 职责：
 * - 提供卡尔曼滤波算法
 * - 滤除测距数据噪声
 * - 提高测距精度和稳定性
 * - 支持参数动态调整
 */

#pragma once

#include "LA/Device/Core/Device.h"
#include <QObject>
#include <QString>
#include <QVariantMap>
#include <vector>
#include <memory>

namespace LA::Device::Strategy {

/**
 * @brief 卡尔曼滤波策略实现
 * 
 * 适用场景：
 * - 高噪声环境下的测距
 * - 要求高精度的测量场合
 * - 动态目标跟踪测距
 * - 实时数据处理需求
 */
class KalmanFilter : public QObject, public IStrategy {
    Q_OBJECT

public:
    explicit KalmanFilter(QObject* parent = nullptr);
    virtual ~KalmanFilter() = default;

    // === IStrategy接口实现 ===
    
    QString getStrategyType() const override { return "filtering"; }
    QString getStrategyName() const override { return "kalman"; }
    QVariantMap executeStrategy(const QVariantMap& input) override;
    QVariantMap getStrategyConfig() const override;
    bool setStrategyConfig(const QVariantMap& config) override;

    // === 卡尔曼滤波参数配置 ===
    
    /**
     * @brief 设置过程噪声协方差
     * @param Q 过程噪声方差 (默认0.01)
     */
    void setProcessNoise(double Q);
    
    /**
     * @brief 设置观测噪声协方差  
     * @param R 观测噪声方差 (默认0.1)
     */
    void setMeasurementNoise(double R);
    
    /**
     * @brief 设置初始估计误差协方差
     * @param P 初始协方差 (默认1.0)
     */
    void setInitialCovariance(double P);
    
    /**
     * @brief 设置状态转移矩阵参数
     * @param dt 时间间隔 (默认1.0)
     */
    void setStateTransition(double dt = 1.0);
    
    /**
     * @brief 重置滤波器状态
     */
    void reset();
    
    /**
     * @brief 获取滤波器状态
     * @return 状态信息
     */
    QVariantMap getFilterState() const;

Q_SIGNALS:
    void filterUpdated(double filteredValue, double gain);

private:
    // 卡尔曼滤波器状态变量
    struct KalmanState {
        double x;           // 状态估计 (距离)
        double v;           // 速度估计 (变化率)
        double P[2][2];     // 协方差矩阵
        double Q[2][2];     // 过程噪声协方差矩阵
        double R;           // 观测噪声方差
        double F[2][2];     // 状态转移矩阵
        double H[1][2];     // 观测矩阵
        bool initialized;   // 是否已初始化
        int updateCount;    // 更新次数
        double lastGain;    // 上次卡尔曼增益
    } m_state;
    
    // 滤波配置
    struct FilterConfig {
        double processNoise = 0.01;         // 过程噪声方差
        double measurementNoise = 0.1;      // 观测噪声方差  
        double initialCovariance = 1.0;     // 初始协方差
        double timeInterval = 1.0;          // 时间间隔
        bool adaptiveNoise = false;         // 自适应噪声
        double adaptiveThreshold = 2.0;     // 自适应阈值
        int windowSize = 10;                // 滑动窗口大小
    } m_config;
    
    // 历史数据 (用于自适应调整)
    std::vector<double> m_residualHistory;
    std::vector<double> m_gainHistory;
    
    // === 内部方法 ===
    
    /**
     * @brief 初始化滤波器
     * @param initialValue 初始测量值
     */
    void initializeFilter(double initialValue);
    
    /**
     * @brief 预测步骤
     */
    void predict();
    
    /**
     * @brief 更新步骤  
     * @param measurement 测量值
     * @return 滤波后的值
     */
    double update(double measurement);
    
    /**
     * @brief 计算卡尔曼增益
     * @return 卡尔曼增益
     */
    double calculateKalmanGain();
    
    /**
     * @brief 矩阵运算辅助函数
     */
    void matrixMultiply2x2(const double A[2][2], const double B[2][2], double result[2][2]);
    void matrixAdd2x2(const double A[2][2], const double B[2][2], double result[2][2]);
    void matrixInverse2x2(const double A[2][2], double result[2][2]);
    double matrixDeterminant2x2(const double A[2][2]);
    
    /**
     * @brief 自适应噪声调整
     * @param residual 残差值
     */
    void adaptNoiseParameters(double residual);
    
    /**
     * @brief 检测异常值
     * @param measurement 测量值
     * @param prediction 预测值
     * @return 是否为异常值
     */
    bool isOutlier(double measurement, double prediction);
    
    /**
     * @brief 格式化输出结果
     * @param filteredValue 滤波值
     * @param originalValue 原始值
     * @param gain 卡尔曼增益
     * @param residual 残差
     * @return 结果映射
     */
    QVariantMap formatResult(double filteredValue, double originalValue, 
                           double gain, double residual);
    
    /**
     * @brief 更新历史数据
     */
    void updateHistory(double residual, double gain);
    
    /**
     * @brief 计算滤波性能指标
     * @return 性能指标
     */
    QVariantMap calculatePerformanceMetrics();
};

/**
 * @brief 扩展卡尔曼滤波器 (用于非线性系统)
 */
class ExtendedKalmanFilter : public KalmanFilter {
    Q_OBJECT

public:
    explicit ExtendedKalmanFilter(QObject* parent = nullptr);
    
    QString getStrategyName() const override { return "extended_kalman"; }
    QVariantMap executeStrategy(const QVariantMap& input) override;
    
    /**
     * @brief 设置非线性状态函数
     * @param function 状态转移函数类型
     */
    void setNonlinearFunction(const QString& function);

private:
    QString m_nonlinearFunction = "linear";
    
    /**
     * @brief 计算雅可比矩阵
     * @param x 状态向量
     * @return 雅可比矩阵
     */
    void calculateJacobian(double x[2], double jacobian[2][2]);
    
    /**
     * @brief 非线性状态转移函数
     * @param x 输入状态
     * @param result 输出状态
     */
    void nonlinearStateFunction(const double x[2], double result[2]);
};

/**
 * @brief 无迹卡尔曼滤波器 (用于高度非线性系统)
 */
class UnscentedKalmanFilter : public KalmanFilter {
    Q_OBJECT

public:
    explicit UnscentedKalmanFilter(QObject* parent = nullptr);
    
    QString getStrategyName() const override { return "unscented_kalman"; }
    QVariantMap executeStrategy(const QVariantMap& input) override;
    
    /**
     * @brief 设置Sigma点参数
     * @param alpha Alpha参数 (默认0.001)
     * @param beta Beta参数 (默认2.0)
     * @param kappa Kappa参数 (默认0)
     */
    void setSigmaPointParams(double alpha = 0.001, double beta = 2.0, double kappa = 0.0);

private:
    double m_alpha = 0.001;
    double m_beta = 2.0;
    double m_kappa = 0.0;
    
    /**
     * @brief 生成Sigma点
     */
    void generateSigmaPoints(const double mean[2], const double cov[2][2], 
                           double sigmaPoints[5][2]);
    
    /**
     * @brief 无迹变换
     */
    void unscentedTransform(const double sigmaPoints[5][2], 
                          double mean[2], double cov[2][2]);
};

} // namespace LA::Device::Strategy