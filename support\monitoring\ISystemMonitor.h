#pragma once

#include <LA/Foundation/Core/CommonTypes.h>
#include <QDateTime>
#include <QObject>
#include <QString>
#include <QVariantMap>
#include <memory>

namespace LA {
namespace Support {
namespace Monitoring {

using namespace Foundation::Core;

/**
 * @brief 监控指标类型枚举
 */
enum class MetricType {
    CPU_USAGE = 0,          // CPU使用率
    MEMORY_USAGE = 1,       // 内存使用率
    DISK_USAGE = 2,         // 磁盘使用率
    NETWORK_IO = 3,         // 网络IO
    THREAD_COUNT = 4,       // 线程数量
    PROCESS_COUNT = 5,      // 进程数量
    HANDLE_COUNT = 6,       // 句柄数量
    ERROR_COUNT = 7,        // 错误计数
    WARNING_COUNT = 8,      // 警告计数
    RESPONSE_TIME = 9,      // 响应时间
    THROUGHPUT = 10,        // 吞吐量
    QUEUE_SIZE = 11,        // 队列大小
    CACHE_HIT_RATE = 12,    // 缓存命中率
    CONNECTION_COUNT = 13,  // 连接数
    CUSTOM = 99             // 自定义指标
};

/**
 * @brief 监控数据点
 */
struct MetricDataPoint {
    QString   name;         // 指标名称
    MetricType type;        // 指标类型
    double    value;        // 数值
    QString   unit;         // 单位
    QDateTime timestamp;    // 时间戳
    QString   description;  // 描述
    QString   source;       // 数据源
    QVariantMap metadata;   // 元数据

    MetricDataPoint() : type(MetricType::CUSTOM), value(0.0) {
        timestamp = QDateTime::currentDateTime();
    }
    
    MetricDataPoint(const QString& n, MetricType t, double v, const QString& u = QString())
        : name(n), type(t), value(v), unit(u) {
        timestamp = QDateTime::currentDateTime();
    }
};

/**
 * @brief 监控阈值配置
 */
struct MetricThreshold {
    double  warningThreshold;   // 警告阈值
    double  criticalThreshold;  // 严重阈值
    bool    enabled;            // 是否启用
    QString alertMessage;       // 告警消息模板
    
    MetricThreshold() : warningThreshold(80.0), criticalThreshold(90.0), enabled(true) {}
};

/**
 * @brief 监控配置
 */
struct MonitoringConfig {
    int     updateInterval;         // 更新间隔(毫秒)
    bool    enableHistoricalData;  // 启用历史数据
    int     historyRetentionDays;  // 历史数据保留天数
    bool    enableAlerts;          // 启用告警
    bool    enableAutoReporting;   // 启用自动报告
    int     reportInterval;        // 报告间隔(分钟)
    QString reportDirectory;       // 报告目录
    int     maxDataPoints;         // 最大数据点数
    bool    persistentStorage;     // 持久化存储
    
    MonitoringConfig() 
        : updateInterval(1000)
        , enableHistoricalData(true)
        , historyRetentionDays(30)
        , enableAlerts(true)
        , enableAutoReporting(false)
        , reportInterval(60)
        , reportDirectory("reports")
        , maxDataPoints(10000)
        , persistentStorage(false) {}
};

/**
 * @brief 告警级别枚举
 */
enum class AlertLevel {
    INFO = 0,       // 信息
    WARNING = 1,    // 警告
    CRITICAL = 2,   // 严重
    FATAL = 3       // 致命
};

/**
 * @brief 告警信息
 */
struct AlertInfo {
    QString         id;             // 告警ID
    QString         name;           // 告警名称
    AlertLevel      level;          // 告警级别
    MetricType      metricType;     // 关联指标类型
    QString         message;        // 告警消息
    QDateTime       triggeredTime;  // 触发时间
    QDateTime       resolvedTime;   // 解决时间
    bool            isResolved;     // 是否已解决
    QString         source;         // 告警源
    QVariantMap     context;        // 上下文信息
    
    AlertInfo() : level(AlertLevel::INFO), metricType(MetricType::CUSTOM), isResolved(false) {
        triggeredTime = QDateTime::currentDateTime();
    }
};

/**
 * @brief 系统监控器接口
 */
class ISystemMonitor : public QObject, public IManager {
    Q_OBJECT

public:
    virtual ~ISystemMonitor() = default;

    /**
     * @brief 设置监控配置
     */
    virtual void setConfig(const MonitoringConfig& config) = 0;

    /**
     * @brief 获取监控配置
     */
    virtual MonitoringConfig getConfig() const = 0;

    /**
     * @brief 启动监控
     */
    virtual SimpleResult startMonitoring() = 0;

    /**
     * @brief 停止监控
     */
    virtual SimpleResult stopMonitoring() = 0;

    /**
     * @brief 暂停监控
     */
    virtual SimpleResult pauseMonitoring() = 0;

    /**
     * @brief 恢复监控
     */
    virtual SimpleResult resumeMonitoring() = 0;

    /**
     * @brief 检查是否正在监控
     */
    virtual bool isMonitoring() const = 0;

    /**
     * @brief 注册指标
     */
    virtual bool registerMetric(const QString& name, MetricType type, const QString& unit = QString()) = 0;

    /**
     * @brief 注销指标
     */
    virtual void unregisterMetric(const QString& name) = 0;

    /**
     * @brief 更新指标值
     */
    virtual SimpleResult updateMetric(const QString& name, double value, const QString& source = QString()) = 0;

    /**
     * @brief 批量更新指标
     */
    virtual SimpleResult updateMetrics(const QList<MetricDataPoint>& dataPoints) = 0;

    /**
     * @brief 获取当前指标值
     */
    virtual Result<MetricDataPoint> getCurrentMetric(const QString& name) const = 0;

    /**
     * @brief 获取所有当前指标
     */
    virtual QList<MetricDataPoint> getAllCurrentMetrics() const = 0;

    /**
     * @brief 获取指标历史数据
     */
    virtual QList<MetricDataPoint> getMetricHistory(const QString& name, 
                                                   const QDateTime& startTime, 
                                                   const QDateTime& endTime) const = 0;

    /**
     * @brief 获取指标统计信息
     */
    virtual QVariantMap getMetricStatistics(const QString& name, 
                                           const QDateTime& startTime, 
                                           const QDateTime& endTime) const = 0;

    /**
     * @brief 设置指标阈值
     */
    virtual SimpleResult setMetricThreshold(const QString& name, const MetricThreshold& threshold) = 0;

    /**
     * @brief 获取指标阈值
     */
    virtual Result<MetricThreshold> getMetricThreshold(const QString& name) const = 0;

    /**
     * @brief 移除指标阈值
     */
    virtual void removeMetricThreshold(const QString& name) = 0;

    /**
     * @brief 获取活跃告警
     */
    virtual QList<AlertInfo> getActiveAlerts() const = 0;

    /**
     * @brief 获取告警历史
     */
    virtual QList<AlertInfo> getAlertHistory(const QDateTime& startTime, 
                                            const QDateTime& endTime) const = 0;

    /**
     * @brief 确认告警
     */
    virtual SimpleResult acknowledgeAlert(const QString& alertId) = 0;

    /**
     * @brief 解决告警
     */
    virtual SimpleResult resolveAlert(const QString& alertId, const QString& resolution = QString()) = 0;

    /**
     * @brief 生成监控报告
     */
    virtual StringResult generateReport(const QDateTime& startTime, 
                                       const QDateTime& endTime, 
                                       const QString& format = "html") = 0;

    /**
     * @brief 导出监控数据
     */
    virtual SimpleResult exportData(const QString& filePath, 
                                   const QDateTime& startTime, 
                                   const QDateTime& endTime, 
                                   const QString& format = "csv") = 0;

    /**
     * @brief 清理历史数据
     */
    virtual SimpleResult cleanupHistoryData(const QDateTime& beforeTime) = 0;

    /**
     * @brief 获取系统健康状态
     */
    virtual QVariantMap getSystemHealthStatus() const = 0;

    /**
     * @brief 获取监控统计信息
     */
    virtual QVariantMap getMonitoringStatistics() const = 0;

signals:
    /**
     * @brief 指标更新信号
     */
    void metricUpdated(const QString& name, const MetricDataPoint& dataPoint);

    /**
     * @brief 告警触发信号
     */
    void alertTriggered(const AlertInfo& alert);

    /**
     * @brief 告警解决信号
     */
    void alertResolved(const QString& alertId);

    /**
     * @brief 监控状态变化信号
     */
    void monitoringStatusChanged(bool isMonitoring);

    /**
     * @brief 系统健康状态变化信号
     */
    void systemHealthChanged(const QVariantMap& healthStatus);
};

/**
 * @brief 性能监控器接口
 */
class IPerformanceMonitor {
public:
    virtual ~IPerformanceMonitor() = default;

    /**
     * @brief 获取监控器名称
     */
    virtual QString getName() const = 0;

    /**
     * @brief 获取支持的指标类型
     */
    virtual QList<MetricType> getSupportedMetrics() const = 0;

    /**
     * @brief 收集指标数据
     */
    virtual QList<MetricDataPoint> collectMetrics() = 0;

    /**
     * @brief 检查是否可用
     */
    virtual bool isAvailable() const = 0;

    /**
     * @brief 初始化监控器
     */
    virtual SimpleResult initialize(const ConfigParameters& config = {}) = 0;

    /**
     * @brief 关闭监控器
     */
    virtual SimpleResult shutdown() = 0;
};

/**
 * @brief 监控工厂接口
 */
class IMonitoringFactory {
public:
    virtual ~IMonitoringFactory() = default;

    /**
     * @brief 创建系统监控器
     */
    virtual std::shared_ptr<ISystemMonitor> createSystemMonitor(const MonitoringConfig& config = MonitoringConfig()) = 0;

    /**
     * @brief 注册性能监控器
     */
    virtual bool registerPerformanceMonitor(const QString& name, std::shared_ptr<IPerformanceMonitor> monitor) = 0;

    /**
     * @brief 注销性能监控器
     */
    virtual void unregisterPerformanceMonitor(const QString& name) = 0;

    /**
     * @brief 获取性能监控器
     */
    virtual std::shared_ptr<IPerformanceMonitor> getPerformanceMonitor(const QString& name) const = 0;
};

// 工具函数
QString metricTypeToString(MetricType type);
MetricType stringToMetricType(const QString& typeStr);
QString alertLevelToString(AlertLevel level);
AlertLevel stringToAlertLevel(const QString& levelStr);

}  // namespace Monitoring
}  // namespace Support
}  // namespace LA