#ifndef EMBEDDED_FEATURE_MANAGER_CONFIG_H
#define EMBEDDED_FEATURE_MANAGER_CONFIG_H

// 版本信息
#define EMBEDDED_FEATURE_MANAGER_VERSION_MAJOR @PROJECT_VERSION_MAJOR@
#define EMBEDDED_FEATURE_MANAGER_VERSION_MINOR @PROJECT_VERSION_MINOR@
#define EMBEDDED_FEATURE_MANAGER_VERSION_PATCH @PROJECT_VERSION_PATCH@
#define EMBEDDED_FEATURE_MANAGER_VERSION_STRING "@PROJECT_VERSION@"

// 构建信息
#define EMBEDDED_FEATURE_MANAGER_BUILD_TYPE "@CMAKE_BUILD_TYPE@"
#define EMBEDDED_FEATURE_MANAGER_COMPILER "@CMAKE_CXX_COMPILER_ID@"

// 功能标志
#cmakedefine01 BUILD_PLUGIN_TESTS
#cmakedefine01 BUILD_PLUGIN_EXAMPLES

// Qt版本信息
#define EMBEDDED_FEATURE_MANAGER_QT_VERSION "@Qt5_VERSION@"

// 安装路径
#define EMBEDDED_FEATURE_MANAGER_INSTALL_PREFIX "@CMAKE_INSTALL_PREFIX@"

#endif // EMBEDDED_FEATURE_MANAGER_CONFIG_H