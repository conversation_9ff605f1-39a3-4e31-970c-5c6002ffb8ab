#pragma once

/**
 * @file ICommunicationManager.h
 * @brief Linus式通信管理器接口定义 - Layer 3 系统级接口设计
 * 
 * 遵循Linus Torvalds的设计哲学：
 * - "Good programmers worry about data structures" - 基于CommonTypes.h的数据结构
 * - 最小接口原则 - 只负责会话管理，不涉及具体通信细节
 * - 单一职责 - 纯粹的多会话管理和生命周期管理
 * - Layer 3接口 - 依赖Session层，为Application层提供系统抽象
 */

#include <QObject>
#include <QString>
#include <QStringList>
#include <QMap>
#include <memory>
#include "support/foundation/core/CommonTypes.h"

// 前向声明
namespace LA {
namespace Communication {
namespace Session { class ICommunicationSession; }
}
}

namespace LA {
namespace Communication {
namespace Manager {

// 使用Foundation层的标准类型
using namespace LA::Foundation::Core;

/**
 * @brief 纯粹的通信管理器接口
 * 
 * Linus式设计原则:
 * - 最小接口: 只提供必需的多会话管理功能
 * - 无业务逻辑: 不涉及具体设备操作、协议处理
 * - 可测试性: 每个方法都可以独立测试
 * - 可扩展性: 支持多种会话类型和配置
 */
class ICommunicationManager : public QObject {
    Q_OBJECT

public:
    explicit ICommunicationManager(QObject* parent = nullptr) : QObject(parent) {}
    virtual ~ICommunicationManager() = default;

    // === 管理器生命周期 ===
    
    /**
     * @brief 初始化通信管理器
     * @param config 初始化配置参数
     * @return 操作结果
     */
    virtual SimpleResult initialize(const ConfigParameters& config = {}) = 0;
    
    /**
     * @brief 关闭通信管理器
     * @return 操作结果
     */
    virtual SimpleResult shutdown() = 0;
    
    /**
     * @brief 检查管理器是否已初始化
     * @return 是否已初始化
     */
    virtual bool isInitialized() const = 0;

    // === 会话管理 ===
    
    /**
     * @brief 创建通信会话
     * @param sessionId 会话ID
     * @param sessionType 会话类型
     * @param config 会话配置
     * @return 操作结果
     */
    virtual SimpleResult createSession(const QString& sessionId, 
                                     const QString& sessionType, 
                                     const ConfigParameters& config) = 0;
    
    /**
     * @brief 销毁通信会话
     * @param sessionId 会话ID
     * @return 操作结果
     */
    virtual SimpleResult destroySession(const QString& sessionId) = 0;
    
    /**
     * @brief 获取通信会话
     * @param sessionId 会话ID
     * @return 会话实例的智能指针，nullptr表示不存在
     */
    virtual std::shared_ptr<Session::ICommunicationSession> getSession(const QString& sessionId) = 0;
    
    /**
     * @brief 检查会话是否存在
     * @param sessionId 会话ID
     * @return 是否存在
     */
    virtual bool hasSession(const QString& sessionId) const = 0;

    // === 批量操作 ===
    
    /**
     * @brief 打开所有会话
     * @return 操作结果
     */
    virtual SimpleResult openAllSessions() = 0;
    
    /**
     * @brief 关闭所有会话
     * @return 操作结果
     */
    virtual SimpleResult closeAllSessions() = 0;
    
    /**
     * @brief 获取所有会话ID
     * @return 会话ID列表
     */
    virtual QStringList getAllSessionIds() const = 0;
    
    /**
     * @brief 获取指定状态的会话ID
     * @param status 连接状态
     * @return 匹配状态的会话ID列表
     */
    virtual QStringList getSessionsByStatus(ConnectionStatus status) const = 0;

    // === 配置管理 ===
    
    /**
     * @brief 更新会话配置
     * @param sessionId 会话ID
     * @param config 新配置参数
     * @return 操作结果
     */
    virtual SimpleResult updateSessionConfig(const QString& sessionId, 
                                           const ConfigParameters& config) = 0;
    
    /**
     * @brief 获取会话配置
     * @param sessionId 会话ID
     * @return 配置参数
     */
    virtual ConfigParameters getSessionConfig(const QString& sessionId) const = 0;
    
    /**
     * @brief 设置全局配置
     * @param config 全局配置参数
     * @return 操作结果
     */
    virtual SimpleResult setGlobalConfig(const ConfigParameters& config) = 0;
    
    /**
     * @brief 获取全局配置
     * @return 全局配置参数
     */
    virtual ConfigParameters getGlobalConfig() const = 0;

    // === 状态查询 ===
    
    /**
     * @brief 获取管理器统计信息
     * @return 统计信息
     */
    virtual DeviceStatistics getManagerStatistics() const = 0;
    
    /**
     * @brief 获取会话统计信息
     * @param sessionId 会话ID
     * @return 统计信息
     */
    virtual DeviceStatistics getSessionStatistics(const QString& sessionId) const = 0;
    
    /**
     * @brief 获取最后错误信息
     * @return 错误描述
     */
    virtual QString errorString() const = 0;
    
    /**
     * @brief 重置统计信息
     */
    virtual void resetStatistics() = 0;
    
    /**
     * @brief 获取会话数量
     * @return 会话总数
     */
    virtual int getSessionCount() const = 0;

    // === 监控和诊断 ===
    
    /**
     * @brief 执行健康检查
     * @return 操作结果
     */
    virtual SimpleResult performHealthCheck() = 0;
    
    /**
     * @brief 获取系统状态报告
     * @return 状态报告字符串
     */
    virtual QString getStatusReport() const = 0;

signals:
    /**
     * @brief 管理器状态变化信号
     * @param initialized 是否已初始化
     */
    void managerStatusChanged(bool initialized);
    
    /**
     * @brief 会话创建信号
     * @param sessionId 会话ID
     * @param sessionType 会话类型
     */
    void sessionCreated(const QString& sessionId, const QString& sessionType);
    
    /**
     * @brief 会话销毁信号
     * @param sessionId 会话ID
     */
    void sessionDestroyed(const QString& sessionId);
    
    /**
     * @brief 会话状态变化信号
     * @param sessionId 会话ID
     * @param status 新状态
     */
    void sessionStatusChanged(const QString& sessionId, ConnectionStatus status);
    
    /**
     * @brief 管理器错误信号
     * @param error 错误描述
     */
    void managerError(const QString& error);
};

/**
 * @brief 通信管理器工厂接口
 * 
 * 用于创建不同类型的通信管理器
 */
class ICommunicationManagerFactory {
public:
    virtual ~ICommunicationManagerFactory() = default;
    
    /**
     * @brief 创建通信管理器实例
     * @param type 管理器类型
     * @return 通信管理器实例的智能指针
     */
    virtual std::shared_ptr<ICommunicationManager> createManager(const QString& type) = 0;
    
    /**
     * @brief 检查是否支持指定管理器类型
     * @param type 管理器类型
     * @return 是否支持
     */
    virtual bool supportsManagerType(const QString& type) const = 0;
    
    /**
     * @brief 获取支持的管理器类型列表
     * @return 支持的类型列表
     */
    virtual QStringList getSupportedManagerTypes() const = 0;
};

} // namespace Manager
} // namespace Communication
} // namespace LA