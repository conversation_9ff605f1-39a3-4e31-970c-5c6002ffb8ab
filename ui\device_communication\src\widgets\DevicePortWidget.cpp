/*
 * DevicePortWidget.cpp - Linus式设备端口统一管理组件
 *
 * 核心设计理念：
 * 1. "做用户想做的事，而不是让用户学习我们的架构"
 * 2. "一眼就能看到重要信息，一击就能完成常见操作"
 * 3. "让计算机做计算机擅长的事（自动匹配），让用户做用户擅长的事（确认和决策）"
 */

#include "LA/UI/DeviceCommunication/widgets/DevicePortWidget.h"
// #include <LA/DeviceManagement/IDeviceRegistry.h>  // 暂时注释，等待实现
// #include <LA/DeviceManagement/Discovery/IDeviceDiscoveryService.h>  // 暂时注释，等待实现
// Linus: "UI层需要包含协调器的完整定义"
#include <LA/Communication/DeviceMatching/IDeviceMatchingService.h>
#include <LA/Communication/PortManagement/IPortManager.h>
#include <LA/DeviceManagement/DeviceManagementOrchestrator.h>
#include <LA/Foundation/Core/CommonTypes.h>


#include <QApplication>
#include <QComboBox>
#include <QDebug>
#include <QDialog>
#include <QDialogButtonBox>
#include <QFormLayout>
#include <QGroupBox>
#include <QHBoxLayout>
#include <QHeaderView>
#include <QLabel>
#include <QMessageBox>
#include <QPushButton>
#include <QSerialPortInfo>  // 添加串口信息支持
#include <QSizePolicy>
#include <QStyle>
#include <QTableWidget>
#include <QTimer>
#include <QVBoxLayout>
#include <QWidget>


namespace LA {
namespace UI {
namespace DeviceCommunication {

DevicePortWidget::DevicePortWidget(QWidget *parent)
    : QWidget(parent),
      m_deviceManagementOrchestrator(nullptr),  // Linus: "UI层依赖业务协调层"
      m_statusLabel(nullptr),
      m_devicePortTable(nullptr),
      m_autoConnectBtn(nullptr),
      m_refreshBtn(nullptr),
      m_disconnectAllBtn(nullptr),
      m_autoRefreshTimer(new QTimer(this)) {
    setupUI();
    connectSignals();

    // Linus: "端口扫描5秒一次，设备列表只使用事件驱动"
    m_autoRefreshTimer->setInterval(5000);  // 5秒端口扫描
    // 暂时关闭自动刷新，只使用事件驱动
    // m_autoRefreshTimer->start();

    qDebug() << "DevicePortWidget: Linus式统一界面初始化完成";
}

// Linus: "分层架构 - 设置业务协调层"
void DevicePortWidget::setDeviceManagementOrchestrator(LA::DeviceManagement::DeviceManagementOrchestrator *orchestrator) {
    m_deviceManagementOrchestrator = orchestrator;

    if (m_deviceManagementOrchestrator) {
        qDebug() << "DevicePortWidget: 已设置DeviceManagementOrchestrator，启用分层架构模式";

        // TODO: 连接协调器的信号到UI更新槽
        // connect(m_deviceManagementOrchestrator, &DeviceManagementOrchestrator::deviceDiscovered,
        //         this, &DevicePortWidget::onDeviceDiscovered);
        // connect(m_deviceManagementOrchestrator, &DeviceManagementOrchestrator::portDiscovered,
        //         this, &DevicePortWidget::onPortDiscovered);

        updateStatus("已启用Linus式分层架构模式", StatusType::Info);
    } else {
        qDebug() << "DevicePortWidget: DeviceManagementOrchestrator已清除，回退到兼容模式";
        updateStatus("回退到兼容模式", StatusType::Warning);
    }
}

void DevicePortWidget::setupUI() {
    // Linus: "Size for content with improved ComboBox width"
    setMinimumWidth(420);  // 增加宽度以适应更大的ComboBox (140+80+70+80+边距)
    setMaximumWidth(500);  // 相应增加最大宽度

    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(5, 5, 5, 5);
    mainLayout->setSpacing(5);

    // Linus: "Show status first - user wants to know what's happening"
    createStatusSection(mainLayout);

    // Linus: "The main work area should be the biggest thing on screen"
    createDevicePortTable(mainLayout);

    // Linus: "Make the common operations obvious and big"
    createActionButtons(mainLayout);
}

void DevicePortWidget::createStatusSection(QVBoxLayout *layout) {
    QGroupBox *  statusGroup  = new QGroupBox("连接状态");
    QVBoxLayout *statusLayout = new QVBoxLayout(statusGroup);

    m_statusLabel = new QLabel("正在扫描设备和端口...");
    m_statusLabel->setStyleSheet("QLabel { "
                                 "background-color: #e3f2fd; "
                                 "border: 1px solid #bbdefb; "
                                 "color: #1976d2; "
                                 "padding: 8px; "
                                 "font-weight: bold; "
                                 "}");
    m_statusLabel->setAlignment(Qt::AlignCenter);

    statusLayout->addWidget(m_statusLabel);
    layout->addWidget(statusGroup);
}

void DevicePortWidget::createDevicePortTable(QVBoxLayout *layout) {
    QGroupBox *  tableGroup  = new QGroupBox("设备与端口");
    QVBoxLayout *tableLayout = new QVBoxLayout(tableGroup);

    /*
     * Linus: "用户不想看到两个分离的列表，他们想看到可以连接的组合"
     *
     * 表格设计：
     * | 设备名称 | 端口 | 状态 | 操作 |
     * | 激光器1  | COM3 | 已连接 | [断开] |
     * | 相机设备 | COM5 | 可连接 | [连接] |
     * | 未知设备 | COM1 | 不兼容 | [配置] |
     */

    m_devicePortTable = new QTableWidget();
    m_devicePortTable->setColumnCount(4);
    m_devicePortTable->setHorizontalHeaderLabels(QStringList() << "设备"
                                                               << "端口"
                                                               << "状态"
                                                               << "操作");

    // Linus: "Make it look professional but not cluttered"
    m_devicePortTable->setAlternatingRowColors(true);
    m_devicePortTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_devicePortTable->verticalHeader()->setVisible(false);
    m_devicePortTable->setShowGrid(true);          // 显示网格便于看到列边界
    m_devicePortTable->setGridStyle(Qt::DotLine);  // 使用点线网格

    // Linus: "Content drives layout with improved device column width"
    // 增加设备列宽度以适应更大的ComboBox
    // m_devicePortTable->setColumnWidth(0, 140);  // 设备名 (适应新ComboBox尺寸)
    // m_devicePortTable->setColumnWidth(1, 80);   // 端口 (足够显示COM+长数字)
    // m_devicePortTable->setColumnWidth(2, 70);   // 状态
    // m_devicePortTable->setColumnWidth(3, 80);   // 操作按钮

    // 自动调整宽度
    m_devicePortTable->setSizeAdjustPolicy(QAbstractScrollArea::AdjustToContents);
    m_devicePortTable->resizeColumnsToContents();
    m_devicePortTable->resizeRowsToContents();

    // Linus: "Make columns resizable - let users decide"
    QHeaderView *header = m_devicePortTable->horizontalHeader();
    header->setSectionResizeMode(QHeaderView::Interactive);  // 允许拖拽调整
    header->setStretchLastSection(false);                    // 最后一列不自动拉伸

    // Linus: "Good UX shows what users can do"
    header->setDefaultSectionSize(80);                       // 默认列宽
    header->setMinimumSectionSize(50);                       // 最小列宽(防止压缩太小)
    header->setMaximumSectionSize(300);                      // 最大列宽(防止过度拉伸)
    header->setSectionResizeMode(QHeaderView::Interactive);  // 重要：确保边界可拖拽

    // Linus: "Show tooltips for truncated content and interaction hints"
    m_devicePortTable->setMouseTracking(true);
    header->setToolTip("拖拽列边界可调整列宽");

    // Linus: "Visual feedback for user interactions"
    header->setHighlightSections(true);  // 高亮显示鼠标悬停的列
    header->setSectionsClickable(true);  // 允许点击列头

    // Linus: "Make drag areas visually obvious"
    header->setStyleSheet("QHeaderView::section { "
                          "background-color: #f0f0f0; "
                          "padding: 4px; "
                          "border: 1px solid #c0c0c0; "
                          "font-weight: bold; "
                          "} "
                          "QHeaderView::section:hover { "
                          "background-color: #e0e0e0; "
                          "} "
                          "QHeaderView::section:pressed { "
                          "background-color: #d0d0d0; "
                          "}");

    // Linus: "Table should be the main focus"
    m_devicePortTable->setMinimumHeight(200);

    // Linus: "Row height should accommodate button content"
    m_devicePortTable->verticalHeader()->setDefaultSectionSize(30);  // 适应24px按钮高度+边距

    tableLayout->addWidget(m_devicePortTable);
    layout->addWidget(tableGroup);
}

void DevicePortWidget::createActionButtons(QVBoxLayout *layout) {
    QGroupBox *  actionGroup  = new QGroupBox("快速操作");
    QHBoxLayout *buttonLayout = new QHBoxLayout(actionGroup);

    // Linus: "Most common operation should be the biggest button"
    m_autoConnectBtn = new QPushButton("自动连接所有设备");
    m_autoConnectBtn->setStyleSheet("QPushButton { "
                                    "background-color: #4caf50; "
                                    "color: white; "
                                    "font-weight: bold; "
                                    "padding: 10px; "
                                    "border: none; "
                                    "border-radius: 4px; "
                                    "}"
                                    "QPushButton:hover { background-color: #45a049; }"
                                    "QPushButton:pressed { background-color: #3d8b40; }");

    m_refreshBtn       = new QPushButton("刷新");
    m_disconnectAllBtn = new QPushButton("断开全部");

    buttonLayout->addWidget(m_autoConnectBtn, 2);  // 占2份空间
    buttonLayout->addWidget(m_refreshBtn, 1);
    buttonLayout->addWidget(m_disconnectAllBtn, 1);

    layout->addWidget(actionGroup);
}

void DevicePortWidget::connectSignals() {
    connect(m_autoConnectBtn, &QPushButton::clicked, this, &DevicePortWidget::autoConnectAll);
    connect(m_refreshBtn, &QPushButton::clicked, this, &DevicePortWidget::refreshDevicesAndPorts);
    connect(m_disconnectAllBtn, &QPushButton::clicked, this, &DevicePortWidget::disconnectAll);

    connect(m_autoRefreshTimer, &QTimer::timeout, this, &DevicePortWidget::onAutoRefreshTimer);

    connect(m_devicePortTable, &QTableWidget::cellClicked, this, &DevicePortWidget::onTableCellClicked);

    // Linus: "Provide feedback for user interactions"
    connect(m_devicePortTable->horizontalHeader(), &QHeaderView::sectionResized, this, &DevicePortWidget::onHeaderSectionResized);

    // Linus: "Double-click should do the most useful thing"
    connect(m_devicePortTable->horizontalHeader(), &QHeaderView::sectionDoubleClicked, this, &DevicePortWidget::onHeaderSectionDoubleClicked);
}

// 设置后端服务
void DevicePortWidget::setDeviceRegistry(std::shared_ptr<LA::DeviceManagement::IDeviceRegistry> registry) {
    // m_deviceRegistry = registry;  // 暂时注释，等待IDeviceRegistry实现
    if (registry) {
        qDebug() << "DevicePortWidget: 设备注册表已设置 (存根实现)";
        refreshDevicesAndPorts();
    }
}

void DevicePortWidget::setDeviceDiscoveryService(std::shared_ptr<LA::DeviceManagement::Discovery::IDeviceDiscoveryService> service) {
    // m_discoveryService = service;  // 暂时注释，等待IDeviceDiscoveryService实现
    if (service) {
        qDebug() << "DevicePortWidget: 设备发现服务已设置 (存根实现)";

        // TODO: 连接发现服务信号，等待IDeviceDiscoveryService实现
    }
}

void DevicePortWidget::setPortManager(std::shared_ptr<LA::Communication::PortManagement::IPortManager> manager) {
    m_portManager = manager;
    if (m_portManager) {
        qDebug() << "DevicePortWidget: 端口管理器已设置";
        refreshDevicesAndPorts();
    }
}

void DevicePortWidget::setDeviceMatchingService(std::shared_ptr<LA::Communication::DeviceMatching::IDeviceMatchingService> service) {
    m_deviceMatchingService = service;
    if (m_deviceMatchingService) {
        qDebug() << "DevicePortWidget: 设备匹配服务已设置";

        // 连接设备匹配服务信号
        connect(m_deviceMatchingService.get(),
                &LA::Communication::DeviceMatching::IDeviceMatchingService::autoMatchStarted,
                this,
                &DevicePortWidget::onAutoMatchStarted);
        connect(m_deviceMatchingService.get(),
                &LA::Communication::DeviceMatching::IDeviceMatchingService::matchingProgressChanged,
                this,
                &DevicePortWidget::onMatchingProgressChanged);
        connect(m_deviceMatchingService.get(),
                &LA::Communication::DeviceMatching::IDeviceMatchingService::autoMatchCompleted,
                this,
                &DevicePortWidget::onAutoMatchCompleted);
        connect(m_deviceMatchingService.get(),
                &LA::Communication::DeviceMatching::IDeviceMatchingService::manualMatchCompleted,
                this,
                &DevicePortWidget::onManualMatchCompleted);
        connect(m_deviceMatchingService.get(),
                &LA::Communication::DeviceMatching::IDeviceMatchingService::matchingStatusChanged,
                this,
                &DevicePortWidget::onMatchingStatusChanged);
        connect(m_deviceMatchingService.get(),
                &LA::Communication::DeviceMatching::IDeviceMatchingService::matchingFailed,
                this,
                &DevicePortWidget::onMatchingFailed);

        // TODO: 设置端口管理器给匹配服务 (接口中暂时不支持)
        // if (m_portManager) {
        //     m_deviceMatchingService->setPortManager(m_portManager);
        // }

        refreshDevicesAndPorts();
    }
}

void DevicePortWidget::refreshDevicesAndPorts() {
    // Linus: "防抖机制 - 避免频繁刷新的连锁反应"
    static QTimer *debounceTimer = nullptr;
    static int     callCount     = 0;
    callCount++;

    if (!debounceTimer) {
        debounceTimer = new QTimer(this);
        debounceTimer->setSingleShot(true);
        debounceTimer->setInterval(200);  // 200ms防抖
        connect(debounceTimer, &QTimer::timeout, this, &DevicePortWidget::doRefreshDevicesAndPorts);
    }

    qDebug() << "DevicePortWidget: 刷新请求 (第" << callCount << "次) - 防抖中...";
    debounceTimer->start();  // 重启定时器，实现防抖
}

void DevicePortWidget::doRefreshDevicesAndPorts() {
    static int actualRefreshCount = 0;
    actualRefreshCount++;
    qDebug() << "========================================";
    qDebug() << "🔄 DevicePortWidget: UI层数据流刷新开始 (第" << actualRefreshCount << "次)";
    qDebug() << "========================================";

    // Linus: "UI层只负责展示，业务逻辑委托给DeviceManagementOrchestrator"
    if (m_deviceManagementOrchestrator) {
        qDebug() << "📋 DevicePortWidget: 委托业务逻辑给DeviceManagementOrchestrator";

        // 启动设备发现
        if (!m_deviceManagementOrchestrator->isDiscoveryActive()) {
            qDebug() << "🔍 DevicePortWidget: 启动设备发现流程";
            m_deviceManagementOrchestrator->startDeviceDiscovery();
        } else {
            qDebug() << "⚠️  DevicePortWidget: 设备发现已在进行中，跳过重复启动";
        }

        // 获取可用端口并创建UI行
        qDebug() << "🔌 DevicePortWidget: 请求获取可用端口列表";
        QStringList availablePorts = m_deviceManagementOrchestrator->getAvailablePorts();
        qDebug() << "🔌 DevicePortWidget: 从DeviceManagementOrchestrator获取到" << availablePorts.size() << "个端口:" << availablePorts;
        qDebug() << "DevicePortWidget: 协调器报告" << availablePorts.size() << "个可用端口";

        // Linus: "使用协调器的端口信息创建UI行"
        updatePortListFromOrchestrator(availablePorts);

    } else {
        qDebug() << "DevicePortWidget: DeviceManagementOrchestrator未设置，使用旧的增量更新";
        updatePortListIncrementally();
    }
}

// Linus: "使用DeviceManagementOrchestrator的端口信息更新UI"
void DevicePortWidget::updatePortListFromOrchestrator(const QStringList &availablePorts) {
    qDebug() << "DevicePortWidget: 使用协调器端口信息更新UI，共" << availablePorts.size() << "个端口";

    // 获取当前UI中的端口
    QStringList currentUIPorts;
    for (int row = 0; row < m_devicePortTable->rowCount(); ++row) {
        QWidget *portWidget = m_devicePortTable->cellWidget(row, 1);
        if (portWidget) {
            QLabel *portLabel = portWidget->findChild<QLabel *>();
            if (portLabel) {
                currentUIPorts.append(portLabel->text());
            }
        }
    }

    // 添加新端口
    for (const QString &portName : availablePorts) {
        if (!currentUIPorts.contains(portName)) {
            qDebug() << "DevicePortWidget: 添加新端口" << portName;
            addPortRow(portName);
        }
    }

    // 移除不存在的端口
    for (const QString &uiPort : currentUIPorts) {
        if (!availablePorts.contains(uiPort)) {
            qDebug() << "DevicePortWidget: 移除端口" << uiPort;
            removePortRow(uiPort);
        }
    }
}

void DevicePortWidget::updatePortListIncrementally() {
    // Linus: "只扫描端口变化，不重建整个UI"
    QList<QSerialPortInfo> availablePorts = QSerialPortInfo::availablePorts();
    QStringList            currentPorts;

    for (const QSerialPortInfo &portInfo : availablePorts) {
        currentPorts << portInfo.portName();
    }

    // 获取表格中现有的端口
    QStringList existingPorts;
    for (int row = 0; row < m_devicePortTable->rowCount(); ++row) {
        QTableWidgetItem *portItem = m_devicePortTable->item(row, 1);
        if (portItem) {
            existingPorts << portItem->text();
        }
    }

    // 检查是否有变化
    if (currentPorts.toSet() == existingPorts.toSet()) {
        qDebug() << "DevicePortWidget: 端口列表无变化，跳过更新";
        return;
    }

    qDebug() << "DevicePortWidget: 检测到端口变化，执行增量更新";
    updatePortTable();
}

// Linus: "细粒度增量更新 - 最低消耗原则"
void DevicePortWidget::updatePortTable() {
    QStringList currentPorts = getCurrentPorts();
    QStringList tablePorts   = getTablePorts();

    // 找出新增的端口
    for (const QString &portName : currentPorts) {
        if (!tablePorts.contains(portName)) {
            qDebug() << "DevicePortWidget: 新端口" << portName << "- 添加行";
            handlePortAdded(portName);
        }
    }

    // 找出删除的端口
    for (const QString &portName : tablePorts) {
        if (!currentPorts.contains(portName)) {
            qDebug() << "DevicePortWidget: 端口" << portName << "已移除 - 删除行";
            handlePortRemoved(portName);
        }
    }

    qDebug() << "DevicePortWidget: 增量更新完成 - 当前端口数:" << currentPorts.size();
}

QStringList DevicePortWidget::getCurrentPorts() const {
    QStringList            ports;
    QList<QSerialPortInfo> availablePorts = QSerialPortInfo::availablePorts();

    for (const QSerialPortInfo &portInfo : availablePorts) {
        ports << portInfo.portName();
    }

    return ports;
}

QStringList DevicePortWidget::getTablePorts() const {
    QStringList ports;

    for (int row = 0; row < m_devicePortTable->rowCount(); ++row) {
        QTableWidgetItem *portItem = m_devicePortTable->item(row, 1);
        if (portItem) {
            ports << portItem->text();
        }
    }

    return ports;
}

// Linus: "端口生命周期管理"
void DevicePortWidget::handlePortAdded(const QString &portName) {
    qDebug() << "DevicePortWidget: 处理新端口" << portName;
    addPortRow(portName);

    // 启动设备匹配
    if (m_deviceMatchingService) {
        qDebug() << "DevicePortWidget: 启动端口" << portName << "的设备匹配";
        m_deviceMatchingService->autoMatchDevice(portName);
    }
}

void DevicePortWidget::handlePortRemoved(const QString &portName) {
    qDebug() << "DevicePortWidget: 处理端口移除" << portName;

    // 断开设备连接
    QString deviceId = m_portDeviceMap.value(portName);
    if (!deviceId.isEmpty()) {
        qDebug() << "DevicePortWidget: 断开端口" << portName << "上的设备" << deviceId;
        disconnectDevice(deviceId);
    }

    // 删除表格行
    removePortRow(portName);

    // 清理资源
    cleanupPortResources(portName);
}

void DevicePortWidget::cleanupPortResources(const QString &portName) {
    // 清理端口相关的映射和状态
    QString deviceId = m_portDeviceMap.value(portName);
    if (!deviceId.isEmpty()) {
        m_devicePortMap.remove(deviceId);
        m_portDeviceMap.remove(portName);
    }

    // 清理自动匹配记录
    m_attemptedAutoMatchPorts.remove(portName);

    qDebug() << "DevicePortWidget: 端口" << portName << "资源清理完成";
}

// Linus: "行级别增量更新方法"
void DevicePortWidget::addPortRow(const QString &portName) {
    int row = m_devicePortTable->rowCount();

    // 自动识别设备类型
    QString                deviceType     = "未知设备";
    QList<QSerialPortInfo> availablePorts = QSerialPortInfo::availablePorts();
    for (const QSerialPortInfo &portInfo : availablePorts) {
        if (portInfo.portName() == portName) {
            deviceType = identifyDeviceType(portInfo);
            break;
        }
    }

    // 使用原有的addDevicePortRow方法创建行
    QMap<QString, QString> emptySelections;
    addDevicePortRow(row, deviceType, portName, ConnectionStatus::Unidentified, emptySelections);

    qDebug() << "DevicePortWidget: 已添加端口行" << portName << "设备类型:" << deviceType;
}

void DevicePortWidget::removePortRow(const QString &portName) {
    int rowIndex = findPortRowIndex(portName);
    if (rowIndex >= 0) {
        m_devicePortTable->removeRow(rowIndex);
        qDebug() << "DevicePortWidget: 已删除端口行" << portName << "行索引:" << rowIndex;
    } else {
        qDebug() << "DevicePortWidget: 未找到端口行" << portName;
    }
}

void DevicePortWidget::updatePortRow(const QString &portName) {
    int rowIndex = findPortRowIndex(portName);
    if (rowIndex >= 0) {
        // TODO: 实现行状态更新逻辑
        qDebug() << "DevicePortWidget: 更新端口行" << portName << "行索引:" << rowIndex;
    }
}

int DevicePortWidget::findPortRowIndex(const QString &portName) const {
    for (int row = 0; row < m_devicePortTable->rowCount(); ++row) {
        QTableWidgetItem *portItem = m_devicePortTable->item(row, 1);
        if (portItem && portItem->text() == portName) {
            return row;
        }
    }
    return -1;
}

void DevicePortWidget::doFullRefresh() {
    // Linus: "Preserve user choices during refresh"
    QMap<QString, QString> userSelections;  // portName -> selectedDeviceId

    // 保存用户的设备选择
    for (int row = 0; row < m_devicePortTable->rowCount(); ++row) {
        QTableWidgetItem *portItem   = m_devicePortTable->item(row, 1);
        QWidget *         cellWidget = m_devicePortTable->cellWidget(row, 0);

        if (portItem && cellWidget) {
            QComboBox *combo = cellWidget->findChild<QComboBox *>();
            if (combo && combo->currentIndex() > 0) {  // 不是"选择设备..."
                QString portName         = portItem->text();
                QString selectedDeviceId = combo->currentData().toString();
                userSelections[portName] = selectedDeviceId;
                qDebug() << "DevicePortWidget: 保存用户选择 - 端口" << portName << "设备" << selectedDeviceId;
            }
        }
    }

    // 清空现有数据
    m_devicePortTable->setRowCount(0);

    // Linus: "Make it work even without full backend support"
    if (!m_portManager) {
        qDebug() << "DevicePortWidget: 端口管理器未初始化，使用直接扫描模式";
        updateStatus("正在直接扫描系统端口...", StatusType::Warning);
    }

    // 获取端口信息 - 使用QSerialPortInfo扫描实际串口
    QStringList            ports;
    QList<QSerialPortInfo> availablePorts = QSerialPortInfo::availablePorts();

    qDebug() << "📡 DevicePortWidget: 底层串口扫描发现" << availablePorts.size() << "个系统端口";

    for (const QSerialPortInfo &portInfo : availablePorts) {
        QString portName = portInfo.portName();
        ports << portName;
        qDebug() << "  📡 端口详情:" << portName << "| 描述:" << portInfo.description() << "| 制造商:" << portInfo.manufacturer();
    }

    /*
     * Linus: "Show available ports first, let user choose devices"
     *
     * 新策略：端口优先模式 - 显示可用端口，用户通过ComboBox选择设备
     * 1. 显示已连接的设备-端口对
     * 2. 显示所有可用端口，每个端口都有设备选择ComboBox
     */

    int row = 0;

    // 1. 已连接的设备
    for (auto it = m_devicePortMap.begin(); it != m_devicePortMap.end(); ++it) {
        addDevicePortRow(row++, it.key(), it.value(), ConnectionStatus::Connected, userSelections);
    }

    // 2. 显示所有可用端口
    for (const QSerialPortInfo &portInfo : availablePorts) {
        QString portName = portInfo.portName();
        if (!m_portDeviceMap.contains(portName)) {
            // Linus: "Auto-match only once for new ports"
            QString deviceType = "未知设备";
            bool    isNewPort  = !m_attemptedAutoMatchPorts.contains(portName);

            if (isNewPort) {
                // 自动识别设备类型作为默认选择（仅对新端口）
                deviceType = identifyDeviceType(portInfo);
                m_attemptedAutoMatchPorts.insert(portName);
                qDebug() << "DevicePortWidget: 新端口" << portName << "自动识别为:" << deviceType;

                // 如果识别成功且有匹配服务，尝试自动匹配
                if (deviceType != "未知设备" && m_deviceMatchingService) {
                    qDebug() << "DevicePortWidget: 启动端口" << portName << "的自动匹配";
                    auto result = m_deviceMatchingService->autoMatchDevice(portName);
                    if (result.success) {
                        qDebug() << "DevicePortWidget: 端口" << portName << "自动匹配成功:" << result.deviceType;
                    }
                }
            } else {
                qDebug() << "DevicePortWidget: 端口" << portName << "已尝试过自动匹配，跳过";
            }

            addDevicePortRow(row++,
                             deviceType,
                             portName,
                             (deviceType == "未知设备" || deviceType == "未识别工业设备") ? ConnectionStatus::UnknownDevice : ConnectionStatus::CanConnect,
                             userSelections);
        }
    }

    if (availablePorts.size() > 0) {
        updateStatus(QString("发现 %1 个可用端口，请在设备列表中选择要匹配的设备").arg(availablePorts.size()), StatusType::Info);
    } else {
        updateStatus("没有发现可用端口，请检查设备连接", StatusType::Warning);
    }

    updateStatistics();
}

void DevicePortWidget::updateDevicePortRow(int row, ConnectionStatus status) {
    if (row < 0 || row >= m_devicePortTable->rowCount()) {
        qWarning() << "DevicePortWidget: updateDevicePortRow - 无效行号:" << row;
        return;
    }

    // 更新状态列
    QTableWidgetItem *statusItem = m_devicePortTable->item(row, 2);
    if (statusItem) {
        statusItem->setText(getStatusText(status));
        statusItem->setForeground(getStatusColor(status));
        statusItem->setData(Qt::UserRole, static_cast<int>(status));
    }

    // 更新操作按钮
    QString deviceId = m_devicePortTable->item(row, 0) ? m_devicePortTable->item(row, 0)->data(Qt::UserRole).toString() : "";
    QString portName = m_devicePortTable->item(row, 1) ? m_devicePortTable->item(row, 1)->text() : "";

    QPushButton *newButton = createActionButton(deviceId, portName, status);

    // 创建容器widget确保按钮正确居中（与addDevicePortRow保持一致）
    QWidget *    buttonContainer = new QWidget();
    QHBoxLayout *buttonLayout    = new QHBoxLayout(buttonContainer);
    buttonLayout->setContentsMargins(1, 1, 1, 1);
    buttonLayout->setAlignment(Qt::AlignCenter);
    buttonLayout->addWidget(newButton);

    m_devicePortTable->setCellWidget(row, 3, buttonContainer);

    qDebug() << "DevicePortWidget: 更新行" << row << "状态为" << getStatusText(status);
}

void DevicePortWidget::addDevicePortRow(int                           row,
                                        const QString &               deviceName,
                                        const QString &               portName,
                                        ConnectionStatus              status,
                                        const QMap<QString, QString> &userSelections) {
    m_devicePortTable->insertRow(row);

    // 设备名列 - 使用标准ComboBox，按Qt官方示例方式
    QComboBox *deviceCombo = new QComboBox();
    deviceCombo->setEditable(false);

    // Linus: "Size for content visibility, not cell constraints"
    // 修复用户反馈：ComboBox太小，内容看不清
    deviceCombo->setMinimumWidth(120);  // 增加最小宽度确保内容可见
    deviceCombo->setMaximumWidth(140);  // 适度最大宽度，不破坏表格布局
    deviceCombo->setMinimumHeight(26);  // 增加高度提供更好的可读性
    deviceCombo->setMaximumHeight(26);  // 固定高度保持一致性
    deviceCombo->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Fixed);

    // Linus: "Optimized style for enhanced content visibility"
    // 修复用户反馈：ComboBox内容不清晰，需要更好的可读性
    deviceCombo->setStyleSheet("QComboBox {"
                               "   padding: 4px 6px;"
                               "   border: 1px solid #888888;"
                               "   border-radius: 3px;"
                               "   font-size: 9pt;"
                               "   font-weight: normal;"
                               "   background-color: white;"
                               "   color: #333333;"
                               "   min-height: 22px;"
                               "   max-height: 22px;"
                               "}"
                               "QComboBox:hover {"
                               "   border: 1px solid #0078d4;"
                               "   background-color: #f8f8f8;"
                               "}"
                               "QComboBox:focus {"
                               "   border: 2px solid #0078d4;"
                               "   outline: none;"
                               "}"
                               "QComboBox::drop-down {"
                               "   subcontrol-origin: padding;"
                               "   subcontrol-position: top right;"
                               "   width: 14px;"
                               "   border: none;"
                               "}"
                               "QComboBox::down-arrow {"
                               "   width: 8px;"
                               "   height: 8px;"
                               "   image: "
                               "url(data:image/"
                               "svg+xml;base64,"
                               "PHN2ZyB3aWR0aD0iOCIgaGVpZ2h0PSI4IiB2aWV3Qm94PSIwIDAgOCA4IiBmaWxsPSIjNDQ0IiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxwYXR"
                               "oIGQ9Ik00IDZMMSAzaDZ6Ii8+PC9zdmc+);"
                               "}"
                               "QComboBox QAbstractItemView {"
                               "   font-size: 9pt;"
                               "   padding: 3px;"
                               "   border: 1px solid #888888;"
                               "   background-color: white;"
                               "   selection-background-color: #0078d4;"
                               "   selection-color: white;"
                               "   min-width: 180px;"
                               "   outline: none;"
                               "   alternate-background-color: #f0f0f0;"
                               "}"
                               "QComboBox QAbstractItemView::item {"
                               "   padding: 6px 8px;"
                               "   min-height: 18px;"
                               "}"
                               "QComboBox QAbstractItemView::item:hover {"
                               "   background-color: #e3f2fd;"
                               "}");

    // Linus: "Show what matters, hide complexity in tooltips"
    deviceCombo->addItem("选择设备...", "");

    // 工业设备选项 - 使用简洁显示，详细信息在tooltip
    if (m_deviceMatchingService) {
        auto communicableDevices = m_deviceMatchingService->getCommunicableDevices();
        qDebug() << "DevicePortWidget: 从设备匹配服务获取" << communicableDevices.size() << "个设备类型";

        for (const auto &device : communicableDevices) {
            // 显示设备ID（如Nova-A1），而不是设备类型
            QString displayName = device.deviceId;  // 直接显示设备ID，如"Nova-A1"
            QString description = device.description.isEmpty() ? QString("%1 - %2制造").arg(device.deviceType, device.displayName) : device.description;

            deviceCombo->addItem(displayName, device.deviceId);
            deviceCombo->setItemData(deviceCombo->count() - 1, description, Qt::ToolTipRole);

            // 根据制造商（displayName）设置图标
            if (device.displayName == "Nova") {
                deviceCombo->setItemData(deviceCombo->count() - 1, "🔬", Qt::DecorationRole);
            } else if (device.deviceType.contains("Laser")) {
                deviceCombo->setItemData(deviceCombo->count() - 1, "⚡", Qt::DecorationRole);
            } else if (device.deviceType.contains("Motor")) {
                deviceCombo->setItemData(deviceCombo->count() - 1, "⚙", Qt::DecorationRole);
            } else if (device.deviceType.contains("Machine")) {
                deviceCombo->setItemData(deviceCombo->count() - 1, "🏭", Qt::DecorationRole);
            } else {
                deviceCombo->setItemData(deviceCombo->count() - 1, "📊", Qt::DecorationRole);
            }
        }
    } else {
        // 回退到硬编码设备列表（如果设备匹配服务不可用）
        qDebug() << "DevicePortWidget: 设备匹配服务不可用，使用默认设备列表";

        struct DeviceOption {
            QString shortName;
            QString fullName;
            QString deviceId;
        };

        QList<DeviceOption> devices = {{"激光传感器", "激光位移传感器 - 高精度距离测量", "laser_displacement"},
                                       {"步进电机", "步进电机驱动器 - 精密运动控制", "stepper_motor"},
                                       {"工业传感器", "通用工业传感器 - 多参数监测", "industrial_sensor"},
                                       {"串口设备", "通用串口设备 - 标准通信协议", "generic_serial"}};

        for (const auto &device : devices) {
            deviceCombo->addItem(device.shortName, device.deviceId);
            deviceCombo->setItemData(deviceCombo->count() - 1, device.fullName, Qt::ToolTipRole);
        }
    }

    // Linus: "User choice overrides auto-detection"
    // 优先恢复用户手动选择，其次才是自动识别
    if (userSelections.contains(portName)) {
        QString selectedDeviceId = userSelections[portName];
        for (int i = 0; i < deviceCombo->count(); ++i) {
            if (deviceCombo->itemData(i).toString() == selectedDeviceId) {
                deviceCombo->setCurrentIndex(i);
                qDebug() << "DevicePortWidget: 恢复用户选择 - 端口" << portName << "设备ID" << selectedDeviceId;
                break;
            }
        }
    } else {
        // 设置当前选择（如果有识别的设备）
        QString displayName  = getDeviceDisplayName(deviceName);
        int     currentIndex = deviceCombo->findText(displayName, Qt::MatchContains);
        if (currentIndex > 0) {
            deviceCombo->setCurrentIndex(currentIndex);
        }
    }

    // Linus: "Smart interaction handling - preserve user choices"
    // 修复用户反馈：打开下拉框时会因为刷新自动关闭
    connect(deviceCombo, QOverload<int>::of(&QComboBox::currentIndexChanged), [this, row, portName](int index) {
        QComboBox *combo = qobject_cast<QComboBox *>(sender());
        if (combo && index > 0) {
            QString selectedDeviceId   = combo->currentData().toString();
            QString selectedDeviceName = combo->currentText();
            qDebug() << "DevicePortWidget: 手动选择设备" << selectedDeviceName << "(" << selectedDeviceId << ") 匹配端口" << portName;

            // 更新内部映射，避免触发全局刷新
            m_devicePortMap[selectedDeviceId] = portName;
            m_portDeviceMap[portName]         = selectedDeviceId;

            // 更新状态为手动识别
            updateDevicePortRow(row, ConnectionStatus::ManualIdentified);

            // 执行手动匹配
            if (m_deviceMatchingService) {
                m_deviceMatchingService->manualMatchDevice(portName, selectedDeviceId);
            } else {
                updateStatus(QString("已手动选择 %1 匹配端口 %2").arg(selectedDeviceName).arg(portName), StatusType::Info);
            }
        }
    });

    deviceCombo->setToolTip("选择要匹配到此端口的设备类型");

    // Linus: "Override showPopup to control position"
    // 使用自定义类来控制下拉框位置
    class PositionedComboBox : public QComboBox {
      public:
        PositionedComboBox(QWidget *parent = nullptr) : QComboBox(parent) {
        }
        void showPopup() override {
            QComboBox::showPopup();
            // 延迟调整位置，确保popup已显示
            QTimer::singleShot(0, [this]() {
                QAbstractItemView *popup = view();
                if (popup) {
                    QPoint comboPos = mapToGlobal(QPoint(0, 0));
                    popup->move(comboPos.x() - 25, comboPos.y() - 3);
                }
            });
        }
    };

    // Linus: "Restore user selection after reconstruction"
    // 优先恢复用户手动选择，其次才是自动识别
    if (userSelections.contains(portName)) {
        QString selectedDeviceId = userSelections[portName];
        for (int i = 0; i < deviceCombo->count(); ++i) {
            if (deviceCombo->itemData(i).toString() == selectedDeviceId) {
                deviceCombo->setCurrentIndex(i);
                qDebug() << "DevicePortWidget: 恢复用户选择 - 端口" << portName << "设备ID" << selectedDeviceId;
                break;
            }
        }
    } else {
        // 设置当前选择（如果有识别的设备）
        QString displayName  = getDeviceDisplayName(deviceName);
        int     currentIndex = deviceCombo->findText(displayName, Qt::MatchContains);
        if (currentIndex > 0) {
            deviceCombo->setCurrentIndex(currentIndex);
        }
    }

    // 重新连接信号（在替换ComboBox后需要重新连接）
    connect(deviceCombo, QOverload<int>::of(&QComboBox::currentIndexChanged), [this, row, portName](int index) {
        QComboBox *combo = qobject_cast<QComboBox *>(sender());
        if (combo && index > 0) {
            QString selectedDeviceId   = combo->currentData().toString();
            QString selectedDeviceName = combo->currentText();
            qDebug() << "DevicePortWidget: 手动选择设备" << selectedDeviceName << "(" << selectedDeviceId << ") 匹配端口" << portName;

            // 更新内部映射，避免触发全局刷新
            m_devicePortMap[selectedDeviceId] = portName;
            m_portDeviceMap[portName]         = selectedDeviceId;

            // 执行手动匹配
            if (m_deviceMatchingService) {
                m_deviceMatchingService->manualMatchDevice(portName, selectedDeviceId);
            } else {
                updateStatus(QString("已选择 %1 匹配端口 %2").arg(selectedDeviceName).arg(portName), StatusType::Info);
            }
        }
    });

    m_devicePortTable->setCellWidget(row, 0, deviceCombo);

    // Linus: "Row height follows content height precisely"
    m_devicePortTable->setRowHeight(row, 30);  // 26px ComboBox + 4px 边距提供更好的视觉效果

    // 端口列 - Linus: "Tooltip for any potentially long port names"
    QTableWidgetItem *portItem = new QTableWidgetItem(portName);
    portItem->setToolTip(QString("端口: %1").arg(portName));
    m_devicePortTable->setItem(row, 1, portItem);

    // 状态列
    QTableWidgetItem *statusItem = new QTableWidgetItem(getStatusText(status));
    statusItem->setForeground(getStatusColor(status));
    statusItem->setData(Qt::UserRole, static_cast<int>(status));
    m_devicePortTable->setItem(row, 2, statusItem);

    // 操作按钮列 - Linus: "Proper widget containment prevents floating buttons"
    QPushButton *actionBtn = createActionButton(deviceName, portName, status);

    // 创建容器widget确保按钮正确居中
    QWidget *    buttonContainer = new QWidget();
    QHBoxLayout *buttonLayout    = new QHBoxLayout(buttonContainer);
    buttonLayout->setContentsMargins(1, 1, 1, 1);  // 最小边距
    buttonLayout->setAlignment(Qt::AlignCenter);   // 居中对齐
    buttonLayout->addWidget(actionBtn);

    // Linus: "Debug button creation"
    qDebug() << "DevicePortWidget: 创建操作按钮:" << actionBtn->text() << "尺寸:" << actionBtn->size();

    m_devicePortTable->setCellWidget(row, 3, buttonContainer);
}

QPushButton *DevicePortWidget::createActionButton(const QString &deviceId, const QString &portName, ConnectionStatus status) {
    QPushButton *btn = new QPushButton();

    // Linus: "Buttons should match row height and be functional"
    btn->setFixedSize(75, 24);                                   // 匹配ComboBox高度26px（留2px边距）
    btn->setFont(QFont("Microsoft YaHei", 8));                   // 可读字体大小
    btn->setSizePolicy(QSizePolicy::Fixed, QSizePolicy::Fixed);  // 强制固定尺寸

    // Linus: "Explicit size constraints in stylesheet"
    QString baseStyle = "QPushButton { "
                        "border: 1px solid %1; "
                        "border-radius: 2px; "
                        "padding: 2px 4px; "
                        "font-weight: normal; "
                        "font-size: 8pt; "
                        "min-width: 75px; "   // 强制最小宽度
                        "max-width: 75px; "   // 强制最大宽度
                        "min-height: 24px; "  // 匹配ComboBox高度（26px-2px边距）
                        "max-height: 24px; "  // 匹配ComboBox高度（26px-2px边距）
                        "} "
                        "QPushButton:hover { "
                        "border: 2px solid %1; "
                        "} "
                        "QPushButton:pressed { "
                        "background-color: %2; "
                        "}";

    switch (status) {
    case ConnectionStatus::Connected:
        btn->setText("断开");
        btn->setStyleSheet(baseStyle.arg("#f44336", "#d32f2f") + "QPushButton { background-color: #f44336; color: white; }");
        connect(btn, &QPushButton::clicked, [this, deviceId]() { disconnectDevice(deviceId); });
        break;

    case ConnectionStatus::CanConnect:
        btn->setText("连接");
        btn->setStyleSheet(baseStyle.arg("#4caf50", "#388e3c") + "QPushButton { background-color: #4caf50; color: white; }");
        connect(
            btn,
            &QPushButton::clicked,
            this,
            [this, deviceId, portName]() {
                qDebug() << "DevicePortWidget: 连接按钮被点击 - 设备:" << deviceId << "端口:" << portName;
                try {
                    if (portName == "等待匹配") {
                        // 显示手动选择对话框
                        showManualMatchDialog(deviceId);
                    } else {
                        // 直接连接到指定端口
                        connectDeviceToPort(deviceId, portName);
                    }
                } catch (const std::exception &e) {
                    qWarning() << "DevicePortWidget: 连接操作异常:" << e.what();
                } catch (...) {
                    qWarning() << "DevicePortWidget: 连接操作未知异常";
                }
            },
            Qt::QueuedConnection);
        break;

    case ConnectionStatus::NoPort:
        btn->setText("扫描");
        btn->setStyleSheet(baseStyle.arg("#ff9800", "#f57c00") + "QPushButton { background-color: #ff9800; color: white; }");
        connect(btn, &QPushButton::clicked, [this]() { refreshDevicesAndPorts(); });
        break;

    case ConnectionStatus::UnknownDevice:
        btn->setText("识别");
        btn->setStyleSheet(baseStyle.arg("#9c27b0", "#7b1fa2") + "QPushButton { background-color: #9c27b0; color: white; }");
        connect(btn, &QPushButton::clicked, [this, portName]() { tryIdentifyDevice(portName); });
        break;

    case ConnectionStatus::Unidentified:
        btn->setText("识别");
        btn->setStyleSheet(baseStyle.arg("#9c27b0", "#7b1fa2") + "QPushButton { background-color: #9c27b0; color: white; }");
        connect(btn, &QPushButton::clicked, [this, portName]() { startDeviceIdentification(portName); });
        break;

    case ConnectionStatus::ManualIdentified:
        btn->setText("连接");
        btn->setStyleSheet(baseStyle.arg("#1976d2", "#1565c0") + "QPushButton { background-color: #1976d2; color: white; }");
        connect(btn, &QPushButton::clicked, [this, portName]() {
            // 连接手动识别的设备
            connectIdentifiedDevice(portName);
        });
        break;

    case ConnectionStatus::AutoIdentified:
        btn->setText("连接");
        btn->setStyleSheet(baseStyle.arg("#4caf50", "#388e3c") + "QPushButton { background-color: #4caf50; color: white; }");
        connect(btn, &QPushButton::clicked, [this, portName]() {
            // 连接自动识别的设备
            connectIdentifiedDevice(portName);
        });
        break;

    default:
        btn->setText("配置");
        btn->setStyleSheet(baseStyle.arg("#757575", "#616161") + "QPushButton { background-color: #757575; color: white; }");
        break;
    }

    return btn;
}

void DevicePortWidget::autoConnectAll() {
    qDebug() << "========================================";
    qDebug() << "🚀 DevicePortWidget: 启动自动连接所有设备流程";
    qDebug() << "========================================";

    if (!m_deviceMatchingService) {
        qDebug() << "❌ DevicePortWidget: 设备匹配服务未初始化";
        updateStatus("设备匹配服务未初始化，无法自动连接", StatusType::Error);
        return;
    }

    qDebug() << "✅ DevicePortWidget: 设备匹配服务已可用";

    // 获取所有可用端口
    auto availablePorts = QSerialPortInfo::availablePorts();
    qDebug() << "🔍 DevicePortWidget: 系统端口扫描结果:" << availablePorts.size() << "个端口";
    
    if (availablePorts.isEmpty()) {
        qDebug() << "⚠️  DevicePortWidget: 没有发现可用端口";
        updateStatus("没有可用端口，无法进行自动连接", StatusType::Warning);
        return;
    }

    for (const auto& portInfo : availablePorts) {
        qDebug() << "  📋 发现端口:" << portInfo.portName() << "| 描述:" << portInfo.description();
    }

    updateStatus(QString("开始自动匹配和连接 %1 个端口...").arg(availablePorts.size()), StatusType::Info);

    // 为每个可用端口启动自动匹配
    int startedMatches = 0;
    for (const auto &portInfo : availablePorts) {
        QString portName = portInfo.portName();

        // 检查端口是否已被占用
        if (!m_portDeviceMap.contains(portName)) {
            qDebug() << "DevicePortWidget: 开始匹配端口" << portName;
            m_deviceMatchingService->autoMatchDevice(portName);
            startedMatches++;
        } else {
            qDebug() << "DevicePortWidget: 端口" << portName << "已连接设备" << m_portDeviceMap[portName];
        }
    }

    if (startedMatches > 0) {
        updateStatus(QString("已启动 %1 个端口的自动匹配，请等待...").arg(startedMatches), StatusType::Info);
    } else {
        updateStatus("所有端口都已连接设备", StatusType::Success);
    }
    // 延迟刷新显示，避免与自动匹配冲突
    QTimer::singleShot(2000, this, &DevicePortWidget::refreshDevicesAndPorts);
}

void DevicePortWidget::disconnectAll() {
    qDebug() << "DevicePortWidget: 断开所有设备连接";

    QStringList connectedDevices = m_devicePortMap.keys();
    for (const QString &deviceId : connectedDevices) {
        disconnectDevice(deviceId);
    }

    updateStatus("所有设备已断开", StatusType::Info);
    refreshDevicesAndPorts();
}

bool DevicePortWidget::connectDeviceToPort(const QString &deviceId, const QString &portName) {
    qDebug() << "连接设备" << deviceId << "到端口" << portName;

    // 检查兼容性
    if (!isDevicePortCompatible(deviceId, portName)) {
        updateStatus(QString("设备 %1 与端口 %2 不兼容").arg(deviceId).arg(portName), StatusType::Error);
        return false;
    }

    // 建立连接映射
    m_devicePortMap[deviceId] = portName;
    m_portDeviceMap[portName] = deviceId;

    // TODO: 调用实际的连接逻辑
    // m_portManager->connectDevice(deviceId, portName);

    updateStatus(QString("设备 %1 已连接到端口 %2").arg(getDeviceDisplayName(deviceId)).arg(portName), StatusType::Success);

    emit deviceConnected(deviceId, portName);
    return true;
}

void DevicePortWidget::disconnectDevice(const QString &deviceId) {
    if (!m_devicePortMap.contains(deviceId)) {
        return;
    }

    QString portName = m_devicePortMap[deviceId];

    m_devicePortMap.remove(deviceId);
    m_portDeviceMap.remove(portName);

    updateStatus(QString("设备 %1 已断开").arg(getDeviceDisplayName(deviceId)), StatusType::Info);

    emit deviceDisconnected(deviceId, portName);
    refreshDevicesAndPorts();
}

void DevicePortWidget::updateStatus(const QString &message, StatusType type) {
    m_statusLabel->setText(message);

    QString styleSheet;
    switch (type) {
    case StatusType::Info:
        styleSheet = "QLabel { background-color: #e3f2fd; border: 1px solid #bbdefb; color: #1976d2; padding: 8px; font-weight: bold; }";
        break;
    case StatusType::Success:
        styleSheet = "QLabel { background-color: #e8f5e8; border: 1px solid #c8e6c9; color: #2e7d32; padding: 8px; font-weight: bold; }";
        break;
    case StatusType::Warning:
        styleSheet = "QLabel { background-color: #fff3e0; border: 1px solid #ffcc02; color: #f57c00; padding: 8px; font-weight: bold; }";
        break;
    case StatusType::Error:
        styleSheet = "QLabel { background-color: #ffebee; border: 1px solid #ffcdd2; color: #c62828; padding: 8px; font-weight: bold; }";
        break;
    }

    m_statusLabel->setStyleSheet(styleSheet);
}

void DevicePortWidget::updateStatistics() {
    int totalRows      = m_devicePortTable->rowCount();
    int connectedCount = m_devicePortMap.size();

    updateStatus(QString("共 %1 项，已连接 %2 个设备").arg(totalRows).arg(connectedCount), StatusType::Info);
}

// 槽函数实现
// void DevicePortWidget::onDeviceDiscovered(const LA::Foundation::Core::DeviceInfo& device)
// {
//     qDebug() << "DevicePortWidget: 发现新设备:" << device.deviceName;
//     refreshDevicesAndPorts();
// }

void DevicePortWidget::onDeviceLost(const QString &deviceId) {
    qDebug() << "DevicePortWidget: 设备丢失:" << deviceId;

    // 如果设备已连接，先断开
    if (m_devicePortMap.contains(deviceId)) {
        disconnectDevice(deviceId);
    }

    refreshDevicesAndPorts();
}

// void DevicePortWidget::onPortAdded(const LA::Foundation::Core::PortInfo& port)
// {
//     qDebug() << "DevicePortWidget: 新增端口:" << port.portName;
//     refreshDevicesAndPorts();
// }

void DevicePortWidget::onPortRemoved(const QString &portName) {
    qDebug() << "DevicePortWidget: 端口移除:" << portName;

    // 如果端口已连接设备，先断开
    if (m_portDeviceMap.contains(portName)) {
        QString deviceId = m_portDeviceMap[portName];
        disconnectDevice(deviceId);
    }

    refreshDevicesAndPorts();
}

void DevicePortWidget::onAutoRefreshTimer() {
    // Linus: "Smart refresh - only when necessary"
    // 定期刷新，但不要太频繁打印日志
    static int refreshCount = 0;
    if (++refreshCount % 6 == 0) {  // 每30秒打印一次日志 (5秒*6=30秒)
        qDebug() << "DevicePortWidget: 定期刷新 (第" << refreshCount << "次)";
    }

    // 检查是否有ComboBox正在被使用（防止干扰用户操作）
    bool hasActiveComboBox = false;
    for (int row = 0; row < m_devicePortTable->rowCount(); ++row) {
        QWidget *cellWidget = m_devicePortTable->cellWidget(row, 0);
        if (cellWidget) {
            QComboBox *combo = cellWidget->findChild<QComboBox *>();
            if (combo && combo->view() && combo->view()->isVisible()) {
                hasActiveComboBox = true;
                qDebug() << "DevicePortWidget: 检测到活跃的ComboBox，跳过刷新";
                break;
            }
        }
    }

    if (!hasActiveComboBox) {
        refreshDevicesAndPorts();
    }
}

void DevicePortWidget::onTableCellClicked(int row, int column) {
    Q_UNUSED(column)
    qDebug() << "DevicePortWidget: 表格行" << row << "被点击";

    // 可以在这里添加行选择逻辑
}

void DevicePortWidget::onConnectButtonClicked() {
    qDebug() << "DevicePortWidget: 连接按钮被点击";
    // TODO: 实现连接逻辑
}

void DevicePortWidget::onDisconnectButtonClicked() {
    qDebug() << "DevicePortWidget: 断开按钮被点击";
    // TODO: 实现断开逻辑
}

void DevicePortWidget::onHeaderSectionResized(int logicalIndex, int oldSize, int newSize) {
    // Linus: "Give user feedback about what they accomplished"
    QString columnName;
    switch (logicalIndex) {
    case 0:
        columnName = "设备";
        break;
    case 1:
        columnName = "端口";
        break;
    case 2:
        columnName = "状态";
        break;
    case 3:
        columnName = "操作";
        break;
    default:
        columnName = QString("列%1").arg(logicalIndex);
    }

    qDebug() << "DevicePortWidget: 用户调整了" << columnName << "列宽：" << oldSize << "px →" << newSize << "px";

    // 更新状态显示
    updateStatus(QString("%1列宽已调整为%2px").arg(columnName).arg(newSize), StatusType::Info);

    // Linus: "Adapt widget size to content changes"
    // 可选：动态调整widget总宽度
    int          totalWidth = 0;
    QHeaderView *header     = m_devicePortTable->horizontalHeader();
    for (int i = 0; i < header->count(); ++i) {
        totalWidth += header->sectionSize(i);
    }
    totalWidth += 20;  // 边距

    if (totalWidth > minimumWidth() && totalWidth <= 500) {
        setMinimumWidth(totalWidth);
        qDebug() << "DevicePortWidget: 自适应调整总宽度到" << totalWidth << "px";
    }
}

void DevicePortWidget::onHeaderSectionDoubleClicked(int logicalIndex) {
    // Linus: "Auto-size to content is the most useful double-click action"
    QString columnName;
    switch (logicalIndex) {
    case 0:
        columnName = "设备";
        break;
    case 1:
        columnName = "端口";
        break;
    case 2:
        columnName = "状态";
        break;
    case 3:
        columnName = "操作";
        break;
    default:
        columnName = QString("列%1").arg(logicalIndex);
    }

    qDebug() << "DevicePortWidget: 双击" << columnName << "列，自动调整宽度";

    // 计算内容的最佳宽度
    int optimalWidth = calculateOptimalColumnWidth(logicalIndex);

    QHeaderView *header   = m_devicePortTable->horizontalHeader();
    int          oldWidth = header->sectionSize(logicalIndex);
    header->resizeSection(logicalIndex, optimalWidth);

    updateStatus(QString("双击自动调整%1列宽：%2px → %3px").arg(columnName).arg(oldWidth).arg(optimalWidth), StatusType::Success);

    qDebug() << "DevicePortWidget: 列" << logicalIndex << "从" << oldWidth << "px调整到" << optimalWidth << "px";
}

// 辅助方法实现
QString DevicePortWidget::getDeviceDisplayName(const QString &deviceId) const {
    // Linus: "Show meaningful names, not internal IDs"
    if (deviceId.contains("CH340")) {
        return "CH340设备";
    } else if (deviceId.contains("FTDI")) {
        return "FTDI设备";
    } else if (deviceId.contains("CP210")) {
        return "CP210x设备";
    } else if (deviceId.contains("Arduino")) {
        return "Arduino设备";
    } else if (deviceId.contains("Prolific")) {
        return "Prolific设备";
    } else if (deviceId.contains("USB串口")) {
        return "USB串口";
    } else if (deviceId.contains("Virtual") || deviceId.contains("虚拟")) {
        return "虚拟串口";
    } else if (deviceId.contains("JLink")) {
        return "JLink调试器";
    } else if (deviceId.startsWith("标准串口")) {
        return "标准串口";
    }

    // 默认显示简化的设备名
    return deviceId.length() > 12 ? deviceId.left(10) + "..." : deviceId;
}

QString DevicePortWidget::findBestPortForDevice(const QString &deviceId, const QStringList &availablePorts) const {
    // 简化的匹配逻辑
    Q_UNUSED(deviceId)
    for (const QString &portName : availablePorts) {
        if (!m_portDeviceMap.contains(portName)) {
            return portName;
        }
    }
    return QString();
}

bool DevicePortWidget::isDevicePortCompatible(const QString &deviceId, const QString &portName) const {
    // 简化的兼容性检查
    Q_UNUSED(deviceId)
    Q_UNUSED(portName)
    return true;  // 暂时总是返回兼容
}

QString DevicePortWidget::getStatusText(ConnectionStatus status) const {
    switch (status) {
    case ConnectionStatus::Connected:
        return "已连接";
    case ConnectionStatus::CanConnect:
        return "待连接";
    case ConnectionStatus::NoPort:
        return "无端口";
    case ConnectionStatus::UnknownDevice:
        return "待识别";
    case ConnectionStatus::Incompatible:
        return "不兼容";
    case ConnectionStatus::Unidentified:
        return "未识别";
    case ConnectionStatus::ManualIdentified:
        return "手动识别";
    case ConnectionStatus::AutoIdentified:
        return "自动识别";
    default:
        return "未知";
    }
}

QColor DevicePortWidget::getStatusColor(ConnectionStatus status) const {
    switch (status) {
    case ConnectionStatus::Connected:
        return QColor("#2e7d32");  // 绿色
    case ConnectionStatus::CanConnect:
        return QColor("#1976d2");  // 蓝色
    case ConnectionStatus::NoPort:
        return QColor("#f57c00");  // 橙色
    case ConnectionStatus::UnknownDevice:
        return QColor("#9c27b0");  // 紫色
    case ConnectionStatus::Incompatible:
        return QColor("#d32f2f");  // 红色
    case ConnectionStatus::Unidentified:
        return QColor("#757575");  // 灰色 - 未识别
    case ConnectionStatus::ManualIdentified:
        return QColor("#1976d2");  // 蓝色 - 手动识别
    case ConnectionStatus::AutoIdentified:
        return QColor("#2e7d32");  // 绿色 - 自动识别
    default:
        return QColor("#757575");  // 灰色
    }
}

void DevicePortWidget::tryIdentifyDevice(const QString &portName) {
    qDebug() << "DevicePortWidget: 尝试识别端口" << portName << "上的设备";

    // TODO: 实现设备识别逻辑
    updateStatus(QString("正在识别端口 %1 上的设备...").arg(portName), StatusType::Warning);
}

void DevicePortWidget::startDeviceIdentification(const QString &portName) {
    qDebug() << "DevicePortWidget: 启动端口" << portName << "的设备识别";

    if (!m_deviceMatchingService) {
        updateStatus("设备匹配服务未初始化，无法进行设备识别", StatusType::Error);
        return;
    }

    // 更新状态为识别中
    updatePortStatus(portName, "识别中...");
    updateStatus(QString("正在识别端口 %1 上的设备...").arg(portName), StatusType::Info);

    // 启动自动匹配（这会轮询所有设备类型）
    m_deviceMatchingService->autoMatchDevice(portName);
}

void DevicePortWidget::connectIdentifiedDevice(const QString &portName) {
    qDebug() << "DevicePortWidget: 连接端口" << portName << "上已识别的设备";

    // 获取已识别的设备ID
    QString deviceId = m_portDeviceMap.value(portName);
    if (deviceId.isEmpty()) {
        updateStatus(QString("端口 %1 没有已识别的设备").arg(portName), StatusType::Error);
        return;
    }

    // 执行连接
    if (connectDeviceToPort(deviceId, portName)) {
        updateStatus(QString("设备 %1 已连接到端口 %2").arg(deviceId).arg(portName), StatusType::Success);
        refreshDevicesAndPorts();
    } else {
        updateStatus(QString("连接设备 %1 到端口 %2 失败").arg(deviceId).arg(portName), StatusType::Error);
    }
}

void DevicePortWidget::updatePortStatus(const QString &portName, const QString &status) {
    // 在表格中找到对应端口的行并更新状态
    for (int row = 0; row < m_devicePortTable->rowCount(); ++row) {
        QTableWidgetItem *portItem = m_devicePortTable->item(row, 1);
        if (portItem && portItem->text() == portName) {
            QTableWidgetItem *statusItem = m_devicePortTable->item(row, 2);
            if (statusItem) {
                statusItem->setText(status);
                statusItem->setBackground(QColor("#fff3cd"));  // 黄色背景表示进行中
            }
            break;
        }
    }
}

// 状态查询接口
int DevicePortWidget::getConnectedDeviceCount() const {
    return m_devicePortMap.size();
}

QStringList DevicePortWidget::getConnectedDevices() const {
    return m_devicePortMap.keys();
}

bool DevicePortWidget::isDeviceConnected(const QString &deviceId) const {
    return m_devicePortMap.contains(deviceId);
}

// 智能工业设备识别算法
QString DevicePortWidget::identifyDeviceType(const QSerialPortInfo &portInfo) const {
    QString description  = portInfo.description().toLower();
    QString manufacturer = portInfo.manufacturer().toLower();
    QString portName     = portInfo.portName();

    // Linus: "Industrial devices have distinctive signatures"

    // 1. 通过USB厂商ID识别工业设备（最可靠）
    if (portInfo.hasVendorIdentifier()) {
        quint16 vendorId = portInfo.vendorIdentifier();
        switch (vendorId) {
        case 0x0403:
            return "工业传感器";  // FTDI芯片常用于传感器
        case 0x1A86:
            return "步进电机驱动器";  // CH340常用于电机控制
        case 0x10C4:
            return "温度传感器";  // CP210x常用于温度监控
        case 0x067B:
            return "压力传感器";  // Prolific用于压力监测
        case 0x1366:
            return "激光位移传感器";  // SEGGER (JLink)
        }
    }

    // 2. 通过设备描述识别工业设备类型
    if (description.contains("jlink") || description.contains("segger"))
        return "激光位移传感器";
    if (description.contains("ch340") || description.contains("ch341"))
        return "步进电机驱动器";
    if (description.contains("ftdi") || description.contains("ft232"))
        return "工业传感器";
    if (description.contains("cp210") || description.contains("cp2102"))
        return "温度传感器";
    if (description.contains("prolific") || description.contains("pl2303"))
        return "压力传感器";
    if (description.contains("virtual"))
        return "虚拟工业设备";
    if (description.contains("arduino"))
        return "原型控制器";

    // 3. 通过制造商信息识别
    if (manufacturer.contains("segger"))
        return "激光位移传感器";
    if (manufacturer.contains("wch"))
        return "步进电机驱动器";
    if (manufacturer.contains("ftdi"))
        return "工业传感器";
    if (manufacturer.contains("prolific"))
        return "压力传感器";
    if (manufacturer.contains("silicon"))
        return "温度传感器";
    if (manufacturer.contains("eltima"))
        return "虚拟工业设备";

    // 4. 通过端口名称和描述的组合识别
    if (portName.startsWith("COM")) {
        if (description.contains("uart"))
            return "通用工业控制器";
        if (description.contains("usb"))
            return "USB工业设备";
        return "工业通讯设备";
    }

    // 5. 默认工业设备分类
    if (description.contains("usb") || description.contains("serial")) {
        return "工业通讯设备";
    }

    // 6. 对于COM端口，提供基本的工业设备分类
    if (portName.startsWith("COM")) {
        return "工业串口设备";
    }

    return "未识别工业设备";
}

int DevicePortWidget::calculateOptimalColumnWidth(int columnIndex) const {
    // Linus: "Calculate based on actual content, not guesswork"
    QFontMetrics fontMetrics(m_devicePortTable->font());
    int          maxWidth = 0;

    // 检查列头宽度
    QString headerText  = m_devicePortTable->horizontalHeaderItem(columnIndex)->text();
    int     headerWidth = fontMetrics.horizontalAdvance(headerText) + 20;  // 20px padding
    maxWidth            = qMax(maxWidth, headerWidth);

    // 检查所有行的内容宽度
    for (int row = 0; row < m_devicePortTable->rowCount(); ++row) {
        QString cellText;

        if (columnIndex == 3) {             // 操作列
            maxWidth = qMax(maxWidth, 80);  // 按钮固定宽度
            continue;
        }

        QTableWidgetItem *item = m_devicePortTable->item(row, columnIndex);
        if (item) {
            cellText = item->text();
        }

        if (!cellText.isEmpty()) {
            int cellWidth = fontMetrics.horizontalAdvance(cellText) + 16;  // 16px padding
            maxWidth      = qMax(maxWidth, cellWidth);
        }
    }

    // 针对不同列的特殊优化
    switch (columnIndex) {
    case 0:                              // 设备列 - 适应更大的ComboBox
        maxWidth = qMax(maxWidth, 120);  // 增加最小宽度适应ComboBox
        maxWidth = qMin(maxWidth, 180);  // 增加最大宽度限制
        break;
    case 1:                              // 端口列
        maxWidth = qMax(maxWidth, 60);   // COM端口最小宽度
        maxWidth = qMin(maxWidth, 120);  // 端口名不会太长
        break;
    case 2:  // 状态列
        maxWidth = qMax(maxWidth, 60);
        maxWidth = qMin(maxWidth, 100);
        break;
    case 3:             // 操作列
        maxWidth = 80;  // 固定按钮宽度
        break;
    }

    return maxWidth;
}

// === 设备匹配服务槽函数实现 ===

void DevicePortWidget::onAutoMatchStarted(const QString &portName, int deviceCount) {
    qDebug() << "DevicePortWidget: 端口" << portName << "开始自动匹配，需检测" << deviceCount << "个设备";
    updateStatus(QString("正在自动匹配端口 %1，检测 %2 个设备...").arg(portName).arg(deviceCount), StatusType::Info);

    // 更新表格中对应端口的状态
    for (int row = 0; row < m_devicePortTable->rowCount(); ++row) {
        QTableWidgetItem *portItem = m_devicePortTable->item(row, 1);
        if (portItem && portItem->text() == portName) {
            QTableWidgetItem *statusItem = m_devicePortTable->item(row, 2);
            if (statusItem) {
                statusItem->setText("匹配中...");
                statusItem->setBackground(QColor("#fff3cd"));  // 黄色背景表示进行中
            }
            break;
        }
    }
}

void DevicePortWidget::onMatchingProgressChanged(const QString &portName, int progress, const QString &currentDevice) {
    qDebug() << "DevicePortWidget: 端口" << portName << "匹配进度" << progress << "% 当前检测:" << currentDevice;
    updateStatus(QString("端口 %1: %2% - 检测 %3").arg(portName).arg(progress).arg(currentDevice), StatusType::Info);

    // 更新表格显示进度
    for (int row = 0; row < m_devicePortTable->rowCount(); ++row) {
        QTableWidgetItem *portItem = m_devicePortTable->item(row, 1);
        if (portItem && portItem->text() == portName) {
            QTableWidgetItem *statusItem = m_devicePortTable->item(row, 2);
            if (statusItem) {
                statusItem->setText(QString("匹配中 %1%").arg(progress));
            }
            break;
        }
    }
}

void DevicePortWidget::onAutoMatchCompleted(const QString &portName, const LA::Communication::DeviceMatching::DeviceMatchResult &result) {
    qDebug() << "========================================";
    qDebug() << "🎯 DevicePortWidget: 端口" << portName << "自动匹配完成";
    qDebug() << "  ✅ 匹配成功:" << result.success;
    qDebug() << "  🔧 设备类型:" << result.deviceType;
    qDebug() << "  🆔 设备ID:" << result.deviceId;
    qDebug() << "  ⏱️  匹配耗时:" << result.matchTimeMs << "ms";
    qDebug() << "========================================";

    if (result.success) {
        // 匹配成功，更新设备-端口映射
        m_devicePortMap[result.deviceId] = portName;
        m_portDeviceMap[portName]        = result.deviceId;
        
        qDebug() << "🔗 DevicePortWidget: 更新内部映射表";
        qDebug() << "  📝 设备->端口映射:" << result.deviceId << "->" << portName;
        qDebug() << "  📝 端口->设备映射:" << portName << "->" << result.deviceId;

        updateStatus(QString("端口 %1 自动匹配成功: %2 (耗时 %3ms)").arg(portName).arg(result.deviceType).arg(result.matchTimeMs), StatusType::Success);

        // 更新表格显示
        for (int row = 0; row < m_devicePortTable->rowCount(); ++row) {
            QTableWidgetItem *portItem = m_devicePortTable->item(row, 1);
            if (portItem && portItem->text() == portName) {
                // 更新设备名称
                QTableWidgetItem *deviceItem = m_devicePortTable->item(row, 0);
                if (deviceItem) {
                    deviceItem->setText(result.deviceType);
                }

                // 更新状态为自动识别
                updateDevicePortRow(row, ConnectionStatus::AutoIdentified);

                // 同时更新设备下拉框的选择
                QWidget *deviceWidget = m_devicePortTable->cellWidget(row, 0);
                if (deviceWidget) {
                    QComboBox *deviceCombo = deviceWidget->findChild<QComboBox *>();
                    if (deviceCombo) {
                        // 找到匹配的设备ID并设置为当前选择
                        for (int i = 0; i < deviceCombo->count(); ++i) {
                            if (deviceCombo->itemData(i).toString() == result.deviceId) {
                                deviceCombo->setCurrentIndex(i);
                                break;
                            }
                        }
                    }
                }
                break;
            }
        }
    } else {
        updateStatus(QString("端口 %1 自动匹配失败: %2").arg(portName).arg(result.errorMessage), StatusType::Warning);

        // 重置状态为未识别
        for (int row = 0; row < m_devicePortTable->rowCount(); ++row) {
            QTableWidgetItem *portItem = m_devicePortTable->item(row, 1);
            if (portItem && portItem->text() == portName) {
                updateDevicePortRow(row, ConnectionStatus::Unidentified);
                break;
            }
        }
    }

    // 延迟刷新，避免频繁刷新
    QTimer::singleShot(1000, this, &DevicePortWidget::refreshDevicesAndPorts);
}

void DevicePortWidget::onManualMatchCompleted(const QString &portName, const LA::Communication::DeviceMatching::DeviceMatchResult &result) {
    qDebug() << "DevicePortWidget: 端口" << portName << "手动匹配完成，成功:" << result.success;

    if (result.success) {
        // 手动匹配成功处理
        m_devicePortMap[result.deviceId] = portName;
        m_portDeviceMap[portName]        = result.deviceId;

        updateStatus(QString("端口 %1 手动匹配成功: %2").arg(portName).arg(result.deviceType), StatusType::Success);
        // 延迟刷新，避免频繁刷新
        QTimer::singleShot(1000, this, &DevicePortWidget::refreshDevicesAndPorts);
    } else {
        updateStatus(QString("端口 %1 手动匹配失败: %2").arg(portName).arg(result.errorMessage), StatusType::Error);
    }
}

void DevicePortWidget::onMatchingStatusChanged(const QString &portName, const QString &status) {
    qDebug() << "DevicePortWidget: 端口" << portName << "状态变更:" << status;
    updateStatus(QString("端口 %1: %2").arg(portName).arg(status), StatusType::Info);
}

void DevicePortWidget::onMatchingFailed(const QString &portName, const QString &error) {
    qDebug() << "DevicePortWidget: 端口" << portName << "匹配失败:" << error;
    updateStatus(QString("端口 %1 匹配失败: %2").arg(portName).arg(error), StatusType::Error);

    // 重置表格中的状态
    for (int row = 0; row < m_devicePortTable->rowCount(); ++row) {
        QTableWidgetItem *portItem = m_devicePortTable->item(row, 1);
        if (portItem && portItem->text() == portName) {
            updateDevicePortRow(row, ConnectionStatus::CanConnect);
            break;
        }
    }
}

// === 手动匹配对话框实现 ===

void DevicePortWidget::showManualMatchDialog(const QString &deviceId) {
    // 现在不需要对话框，设备匹配已集成到表格的ComboBox中
    qDebug() << "DevicePortWidget: 手动匹配已集成到设备列表的下拉框中，设备:" << deviceId;
    updateStatus("请在设备列表中直接选择要匹配的设备", StatusType::Info);
}

// 原始对话框实现已移动到这里作为备份参考
/*
void DevicePortWidget::showManualMatchDialogOriginal(const QString& deviceId)
{
    if (!m_deviceMatchingService) {
        updateStatus("设备匹配服务未初始化", StatusType::Error);
        return;
    }

    // 获取可用端口
    auto availablePorts = QSerialPortInfo::availablePorts();
    if (availablePorts.isEmpty()) {
        updateStatus("没有可用端口", StatusType::Warning);
        return;
    }

    // 创建对话框
    QDialog dialog(this);
    dialog.setWindowTitle("手动匹配设备");
    dialog.setModal(true);
    dialog.resize(350, 200);

    QVBoxLayout* layout = new QVBoxLayout(&dialog);

    // 设备信息显示
    QLabel* deviceLabel = new QLabel(QString("设备: %1").arg(deviceId));
    deviceLabel->setStyleSheet("font-weight: bold; color: #2196f3;");
    layout->addWidget(deviceLabel);

    // 端口选择
    QFormLayout* formLayout = new QFormLayout();

    QComboBox* portCombo = new QComboBox();
    portCombo->addItem("选择端口...", "");

    for (const auto& portInfo : availablePorts) {
        QString portName = portInfo.portName();
        QString description = QString("%1 - %2").arg(portName).arg(portInfo.description());
        portCombo->addItem(description, portName);
    }

    formLayout->addRow("可用端口:", portCombo);

    // 匹配方式选择
    QComboBox* modeCombo = new QComboBox();
    modeCombo->addItem("自动匹配 (推荐)", "auto");
    modeCombo->addItem("手动确认", "manual");

    formLayout->addRow("匹配方式:", modeCombo);

    layout->addLayout(formLayout);

    // 按钮
    QDialogButtonBox* buttonBox = new QDialogButtonBox(
        QDialogButtonBox::Ok | QDialogButtonBox::Cancel, &dialog);

    connect(buttonBox, &QDialogButtonBox::accepted, &dialog, &QDialog::accept);
    connect(buttonBox, &QDialogButtonBox::rejected, &dialog, &QDialog::reject);

    layout->addWidget(buttonBox);

    // 显示对话框
    if (dialog.exec() == QDialog::Accepted) {
        QString selectedPort = portCombo->currentData().toString();
        QString matchMode = modeCombo->currentData().toString();

        if (selectedPort.isEmpty()) {
            updateStatus("请选择一个端口", StatusType::Warning);
            return;
        }

        qDebug() << "DevicePortWidget: 手动匹配" << deviceId << "到端口" << selectedPort << "模式:" << matchMode;

        if (matchMode == "auto") {
            // 自动匹配模式
            updateStatus(QString("开始自动匹配设备 %1 到端口 %2...").arg(deviceId).arg(selectedPort), StatusType::Info);
            m_deviceMatchingService->autoMatchDevice(selectedPort);
        } else {
            // 手动确认模式
            updateStatus(QString("手动匹配设备 %1 到端口 %2...").arg(deviceId).arg(selectedPort), StatusType::Info);
            m_deviceMatchingService->manualMatchDevice(selectedPort, deviceId);
        }
    }
}
*/

}  // namespace DeviceCommunication
}  // namespace UI
}  // namespace LA