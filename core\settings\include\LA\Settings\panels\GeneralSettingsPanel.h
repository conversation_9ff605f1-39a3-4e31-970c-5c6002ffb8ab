#pragma once

#include "../SettingsPanel.h"
#include <QCheckBox>
#include <QComboBox>
#include <QFileDialog>
#include <QGridLayout>
#include <QGroupBox>
#include <QLabel>
#include <QLineEdit>
#include <QPushButton>
#include <QSpinBox>

namespace LA {
namespace Settings {

/**
 * @brief 通用设置面板
 *
 * 提供通用的应用程序设置，包括语言、启动选项、界面布局等
 */
class GeneralSettingsPanel : public SettingsPanel {
    Q_OBJECT

  public:
    explicit GeneralSettingsPanel(QWidget *parent = nullptr);
    virtual ~GeneralSettingsPanel() = default;

  protected:
    // SettingsPanel接口实现
    void createUI() override;
    void connectSignals() override;
    void loadSpecificSettings() override;
    void saveSpecificSettings() override;
    void resetSpecificSettings() override;
    bool validateSpecificSettings() override;
    void applySpecificSettings() override;

  private slots:
    void onLanguageChanged(const QString &language);
    void onStartupBehaviorChanged();
    void onAutoSaveChanged(bool enabled);
    void onAutoSaveIntervalChanged(int minutes);
    void onWorkspacePathChanged();
    void onBrowseWorkspacePath();
    void onResetToDefaults();

  private:
    void setupLanguageSettings();
    void setupStartupSettings();
    void setupAutoSaveSettings();
    void setupWorkspaceSettings();
    void setupActionButtons();
    void loadAvailableLanguages();
    bool validateWorkspacePath();

  private:
    // 语言设置组件
    QGroupBox *m_languageGroup;
    QComboBox *m_languageComboBox;
    QLabel *   m_languageDescriptionLabel;

    // 启动设置组件
    QGroupBox *m_startupGroup;
    QCheckBox *m_startWithSystemCheckBox;
    QCheckBox *m_restoreLastSessionCheckBox;
    QCheckBox *m_showSplashScreenCheckBox;
    QCheckBox *m_checkUpdatesCheckBox;

    // 自动保存设置组件
    QGroupBox *m_autoSaveGroup;
    QCheckBox *m_autoSaveEnabledCheckBox;
    QSpinBox * m_autoSaveIntervalSpinBox;
    QLabel *   m_autoSaveIntervalLabel;

    // 工作空间设置组件
    QGroupBox *  m_workspaceGroup;
    QLineEdit *  m_workspacePathLineEdit;
    QPushButton *m_browseWorkspaceButton;
    QLabel *     m_workspaceDescriptionLabel;

    // 操作按钮
    QHBoxLayout *m_buttonLayout;
    QPushButton *m_resetToDefaultsButton;
    QPushButton *m_applyButton;

    // 当前设置
    QString m_currentLanguage;
    bool    m_startWithSystem;
    bool    m_restoreLastSession;
    bool    m_showSplashScreen;
    bool    m_checkUpdates;
    bool    m_autoSaveEnabled;
    int     m_autoSaveInterval;
    QString m_workspacePath;
};

}  // namespace Settings
}  // namespace LA
