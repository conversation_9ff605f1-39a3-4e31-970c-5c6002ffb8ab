#ifndef LA_THREADMONITOR_H
#define LA_THREADMONITOR_H

#include "../../include/LA/Thread/ThreadTypes.h"
#include <QObject>
#include <QTimer>
#include <QMap>

namespace LA {
namespace Thread {

/**
 * @brief 线程监控类
 * 
 * 监控系统中所有线程的运行状态、性能指标和资源使用情况。
 * 提供实时监控、性能分析和异常告警功能。
 */
class ThreadMonitor : public QObject
{
    Q_OBJECT

public:
    // 监控级别
    enum class MonitorLevel {
        Basic,      // 基础监控
        Detailed,   // 详细监控
        Diagnostic  // 诊断级监控
    };

    explicit ThreadMonitor(QObject* parent = nullptr);
    ~ThreadMonitor();

    // 监控控制
    void startMonitoring();
    void stopMonitoring();
    bool isMonitoring() const;
    void setMonitorLevel(MonitorLevel level);
    MonitorLevel getMonitorLevel() const;
    
    // 监控配置
    void setUpdateInterval(int intervalMs);
    int getUpdateInterval() const;
    void setPerformanceThreshold(double cpuThreshold, qint64 memoryThreshold);
    void enableAlerts(bool enable);
    bool areAlertsEnabled() const;
    
    // 线程注册
    void registerThread(const QString& name, QThread* thread);
    void unregisterThread(const QString& name);
    void registerThreadStatistics(const QString& name, const ThreadStatistics& stats);
    
    // 监控数据获取
    QList<ThreadStatistics> getAllThreadStatistics() const;
    ThreadStatistics getThreadStatistics(const QString& name) const;
    QMap<QString, double> getSystemMetrics() const;
    QStringList getActiveThreads() const;
    QStringList getProblematicThreads() const;
    
    // 性能分析
    double getAverageSystemCpuUsage() const;
    qint64 getTotalSystemMemoryUsage() const;
    int getTotalThreadCount() const;
    int getActiveThreadCount() const;
    double getSystemEfficiency() const;

signals:
    // 监控状态信号
    void monitoringStarted();
    void monitoringStopped();
    void monitoringDataUpdated();
    
    // 性能告警信号
    void threadPerformanceWarning(const QString& threadName, const ThreadStatistics& stats);
    void systemPerformanceWarning(double cpuUsage, qint64 memoryUsage);
    void threadUnresponsive(const QString& threadName, qint64 unresponsiveTime);
    void threadCrashed(const QString& threadName, const QString& reason);
    
    // 统计信号
    void systemMetricsUpdated(const QMap<QString, double>& metrics);
    void threadStatisticsUpdated(const QString& threadName, const ThreadStatistics& stats);

private slots:
    void updateMonitoringData();
    void checkPerformanceThresholds();
    void analyzeThreadBehavior();

private:
    struct MonitoringData {
        ThreadStatistics currentStats;
        ThreadStatistics previousStats;
        QList<double> cpuHistory;
        QList<qint64> memoryHistory;
        qint64 lastUpdateTime;
        int unresponsiveCount;
        bool isProblematic;
        
        MonitoringData() : lastUpdateTime(0), unresponsiveCount(0), isProblematic(false) {}
    };
    
    void collectThreadStatistics();
    void collectSystemMetrics();
    void analyzePerformanceTrends(const QString& threadName, MonitoringData& data);
    void detectAnomalies(const QString& threadName, const MonitoringData& data);
    void generateAlert(const QString& threadName, const QString& alertType, const QString& message);

private:
    // 监控状态
    bool m_isMonitoring;
    MonitorLevel m_monitorLevel;
    QTimer* m_updateTimer;
    int m_updateInterval;
    
    // 监控配置
    double m_cpuThreshold;
    qint64 m_memoryThreshold;
    bool m_alertsEnabled;
    
    // 监控数据
    QMap<QString, QThread*> m_registeredThreads;
    QMap<QString, MonitoringData> m_monitoringData;
    QMap<QString, double> m_systemMetrics;
    
    // 历史数据保留时长(秒)
    static const int HISTORY_RETENTION_SECONDS = 300;
    static const int MAX_HISTORY_POINTS = 100;
    
    Q_DISABLE_COPY(ThreadMonitor)
};

} // namespace Thread
} // namespace LA

Q_DECLARE_METATYPE(LA::Thread::ThreadMonitor::MonitorLevel)

#endif // LA_THREADMONITOR_H