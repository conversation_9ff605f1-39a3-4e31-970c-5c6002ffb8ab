#pragma once

#include <QObject>
#include <QTimer>
#include <QStringList>
#include <QMap>

// 前置声明
namespace LA {
namespace DeviceManagement {
    class DeviceInstanceManager;
    class DeviceManagementOrchestrator;
}
}

namespace LA {
namespace DeviceManagement {
namespace Legacy {

/**
 * @brief Legacy设备节点桥接器
 * 
 * 职责：
 * - 提供向后兼容的CDevNode接口
 * - 内部委托给现代化的DeviceInstanceManager
 * - 逐步迁移遗留代码到新架构
 * 
 * 设计原则：
 * 1. 桥接模式：新旧接口之间的适配
 * 2. 委托模式：实际逻辑由现代化组件处理
 * 3. 逐步迁移：保证兼容性的同时引导使用新接口
 */
class LegacyDeviceNodeBridge : public QObject {
    Q_OBJECT
    
public:
    ~LegacyDeviceNodeBridge();
    
    // 单例接口（兼容CDevNode::getInstance()）
    static LegacyDeviceNodeBridge &getInstance() {
        static LegacyDeviceNodeBridge instance;
        return instance;
    }
    
    // ====== 遗留设备注册配置结构（兼容性） ======
    struct StCreateConfig {
        QString device;        // 设备类型
        QString port_name;     // 端口名
        QString series_num;    // 序列号
    };
    
    struct StRegisterConfig {
        bool is_only;                    // 是否独占设备
        QString device_kind;             // 设备类别 
        StCreateConfig create_config;    // 创建配置
    };
    
    // ====== 遗留接口兼容（向后兼容CDevNode） ======
    
    // 设备注册接口
    void* registerDev(const StRegisterConfig &register_config);
    void* registerCommDev(const StRegisterConfig &register_config);
    
    // 设备管理接口
    bool cleanDev(const QString &instance_name);
    bool cleanCommDev(const QString &instance_name);
    void cleanCommDevs();
    
    // 设备查询接口
    uint8_t getDevsNum();
    uint8_t getCommDevsNum();
    void* getDev(const QString &key);
    void* getCommDev(const QString &key);
    
    // 设备控制接口
    bool startDev(const QString &instance_name);
    bool stopDev(const QString &instance_name);
    bool startAllDevs();
    bool stopAllDevs();
    
    // ====== 现代化接口（推荐使用） ======
    
    // 获取现代化设备实例管理器
    DeviceInstanceManager* getDeviceInstanceManager() const;
    
    // 获取设备管理编排器
    DeviceManagementOrchestrator* getDeviceManagementOrchestrator() const;
    
signals:
    // 兼容信号
    void devicesList_signal(const QStringList &device_list);
    void commDevicesList_signal(const QStringList &comm_device_list);
    void subThreadSignal(bool enable);
    
    // 现代化信号
    void deviceInstanceCreated(const QString &instanceId, const QString &deviceType);
    void deviceInstanceDestroyed(const QString &instanceId);
    
private slots:
    void onPeriodicUpdate();
    void onInstanceCreated(const QString &instanceId);
    void onInstanceDestroyed(const QString &instanceId);
    
private:
    explicit LegacyDeviceNodeBridge(QObject *parent = nullptr);
    
    // ====== 内部方法 ======
    void initializeModernComponents();
    void setupPeriodicUpdates();
    void updateDeviceLists();
    QString convertLegacyConfigToModern(const StRegisterConfig &legacyConfig, QVariantMap &modernConfig);
    
private:
    // 现代化组件依赖
    DeviceInstanceManager *m_deviceInstanceManager;
    DeviceManagementOrchestrator *m_orchestrator;
    
    // 周期性更新
    QTimer *m_periodicTimer;
    static const int PERIODIC_UPDATE_INTERVAL_MS = 5000;  // 5秒更新一次
    
    // 遗留数据缓存（用于兼容查询接口）
    struct LegacyDeviceInfo {
        QString instanceId;
        QString deviceType;
        QString deviceCategory;
        void* legacyDeviceObject;  // 保存原始设备对象指针
    };
    
    QMap<QString, LegacyDeviceInfo> m_legacyDeviceMap;     // instance_name -> device_info
    QMap<QString, LegacyDeviceInfo> m_legacyCommDeviceMap; // instance_name -> comm_device_info
    
    // 缓存的设备列表
    QStringList m_lastDevicesList;
    QStringList m_lastCommDevicesList;
    
    // 统计信息
    mutable uint8_t m_cachedDevCount;
    mutable uint8_t m_cachedCommDevCount;
    mutable bool m_needUpdateCounts;
};

} // namespace Legacy
} // namespace DeviceManagement  
} // namespace LA