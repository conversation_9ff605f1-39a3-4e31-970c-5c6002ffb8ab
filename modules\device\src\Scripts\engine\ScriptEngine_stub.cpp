/**
 * @file ScriptEngine_stub.cpp
 * @brief 脚本引擎存根实现 - 当Lua不可用时的简化版本
 */

#include "ScriptEngine.h"
#include <QDebug>

namespace LA::Device::Script {

ScriptEngine::ScriptEngine(QObject* parent)
    : QObject(parent)
    , m_luaState(nullptr)
    , m_sandboxEnabled(true)
    , m_memoryLimitKB(1024)
    , m_timeLimitMs(5000)
    , m_timeoutTimer(new QTimer(this))
    , m_executionCount(0)
    , m_totalExecutionTime(0)
{
    qWarning() << "[ScriptEngine] Using stub implementation - scripting disabled";
    m_timeoutTimer->setSingleShot(true);
    connect(m_timeoutTimer, &QTimer::timeout, this, &ScriptEngine::onExecutionTimeout);
}

ScriptEngine::~ScriptEngine() {
    // No cleanup needed in stub version
}

bool ScriptEngine::initializeLua() {
    qWarning() << "[ScriptEngine] Lua scripting not enabled";
    return false;
}

bool ScriptEngine::loadScript(const QString& scriptFile) {
    Q_UNUSED(scriptFile)
    qWarning() << "[ScriptEngine] loadScript called but Lua is disabled";
    return false;
}

bool ScriptEngine::reloadScript() {
    qWarning() << "[ScriptEngine] reloadScript called but Lua is disabled";
    return false;
}

QPair<bool, QString> ScriptEngine::validateScript(const QString& script) {
    Q_UNUSED(script)
    return qMakePair(false, QString("Scripting disabled - Lua not available"));
}

QVariantMap ScriptEngine::executeDeviceBehavior(const QString& deviceType, const QString& event, const QVariantMap& params) {
    Q_UNUSED(deviceType)
    Q_UNUSED(event)
    Q_UNUSED(params)
    
    QVariantMap result;
    result["success"] = false;
    result["error"] = "Scripting disabled - Lua not available";
    return result;
}

void ScriptEngine::registerDeviceAPI() {
    // No-op in stub version
}

void ScriptEngine::setupSandbox() {
    // No-op in stub version
}

QVariantMap ScriptEngine::executeStrategySelection(const QString& strategyType, const QVariantMap& input) {
    Q_UNUSED(strategyType);
    Q_UNUSED(input);
    QVariantMap result;
    result["success"] = false;
    result["error"] = "Scripting disabled - Lua not available";
    return result;
}

QVariant ScriptEngine::callFunction(const QString& functionName, const QVariantList& args) {
    Q_UNUSED(functionName);
    Q_UNUSED(args);
    return QVariant();
}

void ScriptEngine::setSandboxMode(bool enabled) {
    m_sandboxEnabled = enabled;
}

void ScriptEngine::setResourceLimits(int memoryLimitKB, int timeLimitMs) {
    m_memoryLimitKB = memoryLimitKB;
    m_timeLimitMs = timeLimitMs;
}

void ScriptEngine::setVariable(const QString& name, const QVariant& value) {
    Q_UNUSED(name);
    Q_UNUSED(value);
}

QVariant ScriptEngine::getVariable(const QString& name) {
    Q_UNUSED(name);
    return QVariant();
}

QVariantMap ScriptEngine::getExecutionStats() const {
    QVariantMap stats;
    stats["execution_count"] = 0;
    stats["total_execution_time"] = 0;
    stats["script_enabled"] = false;
    return stats;
}

QString ScriptEngine::getLastError() const {
    return "Scripting disabled - Lua not available";
}

void ScriptEngine::onExecutionTimeout() {
    emit executionTimeout();
}

QVariant ScriptEngine::luaToVariant(int index) const {
    Q_UNUSED(index);
    return QVariant();
}

void ScriptEngine::variantToLua(const QVariant& value) {
    Q_UNUSED(value);
}

bool ScriptEngine::checkMemoryLimit() const {
    return true;
}

void ScriptEngine::cleanup() {
    // No-op in stub version
}

// Static Lua callback functions - stubs
int ScriptEngine::luaDeviceExecuteCommand(lua_State* L) {
    Q_UNUSED(L);
    return 0;
}

int ScriptEngine::luaDeviceSetStrategy(lua_State* L) {
    Q_UNUSED(L);
    return 0;
}

int ScriptEngine::luaDeviceGetSpec(lua_State* L) {
    Q_UNUSED(L);
    return 0;
}

int ScriptEngine::luaDeviceGetEnvironment(lua_State* L) {
    Q_UNUSED(L);
    return 0;
}

int ScriptEngine::luaLogMessage(lua_State* L) {
    Q_UNUSED(L);
    return 0;
}

} // namespace LA::Device::Script