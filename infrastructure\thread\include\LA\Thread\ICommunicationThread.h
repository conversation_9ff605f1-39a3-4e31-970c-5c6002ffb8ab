#ifndef LA_ICOMMUNICATIONTHREAD_H
#define LA_ICOMMUNICATIONTHREAD_H

#include "ThreadTypes.h"
#include <QThread>
#include <QByteArray>

namespace LA {
namespace Thread {

/**
 * @brief 通信线程接口
 * 
 * 专门用于设备通信的线程，提供实时、可靠的数据传输能力。
 * 支持多种通信协议和连接方式，具备自动重连和错误恢复功能。
 */
class ICommunicationThread : public QThread
{
    Q_OBJECT

public:
    // 通信状态
    enum class CommunicationState {
        Disconnected,   // 未连接
        Connecting,     // 连接中
        Connected,      // 已连接
        Communicating,  // 通信中
        Reconnecting,   // 重连中
        Error           // 错误状态
    };

    // 通信类型
    enum class CommunicationType {
        Interaction,    // 交互式通信
        Analysis,       // 数据分析通信
        RawData,        // 原始数据通信
        Command         // 命令通信
    };

    virtual ~ICommunicationThread() = default;

    // 基本信息
    virtual QString getDeviceId() const = 0;
    virtual QString getDeviceName() const = 0;
    virtual CommunicationState getCommunicationState() const = 0;
    virtual CommunicationType getCommunicationType() const = 0;
    
    // 连接管理
    virtual bool connectToDevice() = 0;
    virtual void disconnectFromDevice() = 0;
    virtual bool isConnected() const = 0;
    virtual bool isConnecting() const = 0;
    
    // 通信控制
    virtual void startCommunication() = 0;
    virtual void stopCommunication() = 0;
    virtual bool isCommunicating() const = 0;
    virtual void pauseCommunication() = 0;
    virtual void resumeCommunication() = 0;
    
    // 数据传输
    virtual bool sendData(const QByteArray& data) = 0;
    virtual bool sendCommand(const QByteArray& command) = 0;
    virtual void clearSendQueue() = 0;
    virtual int getSendQueueSize() const = 0;
    
    // 配置管理
    virtual void setCommunicationType(CommunicationType type) = 0;
    virtual void setAutoReconnect(bool enable) = 0;
    virtual bool isAutoReconnectEnabled() const = 0;
    virtual void setReconnectInterval(int intervalMs) = 0;
    virtual void setDataTimeout(int timeoutMs) = 0;
    
    // 统计信息
    virtual qint64 getBytesSent() const = 0;
    virtual qint64 getBytesReceived() const = 0;
    virtual qint64 getMessagesSent() const = 0;
    virtual qint64 getMessagesReceived() const = 0;
    virtual qint64 getConnectionTime() const = 0;
    virtual int getErrorCount() const = 0;
    
    // 错误处理
    virtual QString getLastError() const = 0;
    virtual void clearErrors() = 0;
    virtual void resetStatistics() = 0;

signals:
    // 连接状态信号
    void connectionStateChanged(CommunicationState oldState, CommunicationState newState);
    void deviceConnected(const QString& deviceId);
    void deviceDisconnected(const QString& deviceId);
    void connectionError(const QString& deviceId, const QString& error);
    
    // 数据传输信号
    void dataReceived(const QString& deviceId, const QByteArray& data);
    void dataSent(const QString& deviceId, const QByteArray& data);
    void commandSent(const QString& deviceId, const QByteArray& command);
    
    // 协议解析信号
    void frameReceived(const QString& deviceId, const QByteArray& frame);
    void frameParsed(const QString& deviceId, const QMap<QString, QString>& parsedData);
    void protocolError(const QString& deviceId, const QString& error);
    
    // 状态监控信号
    void communicationStarted(const QString& deviceId);
    void communicationStopped(const QString& deviceId);
    void statisticsUpdated(const QString& deviceId, qint64 bytesSent, qint64 bytesReceived);
    void performanceWarning(const QString& deviceId, const QString& warning);

public slots:
    // 外部控制槽
    virtual void handleExternalCommand(const QByteArray& command) = 0;
    virtual void handleConfigurationChange(const QString& key, const QVariant& value) = 0;
    virtual void handleEmergencyStop() = 0;

protected:
    explicit ICommunicationThread(QObject* parent = nullptr) : QThread(parent) {}

    // 子类需要实现的核心方法
    virtual void initializeDevice() = 0;
    virtual bool establishConnection() = 0;
    virtual void processReceivedData(const QByteArray& data) = 0;
    virtual bool parseProtocolFrame(const QByteArray& frame, QMap<QString, QString>& parsedData) = 0;
    virtual void handleCommunicationError(const QString& error) = 0;

private:
    Q_DISABLE_COPY(ICommunicationThread)
};

} // namespace Thread
} // namespace LA

Q_DECLARE_METATYPE(LA::Thread::ICommunicationThread::CommunicationState)
Q_DECLARE_METATYPE(LA::Thread::ICommunicationThread::CommunicationType)

#endif // LA_ICOMMUNICATIONTHREAD_H