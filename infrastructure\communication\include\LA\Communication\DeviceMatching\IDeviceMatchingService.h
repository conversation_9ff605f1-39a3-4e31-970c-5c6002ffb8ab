#pragma once

/**
 * @file IDeviceMatchingService.h
 * @brief 设备端口匹配服务接口
 * 
 * 支持自动匹配和手动匹配两种方式：
 * 1. 自动匹配：通过版本查询指令在5秒内识别设备类型
 * 2. 手动匹配：用户从下拉框选择可通讯设备
 */

#include <QObject>
#include <QString>
#include <QStringList>
#include <QTimer>
#include <memory>

namespace LA {
namespace Communication {
namespace DataStructures {
struct DeviceAttributes;
}
namespace DeviceMatching {

/**
 * @brief 设备匹配结果
 */
struct DeviceMatchResult {
    bool success = false;           // 匹配是否成功
    QString deviceId;               // 匹配到的设备ID
    QString deviceType;             // 设备类型
    QString portName;               // 端口名称
    QString versionInfo;            // 版本信息
    QString matchMethod;            // 匹配方式 ("auto" | "manual")
    int matchTimeMs = 0;            // 匹配耗时(毫秒)
    QString errorMessage;           // 错误信息
    
    DeviceMatchResult() = default;
    DeviceMatchResult(const QString& error) : errorMessage(error) {}
    
    bool isValid() const { return success && !deviceId.isEmpty(); }
};

/**
 * @brief 版本查询指令配置
 */
struct VersionQueryCommand {
    QString deviceType;             // 设备类型
    QString command;                // 查询指令
    QString expectedResponse;       // 期望响应模式(正则表达式)
    int timeoutMs = 1000;           // 超时时间
    QString description;            // 描述
    
    VersionQueryCommand() = default;
    VersionQueryCommand(const QString& type, const QString& cmd, const QString& response)
        : deviceType(type), command(cmd), expectedResponse(response) {}
};

/**
 * @brief 可通讯设备信息
 */
struct CommunicableDevice {
    QString deviceId;               // 设备ID
    QString deviceType;             // 设备类型  
    QString displayName;            // 显示名称
    QString description;            // 描述信息
    VersionQueryCommand versionQuery; // 版本查询配置
    bool isAutoDetectable = true;   // 是否支持自动检测
    
    CommunicableDevice() = default;
    CommunicableDevice(const QString& id, const QString& type, const QString& name)
        : deviceId(id), deviceType(type), displayName(name) {}
};

/**
 * @brief 设备端口匹配服务接口
 */
class IDeviceMatchingService : public QObject 
{
    Q_OBJECT

public:
    explicit IDeviceMatchingService(QObject* parent = nullptr) : QObject(parent) {}
    virtual ~IDeviceMatchingService() = default;
    
    // === 核心匹配功能 ===
    
    /**
     * @brief 自动匹配端口设备 (5秒内完成)
     * @param portName 端口名称
     * @return 匹配结果
     */
    virtual DeviceMatchResult autoMatchDevice(const QString& portName) = 0;
    
    /**
     * @brief 手动匹配设备到端口
     * @param portName 端口名称  
     * @param deviceId 选择的设备ID
     * @return 匹配结果
     */
    virtual DeviceMatchResult manualMatchDevice(const QString& portName, const QString& deviceId) = 0;
    
    /**
     * @brief 取消正在进行的匹配过程
     * @param portName 端口名称
     */
    virtual void cancelMatching(const QString& portName) = 0;
    
    // === 设备管理 ===
    
    /**
     * @brief 获取所有可通讯设备列表
     * @return 设备列表
     */
    virtual QList<CommunicableDevice> getCommunicableDevices() const = 0;
    
    /**
     * @brief 注册新的可通讯设备
     * @param device 设备信息
     */
    virtual void registerCommunicableDevice(const CommunicableDevice& device) = 0;
    
    /**
     * @brief 添加版本查询指令
     * @param deviceType 设备类型
     * @param command 查询指令配置
     */
    virtual void addVersionQueryCommand(const QString& deviceType, const VersionQueryCommand& command) = 0;
    
    // === 查询功能 ===
    
    /**
     * @brief 获取端口的匹配结果
     * @param portName 端口名称
     * @return 匹配结果，如果未匹配返回invalid result
     */
    virtual DeviceMatchResult getPortMatchResult(const QString& portName) const = 0;
    
    /**
     * @brief 检查端口是否正在匹配中
     * @param portName 端口名称
     */
    virtual bool isMatching(const QString& portName) const = 0;
    
    /**
     * @brief 获取匹配进度 (0-100)
     * @param portName 端口名称
     */
    virtual int getMatchingProgress(const QString& portName) const = 0;

signals:
    /**
     * @brief 自动匹配开始
     * @param portName 端口名称
     * @param deviceCount 要检测的设备数量
     */
    void autoMatchStarted(const QString& portName, int deviceCount);
    
    /**
     * @brief 匹配进度更新
     * @param portName 端口名称
     * @param progress 进度 (0-100)
     * @param currentDevice 当前检测的设备类型
     */
    void matchingProgressChanged(const QString& portName, int progress, const QString& currentDevice);
    
    /**
     * @brief 自动匹配完成
     * @param portName 端口名称
     * @param result 匹配结果
     */
    void autoMatchCompleted(const QString& portName, const DeviceMatchResult& result);
    
    /**
     * @brief 手动匹配完成
     * @param portName 端口名称
     * @param result 匹配结果
     */
    void manualMatchCompleted(const QString& portName, const DeviceMatchResult& result);
    
    /**
     * @brief 匹配过程中的状态更新
     * @param portName 端口名称
     * @param status 状态信息
     */
    void matchingStatusChanged(const QString& portName, const QString& status);
    
    /**
     * @brief 匹配失败
     * @param portName 端口名称
     * @param error 错误信息
     */
    void matchingFailed(const QString& portName, const QString& error);
};

} // namespace DeviceMatching
} // namespace Communication
} // namespace LA