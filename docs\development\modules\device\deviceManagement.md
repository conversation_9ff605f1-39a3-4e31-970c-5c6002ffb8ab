# LA设备管理模块

**最后更新**: 2025-01-18  
**状态**: 🎯 **Linus式双层架构设计完成**

## 概述

设备管理模块采用**Linus式双层架构**设计，严格分离设备类型管理和设备实例管理：
[[LA/docs/development/modules/device/deviceSystem|deviceSystem]]
### **双层架构职责分离**

**第一层：设备类型系统**（编译时静态）
- **设备类型注册表** (`modules/device/Types/`)：管理系统支持哪些设备类型，元数据定义
- **静态注册机制**：编译时确定支持的设备种类，性能极佳
- **类型查询服务**：查询通信设备类型数量、测距设备类型等

**第二层：设备实例系统**（运行时动态）  
- **设备实例管理器** (`modules/device_management/Instances/`)：管理具体创建的设备对象
- **生命周期管理**：设备实例的创建、启动、连接、停止、销毁
- **运行时操作**：设备发现、端口匹配、注册流程、状态监控

### **与设备系统模块的协调**
- **设备系统模块** (`modules/device/`)：提供**设备类型注册表、统一指令系统、数据流协调器**
- **设备管理模块** (`modules/device_management/`)：提供**设备实例管理、发现注册流程、生命周期控制**

## 🎯 **Linus式双层架构设计**

### **"Is this a real problem?" - 真实问题识别**

原架构混合了两个不同概念：
- ❌ **设备类型定义**（编译时确定）与**设备实例管理**（运行时动态）混合
- ❌ 查询"有多少种通信设备"与"有多少个设备实例"使用相同接口
- ❌ 性能问题：运行时查询需要编译时就能确定的信息

### **"Is there a simpler way?" - 双层架构解决**

| 层次 | 职责 | 时机 | 性能特点 | 典型查询 |
|------|------|------|----------|----------|
| **设备类型层** | 类型定义、元数据管理 | 编译时静态 | 极快 | "系统支持几种通信设备类型？" |
| **设备实例层** | 实例创建、生命周期管理 | 运行时动态 | 灵活管理 | "当前有几个设备实例在运行？" |

### **"What will this break?" - 兼容性保证**

✅ **不会破坏任何现有功能**，只会带来：
- 性能提升：编译时查询vs运行时查询
- 职责清晰：类型管理vs实例管理
- 代码简化：静态注册vs动态管理
- 测试简化：编译时验证vs运行时测试

### **Linus式设计原则**

1. **"做一件事，做好一件事"**: 类型注册表只管类型，实例管理器只管实例
2. **"Talk is cheap, show me the code"**: 编译时确定类型，运行时创建实例
3. **"Bad programmers worry about the code"**: 重视数据结构分层设计
4. **最小模块原则**: 两个独立模块，职责清晰，可独立测试
5. **依赖注入**: 实例管理器依赖类型注册表，不相互耦合

## 🏗️ **双层架构实现**

### **第一层：设备类型系统（编译时静态）**

#### **1. DeviceTypeRegistry（设备类型注册表）** - 编译时元数据
```cpp
namespace LA::Device::Types {
/**
 * @brief 设备类型注册表 - 编译时静态注册
 * 
 * 定义系统支持哪些种类的设备，每种设备的元数据
 * 类似于C++的type_traits，编译时就确定
 */
class DeviceTypeRegistry {
public:
    // ====== 设备类型查询 (编译时确定) ======
    static QStringList getSupportedDeviceTypes();
    static QStringList getDeviceTypesByCategory(DeviceCategory category);
    static int getCommunicationDeviceTypeCount();      // 查询通信设备类型数量
    static int getRangingDeviceTypeCount();            // 查询测距设备类型数量
    
    // ====== 设备类型元数据 ======
    static DeviceTypeMetadata getDeviceTypeMetadata(const QString& deviceType);
    static QStringList getSupportedCommandsByType(const QString& deviceType);
    static QStringList getSupportedProtocolsByType(const QString& deviceType);
    
    // ====== 静态注册机制 (编译时) ======
    template<typename DeviceClass>
    static bool registerDeviceType();
    
private:
    static QMap<QString, DeviceTypeMetadata> s_deviceTypes; // 编译时填充
};

// 设备类型元数据（编译时确定）
struct DeviceTypeMetadata {
    QString typeName;                    // 设备类型名
    DeviceCategory category;             // 设备类别
    QStringList supportedCommands;       // 支持的命令列表
    QStringList supportedProtocols;      // 支持的协议列表
    QStringList supportedModes;          // 支持的模式列表
    QString defaultProtocol;             // 默认协议
    int defaultBaudRate;                 // 默认波特率
    bool requiresCommunication;          // 是否需要通信
    QVariantMap typeCapabilities;        // 类型级别能力
    
    // 工厂函数指针（用于实例创建）
    std::function<BaseDevice*(const QVariantMap&)> createInstance;
};

// 静态注册宏
#define REGISTER_DEVICE_TYPE(DeviceClass, TypeName) \
    static bool _registered_##DeviceClass = \
        DeviceTypeRegistry::registerDeviceType<DeviceClass>(TypeName);

}
```

### **第二层：设备实例系统（运行时动态）**

#### **1. DeviceInstanceManager（设备实例管理器）** - 运行时动态管理
```cpp
namespace LA::Device::Instances {
/**
 * @brief 设备实例管理器 - 运行时动态管理
 * 
 * 管理具体创建的设备对象实例，生命周期管理
 */
class DeviceInstanceManager : public QObject {
    Q_OBJECT
    
public:
    // ====== 实例创建和销毁 ======
    QString createDeviceInstance(const QString& deviceType, const QVariantMap& config);
    bool destroyDeviceInstance(const QString& instanceId);
    
    // ====== 实例查询 ======
    QStringList getActiveInstanceIds();                     // 当前有几个设备实例在运行
    QStringList getInstancesByType(const QString& deviceType);
    DeviceInstanceInfo getInstanceInfo(const QString& instanceId);
    
    // ====== 实例操作 ======
    bool connectInstance(const QString& instanceId);
    bool disconnectInstance(const QString& instanceId);
    bool isInstanceConnected(const QString& instanceId) const;
    
    // ====== 生命周期管理 ======
    InstanceLifecycle getInstanceLifecycle(const QString& instanceId);
    bool startInstance(const QString& instanceId);
    bool stopInstance(const QString& instanceId);
    
signals:
    void instanceCreated(const QString& instanceId);
    void instanceDestroyed(const QString& instanceId);
    void instanceConnected(const QString& instanceId);
    void instanceDisconnected(const QString& instanceId);
    
private:
    QMap<QString, DeviceInstanceData> m_activeInstances; // 运行时管理
};

// 设备实例数据（运行时变化）
struct DeviceInstanceData {
    QString instanceId;                  // 实例唯一ID
    QString deviceType;                  // 设备类型（引用类型注册表）
    QVariantMap instanceConfig;          // 实例特定配置
    BaseDevice* deviceObject;            // 设备对象指针
    ConnectionStatus connectionStatus;    // 连接状态
    InstanceLifecycle lifecycle;         // 生命周期状态
    QDateTime createTime;                // 创建时间
    QDateTime lastActiveTime;            // 最后活动时间
    QVariantMap runtimeData;             // 运行时数据
};

enum class InstanceLifecycle {
    Created,        // 已创建，未初始化
    Initialized,    // 已初始化，未启动
    Started,        // 已启动，可工作
    Connected,      // 已连接，通信正常
    Error,          // 错误状态
    Stopped,        // 已停止
    Destroyed       // 已销毁
};

}
```

#### **2. DeviceDiscoveryService（设备发现服务）** - 单一职责
**职责**: 只负责发现可通讯设备，与类型系统协作
```cpp
namespace LA::DeviceManagement::Discovery {
class DeviceDiscoveryService {
    // ✅ 负责: 设备扫描、类型识别（通过DeviceTypeRegistry）
    // ❌ 不涉及: 设备实例创建、端口操作、连接管理
    QList<DeviceDiscoveryResult> discoverDevices();
    QString identifyDeviceType(const QVariantMap& deviceInfo);  // 使用DeviceTypeRegistry
    QStringList getSupportedPortTypesForDevice(const QString& deviceType);
};
}
```

#### **3. PortDiscoveryService（端口发现服务适配器）** - 最小接口
**职责**: 只负责发现系统可用端口

#### **4. DevicePortMatcher（设备端口匹配器）** - 纯粹匹配
**职责**: 只负责设备和端口的匹配逻辑
```cpp
namespace LA::DeviceManagement::Matching {
class DevicePortMatcher {
    // ✅ 负责: 匹配算法、兼容性验证、匹配评分
    // ❌ 不涉及: 设备实例化、端口打开、数据传输
    QList<MatchingResult> matchDevicesToPorts(const QList<DeviceDiscoveryResult>& devices,
                                             const QList<PortDiscoveryResult>& ports);
    bool validateMatch(const DeviceDiscoveryResult& device, const PortDiscoveryResult& port);
};
}
```

#### **4. RegistrationManager（注册管理器）** - 统一注册
**职责**: 统一管理设备和端口的注册过程
```cpp
namespace LA::DeviceManagement::Registration {
class RegistrationManager {
    // ✅ 负责: 注册流程、映射关系、状态跟踪
    // ❌ 不涉及: 具体设备业务逻辑、数据传输
    RegistrationResult registerDeviceConnection(const MatchingResult& match);
    QMap<QString, QString> getDevicePortMappings() const;
};
}
```

#### **5. LifecycleController（生命周期控制器）** - 协调同步
**职责**: 协调设备和端口的生命周期同步
```cpp
namespace LA::DeviceManagement::Lifecycle {
class LifecycleController {
    // ✅ 负责: 生命周期协调、状态同步、错误恢复
    // ❌ 不涉及: 设备业务逻辑、协议处理
    bool synchronizeDevicePortLifecycle(const QString& deviceId, const QString& portName);
    bool openDeviceConnection(const QString& deviceId);  // 同步开启设备和端口
    bool closeDeviceConnection(const QString& deviceId); // 同步关闭设备和端口
};
}
```

### **与其他模块的协调关系**

#### **职责边界清晰化**
- **依赖设备系统模块**: 使用UnifiedDeviceRegistry、UnifiedCommandSystem、DeviceDataFlowCoordinator
- **与通信模块协调**: 通过IProtocol、IConnection实现数据传输
- **与UI模块集成**: 通过DeviceManagementPanel在右侧边栏显示
- **为应用层服务**: 提供完整的设备发现→匹配→注册→管理流程

#### **数据流向 (符合数据流架构)**
```mermaid
graph TB
    subgraph "设备管理模块 (Device Management)"
        DDS[DeviceDiscoveryService<br/>设备发现]
        PDS[PortDiscoveryService<br/>端口发现]
        DPM[DevicePortMatcher<br/>设备端口匹配]
        RM[RegistrationManager<br/>注册管理]
        LC[LifecycleController<br/>生命周期控制]
    end
    
    subgraph "设备系统模块 (Device System)"
        UDR[UnifiedDeviceRegistry<br/>统一设备注册表]
        UCS[UnifiedCommandSystem<br/>统一指令系统]
        DDFC[DeviceDataFlowCoordinator<br/>数据流协调器]
        MBD[ModernBaseDevice<br/>现代化设备基类]
    end
    
    subgraph "通信模块 (Communication)"
        IP[IProtocol<br/>协议处理]
        IC[IConnection<br/>连接管理]
        PM[PortManager<br/>端口管理]
    end
    
    DDS --> DPM
    PDS --> DPM
    DPM --> RM
    RM --> LC
    LC --> DDFC
    
    UDR --> UCS
    UCS --> DDFC
    DDFC --> MBD
    
    DDFC --> IP
    IP --> IC
    IC --> PM
    
    classDef management fill:#e3f2fd
    classDef system fill:#f3e5f5
    classDef communication fill:#e8f5e8
    
    class DDS,PDS,DPM,RM,LC management
    class UDR,UCS,DDFC,MBD system
    class IP,IC,PM communication
```

## 📋 **完整设备端口注册流程实现**

### **流程编排器 (DeviceManagementOrchestrator)**

```cpp
namespace LA::DeviceManagement {

/**
 * @brief 设备管理流程编排器
 * 
 * 实现device_port_registration.md中描述的完整流程
 * 遵循数据流架构的组合协调原则
 */
class DeviceManagementOrchestrator : public QObject {
    Q_OBJECT
    
public:
    explicit DeviceManagementOrchestrator(QObject* parent = nullptr);
    
    // ====== 完整注册流程 ======
    void executeFullDiscoveryAndRegistrationFlow();
    void executeDeviceDiscoveryFlow();
    void executePortDiscoveryFlow();
    void executeMatchingFlow();
    void executeRegistrationFlow();
    void executeLifecycleFlow();
    
    // ====== 流程状态查询 ======
    FlowState getCurrentFlowState() const;
    QStringList getDiscoveredDevices() const;
    QStringList getDiscoveredPorts() const;
    QList<MatchingResult> getMatchingResults() const;
    QList<RegistrationResult> getRegistrationResults() const;
    
    // ====== 流程控制 ======
    void pauseFlow();
    void resumeFlow();
    void cancelFlow();
    void resetFlow();
    
signals:
    // 流程状态信号
    void flowStateChanged(FlowState newState);
    void flowCompleted();
    void flowError(const QString& error);
    
    // 阶段完成信号
    void deviceDiscoveryCompleted(int deviceCount);
    void portDiscoveryCompleted(int portCount);
    void matchingCompleted(int matchCount);
    void registrationCompleted(int registrationCount);
    void lifecycleInitialized();
    
private slots:
    void onDeviceDiscoveryFinished();
    void onPortDiscoveryFinished();
    void onMatchingFinished();
    void onRegistrationFinished();
    void onLifecycleInitialized();
    
private:
    // ====== 流程步骤实现 ======
    void step1_DiscoverDevices();
    void step2_DiscoverPorts();
    void step3_MatchDevicesToPorts();
    void step4_RegisterConnections();
    void step5_InitializeLifecycles();
    
    // ====== 错误处理 ======
    void handleDiscoveryError(const QString& error);
    void handleMatchingError(const QString& error);
    void handleRegistrationError(const QString& error);
    
private:
    // ====== 组合最小模块 ======
    std::unique_ptr<Discovery::DeviceDiscoveryService> m_deviceDiscovery;
    std::unique_ptr<Discovery::PortDiscoveryService> m_portDiscovery;
    std::unique_ptr<Matching::DevicePortMatcher> m_matcher;
    std::unique_ptr<Registration::RegistrationManager> m_registrationManager;
    std::unique_ptr<Lifecycle::LifecycleController> m_lifecycleController;
    
    // ====== 流程状态 ======
    FlowState m_currentState;
    QList<DeviceDiscoveryResult> m_discoveredDevices;
    QList<PortDiscoveryResult> m_discoveredPorts;
    QList<MatchingResult> m_matchingResults;
    QList<RegistrationResult> m_registrationResults;
};

enum class FlowState {
    Idle,
    DiscoveringDevices,
    DiscoveringPorts,
    Matching,
    Registering,
    InitializingLifecycle,
    Completed,
    Error,
    Cancelled
};

}
```

### **完整流程实现示例**

```cpp
void DeviceManagementOrchestrator::executeFullDiscoveryAndRegistrationFlow() {
    m_currentState = FlowState::DiscoveringDevices;
    emit flowStateChanged(m_currentState);
    
    // Step 1: 设备发现
    step1_DiscoverDevices();
}

void DeviceManagementOrchestrator::step1_DiscoverDevices() {
    qDebug() << "Step 1: Starting device discovery...";
    
    connect(m_deviceDiscovery.get(), &DeviceDiscoveryService::discoveryFinished,
            this, &DeviceManagementOrchestrator::onDeviceDiscoveryFinished);
    
    m_deviceDiscovery->setDiscoveryTimeout(10000); // 10秒超时
    m_deviceDiscovery->setDiscoveryFilters({"ProximitySensor", "Motor"});
    
    auto discoveredDevices = m_deviceDiscovery->discoverDevices();
    m_discoveredDevices = discoveredDevices;
    
    qDebug() << QString("Discovered %1 devices").arg(discoveredDevices.size());
    onDeviceDiscoveryFinished();
}

void DeviceManagementOrchestrator::onDeviceDiscoveryFinished() {
    emit deviceDiscoveryCompleted(m_discoveredDevices.size());
    
    // 自动进入下一步：端口发现
    m_currentState = FlowState::DiscoveringPorts;
    emit flowStateChanged(m_currentState);
    step2_DiscoverPorts();
}

void DeviceManagementOrchestrator::step2_DiscoverPorts() {
    qDebug() << "Step 2: Starting port discovery...";
    
    auto discoveredPorts = m_portDiscovery->discoverPorts();
    m_discoveredPorts = discoveredPorts;
    
    qDebug() << QString("Discovered %1 ports").arg(discoveredPorts.size());
    onPortDiscoveryFinished();
}

void DeviceManagementOrchestrator::step3_MatchDevicesToPorts() {
    qDebug() << "Step 3: Matching devices to ports...";
    
    m_matcher->setMatchingStrategy(MatchingStrategy::BestMatch);
    auto matchingResults = m_matcher->matchDevicesToPorts(m_discoveredDevices, m_discoveredPorts);
    m_matchingResults = matchingResults;
    
    qDebug() << QString("Created %1 device-port matches").arg(matchingResults.size());
    onMatchingFinished();
}

void DeviceManagementOrchestrator::step4_RegisterConnections() {
    qDebug() << "Step 4: Registering device connections...";
    
    for (const auto& match : m_matchingResults) {
        auto result = m_registrationManager->registerDeviceConnection(match);
        m_registrationResults.append(result);
        
        if (result.success) {
            qDebug() << QString("Registered device %1 to port %2")
                            .arg(result.deviceId, result.portName);
        } else {
            qWarning() << QString("Failed to register device %1: %2")
                              .arg(result.deviceId, result.error);
        }
    }
    
    onRegistrationFinished();
}

void DeviceManagementOrchestrator::step5_InitializeLifecycles() {
    qDebug() << "Step 5: Initializing device lifecycles...";
    
    for (const auto& result : m_registrationResults) {
        if (result.success) {
            bool syncSuccess = m_lifecycleController->synchronizeDevicePortLifecycle(
                result.deviceId, result.portName);
            
            if (syncSuccess) {
                qDebug() << QString("Lifecycle synchronized for device %1").arg(result.deviceId);
            } else {
                qWarning() << QString("Failed to sync lifecycle for device %1").arg(result.deviceId);
            }
        }
    }
    
    m_currentState = FlowState::Completed;
    emit flowStateChanged(m_currentState);
    emit flowCompleted();
    
    qDebug() << "Full discovery and registration flow completed!";
}
```

## 架构设计

### 目录结构

```
modules/device_management/
├── include/LA/DeviceManagement/
│   ├── Registry/                 # 设备注册管理
│   │   ├── DeviceRegistry.h     # 设备注册表
│   │   ├── DeviceTypeInfo.h     # 设备类型信息
│   │   └── RegistrationManager.h # 注册管理器
│   ├── Factory/                  # 设备工厂
│   │   ├── DeviceFactory.h      # 设备工厂接口
│   │   ├── DeviceBuilder.h      # 设备构建器
│   │   └── FactoryManager.h     # 工厂管理器
│   ├── Discovery/                # 设备发现
│   │   ├── DeviceDiscovery.h    # 设备发现接口
│   │   ├── SerialDiscovery.h    # 串口设备发现
│   │   ├── NetworkDiscovery.h   # 网络设备发现
│   │   └── DiscoveryManager.h   # 发现管理器
│   ├── Lifecycle/                # 设备生命周期管理
│   │   ├── DeviceManager.h      # 设备管理器
│   │   ├── InstanceManager.h    # 实例管理器
│   │   └── LifecycleController.h # 生命周期控制器
│   ├── Monitor/                  # 设备监控
│   │   ├── DeviceMonitor.h      # 设备监控器
│   │   ├── HealthChecker.h      # 健康检查器
│   │   └── PerformanceTracker.h # 性能跟踪器
│   └── Core/                     # 核心定义
│       ├── DeviceManagementTypes.h # 类型定义
│       ├── DeviceInfo.h         # 设备信息
│       └── DeviceConfig.h       # 设备配置
├── src/                          # 实现文件
│   ├── Registry/
│   ├── Factory/
│   ├── Discovery/
│   ├── Lifecycle/
│   ├── Monitor/
│   └── Core/
├── tests/                        # 单元测试
│   ├── unit/
│   ├── integration/
│   └── fixtures/
├── docs/                         # 模块文档
│   ├── API.md
│   ├── Examples.md
│   └── Migration.md
├── examples/                     # 使用示例
└── CMakeLists.txt               # 构建配置
```

## 设备注册

新设备采用静态注册方法, 参考：
/**
 * @brief 注册设备
 *
 * @return true
 * @return false
 */
bool CSprm::registerCommDeviceType(void) {
    // 注册 Nova 系列设备
    const auto &property_list = getDevPropertyList();
    for (auto it = property_list.begin(); it != property_list.end(); it++) {
        device::factory::IDeviceFactory::getInstance().registerCommDeviceType(
            it->static_property.device_name.full_name,
            [](IPort *port, const QString &model, const QString &serial_num) -> IProximitySensor * { return new CSprm(port, model, serial_num); },
            [it]() -> IComm::StDevPortConfig { return it->comm_info.default_port_config; });
    }
    return true;
}

## 设备实例管理和显示

参考 F:\02_qt-code\13_LA-Function\LA\modules\core\device\deviceNode

## 实现状态

**当前状态**: ⚠️ **部分实现**

**已实现功能**:
- ✅ 基础设备管理
- ✅ 设备注册表
- ✅ 简单设备控制

**待完善功能**:
- 🔄 设备发现机制
- 🔄 设备状态监控
- 🔄 设备控制器扩展
- 🔄 标准模块目录结构

## 核心组件

### 1. 设备管理器 (DeviceManager)

**功能**: 统一管理所有设备

```cpp
class DeviceManager : public QObject {
    Q_OBJECT

public:
    static DeviceManager* instance();
    
    // 设备注册和管理
    bool registerDevice(const QString& deviceId, std::shared_ptr<IDevice> device);
    bool unregisterDevice(const QString& deviceId);
    std::shared_ptr<IDevice> getDevice(const QString& deviceId) const;
    QStringList getDeviceIds() const;
    
    // 设备发现
    void startDeviceDiscovery();
    void stopDeviceDiscovery();
    QList<DeviceInfo> getDiscoveredDevices() const;
    
    // 设备连接
    bool connectDevice(const QString& deviceId);
    bool disconnectDevice(const QString& deviceId);
    bool isDeviceConnected(const QString& deviceId) const;
    
    // 设备控制
    bool sendCommand(const QString& deviceId, const DeviceCommand& command);
    DeviceStatus getDeviceStatus(const QString& deviceId) const;

signals:
    void deviceRegistered(const QString& deviceId);
    void deviceUnregistered(const QString& deviceId);
    void deviceConnected(const QString& deviceId);
    void deviceDisconnected(const QString& deviceId);
    void deviceStatusChanged(const QString& deviceId, const DeviceStatus& status);
    void deviceDiscovered(const DeviceInfo& deviceInfo);

private:
    explicit DeviceManager(QObject* parent = nullptr);
    void initializeDiscovery();

private:
    QMap<QString, std::shared_ptr<IDevice>> m_devices;
    QMap<QString, std::shared_ptr<IDeviceController>> m_controllers;
    std::unique_ptr<DeviceDiscovery> m_discovery;
    std::unique_ptr<DeviceMonitor> m_monitor;
};
```

### 2. 设备接口 (IDevice)

**功能**: 定义设备的标准接口

```cpp
class IDevice {
public:
    virtual ~IDevice() = default;
    
    // 设备信息
    virtual QString deviceId() const = 0;
    virtual QString deviceName() const = 0;
    virtual DeviceType deviceType() const = 0;
    virtual QString manufacturer() const = 0;
    virtual QString model() const = 0;
    virtual QString version() const = 0;
    
    // 连接管理
    virtual bool connect() = 0;
    virtual bool disconnect() = 0;
    virtual bool isConnected() const = 0;
    virtual ConnectionStatus getConnectionStatus() const = 0;
    
    // 设备控制
    virtual bool sendCommand(const DeviceCommand& command) = 0;
    virtual DeviceResponse getResponse(int timeoutMs = 5000) = 0;
    virtual bool setParameter(const QString& name, const QVariant& value) = 0;
    virtual QVariant getParameter(const QString& name) const = 0;
    
    // 状态监控
    virtual DeviceStatus getStatus() const = 0;
    virtual QStringList getSupportedCommands() const = 0;
    virtual QStringList getAvailableParameters() const = 0;
    
    // 配置管理
    virtual bool loadConfiguration(const QString& configPath) = 0;
    virtual bool saveConfiguration(const QString& configPath) = 0;
    virtual DeviceConfiguration getConfiguration() const = 0;
    virtual bool setConfiguration(const DeviceConfiguration& config) = 0;
};
```

## CMake配置示例

```cmake
# 设备管理系统模块配置
cmake_minimum_required(VERSION 3.16)
project(LA_modules_device_management VERSION 1.0.0 LANGUAGES CXX)

# C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 查找依赖
find_package(Qt5 REQUIRED COMPONENTS Core Network SerialPort)

# 定义源文件
set(DEVICE_SOURCES
    src/manager/DeviceManager.cpp
    src/manager/DeviceRegistry.cpp
    src/controller/DeviceController.cpp
    src/controller/SerialDeviceController.cpp
    src/controller/NetworkDeviceController.cpp
    src/discovery/DeviceDiscovery.cpp
    src/discovery/SerialPortDiscovery.cpp
    src/discovery/NetworkDeviceDiscovery.cpp
    src/monitor/DeviceMonitor.cpp
    src/monitor/StatusMonitor.cpp
)

set(DEVICE_HEADERS
    include/LA/Modules/Device/IDeviceManager.h
    include/LA/Modules/Device/IDevice.h
    include/LA/Modules/Device/IDeviceController.h
    include/LA/Modules/Device/IDeviceRegistry.h
    include/LA/Modules/Device/DeviceTypes.h
    include/LA/Modules/Device/DeviceExports.h
    include/LA/Modules/Device/DeviceManagement.h
)

# 创建库目标
add_library(LA_modules_device_management_lib STATIC
    ${DEVICE_SOURCES}
    ${DEVICE_HEADERS}
)

# 设置包含目录
target_include_directories(LA_modules_device_management_lib
    PUBLIC
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
        $<INSTALL_INTERFACE:include>
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}/src
)

# 链接依赖
target_link_libraries(LA_modules_device_management_lib
    PUBLIC
        LA_infrastructure_interfaces_device_lib
        LA_infrastructure_communication_lib
        Qt5::Core
        Qt5::Network
        Qt5::SerialPort
    PRIVATE
        LA_support_logging_lib
        LA_support_config_lib
)
```

## 使用示例

### 基本设备管理

```cpp
#include <LA/Modules/Device/DeviceManagement.h>

using namespace LA::Modules::Device;

// 获取设备管理器
auto deviceManager = DeviceManager::instance();

// 启动设备发现
deviceManager->startDeviceDiscovery();

// 监听设备发现事件
connect(deviceManager, &DeviceManager::deviceDiscovered,
        [](const DeviceInfo& deviceInfo) {
    qDebug() << "发现设备:" << deviceInfo.name << deviceInfo.id;
});

// 连接设备
if (deviceManager->connectDevice("device001")) {
    // 发送控制命令
    DeviceCommand command;
    command.type = CommandType::Start;
    command.parameters["speed"] = 100;
    
    deviceManager->sendCommand("device001", command);
}
```

## 版本历史

### v1.0.0 (当前版本)

- 基础设备管理功能
- 设备注册表
- 简单设备控制
- 标准模块目录结构

### 计划功能 (v1.1.0)

- 设备发现机制
- 设备状态监控
- 设备控制器扩展
- 设备配置管理
- 子模块独立封装
