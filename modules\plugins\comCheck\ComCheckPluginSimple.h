#pragma once

#include <LA/Plugins/BasePlugin.h>
#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QTextEdit>
#include <QPushButton>
#include <QComboBox>
#include <QSpinBox>
#include <QLabel>
#include <QLineEdit>
#include <QCheckBox>
#include <QSerialPort>
#include <QSerialPortInfo>

namespace LA {
namespace Plugins {
namespace ComCheck {

/**
 * @brief ComCheck插件简化演示版本
 * 
 * 展示Linus式重构架构思路：
 * 1. 保留现有UI和用户体验
 * 2. 底层架构为统一通信系统预留接口
 * 3. 演示插件-设备交互模式
 */
class ComCheckPluginSimple : public BaseFunctionPlugin {
    Q_OBJECT
    Q_PLUGIN_METADATA(IID "com.LA.IPlugin/1.0" FILE "plugin.json")

public:
    explicit ComCheckPluginSimple(QObject* parent = nullptr);
    virtual ~ComCheckPluginSimple() = default;

    // IPlugin接口实现
    QString name() const override { return "ComCheck (演示版)"; }
    QString version() const override { return "1.0.0-demo"; }
    QString description() const override { return "通信检查插件 - Linus式重构演示"; }
    QString author() const override { return "LA Development Team"; }
    QString id() const override { return "com.la.plugins.comcheck.demo"; }
    QStringList dependencies() const override { return QStringList(); }
    QStringList optionalDependencies() const override { return QStringList(); }
    
    bool initialize() override;
    bool start() override { return true; }
    void stop() override {}
    void shutdown() override;
    
    // 状态查询接口
    PluginState getState() const override { return PluginState::Running; }
    bool isInitialized() const override { return true; }
    bool isRunning() const override { return true; }
    bool isEnabled() const override { return true; }
    
    // 其他必需的IPlugin接口方法
    PluginSourceType sourceType() const override { return PluginSourceType::DynamicPlugin; }
    PluginPriority priority() const override { return PluginPriority::Normal; }
    PluginMetadata metadata() const override { return PluginMetadata(); }
    QList<PluginPermission> requiredPermissions() const override { return QList<PluginPermission>(); }
    bool hasPermission(PluginPermission) const override { return true; }
    QVariantMap getConfiguration() const override { return QVariantMap(); }
    bool setConfiguration(const QVariantMap&) override { return true; }
    qint64 getMemoryUsage() const override { return 0; }
    double getCpuUsage() const override { return 0.0; }
    qint64 getLoadTime() const override { return 0; }
    
    // IFunctionPlugin接口实现
    QString getCategory() const override { return "Communication"; }
    QIcon getIcon() const override { return QIcon(); }
    bool supportsFloating() const override { return true; }
    QSize getMinimumSize() const override { return QSize(400, 300); }
    QSize getPreferredSize() const override { return QSize(600, 500); }
    QStringList getSupportedThemes() const override { return {"Default", "Dark"}; }

protected:
    // BaseFunctionPlugin接口实现
    QWidget* doCreateWidget(QWidget* parent = nullptr) override;

public:
    QWidget* createSidebarPanel(QWidget* parent = nullptr);
    
private slots:
    void onConnectPort();
    void onDisconnectPort();
    void onSendData();
    void onReceiveData();
    void onPortChanged();
    void onRefreshPorts();
    void onDeviceChanged();

private:
    void setupUI();
    void updatePortList();
    void updateDeviceList();
    void updateConnectionUI();
    void appendLog(const QString& message, const QString& prefix = "");
    
    // 演示：未来扩展接口
    void demonstrateUnifiedArchitecture();
    void showArchitectureInfo();
    
    // UI组件
    QWidget* m_mainWidget = nullptr;
    QComboBox* m_deviceComboBox = nullptr;       // 设备选择 (演示统一设备注册表)
    QComboBox* m_portComboBox = nullptr;         // 端口选择
    QSpinBox* m_baudrateSpinBox = nullptr;
    QPushButton* m_connectButton = nullptr;
    QPushButton* m_disconnectButton = nullptr;
    QPushButton* m_refreshButton = nullptr;
    QPushButton* m_architectureInfoButton = nullptr;  // 架构信息按钮
    QLineEdit* m_sendLineEdit = nullptr;
    QPushButton* m_sendButton = nullptr;
    QCheckBox* m_hexModeCheckBox = nullptr;
    QCheckBox* m_deviceCmdModeCheckBox = nullptr;     // 设备命令模式 (演示统一指令系统)
    QTextEdit* m_logTextEdit = nullptr;
    
    // 通信组件 (当前使用串口，未来扩展为统一通信系统)
    QSerialPort* m_serialPort = nullptr;
    bool m_isConnected = false;
    QString m_currentDeviceType;
};

} // namespace ComCheck
} // namespace Plugins
} // namespace LA