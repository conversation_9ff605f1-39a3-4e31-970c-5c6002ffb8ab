# Configuration File Handler Module
cmake_minimum_required(VERSION 3.16)

# 设置项目信息
set(MODULE_NAME "LA_Support_Config_File")
set(MODULE_VERSION "1.0.0")

# 查找依赖
find_package(Qt5 REQUIRED COMPONENTS Core Xml)

# 设置源文件
set(HEADERS
    IConfigFileHandler.h
    ConfigFileHandler.h
    JsonConfigFileHandler.h
    IniConfigFileHandler.h
)

set(SOURCES
    ConfigFileHandler.cpp
    JsonConfigFileHandler.cpp
    IniConfigFileHandler.cpp
)

# 创建库
add_library(${MODULE_NAME} STATIC ${SOURCES} ${HEADERS})

# 设置目标属性
set_target_properties(${MODULE_NAME} PROPERTIES
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
    VERSION ${MODULE_VERSION}
    EXPORT_NAME Config::File
)

# 链接依赖
target_link_libraries(${MODULE_NAME}
    PUBLIC
        Qt5::Core
        Qt5::Xml
        LA_Support_Foundation
)

# 设置包含目录
target_include_directories(${MODULE_NAME}
    PUBLIC
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/..>
        $<INSTALL_INTERFACE:include>
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}
)

# 编译定义
target_compile_definitions(${MODULE_NAME}
    PUBLIC
        LA_SUPPORT_CONFIG_FILE_LIBRARY
    PRIVATE
        QT_NO_KEYWORDS
        QT_USE_QSTRINGBUILDER
)

# 条件编译 - YAML支持
option(ENABLE_YAML_SUPPORT "Enable YAML configuration file support" OFF)
if(ENABLE_YAML_SUPPORT)
    find_package(yaml-cpp QUIET)
    if(yaml-cpp_FOUND)
        target_link_libraries(${MODULE_NAME} PRIVATE yaml-cpp)
        target_compile_definitions(${MODULE_NAME} PRIVATE ENABLE_YAML_SUPPORT)
        list(APPEND HEADERS YamlConfigFileHandler.h)
        list(APPEND SOURCES YamlConfigFileHandler.cpp)
        message(STATUS "YAML support enabled")
    else()
        message(WARNING "yaml-cpp not found, YAML support disabled")
    endif()
endif()

# 条件编译 - TOML支持
option(ENABLE_TOML_SUPPORT "Enable TOML configuration file support" OFF)
if(ENABLE_TOML_SUPPORT)
    # 注意：需要找到合适的TOML库
    target_compile_definitions(${MODULE_NAME} PRIVATE ENABLE_TOML_SUPPORT)
    list(APPEND HEADERS TomlConfigFileHandler.h)
    list(APPEND SOURCES TomlConfigFileHandler.cpp)
    message(STATUS "TOML support enabled")
endif()

# 条件编译 - 加密支持
option(ENABLE_ENCRYPTION_SUPPORT "Enable configuration file encryption" ON)
if(ENABLE_ENCRYPTION_SUPPORT)
    target_compile_definitions(${MODULE_NAME} PRIVATE ENABLE_ENCRYPTION_SUPPORT)
    message(STATUS "Configuration file encryption support enabled")
endif()

# 条件编译 - 压缩支持
option(ENABLE_COMPRESSION_SUPPORT "Enable configuration file compression" ON)
if(ENABLE_COMPRESSION_SUPPORT)
    target_compile_definitions(${MODULE_NAME} PRIVATE ENABLE_COMPRESSION_SUPPORT)
    message(STATUS "Configuration file compression support enabled")
endif()

# 安装规则
install(TARGETS ${MODULE_NAME}
    EXPORT LA_Support_Config_FileTargets
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
    INCLUDES DESTINATION include
)

# 安装头文件
install(FILES ${HEADERS}
    DESTINATION include/LA/Support/Config/File
)

# 导出目标
install(EXPORT LA_Support_Config_FileTargets
    FILE LA_Support_Config_FileTargets.cmake
    NAMESPACE LA::Support::Config::
    DESTINATION lib/cmake/LA_Support_Config_File
)

# 创建配置文件
include(CMakePackageConfigHelpers)

configure_package_config_file(
    "${CMAKE_CURRENT_SOURCE_DIR}/Config.cmake.in"
    "${CMAKE_CURRENT_BINARY_DIR}/LA_Support_Config_FileConfig.cmake"
    INSTALL_DESTINATION lib/cmake/LA_Support_Config_File
)

write_basic_package_version_file(
    "${CMAKE_CURRENT_BINARY_DIR}/LA_Support_Config_FileConfigVersion.cmake"
    VERSION ${MODULE_VERSION}
    COMPATIBILITY SameMajorVersion
)

install(FILES
    "${CMAKE_CURRENT_BINARY_DIR}/LA_Support_Config_FileConfig.cmake"
    "${CMAKE_CURRENT_BINARY_DIR}/LA_Support_Config_FileConfigVersion.cmake"
    DESTINATION lib/cmake/LA_Support_Config_File
)

# 测试
if(BUILD_TESTING)
    enable_testing()
    add_subdirectory(tests)
endif()

# 示例程序
option(BUILD_CONFIG_FILE_EXAMPLES "Build configuration file handler examples" OFF)
if(BUILD_CONFIG_FILE_EXAMPLES)
    add_subdirectory(examples)
endif()

# 基准测试
option(BUILD_CONFIG_FILE_BENCHMARKS "Build configuration file handler benchmarks" OFF)
if(BUILD_CONFIG_FILE_BENCHMARKS)
    add_subdirectory(benchmarks)
endif()

# 文档
if(BUILD_DOCUMENTATION)
    find_package(Doxygen)
    if(Doxygen_FOUND)
        set(DOXYGEN_IN ${CMAKE_CURRENT_SOURCE_DIR}/Doxyfile.in)
        set(DOXYGEN_OUT ${CMAKE_CURRENT_BINARY_DIR}/Doxyfile)
        configure_file(${DOXYGEN_IN} ${DOXYGEN_OUT} @ONLY)
        
        add_custom_target(doc_config_file ALL
            COMMAND ${DOXYGEN_EXECUTABLE} ${DOXYGEN_OUT}
            WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
            COMMENT "Generating API documentation for Config File module"
            VERBATIM
        )
    endif()
endif()

# 代码覆盖率
if(ENABLE_CODE_COVERAGE)
    if(CMAKE_COMPILER_IS_GNUCXX)
        target_compile_options(${MODULE_NAME} PRIVATE --coverage)
        target_link_libraries(${MODULE_NAME} PRIVATE gcov)
    endif()
endif()

# 静态分析
if(ENABLE_STATIC_ANALYSIS)
    find_program(CLANG_TIDY_EXE NAMES "clang-tidy")
    if(CLANG_TIDY_EXE)
        set_target_properties(${MODULE_NAME} PROPERTIES
            CXX_CLANG_TIDY "${CLANG_TIDY_EXE};-checks=-*,readability-*,performance-*,modernize-*"
        )
    endif()
endif()

# 内存检查
if(ENABLE_MEMORY_CHECK)
    find_program(VALGRIND_EXE NAMES "valgrind")
    if(VALGRIND_EXE)
        add_custom_target(memcheck_config_file
            COMMAND ${VALGRIND_EXE} --tool=memcheck --leak-check=full --show-reachable=yes 
                    $<TARGET_FILE:${MODULE_NAME}>
            DEPENDS ${MODULE_NAME}
            COMMENT "Running memory check on Config File module"
        )
    endif()
endif()

# 打印构建信息
message(STATUS "Configuring ${MODULE_NAME} v${MODULE_VERSION}")
message(STATUS "  - Source files: ${SOURCES}")
message(STATUS "  - Header files: ${HEADERS}")
message(STATUS "  - Qt5 Core: ${Qt5Core_VERSION}")
message(STATUS "  - Qt5 Xml: ${Qt5Xml_VERSION}")
message(STATUS "  - Install prefix: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "  - Build type: ${CMAKE_BUILD_TYPE}")

# 特性总结
message(STATUS "  - Features:")
message(STATUS "    * JSON support: YES (built-in)")
message(STATUS "    * INI support: YES (built-in)")
message(STATUS "    * XML support: YES (Qt5::Xml)")
message(STATUS "    * YAML support: ${ENABLE_YAML_SUPPORT}")
message(STATUS "    * TOML support: ${ENABLE_TOML_SUPPORT}")
message(STATUS "    * Encryption: ${ENABLE_ENCRYPTION_SUPPORT}")
message(STATUS "    * Compression: ${ENABLE_COMPRESSION_SUPPORT}")
message(STATUS "    * Testing: ${BUILD_TESTING}")
message(STATUS "    * Examples: ${BUILD_CONFIG_FILE_EXAMPLES}")
message(STATUS "    * Benchmarks: ${BUILD_CONFIG_FILE_BENCHMARKS}")
message(STATUS "    * Documentation: ${BUILD_DOCUMENTATION}")
message(STATUS "    * Code coverage: ${ENABLE_CODE_COVERAGE}")
message(STATUS "    * Static analysis: ${ENABLE_STATIC_ANALYSIS}")
message(STATUS "    * Memory check: ${ENABLE_MEMORY_CHECK}")